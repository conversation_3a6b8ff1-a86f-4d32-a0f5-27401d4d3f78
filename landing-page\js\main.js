/**
 * Mr.Tech Landing Page JavaScript
 * Handles interactivity, animations, and user interactions
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initNavigation();
    initScrollEffects();
    initFAQAccordion();
    initTestimonialSlider();
    initBackToTop();
    initCookieConsent();
    initAnimations();
});

/**
 * Navigation functionality
 */
function initNavigation() {
    const header = document.querySelector('.header');
    const menuToggle = document.querySelector('.menu-toggle');
    const navLinks = document.querySelector('.nav-links');
    
    // Header scroll effect
    window.addEventListener('scroll', function() {
        if (window.scrollY > 50) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });
    
    // Mobile menu toggle
    if (menuToggle) {
        menuToggle.addEventListener('click', function() {
            this.classList.toggle('active');
            
            if (navLinks) {
                navLinks.classList.toggle('active');
                
                // If menu is open, add these styles
                if (navLinks.classList.contains('active')) {
                    navLinks.style.display = 'flex';
                    navLinks.style.flexDirection = 'column';
                    navLinks.style.position = 'absolute';
                    navLinks.style.top = '80px';
                    navLinks.style.right = '0';
                    navLinks.style.backgroundColor = 'var(--color-light)';
                    navLinks.style.width = '250px';
                    navLinks.style.padding = '20px';
                    navLinks.style.boxShadow = 'var(--shadow-lg)';
                    navLinks.style.borderRadius = 'var(--radius-lg)';
                    navLinks.style.zIndex = '100';
                } else {
                    // If menu is closed, reset styles
                    setTimeout(() => {
                        navLinks.removeAttribute('style');
                    }, 300);
                }
            }
        });
    }
    
    // Smooth scroll for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            
            // Close mobile menu if open
            if (navLinks && navLinks.classList.contains('active')) {
                menuToggle.click();
            }
            
            const targetId = this.getAttribute('href');
            
            if (targetId === '#') return;
            
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                const headerHeight = header.offsetHeight;
                const targetPosition = targetElement.getBoundingClientRect().top + window.pageYOffset;
                
                window.scrollTo({
                    top: targetPosition - headerHeight,
                    behavior: 'smooth'
                });
            }
        });
    });
}

/**
 * Scroll effects and animations
 */
function initScrollEffects() {
    // Add active class to nav links based on scroll position
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    window.addEventListener('scroll', function() {
        let current = '';
        const headerHeight = document.querySelector('.header').offsetHeight;
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop - headerHeight - 100;
            const sectionHeight = section.offsetHeight;
            
            if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
                current = section.getAttribute('id');
            }
        });
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${current}`) {
                link.classList.add('active');
            }
        });
    });
    
    // Scroll indicator
    const scrollIndicator = document.querySelector('.scroll-indicator');
    
    if (scrollIndicator) {
        scrollIndicator.addEventListener('click', function() {
            const nextSection = document.querySelector('#features');
            
            if (nextSection) {
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = nextSection.getBoundingClientRect().top + window.pageYOffset;
                
                window.scrollTo({
                    top: targetPosition - headerHeight,
                    behavior: 'smooth'
                });
            }
        });
        
        // Hide scroll indicator after user scrolls down
        window.addEventListener('scroll', function() {
            if (window.scrollY > 100) {
                scrollIndicator.style.opacity = '0';
                setTimeout(() => {
                    scrollIndicator.style.visibility = 'hidden';
                }, 300);
            } else {
                scrollIndicator.style.visibility = 'visible';
                scrollIndicator.style.opacity = '0.8';
            }
        });
    }
}

/**
 * FAQ Accordion functionality
 */
function initFAQAccordion() {
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        
        if (question) {
            question.addEventListener('click', function() {
                // Close all other items
                faqItems.forEach(otherItem => {
                    if (otherItem !== item) {
                        otherItem.classList.remove('active');
                    }
                });
                
                // Toggle current item
                item.classList.toggle('active');
            });
        }
    });
}

/**
 * Testimonial slider functionality
 */
function initTestimonialSlider() {
    const testimonials = document.querySelectorAll('.testimonial');
    const prevBtn = document.querySelector('.testimonial-prev');
    const nextBtn = document.querySelector('.testimonial-next');
    
    if (testimonials.length === 0 || !prevBtn || !nextBtn) return;
    
    let currentIndex = 0;
    
    // Initially show only the first testimonial
    testimonials.forEach((testimonial, index) => {
        if (index !== 0) {
            testimonial.style.display = 'none';
        }
    });
    
    // Next button click
    nextBtn.addEventListener('click', function() {
        testimonials[currentIndex].style.display = 'none';
        currentIndex = (currentIndex + 1) % testimonials.length;
        testimonials[currentIndex].style.display = 'block';
        testimonials[currentIndex].style.animation = 'fadeIn 0.5s ease forwards';
    });
    
    // Previous button click
    prevBtn.addEventListener('click', function() {
        testimonials[currentIndex].style.display = 'none';
        currentIndex = (currentIndex - 1 + testimonials.length) % testimonials.length;
        testimonials[currentIndex].style.display = 'block';
        testimonials[currentIndex].style.animation = 'fadeIn 0.5s ease forwards';
    });
}

/**
 * Back to top button functionality
 */
function initBackToTop() {
    const backToTopBtn = document.getElementById('back-to-top');
    
    if (!backToTopBtn) return;
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 300) {
            backToTopBtn.classList.add('visible');
        } else {
            backToTopBtn.classList.remove('visible');
        }
    });
    
    backToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

/**
 * Cookie consent banner functionality
 */
function initCookieConsent() {
    const cookieBanner = document.querySelector('.cookie-consent');
    const acceptBtn = document.querySelector('.cookie-accept');
    const declineBtn = document.querySelector('.cookie-decline');
    
    if (!cookieBanner || !acceptBtn || !declineBtn) return;
    
    // Check if user has already accepted cookies
    const cookiesAccepted = localStorage.getItem('cookiesAccepted');
    
    if (!cookiesAccepted) {
        // Show cookie banner after a short delay
        setTimeout(() => {
            cookieBanner.classList.add('show');
        }, 2000);
        
        // Handle accept button
        acceptBtn.addEventListener('click', function() {
            localStorage.setItem('cookiesAccepted', 'true');
            cookieBanner.classList.remove('show');
        });
        
        // Handle decline button
        declineBtn.addEventListener('click', function() {
            cookieBanner.classList.remove('show');
        });
    }
}

/**
 * Element animations on scroll
 */
function initAnimations() {
    // Add animate-in class to elements that should animate when in view
    const animateElements = document.querySelectorAll('.feature-card, .service-card, .step, .testimonial, .faq-item');
    
    // Add animation delay classes
    animateElements.forEach((el, index) => {
        const modulo = index % 3;
        
        if (modulo === 0) {
            el.classList.add('animate-delay-1');
        } else if (modulo === 1) {
            el.classList.add('animate-delay-2');
        } else {
            el.classList.add('animate-delay-3');
        }
    });
    
    // Function to check if element is in viewport
    function isInViewport(element) {
        const rect = element.getBoundingClientRect();
        return (
            rect.top <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.bottom >= 0
        );
    }
    
    // Function to handle scroll animation
    function handleScrollAnimation() {
        animateElements.forEach(el => {
            if (isInViewport(el) && !el.classList.contains('animate-in')) {
                el.classList.add('animate-in');
            }
        });
    }
    
    // Run once on load
    handleScrollAnimation();
    
    // Run on scroll
    window.addEventListener('scroll', handleScrollAnimation);
} 