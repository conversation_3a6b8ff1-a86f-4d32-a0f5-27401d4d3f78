import React from 'react';
import { usePermissions } from '../../hooks/usePermissions';
import type { Permission } from '../../types/permissions';

interface PermissionGateProps {
  children: React.ReactNode;
  permissions?: Permission[];
  requireAll?: boolean;
  fallback?: React.ReactNode;
  showError?: boolean;
}

const PermissionGate: React.FC<PermissionGateProps> = ({
  children,
  permissions = [],
  requireAll = false,
  fallback = null,
  showError = false,
}) => {
  const { canPerformAction } = usePermissions();

  // If no permissions specified, just check if user is authenticated
  if (permissions.length === 0) {
    return <>{children}</>;
  }

  const hasAccess = canPerformAction(permissions, requireAll);

  if (!hasAccess) {
    if (showError) {
      return (
        <div className='text-center py-8'>
          <p className='text-destructive'>You don't have permission to view this content.</p>
        </div>
      );
    }
    return <>{fallback}</>;
  }

  return <>{children}</>;
};

export default PermissionGate;
