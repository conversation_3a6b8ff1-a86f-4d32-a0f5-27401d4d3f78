import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

/// Spacing utility class for consistent spacing throughout the app
/// Uses the 8px grid system for all spacing values
class Spacing {
  // Private constructor to prevent instantiation
  Spacing._();

  // Base spacing values from AppTheme
  static const double xs = AppTheme.spaceXS;   // 4px
  static const double s = AppTheme.spaceS;     // 8px
  static const double m = AppTheme.spaceM;     // 16px
  static const double l = AppTheme.spaceL;     // 24px
  static const double xl = AppTheme.spaceXL;   // 32px
  static const double xxl = AppTheme.space2XL; // 48px
  static const double xxxl = AppTheme.space3XL; // 64px

  // SizedBox widgets for vertical spacing
  static const Widget verticalXS = SizedBox(height: xs);
  static const Widget verticalS = SizedBox(height: s);
  static const Widget verticalM = SizedBox(height: m);
  static const Widget verticalL = SizedBox(height: l);
  static const Widget verticalXL = SizedBox(height: xl);
  static const Widget verticalXXL = SizedBox(height: xxl);
  static const Widget verticalXXXL = SizedBox(height: xxxl);

  // SizedBox widgets for horizontal spacing
  static const Widget horizontalXS = SizedBox(width: xs);
  static const Widget horizontalS = SizedBox(width: s);
  static const Widget horizontalM = SizedBox(width: m);
  static const Widget horizontalL = SizedBox(width: l);
  static const Widget horizontalXL = SizedBox(width: xl);
  static const Widget horizontalXXL = SizedBox(width: xxl);
  static const Widget horizontalXXXL = SizedBox(width: xxxl);

  // EdgeInsets for padding
  static const EdgeInsets paddingXS = EdgeInsets.all(xs);
  static const EdgeInsets paddingS = EdgeInsets.all(s);
  static const EdgeInsets paddingM = EdgeInsets.all(m);
  static const EdgeInsets paddingL = EdgeInsets.all(l);
  static const EdgeInsets paddingXL = EdgeInsets.all(xl);

  // Horizontal padding
  static const EdgeInsets horizontalPaddingXS = EdgeInsets.symmetric(horizontal: xs);
  static const EdgeInsets horizontalPaddingS = EdgeInsets.symmetric(horizontal: s);
  static const EdgeInsets horizontalPaddingM = EdgeInsets.symmetric(horizontal: m);
  static const EdgeInsets horizontalPaddingL = EdgeInsets.symmetric(horizontal: l);
  static const EdgeInsets horizontalPaddingXL = EdgeInsets.symmetric(horizontal: xl);

  // Vertical padding
  static const EdgeInsets verticalPaddingXS = EdgeInsets.symmetric(vertical: xs);
  static const EdgeInsets verticalPaddingS = EdgeInsets.symmetric(vertical: s);
  static const EdgeInsets verticalPaddingM = EdgeInsets.symmetric(vertical: m);
  static const EdgeInsets verticalPaddingL = EdgeInsets.symmetric(vertical: l);
  static const EdgeInsets verticalPaddingXL = EdgeInsets.symmetric(vertical: xl);

  // Common component padding
  static const EdgeInsets cardPadding = EdgeInsets.all(AppTheme.cardPadding);
  static const EdgeInsets buttonPadding = EdgeInsets.symmetric(
    horizontal: AppTheme.buttonPadding,
    vertical: AppTheme.spaceS,
  );
  static const EdgeInsets inputPadding = EdgeInsets.all(AppTheme.inputPadding);
  static const EdgeInsets pagePadding = EdgeInsets.symmetric(
    horizontal: AppTheme.pageHorizontalPadding,
    vertical: AppTheme.pageVerticalPadding,
  );
  static const EdgeInsets pageHorizontalPadding = EdgeInsets.symmetric(
    horizontal: AppTheme.pageHorizontalPadding,
  );

  // EdgeInsets for margins
  static const EdgeInsets marginXS = EdgeInsets.all(xs);
  static const EdgeInsets marginS = EdgeInsets.all(s);
  static const EdgeInsets marginM = EdgeInsets.all(m);
  static const EdgeInsets marginL = EdgeInsets.all(l);
  static const EdgeInsets marginXL = EdgeInsets.all(xl);

  // Common component margins
  static const EdgeInsets cardMargin = EdgeInsets.all(AppTheme.cardMargin);
  static const EdgeInsets listItemMargin = EdgeInsets.only(bottom: AppTheme.listItemSpacing);

  // Custom spacing methods
  static Widget vertical(double height) => SizedBox(height: height);
  static Widget horizontal(double width) => SizedBox(width: width);

  /// Creates vertical spacing that follows the 8px grid
  /// Rounds the input to the nearest 8px multiple
  static Widget verticalGrid(double height) {
    final gridHeight = (height / 8).round() * 8.0;
    return SizedBox(height: gridHeight);
  }

  /// Creates horizontal spacing that follows the 8px grid
  /// Rounds the input to the nearest 8px multiple
  static Widget horizontalGrid(double width) {
    final gridWidth = (width / 8).round() * 8.0;
    return SizedBox(width: gridWidth);
  }

  /// Creates padding that follows the 8px grid
  static EdgeInsets paddingGrid(double value) {
    final gridValue = (value / 8).round() * 8.0;
    return EdgeInsets.all(gridValue);
  }

  /// Creates symmetric padding that follows the 8px grid
  static EdgeInsets paddingSymmetricGrid({
    double horizontal = 0,
    double vertical = 0,
  }) {
    final gridHorizontal = (horizontal / 8).round() * 8.0;
    final gridVertical = (vertical / 8).round() * 8.0;
    return EdgeInsets.symmetric(
      horizontal: gridHorizontal,
      vertical: gridVertical,
    );
  }

  /// Creates margin that follows the 8px grid
  static EdgeInsets marginGrid(double value) {
    final gridValue = (value / 8).round() * 8.0;
    return EdgeInsets.all(gridValue);
  }

  /// Validates if a value follows the 8px grid system
  static bool isOnGrid(double value) {
    return value % 8 == 0;
  }

  /// Snaps a value to the nearest 8px grid point
  static double snapToGrid(double value) {
    return (value / 8).round() * 8.0;
  }
}

/// Extension on double to easily convert to grid-aligned spacing
extension SpacingExtension on double {
  /// Converts the value to the nearest 8px grid point
  double get onGrid => Spacing.snapToGrid(this);

  /// Creates a vertical SizedBox with grid-aligned height
  Widget get verticalSpace => Spacing.verticalGrid(this);

  /// Creates a horizontal SizedBox with grid-aligned width
  Widget get horizontalSpace => Spacing.horizontalGrid(this);

  /// Creates EdgeInsets.all with grid-aligned value
  EdgeInsets get padding => Spacing.paddingGrid(this);

  /// Creates EdgeInsets.all with grid-aligned value
  EdgeInsets get margin => Spacing.marginGrid(this);
}

/// Extension on int to easily convert to grid-aligned spacing
extension IntSpacingExtension on int {
  /// Converts the value to the nearest 8px grid point
  double get onGrid => Spacing.snapToGrid(toDouble());

  /// Creates a vertical SizedBox with grid-aligned height
  Widget get verticalSpace => Spacing.verticalGrid(toDouble());

  /// Creates a horizontal SizedBox with grid-aligned width
  Widget get horizontalSpace => Spacing.horizontalGrid(toDouble());

  /// Creates EdgeInsets.all with grid-aligned value
  EdgeInsets get padding => Spacing.paddingGrid(toDouble());

  /// Creates EdgeInsets.all with grid-aligned value
  EdgeInsets get margin => Spacing.marginGrid(toDouble());
}
