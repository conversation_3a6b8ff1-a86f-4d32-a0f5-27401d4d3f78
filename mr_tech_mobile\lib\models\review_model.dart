import 'package:cloud_firestore/cloud_firestore.dart';

class ReviewModel {
  final String id;
  final String requestId;
  final String customerId;
  final String customerName;
  final String technicianId;
  final String serviceId;
  final double rating;
  final String comment;
  final DateTime createdAt;

  ReviewModel({
    this.id = '',
    required this.requestId,
    required this.customerId,
    required this.customerName,
    required this.technicianId,
    required this.serviceId,
    required this.rating,
    required this.comment,
    required this.createdAt,
  });

  // Factory constructor to create a ReviewModel from a Firestore document
  factory ReviewModel.fromFirestore(
    DocumentSnapshot<Map<String, dynamic>> doc,
  ) {
    final data = doc.data() ?? {};

    return ReviewModel(
      id: doc.id,
      requestId: data['request_id'] ?? '',
      customerId: data['customer_id'] ?? '',
      customerName: data['customer_name'] ?? '',
      technicianId: data['technician_id'] ?? '',
      serviceId: data['service_id'] ?? '',
      rating: (data['rating'] ?? 0.0).toDouble(),
      comment: data['comment'] ?? '',
      createdAt: (data['created_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  // Factory method to create ReviewModel from a document ID and Map
  // PHASE 1: Prefer snake_case but maintain full backward compatibility
  factory ReviewModel.fromMap(String id, Map<String, dynamic> data) {
    try {
      // Phase 1 Migration: Prefer snake_case, fallback to camelCase for safety

      // Log field access patterns for monitoring
      if (data['customer_id'] == null && data['customerId'] != null) {
        print(
          'ReviewModel.fromMap: Using camelCase fallback for customer_id in review $id',
        );
      }

      return ReviewModel(
        id: id,
        // Phase 1: Prefer snake_case with safe fallbacks
        requestId: data['request_id'] ?? data['requestId'] ?? '',
        customerId: data['customer_id'] ?? data['customerId'] ?? '',
        customerName: data['customer_name'] ?? data['customerName'] ?? '',
        technicianId: data['technician_id'] ?? data['technicianId'] ?? '',
        serviceId: data['service_id'] ?? data['serviceId'] ?? '',
        rating: (data['rating'] ?? 0.0).toDouble(),
        comment: data['comment'] ?? '',
        createdAt:
            _parseTimestamp(data['created_at'] ?? data['createdAt']) ??
            DateTime.now(),
      );
    } catch (e) {
      print('Error parsing ReviewModel from map: $e for ID: $id');
      // Return safe empty review instead of crashing
      return ReviewModel(
        id: id,
        requestId: '',
        customerId: '',
        customerName: '',
        technicianId: '',
        serviceId: '',
        rating: 0.0,
        comment: 'Error loading review',
        createdAt: DateTime.now(),
      );
    }
  }

  // Helper method to safely parse timestamps
  static DateTime? _parseTimestamp(dynamic timestamp) {
    if (timestamp == null) return null;
    if (timestamp is Timestamp) return timestamp.toDate();
    if (timestamp is DateTime) return timestamp;
    if (timestamp is String) {
      try {
        return DateTime.parse(timestamp);
      } catch (e) {
        print('Error parsing timestamp string: $e');
        return null;
      }
    }
    return null;
  }

  // Convert ReviewModel to a Map for Firestore
  // PHASE 2: Write ONLY snake_case (no more dual-write)
  Map<String, dynamic> toFirestore() {
    return {
      // Use ONLY snake_case fields - standardized format
      'request_id': requestId,
      'customer_id': customerId,
      'customer_name': customerName,
      'technician_id': technicianId,
      'service_id': serviceId,
      'rating': rating,
      'comment': comment,
      'created_at': createdAt,
    };
  }

  // Create a copy of the review with updated fields
  ReviewModel copyWith({
    String? id,
    String? requestId,
    String? customerId,
    String? customerName,
    String? technicianId,
    String? serviceId,
    double? rating,
    String? comment,
    DateTime? createdAt,
  }) {
    return ReviewModel(
      id: id ?? this.id,
      requestId: requestId ?? this.requestId,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      technicianId: technicianId ?? this.technicianId,
      serviceId: serviceId ?? this.serviceId,
      rating: rating ?? this.rating,
      comment: comment ?? this.comment,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
