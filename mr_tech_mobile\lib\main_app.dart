import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:provider/provider.dart';
import 'services/translation_service.dart';
import 'services/notification_service.dart';
import 'services/theme_service.dart';
import 'services/auth_service.dart';
import 'services/database_service.dart';
import 'services/app_service.dart';
import 'services/network_service.dart';
import 'views/welcome_screen.dart';
import 'views/main_navigation.dart';
import 'views/onboarding_screen.dart';
import 'package:flutter/foundation.dart';
import 'dart:async';

class MainApp extends StatefulWidget {
  const MainApp({super.key});

  @override
  State<MainApp> createState() => _MainAppState();
}

class _MainAppState extends State<MainApp> with WidgetsBindingObserver {
  // Services
  final AppService _appService = AppService();
  final ThemeService _themeService = ThemeService();
  final TranslationService _translationService = TranslationService();
  final NotificationService _notificationService = NotificationService();
  final AuthService _authService = AuthService();
  final GlobalKey<NavigatorState> _navigatorKey = GlobalKey<NavigatorState>();

  bool _isLoading = true;
  bool _hasCompletedOnboarding = false;
  bool _isAppInBackground = false;
  bool _isResuming = false;
  Timer? _resumeTimer;

  @override
  void initState() {
    super.initState();

    // Configure system UI for iOS safe area handling
    _configureSystemUI();

    // Add observer for app lifecycle events
    WidgetsBinding.instance.addObserver(this);

    // Initialize app
    _initializeApp();
  }

  /// Configure system UI for proper iOS safe area handling
  void _configureSystemUI() {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
        statusBarBrightness: Brightness.light,
        systemNavigationBarColor: Colors.transparent,
        systemNavigationBarDividerColor: Colors.transparent,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );

    // Enable edge-to-edge display
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.edgeToEdge,
    );
  }

  @override
  void dispose() {
    // Cancel any pending timers
    _resumeTimer?.cancel();

    // Remove observer
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  // Enhanced app lifecycle handling with better resume management
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    debugPrint('App lifecycle state changed to: $state');

    switch (state) {
      case AppLifecycleState.paused:
        _handleAppPaused();
        break;
      case AppLifecycleState.inactive:
        _handleAppInactive();
        break;
      case AppLifecycleState.detached:
        _handleAppDetached();
        break;
      case AppLifecycleState.resumed:
        _handleAppResumed();
        break;
      case AppLifecycleState.hidden:
        _handleAppHidden();
        break;
    }
  }

  /// Handle app paused state
  void _handleAppPaused() {
    _isAppInBackground = true;
    _appService.onAppBackground();
  }

  /// Handle app inactive state
  void _handleAppInactive() {
    // App is transitioning between foreground and background
    // Don't perform heavy operations here
  }

  /// Handle app detached state
  void _handleAppDetached() {
    _isAppInBackground = true;
    _appService.onAppBackground();
  }

  /// Handle app hidden state (iOS 13+)
  void _handleAppHidden() {
    _isAppInBackground = true;
    _appService.onAppBackground();
  }

  /// Enhanced app resume handling with error recovery
  void _handleAppResumed() {
    if (!_isAppInBackground) return; // Already in foreground

    _isAppInBackground = false;
    _isResuming = true;

    // Cancel any existing resume timer
    _resumeTimer?.cancel();

    // Use a timer to handle resume operations gracefully
    _resumeTimer = Timer(const Duration(milliseconds: 500), () async {
      try {
        await _performResumeOperations();
      } catch (e) {
        debugPrint('Error during app resume: $e');
        // Attempt recovery
        await _attemptResumeRecovery();
      } finally {
        if (mounted) {
          setState(() {
            _isResuming = false;
          });
        }
      }
    });
  }

  /// Perform operations when app resumes
  Future<void> _performResumeOperations() async {
    debugPrint('Performing app resume operations...');

    try {
      // Re-enable app foreground state
      await _appService.onAppForeground();

      // Refresh authentication state
      await _refreshAuthState();

      // Refresh critical app data
      await _refreshAppData();

      // Re-initialize any failed services
      await _reinitializeServicesIfNeeded();

      debugPrint('App resume operations completed successfully');
    } catch (e) {
      debugPrint('Error in resume operations: $e');
      rethrow;
    }
  }

  /// Attempt to recover from resume errors
  Future<void> _attemptResumeRecovery() async {
    debugPrint('Attempting app resume recovery...');

    try {
      // Force refresh the UI
      if (mounted) {
        setState(() {});
      }

      // Reinitialize critical services
      await _reinitializeCriticalServices();

      // Clear any cached states that might be causing issues
      await _clearProblematicCaches();

      debugPrint('App resume recovery completed');
    } catch (e) {
      debugPrint('Error in resume recovery: $e');
      // If recovery fails, we'll let the app continue with potentially degraded functionality
    }
  }

  /// Refresh authentication state
  Future<void> _refreshAuthState() async {
    try {
      // Force refresh the current user
      await FirebaseAuth.instance.currentUser?.reload();

      // Update onboarding status if needed
      final currentUser = _authService.currentUser;
      if (currentUser != null) {
        _hasCompletedOnboarding = _appService.hasCompletedOnboarding;
      }
    } catch (e) {
      debugPrint('Error refreshing auth state: $e');
    }
  }

  /// Refresh critical app data
  Future<void> _refreshAppData() async {
    try {
      // Refresh notification service
      if (_notificationService.isInitialized) {
        await _notificationService.refreshToken();
      }

      // Refresh any cached data that might be stale
      // This is handled by individual services
    } catch (e) {
      debugPrint('Error refreshing app data: $e');
    }
  }

  /// Reinitialize services if needed
  Future<void> _reinitializeServicesIfNeeded() async {
    try {
      // Check if app service needs reinitialization
      if (!_appService.isInitialized) {
        await _appService.initializeApp();
      }

      // Check if notification service needs reinitialization
      if (!_notificationService.isInitialized) {
        await _notificationService.initialize(_navigatorKey);
      }
    } catch (e) {
      debugPrint('Error reinitializing services: $e');
    }
  }

  /// Reinitialize critical services for recovery
  Future<void> _reinitializeCriticalServices() async {
    try {
      // Reinitialize app service
      await _appService.initializeApp();

      // Reinitialize notification service
      await _notificationService.initialize(_navigatorKey);

      // Update onboarding status
      _hasCompletedOnboarding = _appService.hasCompletedOnboarding;
    } catch (e) {
      debugPrint('Error reinitializing critical services: $e');
    }
  }

  /// Clear problematic caches
  Future<void> _clearProblematicCaches() async {
    try {
      // Clear any image caches that might be causing graphics issues
      PaintingBinding.instance.imageCache.clear();
      PaintingBinding.instance.imageCache.clearLiveImages();

      // Force garbage collection
      if (kDebugMode) {
        // Only in debug mode to avoid performance issues in release
        await Future.delayed(const Duration(milliseconds: 100));
      }
    } catch (e) {
      debugPrint('Error clearing caches: $e');
    }
  }

  Future<void> _initializeApp() async {
    try {
      // Initialize app service (now optimized for fast startup)
      await _appService.initialize();

      // Get onboarding status (now available immediately)
      _hasCompletedOnboarding = _appService.hasCompletedOnboarding;

      // Update loading state immediately after critical services are ready
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }

      // Continue initialization in background without blocking UI
      _continueInitializationInBackground();
    } catch (e) {
      debugPrint('Error initializing app: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Continue non-critical initialization in background
  void _continueInitializationInBackground() {
    Future.microtask(() async {
      try {
        // Initialize notification service with navigator key (non-blocking)
        await _notificationService.initialize(_navigatorKey);

        // Complete any remaining app service initialization
        await _appService.completeAsyncInitialization();

        debugPrint('Background initialization completed');
      } catch (e) {
        debugPrint('Error in background initialization: $e');
      }
    });
  }

  // Handle locale changes
  void _onLocaleChanged(Locale locale) {
    if (mounted) {
      setState(() {
        _translationService.currentLocale = locale;
      });
    }
  }

  // Handle theme changes
  void _onThemeChanged(ThemeMode themeMode) {
    if (mounted) {
      setState(() {
        // Using the toggleTheme method from ThemeService
        _themeService.toggleTheme(themeMode == ThemeMode.dark);
      });
    }
  }

  // Handle authentication completion
  void _onAuthenticationComplete() {
    // Update UI if needed
    if (mounted) {
      setState(() {});
    }
  }

  // Handle onboarding completion
  void _onOnboardingComplete() {
    if (mounted) {
      setState(() {
        _hasCompletedOnboarding = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // If still loading or resuming, show loading screen
    if (_isLoading || _isResuming) {
      return MaterialApp(
        home: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(_isResuming ? 'Resuming...' : 'Loading...'),
              ],
            ),
          ),
        ),
      );
    }

    // Get current user
    final User? currentUser = _authService.currentUser;

    // Create providers with error handling
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<ThemeService>.value(value: _themeService),
        ChangeNotifierProvider<TranslationService>.value(
          value: _translationService,
        ),
        Provider<AuthService>.value(value: _authService),
        Provider<DatabaseService>.value(value: DatabaseService()),
        Provider<NotificationService>.value(value: _notificationService),
        Provider<AppService>.value(value: _appService),
      ],
      child: Consumer<ThemeService>(
        builder: (context, themeService, child) {
          return Consumer<TranslationService>(
            builder: (context, translationService, child) {
              // Get text direction based on current locale
              final textDirection = translationService.textDirection;

              return MaterialApp(
                navigatorKey: _navigatorKey,
                title: 'Mr.Tech',
                theme: themeService.getLightTheme(context),
                darkTheme: themeService.getDarkTheme(context),
                themeMode: themeService.themeMode,
                debugShowCheckedModeBanner: false,
                localizationsDelegates: const [
                  GlobalMaterialLocalizations.delegate,
                  GlobalWidgetsLocalizations.delegate,
                  GlobalCupertinoLocalizations.delegate,
                ],
                supportedLocales: const [
                  Locale('ar'), // Arabic - first for priority
                  Locale('en'), // English
                ],
                locale: translationService.currentLocale,
                // Set the text direction explicitly based on locale
                builder: (context, child) {
                  // Apply Directionality to the entire app
                  return Directionality(
                    textDirection: textDirection,
                    child: Builder(
                      builder:
                          (context) => MediaQuery(
                            // Preserve original MediaQuery but ensure RTL/LTR is correct
                            data: MediaQuery.of(context).copyWith(
                              // TextDirection in MediaQuery.data is not available in newer Flutter versions
                              // Using textScaler instead to preserve the media query update
                              textScaler: MediaQuery.of(context).textScaler,
                            ),
                            child: child!,
                          ),
                    ),
                  );
                },
                home: _buildHomeScreen(currentUser),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildHomeScreen(User? currentUser) {
    // If user is logged in and has completed onboarding
    if (currentUser != null && _hasCompletedOnboarding) {
      return const MainNavigation();
    }

    // If user is logged in but hasn't completed onboarding
    if (currentUser != null && !_hasCompletedOnboarding) {
      return const OnboardingScreen();
    }

    // If user is not logged in
    return WelcomeScreen(
      onLocaleChanged: _onLocaleChanged,
      onAuthenticationComplete: _onAuthenticationComplete,
    );
  }
}
