import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { <PERSON><PERSON> } from '../ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '../ui/tabs';
import { ScrollArea } from '../ui/scroll-area';
import { Separator } from '../ui/separator';
import {
  Activity,
  AlertCircle,
  Bell,
  CheckCircle,
  Clock,
  Users,
  Wifi,
  WifiOff,
  XCircle,
} from 'lucide-react';
import {
  useRealtimeRequests,
  useRealtimeRequestsByStatus,
  useRealtimeStatusHealth,
  useStatusChangeNotifications,
} from '../../hooks/useRealtimeStatus';
import statusNotificationService, {
  type StatusChangeNotification,
} from '../../services/statusNotificationService';
import { RequestStatus } from '../../types/request';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import realtimeStatusService from '../../services/realtimeStatusService';

/**
 * Demo component showcasing real-time status update mechanism
 */
const RealtimeStatusDemo: React.FC = () => {
  const [notifications, setNotifications] = useState<StatusChangeNotification[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);
  const [selectedRequestId, setSelectedRequestId] = useState<string | null>(null);

  // Real-time hooks
  const { requests: allRequests, loading: allLoading } = useRealtimeRequests();
  const { requests: pendingRequests, loading: pendingLoading } = useRealtimeRequestsByStatus(
    RequestStatus.PENDING,
  );
  const { requests: inProgressRequests, loading: inProgressLoading } = useRealtimeRequestsByStatus(
    RequestStatus.IN_PROGRESS,
  );
  const { health, listenerCounts, updateHealth, stopAllListeners } = useRealtimeStatusHealth();

  // Status change notifications for selected request
  useStatusChangeNotifications(selectedRequestId, (oldStatus, newStatus, request) => {
    console.warn(`Request ${request.id} status changed: ${oldStatus} → ${newStatus}`);
  });

  // Subscribe to notifications
  useEffect(() => {
    const unsubscribe = statusNotificationService.subscribe((notification) => {
      setNotifications((prev) => [notification, ...prev.slice(0, 49)]); // Keep last 50
    });

    return unsubscribe;
  }, []);

  // Start/stop monitoring
  const toggleMonitoring = () => {
    if (isMonitoring) {
      statusNotificationService.stopAllMonitoring();
      setIsMonitoring(false);
    } else {
      // Start monitoring all requests
      const requestIds = allRequests.map((r) => r.id);
      statusNotificationService.startMonitoring(requestIds);
      setIsMonitoring(true);
    }
  };

  // Clear notifications
  const clearNotifications = () => {
    setNotifications([]);
    statusNotificationService.clearNotifications();
  };

  // Mark all as read
  const markAllAsRead = () => {
    notifications.forEach((n) => {
      statusNotificationService.markAsRead(n.id);
    });
    setNotifications((prev) => prev.map((n) => ({ ...n, read: true })));
  };

  // Get status color
  const getStatusColor = (status: RequestStatus): string => {
    switch (status) {
      case RequestStatus.PENDING:
        return 'bg-yellow-100 text-yellow-800';
      case RequestStatus.APPROVED:
        return 'bg-blue-100 text-blue-800';
      case RequestStatus.IN_PROGRESS:
        return 'bg-purple-100 text-purple-800';
      case RequestStatus.COMPLETED:
        return 'bg-green-100 text-green-800';
      case RequestStatus.CANCELLED:
      case RequestStatus.REFUSED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Get priority icon
  const getPriorityIcon = (priority: 'low' | 'medium' | 'high') => {
    switch (priority) {
      case 'high':
        return <AlertCircle className='h-4 w-4 text-red-500' />;
      case 'medium':
        return <Clock className='h-4 w-4 text-yellow-500' />;
      case 'low':
        return <CheckCircle className='h-4 w-4 text-green-500' />;
    }
  };

  const unreadCount = notifications.filter((n) => !n.read).length;
  const stats = statusNotificationService.getStats();

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h2 className='text-2xl font-bold tracking-tight'>Real-Time Status Updates</h2>
          <p className='text-muted-foreground'>
            Monitor request status changes in real-time across mobile and web platforms
          </p>
        </div>
        <div className='flex items-center gap-2'>
          {health.isHealthy ? (
            <Wifi className='h-5 w-5 text-green-500' />
          ) : (
            <WifiOff className='h-5 w-5 text-red-500' />
          )}
          <Button onClick={toggleMonitoring} variant={isMonitoring ? 'destructive' : 'default'}>
            {isMonitoring ? 'Stop Monitoring' : 'Start Monitoring'}
          </Button>
        </div>
      </div>

      {/* Health Status */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Activity className='h-5 w-5' />
            System Health
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
            <div className='text-center'>
              <div className='text-2xl font-bold text-blue-600'>{listenerCounts.total}</div>
              <div className='text-sm text-muted-foreground'>Active Listeners</div>
            </div>
            <div className='text-center'>
              <div className='text-2xl font-bold text-green-600'>{stats.totalNotifications}</div>
              <div className='text-sm text-muted-foreground'>Total Notifications</div>
            </div>
            <div className='text-center'>
              <div className='text-2xl font-bold text-yellow-600'>{unreadCount}</div>
              <div className='text-sm text-muted-foreground'>Unread</div>
            </div>
            <div className='text-center'>
              <div className='text-2xl font-bold text-purple-600'>{stats.activeListeners}</div>
              <div className='text-sm text-muted-foreground'>Monitoring Sessions</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue='requests' className='space-y-4'>
        <TabsList>
          <TabsTrigger value='requests'>Live Requests</TabsTrigger>
          <TabsTrigger value='notifications' className='relative'>
            Notifications
            {unreadCount > 0 && (
              <Badge className='ml-2 h-5 w-5 rounded-full p-0 text-xs'>{unreadCount}</Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value='monitoring'>Monitoring</TabsTrigger>
        </TabsList>

        {/* Live Requests Tab */}
        <TabsContent value='requests' className='space-y-4'>
          <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
            {/* All Requests */}
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Users className='h-4 w-4' />
                  All Requests
                  <Badge variant='secondary'>{allRequests.length}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className='h-64'>
                  {allLoading ? (
                    <div className='text-center text-muted-foreground'>Loading...</div>
                  ) : (
                    <div className='space-y-2'>
                      {allRequests.slice(0, 10).map((request) => (
                        <div
                          key={request.id}
                          className={`p-2 rounded border cursor-pointer hover:bg-gray-50 ${
                            selectedRequestId === request.id ? 'ring-2 ring-blue-500' : ''
                          }`}
                          onClick={() => setSelectedRequestId(request.id)}
                        >
                          <div className='flex items-center justify-between'>
                            <div className='text-sm font-medium truncate'>
                              {typeof request.service_name === 'string'
                                ? request.service_name
                                : request.service_name?.en || 'Service'}
                            </div>
                            <Badge className={getStatusColor(request.status)}>
                              {request.status.replace('_', ' ')}
                            </Badge>
                          </div>
                          <div className='text-xs text-muted-foreground'>
                            ID: {request.id.slice(-8)}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </CardContent>
            </Card>

            {/* Pending Requests */}
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Clock className='h-4 w-4' />
                  Pending
                  <Badge variant='secondary'>{pendingRequests.length}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className='h-64'>
                  {pendingLoading ? (
                    <div className='text-center text-muted-foreground'>Loading...</div>
                  ) : (
                    <div className='space-y-2'>
                      {pendingRequests.map((request) => (
                        <div
                          key={request.id}
                          className={`p-2 rounded border cursor-pointer hover:bg-gray-50 ${
                            selectedRequestId === request.id ? 'ring-2 ring-blue-500' : ''
                          }`}
                          onClick={() => setSelectedRequestId(request.id)}
                        >
                          <div className='flex items-center justify-between'>
                            <div className='text-sm font-medium truncate'>
                              {typeof request.service_name === 'string'
                                ? request.service_name
                                : request.service_name?.en || 'Service'}
                            </div>
                          </div>
                          <div className='text-xs text-muted-foreground'>
                            ID: {request.id.slice(-8)}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </CardContent>
            </Card>

            {/* In Progress Requests */}
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Activity className='h-4 w-4' />
                  In Progress
                  <Badge variant='secondary'>{inProgressRequests.length}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className='h-64'>
                  {inProgressLoading ? (
                    <div className='text-center text-muted-foreground'>Loading...</div>
                  ) : (
                    <div className='space-y-2'>
                      {inProgressRequests.map((request) => (
                        <div
                          key={request.id}
                          className={`p-2 rounded border cursor-pointer hover:bg-gray-50 ${
                            selectedRequestId === request.id ? 'ring-2 ring-blue-500' : ''
                          }`}
                          onClick={() => setSelectedRequestId(request.id)}
                        >
                          <div className='flex items-center justify-between'>
                            <div className='text-sm font-medium truncate'>
                              {typeof request.service_name === 'string'
                                ? request.service_name
                                : request.service_name?.en || 'Service'}
                            </div>
                          </div>
                          <div className='text-xs text-muted-foreground'>
                            ID: {request.id.slice(-8)}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </CardContent>
            </Card>
          </div>

          {/* Status Update Section */}
          {selectedRequestId && (
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <Activity className='h-4 w-4' />
                  Update Status
                </CardTitle>
                <CardDescription>
                  Update the status of the selected request to test real-time synchronization
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className='space-y-4'>
                  <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                    <div>
                      <h3 className='text-sm font-medium mb-2'>Selected Request</h3>
                      {allRequests.find((r) => r.id === selectedRequestId) ? (
                        <div className='p-3 border rounded'>
                          <div className='flex items-center justify-between mb-2'>
                            <div className='font-medium'>
                              {(() => {
                                const request = allRequests.find((r) => r.id === selectedRequestId);
                                return typeof request?.service_name === 'string'
                                  ? request?.service_name
                                  : request?.service_name?.en || 'Service';
                              })()}
                            </div>
                            <Badge
                              className={getStatusColor(
                                allRequests.find((r) => r.id === selectedRequestId)?.status ||
                                  RequestStatus.PENDING,
                              )}
                            >
                              {(
                                allRequests.find((r) => r.id === selectedRequestId)?.status || ''
                              ).replace('_', ' ')}
                            </Badge>
                          </div>
                          <div className='text-xs text-muted-foreground'>
                            ID: {selectedRequestId.slice(-8)}
                          </div>
                        </div>
                      ) : (
                        <div className='text-muted-foreground'>No request selected</div>
                      )}
                    </div>
                    <div>
                      <h3 className='text-sm font-medium mb-2'>New Status</h3>
                      <div className='space-y-4'>
                        <Select
                          onValueChange={(value) => {
                            // Handle status selection
                            console.warn('Selected status:', value);
                          }}
                          defaultValue={allRequests.find((r) => r.id === selectedRequestId)?.status}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder='Select status' />
                          </SelectTrigger>
                          <SelectContent>
                            {Object.values(RequestStatus).map((status) => (
                              <SelectItem key={status} value={status}>
                                {status.replace('_', ' ')}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <Button
                          onClick={async () => {
                            // Update status using realtimeStatusService
                            try {
                              const selectedStatus = document.querySelector(
                                '[data-radix-select-value]',
                              )?.textContent;
                              if (!selectedStatus || !selectedRequestId) return;

                              const statusEnum = selectedStatus.includes(' ')
                                ? (selectedStatus.replace(' ', '_') as RequestStatus)
                                : (selectedStatus as RequestStatus);

                              await realtimeStatusService.updateRequestStatus(
                                selectedRequestId,
                                statusEnum,
                              );

                              console.warn(`Status updated to ${statusEnum}`);
                            } catch (error) {
                              console.error('Error updating status:', error);
                            }
                          }}
                        >
                          Update Status
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Notifications Tab */}
        <TabsContent value='notifications' className='space-y-4'>
          <div className='flex items-center justify-between'>
            <h3 className='text-lg font-semibold'>Status Change Notifications</h3>
            <div className='flex gap-2'>
              <Button variant='outline' size='sm' onClick={markAllAsRead}>
                Mark All Read
              </Button>
              <Button variant='outline' size='sm' onClick={clearNotifications}>
                Clear All
              </Button>
            </div>
          </div>

          <Card>
            <CardContent className='p-0'>
              <ScrollArea className='h-96'>
                {notifications.length === 0 ? (
                  <div className='p-8 text-center text-muted-foreground'>
                    <Bell className='h-12 w-12 mx-auto mb-4 opacity-50' />
                    <p>No notifications yet</p>
                    <p className='text-sm'>Start monitoring to see real-time status changes</p>
                  </div>
                ) : (
                  <div className='divide-y'>
                    {notifications.map((notification, index) => (
                      <div
                        key={notification.id}
                        className={`p-4 hover:bg-gray-50 ${
                          !notification.read ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                        }`}
                      >
                        <div className='flex items-start gap-3'>
                          {getPriorityIcon(notification.priority)}
                          <div className='flex-1 min-w-0'>
                            <div className='flex items-center gap-2'>
                              <p className='text-sm font-medium'>{notification.message}</p>
                              {!notification.read && (
                                <Badge variant='secondary' className='text-xs'>
                                  New
                                </Badge>
                              )}
                            </div>
                            <div className='flex items-center gap-4 mt-1'>
                              <p className='text-xs text-muted-foreground'>
                                Request ID: {notification.requestId.slice(-8)}
                              </p>
                              <p className='text-xs text-muted-foreground'>
                                {notification.timestamp.toLocaleTimeString()}
                              </p>
                              <div className='flex gap-1'>
                                <Badge className={getStatusColor(notification.oldStatus)}>
                                  {notification.oldStatus.replace('_', ' ')}
                                </Badge>
                                <span className='text-xs'>→</span>
                                <Badge className={getStatusColor(notification.newStatus)}>
                                  {notification.newStatus.replace('_', ' ')}
                                </Badge>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Monitoring Tab */}
        <TabsContent value='monitoring' className='space-y-4'>
          <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <Card>
              <CardHeader>
                <CardTitle>Listener Statistics</CardTitle>
                <CardDescription>Active real-time listeners by type</CardDescription>
              </CardHeader>
              <CardContent>
                <div className='space-y-3'>
                  <div className='flex justify-between'>
                    <span>Request Listeners:</span>
                    <Badge>{listenerCounts.requests}</Badge>
                  </div>
                  <div className='flex justify-between'>
                    <span>Status Listeners:</span>
                    <Badge>{listenerCounts.statuses}</Badge>
                  </div>
                  <div className='flex justify-between'>
                    <span>Global Listeners:</span>
                    <Badge>{listenerCounts.global}</Badge>
                  </div>
                  <Separator />
                  <div className='flex justify-between font-semibold'>
                    <span>Total Active:</span>
                    <Badge variant='default'>{listenerCounts.total}</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>System Controls</CardTitle>
                <CardDescription>Manage real-time monitoring system</CardDescription>
              </CardHeader>
              <CardContent className='space-y-3'>
                <Button onClick={updateHealth} variant='outline' className='w-full'>
                  Refresh Health Status
                </Button>
                <Button onClick={stopAllListeners} variant='destructive' className='w-full'>
                  Stop All Listeners
                </Button>
                <div className='text-xs text-muted-foreground'>
                  Last health check: {health.timestamp.toLocaleTimeString()}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default RealtimeStatusDemo;
