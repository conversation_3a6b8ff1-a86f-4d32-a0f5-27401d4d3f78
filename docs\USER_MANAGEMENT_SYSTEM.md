# User Management System Documentation

## Overview

The User Management System is a comprehensive solution for managing different types of users in the mr.tech.v2 web portal. It provides role-based access control, data validation, activity tracking, and audit logging capabilities.

## Architecture

### Core Components

1. **User Types & Models** (`src/types/user.ts`)
   - BaseUser interface with dual field naming support
   - CustomerUser, TechnicianUser, AdminUser specialized interfaces
   - UserRole, UserStatus enums
   - Input/Output type definitions

2. **User Service** (`src/services/userService.ts`)
   - Main service for user CRUD operations
   - Integration with Firebase Auth and Firestore
   - Data validation and sanitization
   - Activity tracking integration

3. **Permission Service** (`src/services/permissionService.ts`)
   - Role-based access control
   - Permission checking utilities
   - Admin level permissions

4. **Validation Service** (`src/utils/validation.ts`)
   - Input data validation
   - Email, password, phone number validation
   - Role-specific validation rules

5. **Sanitization Service** (`src/utils/sanitization.ts`)
   - Input data sanitization
   - XSS prevention
   - Data normalization

6. **Activity Tracking Service** (`src/services/activityTrackingService.ts`)
   - User activity logging
   - Audit trail management
   - Security event tracking

## User Types

### Customer Users
- Basic users who request services
- Fields: `anydesk_id`, `total_requests`, `completed_requests`
- Permissions: Limited to own data and creating requests

### Technician Users
- Service providers who handle requests
- Fields: `specialties`, `rating`, `active_requests`, `is_available`, `technician_status`
- Permissions: Can view and update assigned requests, manage availability

### Admin Users
- System administrators with elevated privileges
- Fields: `permissions`, `admin_level`, `last_action`
- Admin Levels:
  - **Super**: Full system access
  - **Standard**: Most admin functions except user deletion and role changes
  - **Limited**: Basic admin functions only

## Dual Field Naming Convention

The system supports both snake_case and camelCase field naming for compatibility:

```typescript
interface BaseUser {
  display_name?: string; // snake_case (primary)
  displayName?: string;  // camelCase (compatibility)
  
  phone_number?: string; // snake_case (primary)
  phoneNumber?: string;  // camelCase (compatibility)
  
  // ... other dual-named fields
}
```

This ensures compatibility between:
- Mobile app (uses snake_case)
- Web portal (uses camelCase)
- Firestore queries (primarily snake_case)

## API Reference

### UserService Methods

#### `createUser(userData: CreateUserInput, createAuthAccount?: boolean): Promise<User>`
Creates a new user with optional Firebase Auth account.

```typescript
const newUser = await userService.createUser({
  email: '<EMAIL>',
  name: 'John Doe',
  role: 'customer',
  password: 'SecurePassword123'
}, true);
```

#### `updateUser(userId: string, updateData: UpdateUserInput, adminId?: string): Promise<User>`
Updates user profile with validation and audit logging.

```typescript
const updatedUser = await userService.updateUser('user-id', {
  name: 'Updated Name',
  phone_number: '+**********'
}, 'admin-id');
```

#### `queryUsers(options?: UserQueryOptions): Promise<User[]>`
Queries users with filtering and pagination.

```typescript
const technicians = await userService.queryUsers({
  role: 'technician',
  is_active: true,
  limit: 20
});
```

#### `suspendUser(userId: string, adminId: string, reason?: string): Promise<User>`
Suspends a user account with audit logging.

#### `activateUser(userId: string, adminId: string): Promise<User>`
Activates a suspended user account.

#### `changeUserRole(userId: string, newRole: UserRole, adminId: string): Promise<User>`
Changes user role with proper data migration.

### Permission System

#### Permission Checking
```typescript
import permissionService from '../services/permissionService';
import { Permission } from '../types/permissions';

// Check single permission
const canCreateUsers = permissionService.hasPermission(user, Permission.CREATE_USER);

// Check multiple permissions (any)
const canManageRequests = permissionService.hasAnyPermission(user, [
  Permission.VIEW_ALL_REQUESTS,
  Permission.UPDATE_REQUEST
]);

// Check resource access
const canAccessResource = permissionService.canAccessResource(
  user,
  resourceOwnerId,
  [Permission.VIEW_USERS]
);
```

#### Admin Level Permissions
- **Super Admin**: All permissions
- **Standard Admin**: All except DELETE_USER and CHANGE_USER_ROLE
- **Limited Admin**: Basic viewing and editing permissions only

### Validation and Sanitization

#### Input Validation
```typescript
import validationService from '../utils/validation';

const result = validationService.validateCreateUserInput(userData);
if (!result.isValid) {
  console.error('Validation errors:', result.errors);
}
```

#### Data Sanitization
```typescript
import sanitizationService from '../utils/sanitization';

const sanitizedData = sanitizationService.sanitizeCreateUserInput(rawInput);
```

### Activity Tracking

#### Track User Activities
```typescript
import activityTrackingService, { ActivityType } from '../services/activityTrackingService';

await activityTrackingService.trackActivity(
  userId,
  ActivityType.PROFILE_UPDATE,
  'Updated phone number',
  { ip_address: '***********' }
);
```

#### Audit Logging
```typescript
await activityTrackingService.logAudit(
  userId,
  adminId,
  AuditAction.UPDATE,
  oldValues,
  newValues,
  'Profile updated by admin'
);
```

## React Integration

### Using the User Management Hook

```typescript
import { useUserManagement } from '../hooks/useUserManagement';

function UserManagementComponent() {
  const {
    users,
    loading,
    error,
    createUser,
    updateUser,
    queryUsers,
    canManageUser,
    canPerformAction
  } = useUserManagement();

  // Load users on component mount
  useEffect(() => {
    queryUsers({ role: 'technician' });
  }, []);

  // Create new user
  const handleCreateUser = async (userData) => {
    try {
      await createUser(userData);
      // Refresh users list
      await queryUsers();
    } catch (error) {
      console.error('Failed to create user:', error);
    }
  };

  return (
    <div>
      {loading && <div>Loading...</div>}
      {error && <div>Error: {error}</div>}
      {users.map(user => (
        <UserCard 
          key={user.id} 
          user={user}
          canEdit={canManageUser(user)}
          canDelete={canPerformAction('delete_user', user)}
        />
      ))}
    </div>
  );
}
```

## Security Considerations

### Data Protection
- All user inputs are validated and sanitized
- Passwords are handled securely through Firebase Auth
- Sensitive data is protected with proper access controls

### Access Control
- Role-based permissions system
- Resource ownership validation
- Admin level restrictions

### Audit Trail
- All user management actions are logged
- IP addresses and user agents tracked
- Comprehensive audit logs for compliance

## Database Schema

### Firestore Collections

#### `users` Collection (Main)
```typescript
{
  id: string,
  email: string,
  name: string,
  display_name: string,
  role: 'admin' | 'technician' | 'customer',
  status: 'active' | 'inactive' | 'suspended' | 'pending',
  is_active: boolean,
  created_at: Timestamp,
  updated_at: Timestamp,
  // ... other fields with dual naming
}
```

#### `technicians` Collection
```typescript
{
  // All base user fields +
  specialties: string[],
  rating: number,
  completed_requests: number,
  active_requests: number,
  is_available: boolean,
  technician_status: 'active' | 'offline' | 'busy' | 'onLeave'
}
```

#### `admins` Collection
```typescript
{
  // All base user fields +
  permissions: string[],
  admin_level: 'super' | 'standard' | 'limited',
  last_action: string,
  last_action_at: Timestamp
}
```

#### `user_activities` Collection
```typescript
{
  id: string,
  user_id: string,
  action: string,
  details: string,
  ip_address: string,
  user_agent: string,
  created_at: Timestamp
}
```

#### `user_audit_logs` Collection
```typescript
{
  id: string,
  user_id: string,
  admin_id: string,
  action: 'create' | 'update' | 'delete' | 'suspend' | 'activate' | 'role_change',
  old_values: object,
  new_values: object,
  reason: string,
  created_at: Timestamp
}
```

## Error Handling

### Validation Errors
```typescript
interface ValidationError {
  field: string;
  message: string;
  code: string;
}
```

### Common Error Codes
- `REQUIRED`: Field is required
- `INVALID_FORMAT`: Invalid format (email, phone, etc.)
- `TOO_SHORT`: Value too short
- `TOO_LONG`: Value too long
- `WEAK_PASSWORD`: Password doesn't meet requirements
- `INVALID_VALUE`: Invalid enum value

## Testing

### Unit Tests
- User service operations
- Validation logic
- Permission checking
- Data sanitization

### Integration Tests
- Firebase integration
- End-to-end user workflows
- Permission enforcement

### Test Coverage
- Aim for >90% code coverage
- Test both success and failure scenarios
- Mock external dependencies

## Performance Considerations

### Firestore Optimization
- Proper indexing for queries
- Pagination for large datasets
- Efficient query patterns

### Caching
- User permissions caching
- Frequently accessed user data
- Activity logs pagination

### Monitoring
- Track user management operations
- Monitor validation failures
- Alert on suspicious activities

## Migration Guide

### From Existing System
1. Export existing user data
2. Map to new user schema
3. Validate and sanitize data
4. Import with proper role assignment
5. Verify permissions and access

### Field Naming Migration
1. Identify dual-named fields
2. Ensure both variants exist
3. Update queries to use snake_case
4. Maintain camelCase for compatibility

## Setup Guide

### 1. Install Dependencies
```bash
npm install firebase
```

### 2. Configure Firebase
Ensure your Firebase configuration includes:
- Authentication enabled
- Firestore database
- Security rules configured

### 3. Initialize Services
```typescript
// Import and use the services
import userService from './services/userService';
import permissionService from './services/permissionService';
import { useUserManagement } from './hooks/useUserManagement';
```

### 4. Set Up Firestore Rules
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection
    match /users/{userId} {
      allow read: if isAuthenticated();
      allow write: if isOwner(userId) || isAdmin();
    }

    // Technicians collection
    match /technicians/{techId} {
      allow read: if isAuthenticated();
      allow write: if isAdmin() || isTechnician();
    }

    // Admins collection
    match /admins/{adminId} {
      allow read: if isAdmin();
      allow write: if isSuperAdmin();
    }

    // Activity logs
    match /user_activities/{activityId} {
      allow read: if isAdmin();
      allow create: if isAuthenticated();
    }

    // Audit logs
    match /user_audit_logs/{auditId} {
      allow read: if isAdmin();
      allow create: if isAdmin();
    }
  }
}
```

### 5. Create Initial Admin User
```typescript
// Run this once to create your first admin
const initialAdmin = await userService.createUser({
  email: '<EMAIL>',
  name: 'System Administrator',
  role: 'admin',
  admin_level: 'super',
  permissions: [], // Will get all permissions as super admin
  password: 'SecurePassword123'
}, true);
```

## Troubleshooting

### Common Issues
1. **Permission Denied**: Check user role and permissions
2. **Validation Failures**: Review input data format
3. **Dual Field Issues**: Ensure both naming conventions are set
4. **Activity Tracking**: Verify service integration

### Debug Tools
- User permission checker
- Validation result inspector
- Activity log viewer
- Audit trail analyzer

### FAQ

**Q: Why dual field naming?**
A: To maintain compatibility between mobile app (snake_case) and web portal (camelCase) while transitioning to a unified naming convention.

**Q: How do I add new permissions?**
A: Add to the Permission enum in `types/permissions.ts` and update role permissions in the same file.

**Q: Can I customize admin levels?**
A: Yes, modify the AdminLevelPermissions object in `permissionService.ts`.

**Q: How do I backup user data?**
A: Use Firebase Admin SDK to export collections, or implement a backup function using the user service.

## Contributing

### Code Style
- Use TypeScript for type safety
- Follow existing naming conventions
- Add JSDoc comments for public methods
- Write tests for new features

### Pull Request Process
1. Create feature branch
2. Implement changes with tests
3. Update documentation
4. Submit PR with description

### Testing Requirements
- Unit tests for new functions
- Integration tests for user flows
- Validation tests for input handling
- Permission tests for access control
