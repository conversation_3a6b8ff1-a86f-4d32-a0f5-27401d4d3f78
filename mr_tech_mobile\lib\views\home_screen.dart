import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/translation_service.dart';
import '../services/database_service.dart';
import '../services/offline_service.dart';
import '../models/request_model.dart';
import '../models/user_model.dart';
import '../models/service_model.dart';
import 'services_screen.dart';
import 'requests_screen.dart';
import 'dashboard_screen.dart';
import 'edit_profile_screen.dart';
// Import for UnifiedAppBar
import '../utils/navigation_utils.dart';
import 'chat_screen.dart';
import '../services/request_service.dart';
import 'dart:async'; // Add this import for StreamSubscription
import '../views/notification_screen.dart';
import '../models/notification_model.dart';
import '../services/notification_service.dart';
// Import the luxury UI kit
import '../widgets/ui_kit.dart';
import '../views/request_details_screen.dart';
import '../utils/network_image_handler.dart';
import 'package:carousel_slider/carousel_slider.dart';
// Import text styles for consistent font styling

extension StringExtension on String {
  String toCapitalized() =>
      length > 0 ? '${this[0].toUpperCase()}${substring(1)}' : '';
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  // Real user data - will be loaded from database
  UserModel? _userModel;

  // Active request - will be null if no active request
  RequestModel? _activeRequest;

  late AnimationController _animationController;
  bool _isLoading = true;
  // Add stream subscription to monitor active requests
  StreamSubscription<RequestModel?>? _requestSubscription;
  // Add stream for notification count
  StreamSubscription<List<NotificationModel>>? _notificationSubscription;
  // Add stream for active requests
  StreamSubscription<List<RequestModel>>? _activeRequestsSubscription;
  // Add stream for all requests to update counters
  StreamSubscription<List<RequestModel>>? _allRequestsSubscription;
  int _unreadNotificationCount = 0;

  // Add a list to store popular services
  List<ServiceModel> _popularServices = [];

  // Add counters for requests and completed requests
  int _totalRequestsCount = 0;
  int _completedRequestsCount = 0;

  // Offline service
  final OfflineService _offlineService = OfflineService();
  bool _isOffline = false;

  @override
  void initState() {
    super.initState();

    // Add lifecycle observer
    WidgetsBinding.instance.addObserver(this);

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );

    // Load real user data
    _loadUserData();

    // Setup notification listener
    _setupNotificationListener();

    // Load popular services
    _loadPopularServices();

    // Setup request count listener
    _setupRequestCountListener();

    // Initialize offline service
    _initializeOfflineService();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.resumed) {
      // Handle app resume
      _handleAppResume();
    }
  }

  /// Handle app resume - refresh data and check connectivity
  Future<void> _handleAppResume() async {
    try {
      debugPrint('HomeScreen: Handling app resume - starting...');

      // Handle offline service resume
      await _offlineService.handleAppResume();
      debugPrint('HomeScreen: Offline service resume completed');

      // Refresh user data
      await _loadUserData();
      debugPrint('HomeScreen: User data refresh completed');

      // Refresh popular services
      await _loadPopularServices();
      debugPrint('HomeScreen: Popular services refresh completed');

      debugPrint('HomeScreen: App resume handling completed successfully');
    } catch (e) {
      debugPrint('Error handling app resume in HomeScreen: $e');
    }
  }

  Future<void> _loadUserData() async {
    try {
      // Get database service
      final databaseService = Provider.of<DatabaseService>(
        context,
        listen: false,
      );

      // Load user data in background to avoid blocking UI
      Future.microtask(() async {
        try {
          _userModel = await databaseService.getCurrentUser();

          if (mounted) {
            setState(() {
              _isLoading = false;
            });
          }
        } catch (e) {
          debugPrint('Error loading user data in background: $e');
          if (mounted) {
            setState(() {
              _isLoading = false;
            });
          }
        }
      });

      // Set up real-time listener for active requests immediately
      _setupActiveRequestsSubscription(databaseService);

      // Start animation immediately for better UX
      _animationController.forward();

      // Set loading to false immediately to show UI faster
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error setting up user data loading: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Setup real-time listener for the active requests using the stream
  void _setupActiveRequestsSubscription(DatabaseService databaseService) {
    // Cancel any existing subscription
    _activeRequestsSubscription?.cancel();

    // Subscribe to real-time updates for all active requests
    _activeRequestsSubscription = databaseService.streamUserActiveRequests().listen(
      (requests) {
        if (mounted) {
          setState(() {
            // Set the most recent active request or null if no active requests
            _activeRequest = requests.isNotEmpty ? requests.first : null;

            // If there's an active request, setup the request-specific listener
            if (_activeRequest != null) {
              _setupRequestListener(_activeRequest!.id);
            } else {
              // Cancel request-specific listener if there's no active request
              _requestSubscription?.cancel();
              _requestSubscription = null;
            }

            // Update loading state
            _isLoading = false;
          });

          // No need to manually refresh request counts as we have a stream listener now
        }
      },
      onError: (error) {
        debugPrint('Error in active requests stream: $error');
        // Handle errors by showing something to the user
        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          // Show error message to user
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error loading active requests: $error'),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
    );
  }

  // Setup real-time listener for the active request
  void _setupRequestListener(String requestId) {
    // Cancel any existing subscription
    _requestSubscription?.cancel();

    // Create a new request service
    final requestService = RequestService();

    // Subscribe to real-time updates
    _requestSubscription = requestService.watchRequest(requestId).listen((
      updatedRequest,
    ) {
      if (updatedRequest != null && mounted) {
        setState(() {
          _activeRequest = updatedRequest;
        });

        // No need to manually refresh request counts as we have a stream listener now

        // We're now relying on FCM notifications instead of local toast notifications
        // Status changes are handled by push notifications from the web app
      }
    });
  }

  // Setup notification listener to update badge count
  void _setupNotificationListener() {
    final notificationService = NotificationService();
    _notificationSubscription = notificationService
        .getNotificationsStream()
        .listen((notifications) {
          if (mounted) {
            setState(() {
              _unreadNotificationCount =
                  notifications.where((n) => !n.read).length;
            });

            // Check for notification flood (duplicate notifications)
            _checkForNotificationFlood(notifications, notificationService);
          }
        });
  }

  // Detect multiple identical notifications and clear them if needed
  void _checkForNotificationFlood(
    List<NotificationModel> notifications,
    NotificationService notificationService,
  ) {
    if (notifications.length >= 10) {
      // Check the most recent notifications for duplicates
      final recentNotifications = notifications.take(10).toList();

      // Group by title and body
      final Map<String, List<NotificationModel>> groupedNotifications = {};

      for (final notification in recentNotifications) {
        final key = '${notification.title}|${notification.body}';
        if (!groupedNotifications.containsKey(key)) {
          groupedNotifications[key] = [];
        }
        groupedNotifications[key]!.add(notification);
      }

      // Find groups with too many duplicate notifications (e.g., 5+ duplicates)
      final floodedGroups =
          groupedNotifications.values
              .where((group) => group.length >= 5)
              .toList();

      if (floodedGroups.isNotEmpty) {
        debugPrint(
          'Detected notification flood! Found ${floodedGroups.length} groups with duplicates',
        );

        // If there's a flood, check if they are all recent (within the last 5 minutes)
        final fiveMinutesAgo = DateTime.now().subtract(
          const Duration(minutes: 5),
        );
        bool isRecentFlood = true;

        for (final group in floodedGroups) {
          for (final notification in group) {
            if (notification.createdAt.toDate().isBefore(fiveMinutesAgo)) {
              isRecentFlood = false;
              break;
            }
          }
          if (!isRecentFlood) break;
        }

        // If it's a recent flood, show a snackbar and offer to clear notifications
        if (isRecentFlood) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(_translate('duplicate_notifications_detected')),
              action: SnackBarAction(
                label: _translate('clear_all'),
                onPressed: () {
                  notificationService.deleteAllNotifications();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(_translate('notifications_cleared')),
                      backgroundColor: Colors.green,
                    ),
                  );
                },
              ),
              duration: const Duration(seconds: 10),
            ),
          );
        }
      }
    }
  }

  // Show in-app notification for status changes
  void _showStatusChangeNotification(String title, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        backgroundColor: Theme.of(context).colorScheme.primary,
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: AppTextStyles.headingMedium(context, color: Colors.white),
            ),
            Text(
              message,
              style: AppTextStyles.bodySmall(
                context,
                color: Colors.white.withOpacity(0.9),
              ),
            ),
          ],
        ),
        duration: const Duration(seconds: 5),
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        action: SnackBarAction(
          label: 'VIEW',
          textColor: Colors.white,
          onPressed: () {
            if (_activeRequest != null) {
              _showRequestDetailsDialog(context, _activeRequest!);
            }
          },
        ),
      ),
    );
  }

  @override
  void dispose() {
    // Remove lifecycle observer
    WidgetsBinding.instance.removeObserver(this);

    // Clean up resources
    _animationController.dispose();
    _requestSubscription?.cancel();
    _notificationSubscription?.cancel();
    _activeRequestsSubscription?.cancel();
    _allRequestsSubscription?.cancel();
    super.dispose();
  }

  void _createNewRequest() {
    NavigationUtils.navigateWithBottomNav(context, ServicesScreen());
  }

  void _viewAllRequests() {
    NavigationUtils.navigateWithBottomNav(context, RequestsScreen());
  }

  // Helper method to translate text
  String _translate(String key) {
    return Provider.of<TranslationService>(
      context,
      listen: false,
    ).translate(key);
  }

  // Helper method to translate text and apply consistent styling
  Widget _getStyledText(
    String key, {
    TextStyle Function(BuildContext, {Color? color})? styleFunction,
    Color? color,
  }) {
    final translatedText = Provider.of<TranslationService>(
      context,
      listen: false,
    ).translate(key);
    final style =
        styleFunction != null
            ? styleFunction(context, color: color)
            : AppTextStyles.bodyMedium(context, color: color);

    return Text(translatedText, style: style);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );
    final isRtl = translationService.isRtl;
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: Stack(
        children: [
          // Main content
          RefreshIndicator(
            onRefresh: () async {
              // Refresh data from Firebase
              await _loadUserData();
            },
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : CustomScrollView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      slivers: [
                        // Simple, flat app bar
                        SliverAppBar(
                          expandedHeight: 80,
                          floating: true,
                          pinned: false,
                          snap: true,
                          elevation: 0,
                          backgroundColor: colorScheme.surface,
                          flexibleSpace: FlexibleSpaceBar(
                            background: SafeArea(
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 20,
                                  vertical: 10,
                                ),
                                child: Row(
                                  children: [
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          _translate('Mr. Tech'),
                                          style: AppTextStyles.headingMedium(
                                            context,
                                            color: colorScheme.onSurface,
                                          ),
                                        ),
                                        Text(
                                          _translate(
                                            'Tech support, simplified',
                                          ),
                                          style: AppTextStyles.bodySmall(
                                            context,
                                            color: colorScheme.onSurface
                                                .withOpacity(0.7),
                                          ),
                                        ),
                                      ],
                                    ),
                                    const Spacer(),
                                    // Offline indicator
                                    if (_isOffline) ...[
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 8,
                                          vertical: 4,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.orange,
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Icon(
                                              Icons.wifi_off,
                                              size: 14,
                                              color: Colors.white,
                                            ),
                                            const SizedBox(width: 4),
                                            Text(
                                              _translate('Offline'),
                                              style: AppTextStyles.labelSmall(
                                                context,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                    ],
                                    // Dashboard button

                                    // Notification bell with badge
                                    Stack(
                                      clipBehavior: Clip.none,
                                      children: [
                                        IconButton(
                                          icon: Icon(
                                            _unreadNotificationCount > 0
                                                ? Icons.notifications_active
                                                : Icons.notifications_outlined,
                                            color: colorScheme.onSurface,
                                            size: 24,
                                          ),
                                          onPressed: _openNotifications,
                                        ),
                                        if (_unreadNotificationCount > 0)
                                          Positioned(
                                            top: 0,
                                            right: 0,
                                            child: Container(
                                              padding: const EdgeInsets.all(4),
                                              decoration: BoxDecoration(
                                                color: Colors.red,
                                                shape: BoxShape.circle,
                                                border: Border.all(
                                                  color: colorScheme.surface,
                                                  width: 1,
                                                ),
                                              ),
                                              constraints: const BoxConstraints(
                                                minWidth: 16,
                                                minHeight: 16,
                                              ),
                                              child: Text(
                                                _unreadNotificationCount > 99
                                                    ? '99+'
                                                    : _unreadNotificationCount
                                                        .toString(),
                                                style: AppTextStyles.labelSmall(
                                                  context,
                                                  color: Colors.white,
                                                ),
                                                textAlign: TextAlign.center,
                                              ),
                                            ),
                                          ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),

                        // Main content
                        SliverToBoxAdapter(
                          child: Padding(
                            padding: const EdgeInsets.fromLTRB(20, 16, 20, 24),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // Welcome section - flat and simple
                                Card(
                                  margin: EdgeInsets.zero,
                                  elevation: 0,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                    side: BorderSide(
                                      color: Colors.grey.shade300,
                                    ),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(16),
                                    child: Column(
                                      children: [
                                        Row(
                                          children: [
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    _translate('Welcome back,'),
                                                    style:
                                                        AppTextStyles.bodyMedium(
                                                          context,
                                                          color: colorScheme
                                                              .onSurface
                                                              .withOpacity(0.7),
                                                        ),
                                                  ),
                                                  const SizedBox(height: 4),
                                                  Text(
                                                    _userModel?.displayName ??
                                                        _translate('Guest'),
                                                    style:
                                                        AppTextStyles.headingMedium(
                                                          context,
                                                          color:
                                                              colorScheme
                                                                  .onSurface,
                                                        ),
                                                    maxLines: 1,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                  ),
                                                  const SizedBox(height: 8),
                                                  // Dashboard indicator
                                                  GestureDetector(
                                                    onTap: () {
                                                      NavigationUtils.navigateKeepingStack(
                                                        context,
                                                        const DashboardScreen(),
                                                      );
                                                    },
                                                    child: Container(
                                                      padding:
                                                          const EdgeInsets.symmetric(
                                                            horizontal: 12,
                                                            vertical: 6,
                                                          ),
                                                      decoration: BoxDecoration(
                                                        color: colorScheme
                                                            .primary
                                                            .withOpacity(0.1),
                                                        borderRadius:
                                                            BorderRadius.circular(
                                                              16,
                                                            ),
                                                        border: Border.all(
                                                          color: colorScheme
                                                              .primary
                                                              .withOpacity(0.3),
                                                        ),
                                                      ),
                                                      child: Row(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        children: [
                                                          Icon(
                                                            Icons
                                                                .analytics_outlined,
                                                            size: 16,
                                                            color:
                                                                colorScheme
                                                                    .primary,
                                                          ),
                                                          const SizedBox(
                                                            width: 6,
                                                          ),
                                                          Text(
                                                            _translate(
                                                              'View Dashboard',
                                                            ),
                                                            style: AppTextStyles.bodySmall(
                                                              context,
                                                              color:
                                                                  colorScheme
                                                                      .primary,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            const SizedBox(width: 16),
                                            // Simple user avatar with navigation
                                            GestureDetector(
                                              onTap: () {
                                                NavigationUtils.navigateKeepingStack(
                                                  context,
                                                  const EditProfileScreen(),
                                                );
                                              },
                                              child: Container(
                                                decoration: BoxDecoration(
                                                  shape: BoxShape.circle,
                                                  border: Border.all(
                                                    color: colorScheme.primary
                                                        .withOpacity(0.2),
                                                    width: 2,
                                                  ),
                                                ),
                                                child: ClipOval(
                                                  child:
                                                      _userModel
                                                                  ?.photoURL
                                                                  ?.isNotEmpty ==
                                                              true
                                                          ? NetworkImageWithFallback(
                                                            imageUrl:
                                                                _userModel!
                                                                    .photoURL,
                                                            width: 64,
                                                            height: 64,
                                                            fit: BoxFit.cover,
                                                            isCircular: true,
                                                            fallbackBuilder:
                                                                () =>
                                                                    _buildInitialsAvatar(),
                                                          )
                                                          : _buildInitialsAvatar(),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),

                                        // Status row with quick stats - simplified
                                        if (_userModel != null) ...[
                                          const SizedBox(height: 16),
                                          Divider(
                                            height: 1,
                                            color: Colors.grey.shade300,
                                          ),
                                          const SizedBox(height: 16),
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceAround,
                                            children: [
                                              // Requests count stat
                                              Column(
                                                children: [
                                                  Row(
                                                    children: [
                                                      Icon(
                                                        Icons.history,
                                                        size: 16,
                                                        color:
                                                            colorScheme.primary,
                                                      ),
                                                      const SizedBox(width: 6),
                                                      Text(
                                                        _totalRequestsCount
                                                            .toString(),
                                                        style:
                                                            AppTextStyles.headingSmall(
                                                              context,
                                                              color:
                                                                  colorScheme
                                                                      .onSurface,
                                                            ),
                                                      ),
                                                    ],
                                                  ),
                                                  const SizedBox(height: 4),
                                                  Text(
                                                    _translate('Requests'),
                                                    style: AppTextStyles.bodySmall(
                                                      context,
                                                      color:
                                                          colorScheme
                                                              .onSurfaceVariant,
                                                    ),
                                                  ),
                                                ],
                                              ),

                                              // AnyDesk indicator - restored
                                              _buildAnyDeskIndicator(context),

                                              // Completed stat
                                              Column(
                                                children: [
                                                  Row(
                                                    children: [
                                                      Icon(
                                                        Icons
                                                            .check_circle_outline,
                                                        size: 16,
                                                        color:
                                                            Colors
                                                                .green
                                                                .shade700,
                                                      ),
                                                      const SizedBox(width: 6),
                                                      Text(
                                                        _completedRequestsCount
                                                            .toString(),
                                                        style:
                                                            AppTextStyles.headingSmall(
                                                              context,
                                                              color:
                                                                  colorScheme
                                                                      .onSurface,
                                                            ),
                                                      ),
                                                    ],
                                                  ),
                                                  const SizedBox(height: 4),
                                                  Text(
                                                    _translate('Completed'),
                                                    style: AppTextStyles.bodySmall(
                                                      context,
                                                      color:
                                                          colorScheme
                                                              .onSurfaceVariant,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                        ],
                                      ],
                                    ),
                                  ),
                                ),

                                const SizedBox(height: 30),

                                // Quick services section - simplified
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Text(
                                          _translate('Quick Services'),
                                          style: AppTextStyles.headingSmall(
                                            context,
                                            color: colorScheme.onSurface,
                                          ),
                                        ),
                                        const Spacer(),
                                        TextButton(
                                          onPressed: () {
                                            NavigationUtils.navigateWithBottomNav(
                                              context,
                                              ServicesScreen(),
                                            );
                                          },
                                          style: TextButton.styleFrom(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8,
                                            ),
                                          ),
                                          child: Text(
                                            _translate('View All'),
                                            style: AppTextStyles.bodyMedium(
                                              context,
                                              color: colorScheme.primary,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 12),
                                    // Quick services carousel
                                    _popularServices.isEmpty
                                        ? const Padding(
                                          padding: EdgeInsets.all(20),
                                          child: Center(
                                            child: CircularProgressIndicator(),
                                          ),
                                        )
                                        : CarouselSlider.builder(
                                          itemCount: _popularServices.length,
                                          options: CarouselOptions(
                                            height: 200,
                                            viewportFraction: 0.85,
                                            enlargeCenterPage: true,
                                            enableInfiniteScroll:
                                                _popularServices.length > 1,
                                            autoPlay:
                                                _popularServices.length > 1,
                                            autoPlayInterval: const Duration(
                                              seconds: 5,
                                            ),
                                            enlargeFactor: 0.12,
                                          ),
                                          itemBuilder: (
                                            context,
                                            index,
                                            realIndex,
                                          ) {
                                            final service =
                                                _popularServices[index];
                                            final colors = [
                                              colorScheme.primary, // Primary
                                              Colors.green.shade700, // Green
                                              colorScheme.tertiary, // Tertiary
                                            ];
                                            final color =
                                                colors[index % colors.length];

                                            return _buildServiceCard(
                                              service: service,
                                              color: color,
                                            );
                                          },
                                        ),
                                  ],
                                ),

                                const SizedBox(height: 30),

                                // Active request section - simplified
                                if (_activeRequest != null) ...[
                                  const SizedBox(height: 30),
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Row(
                                        children: [
                                          Text(
                                            _translate('Active Request'),
                                            style: AppTextStyles.headingSmall(
                                              context,
                                              color: colorScheme.onSurface,
                                            ),
                                          ),
                                          const Spacer(),
                                          TextButton(
                                            onPressed: _viewAllRequests,
                                            style: TextButton.styleFrom(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                    horizontal: 8,
                                                  ),
                                            ),
                                            child: Text(
                                              _translate('View All'),
                                              style: AppTextStyles.bodyMedium(
                                                context,
                                                color: colorScheme.primary,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 12),
                                      // Active request card matching the design
                                      Card(
                                        margin: EdgeInsets.zero,
                                        elevation: 0,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                          side: BorderSide(
                                            color: Colors.grey.shade300,
                                          ),
                                        ),
                                        child: InkWell(
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                          onTap:
                                              () => _showRequestDetailsDialog(
                                                context,
                                                _activeRequest!,
                                              ),
                                          child: Padding(
                                            padding: const EdgeInsets.all(16),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                // Header row with status and request ID
                                                Row(
                                                  children: [
                                                    // Status indicator
                                                    Container(
                                                      padding:
                                                          const EdgeInsets.symmetric(
                                                            horizontal: 8,
                                                            vertical: 4,
                                                          ),
                                                      decoration: BoxDecoration(
                                                        color: _getStatusColor(
                                                          _activeRequest!
                                                              .status,
                                                        ),
                                                        borderRadius:
                                                            BorderRadius.circular(
                                                              12,
                                                            ),
                                                      ),
                                                      child: Row(
                                                        mainAxisSize:
                                                            MainAxisSize.min,
                                                        children: [
                                                          Container(
                                                            width: 6,
                                                            height: 6,
                                                            decoration:
                                                                const BoxDecoration(
                                                                  color:
                                                                      Colors
                                                                          .white,
                                                                  shape:
                                                                      BoxShape
                                                                          .circle,
                                                                ),
                                                          ),
                                                          const SizedBox(
                                                            width: 6,
                                                          ),
                                                          Text(
                                                            _getStatusText(
                                                              _activeRequest!
                                                                  .status,
                                                            ),
                                                            style:
                                                                AppTextStyles.bodySmall(
                                                                  context,
                                                                  color:
                                                                      Colors
                                                                          .white,
                                                                ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                    const Spacer(),
                                                    Text(
                                                      '#REQ-${_activeRequest!.id.substring(0, 4).toUpperCase()}',
                                                      style:
                                                          AppTextStyles.bodySmall(
                                                            context,
                                                            color:
                                                                colorScheme
                                                                    .primary,
                                                          ),
                                                    ),
                                                  ],
                                                ),
                                                const SizedBox(height: 16),

                                                // Service title
                                                Text(
                                                  _translate(
                                                    _activeRequest!.serviceName,
                                                  ),
                                                  style:
                                                      AppTextStyles.headingSmall(
                                                        context,
                                                        color:
                                                            colorScheme
                                                                .onSurface,
                                                      ),
                                                ),
                                                const SizedBox(height: 8),

                                                // Description
                                                Text(
                                                  _activeRequest!
                                                              .customerIssue
                                                              .length >
                                                          100
                                                      ? '${_activeRequest!.customerIssue.substring(0, 100)}...'
                                                      : _activeRequest!
                                                          .customerIssue,
                                                  style: AppTextStyles.bodyMedium(
                                                    context,
                                                    color:
                                                        colorScheme
                                                            .onSurfaceVariant,
                                                  ),
                                                  maxLines: 2,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                ),
                                                const SizedBox(height: 16),

                                                // Bottom row with technician and chat
                                                Row(
                                                  children: [
                                                    // Technician info
                                                    if (_activeRequest!
                                                                .technicianName !=
                                                            null &&
                                                        _activeRequest!
                                                            .technicianName!
                                                            .isNotEmpty) ...[
                                                      Icon(
                                                        Icons.person_outline,
                                                        size: 16,
                                                        color:
                                                            colorScheme
                                                                .onSurfaceVariant,
                                                      ),
                                                      const SizedBox(width: 6),
                                                      Text(
                                                        _translate(
                                                          'Technician',
                                                        ),
                                                        style: AppTextStyles.bodySmall(
                                                          context,
                                                          color:
                                                              colorScheme
                                                                  .onSurfaceVariant,
                                                        ),
                                                      ),
                                                      const SizedBox(width: 8),
                                                      Text(
                                                        _activeRequest!
                                                            .technicianName!,
                                                        style:
                                                            AppTextStyles.bodySmall(
                                                              context,
                                                              color:
                                                                  colorScheme
                                                                      .onSurface,
                                                            ),
                                                      ),
                                                    ] else ...[
                                                      Icon(
                                                        Icons.schedule,
                                                        size: 16,
                                                        color:
                                                            colorScheme
                                                                .onSurfaceVariant,
                                                      ),
                                                      const SizedBox(width: 6),
                                                      Text(
                                                        _translate(
                                                          'Awaiting assignment',
                                                        ),
                                                        style: AppTextStyles.bodySmall(
                                                          context,
                                                          color:
                                                              colorScheme
                                                                  .onSurfaceVariant,
                                                        ),
                                                      ),
                                                    ],
                                                    const Spacer(),

                                                    // Chat button
                                                    Container(
                                                      decoration: BoxDecoration(
                                                        color: colorScheme
                                                            .primary
                                                            .withOpacity(0.1),
                                                        shape: BoxShape.circle,
                                                      ),
                                                      child: IconButton(
                                                        onPressed: () {
                                                          Navigator.of(
                                                            context,
                                                          ).push(
                                                            MaterialPageRoute(
                                                              builder:
                                                                  (
                                                                    context,
                                                                  ) => ChatScreen(
                                                                    request:
                                                                        _activeRequest!,
                                                                  ),
                                                            ),
                                                          );
                                                        },
                                                        icon: Icon(
                                                          Icons
                                                              .chat_bubble_outline,
                                                          color:
                                                              colorScheme
                                                                  .primary,
                                                          size: 20,
                                                        ),
                                                        padding:
                                                            const EdgeInsets.all(
                                                              8,
                                                            ),
                                                        constraints:
                                                            const BoxConstraints(),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 30),
                                ],

                                // Tips & Help section - simplified
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.only(
                                        bottom: 12,
                                      ),
                                      child: Text(
                                        _translate('Tips & Help'),
                                        style: AppTextStyles.headingSmall(
                                          context,
                                          color: colorScheme.onSurface,
                                        ),
                                      ),
                                    ),
                                    // Horizontal scrolling tips
                                    SizedBox(
                                      height: 120,
                                      child: ListView(
                                        scrollDirection: Axis.horizontal,
                                        physics: const BouncingScrollPhysics(),
                                        children: [
                                          _buildSimpleTipCard(
                                            title: _translate('Did you know?'),
                                            content: _translate(
                                              'You can chat directly with our technicians through the app.',
                                            ),
                                            icon: Icons.lightbulb_outline,
                                            color: colorScheme.primary,
                                            onTap: () {
                                              if (_activeRequest != null) {
                                                Navigator.of(context).push(
                                                  MaterialPageRoute(
                                                    builder:
                                                        (context) => ChatScreen(
                                                          request:
                                                              _activeRequest!,
                                                        ),
                                                  ),
                                                );
                                              } else {
                                                ScaffoldMessenger.of(
                                                  context,
                                                ).showSnackBar(
                                                  SnackBar(
                                                    content: Text(
                                                      _translate(
                                                        'You need an active request to chat with a technician.',
                                                      ),
                                                    ),
                                                  ),
                                                );
                                              }
                                            },
                                          ),

                                          const SizedBox(width: 16),

                                          _buildSimpleTipCard(
                                            title: _translate('AnyDesk Access'),
                                            content: _translate(
                                              'For remote support sessions, please download AnyDesk and share your access ID when prompted.',
                                            ),
                                            icon: Icons.computer,
                                            color: Colors.green.shade700,
                                            onTap: () {
                                              // Show AnyDesk dialog or navigate to help page
                                              _showAnyDeskInfoDialog();
                                            },
                                          ),

                                          const SizedBox(width: 16),

                                          _buildSimpleTipCard(
                                            title: _translate('Data Backup'),
                                            content: _translate(
                                              'Always backup your important data before any technical service or repair.',
                                            ),
                                            icon: Icons.backup,
                                            color: Colors.orange.shade700,
                                            onTap: () {
                                              // Show backup info dialog
                                              _showBackupInfoDialog();
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),

                        // Add bottom padding to ensure content is not cut off by bottom navigation
                        SliverToBoxAdapter(
                          child: SizedBox(
                            height: MediaQuery.of(context).padding.bottom + 100,
                          ),
                        ),
                      ],
                    ),
          ),
        ],
      ),
    );
  }

  // Build stylish quick action card with animation
  Widget _buildQuickActionCard({
    required IconData icon,
    required String title,
    required Color color,
    required VoidCallback onTap,
    required double delay,
  }) {
    // Use the simplified service card instead
    return _buildSimpleServiceCard(
      icon: icon,
      title: title,
      color: color,
      onTap: onTap,
    );
  }

  // Build a simple, flat service card
  Widget _buildSimpleServiceCard({
    required IconData icon,
    required String title,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: EdgeInsets.zero,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(color: Colors.grey.shade300),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(icon, color: color, size: 26),
              ),
              const SizedBox(height: 10),
              Text(
                title,
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurface,
                  fontWeight: FontWeight.w600,
                  fontSize: 13,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Build active request card with elegant style
  Widget _buildActiveRequestCard(RequestModel request) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Map request status to color
    Color statusColor = _getStatusColor(request.status);

    return LuxuryCard(
      hasShadow: true,
      elevation: 4,
      borderRadius: BorderRadius.circular(20),
      padding: const EdgeInsets.all(0),
      onTap: () => _showRequestDetailsDialog(context, request),
      child: Column(
        children: [
          // Header with status
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [statusColor.withValues(alpha: 0.8), statusColor],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            child: Row(
              children: [
                Icon(
                  _getStatusIcon(request.status),
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _getStatusText(request.status),
                    style: AppTextStyles.labelLarge(
                      context,
                      color: Colors.white,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    request.createdAt.toString().substring(0, 10),
                    style: AppTextStyles.bodySmall(
                      context,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Content
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Service type
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: statusColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        _getServiceIcon(request.serviceName),
                        color: statusColor,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _translate(request.serviceName),
                            style: AppTextStyles.labelLarge(context),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            request.customerIssue.length > 100
                                ? '${request.customerIssue.substring(0, 100)}...'
                                : request.customerIssue,
                            style: AppTextStyles.bodyMedium(
                              context,
                              color: theme.colorScheme.onSurface.withOpacity(
                                0.7,
                              ),
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 20),
                const Divider(height: 1),
                const SizedBox(height: 20),

                // Technician info (if assigned)
                if (request.technicianName != null &&
                    request.technicianName!.isNotEmpty) ...[
                  Row(
                    children: [
                      const Icon(
                        Icons.engineering,
                        size: 16,
                        color: Colors.grey,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _translate('Technician'),
                        style: AppTextStyles.bodyMedium(
                          context,
                          color: Colors.grey,
                        ),
                      ),
                      const Spacer(),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 6,
                        ),
                        decoration: BoxDecoration(
                          color: statusColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: statusColor.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          request.technicianName!,
                          style: AppTextStyles.bodyMedium(
                            context,
                            color: statusColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],

                // Request ID
                Row(
                  children: [
                    const Icon(Icons.tag, size: 16, color: Colors.grey),
                    const SizedBox(width: 8),
                    Text(
                      _translate('Request ID'),
                      style: AppTextStyles.bodyMedium(
                        context,
                        color: Colors.grey,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      request.id.substring(0, 8),
                      style: AppTextStyles.bodyMedium(context),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Show request details dialog
  void _showRequestDetailsDialog(BuildContext context, RequestModel request) {
    final colorScheme = Theme.of(context).colorScheme;
    final isRtl = Provider.of<TranslationService>(context, listen: false).isRtl;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Icon(
                  _getStatusIcon(request.status),
                  color: _getStatusColor(request.status),
                  size: 20,
                ),
                const SizedBox(width: 10),
                Text(
                  _translate('Request Details'),
                  style: AppTextStyles.headingSmall(context),
                ),
              ],
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDetailRow(
                    Icons.tag_rounded,
                    _translate('Request ID'),
                    request.id,
                    isRtl,
                  ),
                  const SizedBox(height: 16),
                  _buildDetailRow(
                    Icons.miscellaneous_services_rounded,
                    _translate('Service'),
                    _translate(request.serviceName),
                    isRtl,
                  ),
                  const SizedBox(height: 16),
                  _buildDetailRow(
                    _getStatusIcon(request.status),
                    _translate('Status'),
                    _translate(request.status.toString().split('.').last),
                    isRtl,
                    valueColor: _getStatusColor(request.status),
                  ),
                  const SizedBox(height: 16),
                  _buildDetailRow(
                    Icons.calendar_today_rounded,
                    _translate('Date'),
                    request.createdAt.toString().split(' ')[0],
                    isRtl,
                  ),
                  const SizedBox(height: 16),
                  _buildDetailRow(
                    Icons.notes,
                    _translate('Description'),
                    request.customerIssue,
                    isRtl,
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(_translate('Close')),
              ),
              if (request.status == RequestStatus.approved ||
                  request.status == RequestStatus.inProgress)
                FilledButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => ChatScreen(request: request),
                      ),
                    );
                  },
                  child: Text(_translate('Open Chat')),
                ),
            ],
          ),
    );
  }

  // Helper method for detail rows in the dialog
  Widget _buildDetailRow(
    IconData icon,
    String label,
    String value,
    bool isRtl, {
    Color? valueColor,
  }) {
    final theme = Theme.of(context);
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      textDirection: isRtl ? TextDirection.rtl : TextDirection.ltr,
      children: [
        Icon(icon, size: 16, color: theme.colorScheme.onSurfaceVariant),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment:
                isRtl ? CrossAxisAlignment.end : CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTextStyles.labelSmall(
                  context,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: AppTextStyles.bodyMedium(
                  context,
                  color: valueColor ?? theme.colorScheme.onSurface,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Helper method to get status icon
  IconData _getStatusIcon(RequestStatus status) {
    switch (status) {
      case RequestStatus.payment_pending:
        return Icons.payments_outlined;
      case RequestStatus.pending:
        return Icons.hourglass_empty_rounded;
      case RequestStatus.approved:
        return Icons.check_circle_outline_rounded;
      case RequestStatus.inProgress:
      case RequestStatus.in_progress:
        return Icons.engineering_rounded;
      case RequestStatus.completed:
        return Icons.task_alt_rounded;
      case RequestStatus.cancelled:
        return Icons.cancel_outlined;
      case RequestStatus.refused:
        return Icons.block_outlined;
    }
  }

  // Helper functions
  IconData _getServiceIcon(String serviceType) {
    switch (serviceType.toLowerCase()) {
      case 'computer repair':
        return Icons.computer;
      case 'software installation':
        return Icons.install_desktop;
      case 'network setup':
        return Icons.wifi;
      case 'data recovery':
        return Icons.storage;
      case 'virus removal':
        return Icons.security;
      default:
        return Icons.devices;
    }
  }

  Color _getStatusColor(RequestStatus status) {
    final colorScheme = Theme.of(context).colorScheme;

    switch (status) {
      case RequestStatus.payment_pending:
        return Colors.orange;
      case RequestStatus.pending:
        return Colors.orange;
      case RequestStatus.approved:
        return Colors.blue;
      case RequestStatus.inProgress:
      case RequestStatus.in_progress:
        return Colors.green;
      case RequestStatus.completed:
        return colorScheme.primary;
      case RequestStatus.cancelled:
        return Colors.red;
      case RequestStatus.refused:
        return Colors.red;
    }
  }

  String _getStatusText(RequestStatus status) {
    String statusText = status.toString().split('.').last;
    return _translate(statusText.replaceAll('_', ' ').toCapitalized());
  }

  // Navigate to notification screen
  void _openNotifications() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const NotificationScreen()));
  }

  // Add missing initials avatar method
  Widget _buildInitialsAvatar() {
    final initials =
        _userModel?.displayName?.isNotEmpty == true
            ? _userModel!.displayName![0]
            : _userModel?.email != null && _userModel!.email.isNotEmpty
            ? _userModel!.email[0].toUpperCase()
            : 'U';

    return Center(
      child: Text(
        initials,
        style: AppTextStyles.displayLarge(context, color: Colors.white),
      ),
    );
  }

  // Navigate directly to service details screen with the selected service
  void _navigateToService(String serviceName) {
    // Find the service by name (original name, not translated)
    final service = _popularServices.firstWhere(
      (s) => s.name == serviceName,
      orElse: () => _popularServices.first,
    );

    // Log the navigation for analytics
    debugPrint('Navigating to service: ${service.name}');

    // Navigate to request details screen with the selected service
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => RequestDetailsScreen(service: service),
      ),
    );
  }

  // Helper method to ensure service descriptions are properly translated
  String _getTranslatedDescription(ServiceModel service) {
    if (service.description.isEmpty) {
      return '';
    }

    // Get the current language code from the translation service
    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );
    final languageCode = translationService.currentLocale.languageCode;

    // Use the new getTranslatedDescription method if available
    return service.getTranslatedDescription(languageCode);
  }

  // Helper method to build simple tip cards
  Widget _buildSimpleTipCard({
    required String title,
    required String content,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return SizedBox(
      width: 240,
      height: 100, // Fixed height to prevent overflow
      child: Card(
        margin: EdgeInsets.zero,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(color: Colors.grey.shade300),
        ),
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(icon, color: color, size: 18),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        title,
                        style: AppTextStyles.labelMedium(
                          context,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 2),
                      Expanded(
                        child: Text(
                          content,
                          style: AppTextStyles.bodySmall(
                            context,
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 3,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Show AnyDesk info dialog
  void _showAnyDeskInfoDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Wrap(
              alignment: WrapAlignment.start,
              crossAxisAlignment: WrapCrossAlignment.center,
              spacing: 8,
              children: [
                Icon(
                  Icons.computer,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                Text(_translate('AnyDesk Remote Access')),
              ],
            ),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _translate(
                      'AnyDesk is a remote desktop application that allows our technicians to securely access your device for troubleshooting.',
                    ),
                    style: AppTextStyles.bodyMedium(context),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _translate('How it works:'),
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '1. ${_translate('Download AnyDesk from the official website')}',
                  ),
                  Text(
                    '2. ${_translate('Install and launch the application')}',
                  ),
                  Text(
                    '3. ${_translate('Share your AnyDesk ID with our technician when requested')}',
                  ),
                  Text('4. ${_translate('Approve the connection request')}'),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(_translate('Close')),
              ),
              FilledButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  // Add code to open AnyDesk if installed or direct to website
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(_translate('Opening AnyDesk website...')),
                    ),
                  );
                },
                child: Text(_translate('Download')),
              ),
            ],
          ),
    );
  }

  // Show backup info dialog
  void _showBackupInfoDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Wrap(
              alignment: WrapAlignment.start,
              crossAxisAlignment: WrapCrossAlignment.center,
              spacing: 8,
              children: [
                Icon(
                  Icons.backup,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                Text(_translate('Data Backup')),
              ],
            ),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _translate(
                      'Backing up your data is crucial before any technical service to prevent data loss.',
                    ),
                    style: AppTextStyles.bodyMedium(context),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _translate('Recommended backup methods:'),
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '• ${_translate('Cloud storage (Google Drive, Dropbox, etc.)')}',
                  ),
                  Text(
                    '• ${_translate('External hard drive or USB flash drive')}',
                  ),
                  Text('• ${_translate('Device built-in backup features')}'),
                  const SizedBox(height: 16),
                  Text(
                    _translate('What to backup:'),
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text('• ${_translate('Photos and videos')}'),
                  Text('• ${_translate('Documents and work files')}'),
                  Text('• ${_translate('Contacts and messages')}'),
                  Text('• ${_translate('App data and settings')}'),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(_translate('Close')),
              ),
            ],
          ),
    );
  }

  // Build user statistics widget
  Widget _buildUserStat({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        const SizedBox(height: 8),
        Text(value, style: AppTextStyles.labelLarge(context)),
        const SizedBox(height: 2),
        Text(
          label,
          style: AppTextStyles.bodySmall(
            context,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
          ),
        ),
      ],
    );
  }

  // Load popular services from database with caching
  Future<void> _loadPopularServices() async {
    try {
      debugPrint('HomeScreen: Loading popular services...');

      // Try to load from cache first for faster display
      final cachedServices = await _offlineService.getCachedServices();
      debugPrint(
        'HomeScreen: Found ${cachedServices?.length ?? 0} cached services',
      );

      if (cachedServices != null && cachedServices.isNotEmpty && mounted) {
        setState(() {
          _popularServices = cachedServices.take(3).toList();
        });
        debugPrint(
          'HomeScreen: Set ${_popularServices.length} popular services from cache',
        );
      }

      // Load fresh data in background
      Future.microtask(() async {
        try {
          // Check if we have context and it's still mounted
          if (!mounted) {
            debugPrint(
              'HomeScreen: Widget not mounted, skipping fresh data load',
            );
            return;
          }

          final databaseService = Provider.of<DatabaseService>(
            context,
            listen: false,
          );

          debugPrint('HomeScreen: Loading fresh services from database...');
          // Load all active services
          final allServices = await databaseService.getActiveServices();
          debugPrint(
            'HomeScreen: Loaded ${allServices.length} services from database',
          );

          // Only proceed if we got services
          if (allServices.isEmpty) {
            debugPrint('HomeScreen: No services returned from database');
            return;
          }

          // Cache the services for next time
          await _offlineService.cacheServices(allServices);

          // Sort by popularity (if metadata contains popularityIndex)
          allServices.sort((a, b) {
            final popularityA = a.metadata?['popularityIndex'] ?? 0;
            final popularityB = b.metadata?['popularityIndex'] ?? 0;
            return (popularityB as num).compareTo(popularityA as num);
          });

          // Get top 3 services and update UI
          if (mounted) {
            setState(() {
              _popularServices = allServices.take(3).toList();
            });
            debugPrint(
              'HomeScreen: Updated UI with ${_popularServices.length} popular services',
            );
          } else {
            debugPrint('HomeScreen: Widget unmounted before UI update');
          }
        } catch (e) {
          debugPrint('Error loading fresh popular services: $e');

          // If fresh load fails but we have no cached services, try to show something
          if (_popularServices.isEmpty && mounted) {
            debugPrint(
              'HomeScreen: No services available, keeping loading state',
            );
          }
        }
      });
    } catch (e) {
      debugPrint('Error loading popular services: $e');
    }
  }

  // Get icon for service category
  IconData _getServiceIconData(ServiceModel service) {
    if (service.metadata != null && service.metadata!.containsKey('icon')) {
      // Get icon from metadata
      final dynamic iconData = service.metadata!['icon'];
      // If it's a string, map it to an IconData
      if (iconData is String) {
        // Map of common icon names to IconData
        final Map<String, IconData> iconMap = {
          'computer': Icons.computer_rounded,
          'article': Icons.article_rounded,
          'print': Icons.print_rounded,
          'wifi': Icons.wifi_rounded,
          'security': Icons.security_rounded,
          'backup': Icons.backup_rounded,
          'storage': Icons.storage_rounded,
          'devices': Icons.devices_rounded,
        };

        return iconMap[iconData] ?? Icons.miscellaneous_services_rounded;
      }
    }

    // Default icon based on category
    final Map<String, IconData> categoryIcons = {
      'os': Icons.computer_rounded,
      'productivity': Icons.article_rounded,
      'hardware': Icons.devices_other_rounded,
      'network': Icons.wifi_rounded,
      'security': Icons.security_rounded,
      'data': Icons.storage_rounded,
    };

    return categoryIcons[service.category] ??
        Icons.miscellaneous_services_rounded;
  }

  // Setup listener for all requests to keep counters updated
  void _setupRequestCountListener() {
    final requestService = RequestService();

    _allRequestsSubscription = requestService.getUserRequests().listen(
      (requests) {
        if (mounted) {
          // Count completed requests
          final completedRequests =
              requests
                  .where((request) => request.status == RequestStatus.completed)
                  .toList();

          setState(() {
            _totalRequestsCount = requests.length;
            _completedRequestsCount = completedRequests.length;
          });
        }
      },
      onError: (error) {
        debugPrint('Error in requests stream: $error');
      },
    );
  }

  // Initialize offline service
  Future<void> _initializeOfflineService() async {
    try {
      await _offlineService.initialize();

      // Listen to connectivity changes
      _offlineService.connectivityStream.listen((isOnline) {
        if (mounted) {
          setState(() {
            _isOffline = !isOnline;
          });
        }
      });
    } catch (e) {
      debugPrint('Error initializing offline service: $e');
    }
  }

  // Build AnyDesk indicator widget
  Widget _buildAnyDeskIndicator(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final hasAnyDesk =
        _userModel?.anydesk_id != null && _userModel!.anydesk_id!.isNotEmpty;
    final anydeskColor =
        hasAnyDesk ? Colors.green.shade700 : Colors.orange.shade700;

    return InkWell(
      onTap: () {
        if (!hasAnyDesk) {
          // Navigate to profile screen if AnyDesk ID is not set
          NavigationUtils.navigateKeepingStack(
            context,
            const EditProfileScreen(),
          );
          // Show toast to guide user
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                _translate('Please set your AnyDesk ID for remote support'),
              ),
              duration: const Duration(seconds: 3),
            ),
          );
        } else {
          // Show AnyDesk info dialog when AnyDesk ID is set
          _showAnyDeskInfoDialog();
        }
      },
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                hasAnyDesk
                    ? Icons.desktop_windows
                    : Icons.desktop_access_disabled,
                size: 16,
                color: anydeskColor,
              ),
              const SizedBox(width: 6),
              Text(
                hasAnyDesk ? "✓" : "!",
                style: AppTextStyles.headingSmall(context, color: anydeskColor),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            _translate('AnyDesk'),
            style: AppTextStyles.bodySmall(
              context,
              color: colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to build tip cards (for backward compatibility)
  Widget _buildTipCard({
    required String title,
    required String content,
    required IconData icon,
    required Gradient gradient,
    required VoidCallback onTap,
  }) {
    // Use the simple tip card instead
    return _buildSimpleTipCard(
      title: title,
      content: content,
      icon: icon,
      color: gradient.colors.first,
      onTap: onTap,
    );
  }

  // Build a service card for the carousel
  Widget _buildServiceCard({
    required ServiceModel service,
    required Color color,
  }) {
    // Get the current language code from the translation service
    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );
    final languageCode = translationService.currentLocale.languageCode;
    final colorScheme = Theme.of(context).colorScheme;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    // Ensure proper translation of service name and description
    final translatedName = service.getTranslatedName(languageCode);
    final translatedDescription = _getTranslatedDescription(service);

    // Adjust color for dark mode
    final containerColor =
        isDarkMode ? colorScheme.surfaceContainerHighest : colorScheme.surface;

    final iconBgColor =
        isDarkMode ? color.withOpacity(0.2) : color.withOpacity(0.1);

    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
        side: BorderSide(color: Colors.grey.shade300, width: 1),
      ),
      child: InkWell(
        onTap: () => _navigateToService(service.name),
        borderRadius: BorderRadius.circular(10),
        child: Container(
          padding: const EdgeInsets.all(16),
          height: 200, // Increased height to accommodate more text
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            color: containerColor,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Icon and title row
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: iconBgColor,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      _getServiceIconData(service),
                      color: color,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      translatedName,
                      style: AppTextStyles.labelLarge(
                        context,
                        color: colorScheme.onSurface,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12), // Increased spacing
              // Description
              if (translatedDescription.isNotEmpty)
                Expanded(
                  child: Text(
                    translatedDescription,
                    style: AppTextStyles.bodySmall(
                      context,
                      color:
                          isDarkMode
                              ? colorScheme.onSurfaceVariant.withOpacity(0.9)
                              : colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 4, // Increased max lines
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

              const SizedBox(height: 12), // Added consistent spacing
              // Action button
              Align(
                alignment: Alignment.centerRight,
                child: TextButton.icon(
                  onPressed: () => _navigateToService(service.name),
                  icon: Icon(
                    Icons.arrow_forward,
                    size: 14,
                    color: isDarkMode ? color.withOpacity(0.9) : color,
                  ),
                  label: Text(
                    _translate('Request'),
                    style: AppTextStyles.buttonSmall(
                      context,
                      color: isDarkMode ? color.withOpacity(0.9) : color,
                    ),
                  ),
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 10,
                      vertical: 6,
                    ),
                    backgroundColor:
                        isDarkMode
                            ? colorScheme.surfaceContainerHighest.withOpacity(
                              0.3,
                            )
                            : Colors.transparent,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
