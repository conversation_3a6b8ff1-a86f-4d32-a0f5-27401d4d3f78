/**
 * Type guard utilities for runtime type checking
 * These functions help ensure type safety at runtime
 */

import type { 
  User, 
  UserRole as UserRoleType,
  UserStatus 
} from '../types/user';
import type { 
  Request, 
  RequestStatus as RequestStatusType 
} from '../types/request';
import type { 
  Service, 
  ServiceCategory as ServiceCategoryType 
} from '../types/service';
import type { 
  ChatEvent, 
  ChatMessage 
} from '../types/chat';
import type { 
  Payment, 
  PaymentStatus as PaymentStatusType 
} from '../types/payment';
import type { ApiResponse, AppError } from '../types/common';
import { 
  ChatEventType, 
  ChatMessageType, 
  ErrorCode, 
  PaymentStatus,
  RequestStatus,
  ServiceCategory,
  UserRole
} from '../types/enums';

// Basic type guards
export const isString = (value: unknown): value is string => 
  typeof value === 'string';

export const isNumber = (value: unknown): value is number => 
  typeof value === 'number' && !isNaN(value);

export const isBoolean = (value: unknown): value is boolean => 
  typeof value === 'boolean';

export const isObject = (value: unknown): value is Record<string, unknown> => 
  typeof value === 'object' && value !== null && !Array.isArray(value);

export const isArray = (value: unknown): value is unknown[] => 
  Array.isArray(value);

export const isFunction = (value: unknown): value is Function => 
  typeof value === 'function';

export const isDate = (value: unknown): value is Date => 
  value instanceof Date && !isNaN(value.getTime());

export const isNull = (value: unknown): value is null => 
  value === null;

export const isUndefined = (value: unknown): value is undefined => 
  value === undefined;

export const isNullOrUndefined = (value: unknown): value is null | undefined => 
  value === null || value === undefined;

// Enum validation guards
export const isUserRole = (value: unknown): value is UserRoleType => 
  isString(value) && Object.values(UserRole).includes(value as UserRole);

export const isRequestStatus = (value: unknown): value is RequestStatusType => 
  isString(value) && Object.values(RequestStatus).includes(value as RequestStatus);

export const isPaymentStatus = (value: unknown): value is PaymentStatusType => 
  isString(value) && Object.values(PaymentStatus).includes(value as PaymentStatus);

export const isServiceCategory = (value: unknown): value is ServiceCategoryType => 
  isString(value) && Object.values(ServiceCategory).includes(value as ServiceCategory);

export const isChatMessageType = (value: unknown): value is ChatMessageType => 
  isString(value) && Object.values(ChatMessageType).includes(value as ChatMessageType);

export const isChatEventType = (value: unknown): value is ChatEventType => 
  isString(value) && Object.values(ChatEventType).includes(value as ChatEventType);

export const isErrorCode = (value: unknown): value is ErrorCode => 
  isString(value) && Object.values(ErrorCode).includes(value as ErrorCode);

// Complex type guards
export const isUser = (value: unknown): value is User => {
  if (!isObject(value)) return false;
  
  const user = value as Record<string, unknown>;
  return (
    isString(user.id) &&
    isString(user.email) &&
    isString(user.name) &&
    isUserRole(user.role) &&
    (isUndefined(user.phone) || isString(user.phone)) &&
    (isUndefined(user.avatar) || isString(user.avatar))
  );
};

export const isRequest = (value: unknown): value is Request => {
  if (!isObject(value)) return false;
  
  const request = value as Record<string, unknown>;
  return (
    isString(request.id) &&
    isString(request.customerId) &&
    isString(request.serviceId) &&
    isRequestStatus(request.status) &&
    (isDate(request.createdAt) || isString(request.createdAt)) &&
    (isUndefined(request.technicianId) || isString(request.technicianId))
  );
};

export const isService = (value: unknown): value is Service => {
  if (!isObject(value)) return false;
  
  const service = value as Record<string, unknown>;
  return (
    isString(service.id) &&
    isString(service.name) &&
    isServiceCategory(service.category) &&
    isNumber(service.price) &&
    isBoolean(service.active)
  );
};

export const isChatMessage = (value: unknown): value is ChatMessage => {
  if (!isObject(value)) return false;
  
  const message = value as Record<string, unknown>;
  return (
    isString(message.id) &&
    isString(message.senderId) &&
    isString(message.content) &&
    isChatMessageType(message.type) &&
    (isDate(message.timestamp) || isString(message.timestamp))
  );
};

export const isChatEvent = (value: unknown): value is ChatEvent => {
  if (!isObject(value)) return false;
  
  const event = value as Record<string, unknown>;
  return (
    isChatEventType(event.type) &&
    isObject(event.data) &&
    (isDate(event.timestamp) || isString(event.timestamp)) &&
    isString(event.requestId)
  );
};

export const isPayment = (value: unknown): value is Payment => {
  if (!isObject(value)) return false;
  
  const payment = value as Record<string, unknown>;
  return (
    isString(payment.id) &&
    isString(payment.requestId) &&
    isNumber(payment.amount) &&
    isPaymentStatus(payment.status) &&
    (isDate(payment.createdAt) || isString(payment.createdAt))
  );
};

export const isAppError = (value: unknown): value is AppError => {
  if (!isObject(value)) return false;
  
  const error = value as Record<string, unknown>;
  return (
    isString(error.code) &&
    isString(error.message) &&
    (isUndefined(error.details) || isObject(error.details)) &&
    (isUndefined(error.timestamp) || isDate(error.timestamp)) &&
    (isUndefined(error.context) || isString(error.context))
  );
};

export const isApiResponse = <T>(
  value: unknown,
  dataGuard?: (data: unknown) => data is T
): value is ApiResponse<T> => {
  if (!isObject(value)) return false;
  
  const response = value as Record<string, unknown>;
  const hasValidStructure = (
    isBoolean(response.success) &&
    (isUndefined(response.error) || isString(response.error)) &&
    (isUndefined(response.message) || isString(response.message)) &&
    (isUndefined(response.timestamp) || isString(response.timestamp))
  );
  
  if (!hasValidStructure) return false;
  
  // If data guard is provided and data exists, validate data
  if (dataGuard && response.data !== undefined) {
    return dataGuard(response.data);
  }
  
  return true;
};

// Array type guards
export const isArrayOf = <T>(
  value: unknown,
  itemGuard: (item: unknown) => item is T
): value is T[] => {
  if (!isArray(value)) return false;
  return value.every(itemGuard);
};

export const isUserArray = (value: unknown): value is User[] => 
  isArrayOf(value, isUser);

export const isRequestArray = (value: unknown): value is Request[] => 
  isArrayOf(value, isRequest);

export const isServiceArray = (value: unknown): value is Service[] => 
  isArrayOf(value, isService);

export const isChatMessageArray = (value: unknown): value is ChatMessage[] => 
  isArrayOf(value, isChatMessage);

export const isPaymentArray = (value: unknown): value is Payment[] => 
  isArrayOf(value, isPayment);

// Utility type guards for common patterns
export const hasProperty = <T extends string>(
  obj: unknown,
  prop: T
): obj is Record<T, unknown> => {
  return isObject(obj) && prop in obj;
};

export const hasRequiredProperties = <T extends string>(
  obj: unknown,
  props: T[]
): obj is Record<T, unknown> => {
  if (!isObject(obj)) return false;
  return props.every(prop => prop in obj);
};

// Firebase-specific type guards
export const isFirebaseTimestamp = (value: unknown): boolean => {
  return isObject(value) && 
         hasProperty(value, 'seconds') && 
         hasProperty(value, 'nanoseconds') &&
         isNumber(value.seconds) && 
         isNumber(value.nanoseconds);
};

// Error type guards
export const isError = (value: unknown): value is Error => {
  return value instanceof Error || (
    isObject(value) && 
    hasProperty(value, 'message') && 
    isString(value.message)
  );
};

// Form validation type guards
export const isValidEmail = (value: unknown): value is string => {
  if (!isString(value)) return false;
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(value);
};

export const isValidPhone = (value: unknown): value is string => {
  if (!isString(value)) return false;
  const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
  return phoneRegex.test(value);
};

export const isValidUrl = (value: unknown): value is string => {
  if (!isString(value)) return false;
  try {
    new URL(value);
    return true;
  } catch {
    return false;
  }
};

// ID validation
export const isValidId = (value: unknown): value is string => {
  return isString(value) && value.length > 0 && value.trim() === value;
};

export const isValidUUID = (value: unknown): value is string => {
  if (!isString(value)) return false;
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(value);
};

// Generic assertion function
export const assertType = <T>(
  value: unknown,
  guard: (value: unknown) => value is T,
  errorMessage?: string
): T => {
  if (!guard(value)) {
    throw new Error(errorMessage || 'Type assertion failed');
  }
  return value;
};

// Safe parsing with type guards
export const safeParse = <T>(
  jsonString: string,
  guard: (value: unknown) => value is T
): T | null => {
  try {
    const parsed = JSON.parse(jsonString);
    return guard(parsed) ? parsed : null;
  } catch {
    return null;
  }
};
