import { beforeEach, describe, expect, it, vi } from 'vitest';
import { authService } from '../authService';
import { ErrorCode } from '../../types/enums';

// Mock Firebase Auth
const mockSignInWithEmailAndPassword = vi.fn();
const mockSignOut = vi.fn();
const mockCreateUserWithEmailAndPassword = vi.fn();
const mockSendPasswordResetEmail = vi.fn();
const mockOnAuthStateChanged = vi.fn();

vi.mock('firebase/auth', () => ({
  signInWithEmailAndPassword: mockSignInWithEmailAndPassword,
  signOut: mockSignOut,
  createUserWithEmailAndPassword: mockCreateUserWithEmailAndPassword,
  sendPasswordResetEmail: mockSendPasswordResetEmail,
  onAuthStateChanged: mockOnAuthStateChanged,
  updateProfile: vi.fn(),
}));

// Mock Firebase Firestore
const mockGetDoc = vi.fn();
const mockDoc = vi.fn();

vi.mock('firebase/firestore', () => ({
  getDoc: mockGetDoc,
  doc: mockDoc,
  setDoc: vi.fn(),
  updateDoc: vi.fn(),
  serverTimestamp: vi.fn(),
}));

// Mock other services
vi.mock('./sessionService', () => ({
  default: {
    setPersistence: vi.fn(),
  },
}));

vi.mock('./errorService', () => ({
  errorService: {
    processError: vi.fn(),
  },
}));

describe('AuthService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('login', () => {
    it('should login successfully with valid credentials', async () => {
      const mockUser = {
        uid: 'test-uid',
        email: '<EMAIL>',
      };
      const mockUserCredential = { user: mockUser };
      const mockUserDoc = {
        exists: () => true,
        data: () => ({
          id: 'test-uid',
          email: '<EMAIL>',
          role: 'customer',
          display_name: 'Test User',
        }),
      };

      mockSignInWithEmailAndPassword.mockResolvedValue(mockUserCredential);
      mockDoc.mockReturnValue('mock-doc-ref');
      mockGetDoc.mockResolvedValue(mockUserDoc);

      const result = await authService.login({
        email: '<EMAIL>',
        password: 'password123',
        rememberMe: false,
      });

      expect(result.success).toBe(true);
      expect(result.data?.email).toBe('<EMAIL>');
      expect(mockSignInWithEmailAndPassword).toHaveBeenCalledWith(
        expect.anything(),
        '<EMAIL>',
        'password123'
      );
    });

    it('should return error for missing email', async () => {
      const result = await authService.login({
        email: '',
        password: 'password123',
      });

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe(ErrorCode.VALIDATION_REQUIRED_FIELD);
    });

    it('should return error for missing password', async () => {
      const result = await authService.login({
        email: '<EMAIL>',
        password: '',
      });

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe(ErrorCode.VALIDATION_REQUIRED_FIELD);
    });

    it('should return error for invalid email type', async () => {
      const result = await authService.login({
        email: 123 as any,
        password: 'password123',
      });

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe(ErrorCode.VALIDATION_INVALID_FORMAT);
    });

    it('should handle Firebase auth errors - invalid credentials', async () => {
      mockSignInWithEmailAndPassword.mockRejectedValue(
        new Error('user-not-found')
      );

      const result = await authService.login({
        email: '<EMAIL>',
        password: 'wrongpassword',
      });

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe(ErrorCode.AUTH_INVALID_CREDENTIALS);
    });

    it('should handle Firebase auth errors - too many requests', async () => {
      mockSignInWithEmailAndPassword.mockRejectedValue(
        new Error('too-many-requests')
      );

      const result = await authService.login({
        email: '<EMAIL>',
        password: 'password123',
      });

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe(ErrorCode.AUTH_RATE_LIMITED);
    });

    it('should handle Firebase auth errors - user disabled', async () => {
      mockSignInWithEmailAndPassword.mockRejectedValue(
        new Error('user-disabled')
      );

      const result = await authService.login({
        email: '<EMAIL>',
        password: 'password123',
      });

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe(ErrorCode.AUTH_ACCOUNT_DISABLED);
    });

    it('should handle unknown Firebase auth errors', async () => {
      mockSignInWithEmailAndPassword.mockRejectedValue(
        new Error('unknown-error')
      );

      const result = await authService.login({
        email: '<EMAIL>',
        password: 'password123',
      });

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe(ErrorCode.AUTH_UNKNOWN_ERROR);
    });

    it('should handle user document not found', async () => {
      const mockUser = {
        uid: 'test-uid',
        email: '<EMAIL>',
      };
      const mockUserCredential = { user: mockUser };

      mockSignInWithEmailAndPassword.mockResolvedValue(mockUserCredential);
      mockDoc.mockReturnValue('mock-doc-ref');
      mockGetDoc
        .mockResolvedValueOnce({ exists: () => false }) // admins
        .mockResolvedValueOnce({ exists: () => false }) // technicians
        .mockResolvedValueOnce({ exists: () => false }); // users

      const result = await authService.login({
        email: '<EMAIL>',
        password: 'password123',
      });

      expect(result.success).toBe(false);
      expect(result.error?.message).toContain('Failed to load user data');
    });

    it('should check multiple collections for user data', async () => {
      const mockUser = {
        uid: 'test-uid',
        email: '<EMAIL>',
      };
      const mockUserCredential = { user: mockUser };
      const mockUserDoc = {
        exists: () => true,
        data: () => ({
          id: 'test-uid',
          email: '<EMAIL>',
          role: 'technician',
          display_name: 'Test Technician',
        }),
      };

      mockSignInWithEmailAndPassword.mockResolvedValue(mockUserCredential);
      mockDoc.mockReturnValue('mock-doc-ref');
      mockGetDoc
        .mockResolvedValueOnce({ exists: () => false }) // admins
        .mockResolvedValueOnce(mockUserDoc); // technicians

      const result = await authService.login({
        email: '<EMAIL>',
        password: 'password123',
      });

      expect(result.success).toBe(true);
      expect(result.data?.role).toBe('technician');
      expect(mockGetDoc).toHaveBeenCalledTimes(2); // Should stop after finding in technicians
    });
  });

  describe('logout', () => {
    it('should logout successfully', async () => {
      mockSignOut.mockResolvedValue(undefined);

      const result = await authService.logout();

      expect(result.success).toBe(true);
      expect(mockSignOut).toHaveBeenCalled();
    });

    it('should handle logout errors', async () => {
      mockSignOut.mockRejectedValue(new Error('Logout failed'));

      const result = await authService.logout();

      expect(result.success).toBe(false);
      expect(result.error?.message).toContain('Logout failed');
    });
  });

  describe('resetPassword', () => {
    it('should send password reset email successfully', async () => {
      mockSendPasswordResetEmail.mockResolvedValue(undefined);

      const result = await authService.resetPassword('<EMAIL>');

      expect(result.success).toBe(true);
      expect(mockSendPasswordResetEmail).toHaveBeenCalledWith(
        expect.anything(),
        '<EMAIL>'
      );
    });

    it('should return error for invalid email', async () => {
      const result = await authService.resetPassword('');

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe(ErrorCode.VALIDATION_REQUIRED_FIELD);
    });

    it('should handle Firebase errors', async () => {
      mockSendPasswordResetEmail.mockRejectedValue(
        new Error('user-not-found')
      );

      const result = await authService.resetPassword('<EMAIL>');

      expect(result.success).toBe(false);
      expect(result.error?.message).toContain('user-not-found');
    });
  });

  describe('convertFirebaseUser', () => {
    it('should convert Firebase user from admins collection', async () => {
      const mockFirebaseUser = {
        uid: 'admin-uid',
        email: '<EMAIL>',
        displayName: 'Admin User',
        emailVerified: true,
      };

      const mockAdminDoc = {
        exists: () => true,
        data: () => ({
          id: 'admin-uid',
          email: '<EMAIL>',
          role: 'admin',
          display_name: 'Admin User',
        }),
      };

      mockDoc.mockReturnValue('mock-doc-ref');
      mockGetDoc.mockResolvedValueOnce(mockAdminDoc);

      // Access the private method through the service instance
      const result = await (authService as any).convertFirebaseUser(mockFirebaseUser);

      expect(result).toBeDefined();
      expect(result.role).toBe('admin');
      expect(mockGetDoc).toHaveBeenCalledTimes(1); // Should find in first collection
    });

    it('should return null when user document not found', async () => {
      const mockFirebaseUser = {
        uid: 'unknown-uid',
        email: '<EMAIL>',
      };

      mockDoc.mockReturnValue('mock-doc-ref');
      mockGetDoc
        .mockResolvedValueOnce({ exists: () => false }) // admins
        .mockResolvedValueOnce({ exists: () => false }) // technicians
        .mockResolvedValueOnce({ exists: () => false }); // users

      const result = await (authService as any).convertFirebaseUser(mockFirebaseUser);

      expect(result).toBeNull();
      expect(mockGetDoc).toHaveBeenCalledTimes(3); // Should check all collections
    });
  });

  describe('getCurrentUser', () => {
    it('should return current user when authenticated', async () => {
      const mockCurrentUser = {
        uid: 'current-uid',
        email: '<EMAIL>',
      };
      const mockUserDoc = {
        exists: () => true,
        data: () => ({
          id: 'current-uid',
          email: '<EMAIL>',
          role: 'customer',
        }),
      };

      // Mock auth.currentUser
      vi.mocked(authService as any).auth = { currentUser: mockCurrentUser };
      mockDoc.mockReturnValue('mock-doc-ref');
      mockGetDoc.mockResolvedValue(mockUserDoc);

      const result = await authService.getCurrentUser();

      expect(result.success).toBe(true);
      expect(result.data?.email).toBe('<EMAIL>');
    });

    it('should return error when not authenticated', async () => {
      // Mock auth.currentUser as null
      vi.mocked(authService as any).auth = { currentUser: null };

      const result = await authService.getCurrentUser();

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe(ErrorCode.AUTH_NOT_AUTHENTICATED);
    });
  });
});
