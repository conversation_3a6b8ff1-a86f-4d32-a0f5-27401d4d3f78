#!/usr/bin/env node

/**
 * Code Style Fixer Script
 *
 * This script helps fix common code style issues that can't be automatically
 * resolved by ESLint --fix or Prettier.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const SRC_DIR = path.join(__dirname, '../src');
const EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];

/**
 * Get all files recursively from a directory
 */
function getAllFiles(dir, extensions = EXTENSIONS) {
  const files = [];

  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);

    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        // Skip node_modules, dist, and other build directories
        if (!['node_modules', 'dist', 'build', '.git', '.taskmaster'].includes(item)) {
          traverse(fullPath);
        }
      } else if (extensions.some((ext) => item.endsWith(ext))) {
        files.push(fullPath);
      }
    }
  }

  traverse(dir);
  return files;
}

/**
 * Fix specific import path inconsistencies
 */
function fixImportPaths(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Fix @/ imports in UI components to use relative paths
  if (filePath.includes('components/ui/')) {
    const newContent = content.replace(/from ['"]@\/lib\/utils['"];?/g, "from '../../lib/utils';");

    if (newContent !== content) {
      content = newContent;
      modified = true;
    }
  }

  if (modified) {
    fs.writeFileSync(filePath, content, 'utf8');
    console.log(`✓ Fixed import paths in ${path.relative(process.cwd(), filePath)}`);
  }

  return modified;
}

/**
 * Fix console.log statements to console.warn
 */
function fixConsoleStatements(filePath) {
  // Skip test files
  if (/test|spec|\.test\.|\.spec\./.test(filePath)) {
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  const newContent = content.replace(/console\.log\(/g, 'console.warn(');

  if (newContent !== content) {
    fs.writeFileSync(filePath, newContent, 'utf8');
    console.log(`✓ Fixed console statements in ${path.relative(process.cwd(), filePath)}`);
    return true;
  }

  return false;
}

/**
 * Main execution
 */
function main() {
  console.log('🔧 Starting code style fixes...\n');

  const files = getAllFiles(SRC_DIR);
  let totalFixed = 0;

  console.log(`Found ${files.length} files to process\n`);

  // Apply specific fixes
  console.log('📝 Fixing import paths...');
  for (const file of files) {
    if (fixImportPaths(file)) {
      totalFixed++;
    }
  }

  console.log('\n📝 Fixing console statements...');
  for (const file of files) {
    if (fixConsoleStatements(file)) {
      totalFixed++;
    }
  }

  console.log(`\n✅ Manual fixes completed!`);
  console.log(`📊 Modified ${totalFixed} files`);

  // Run formatter after fixes
  console.log('\n🎨 Running Prettier to format files...');
  try {
    execSync('npm run format', { stdio: 'inherit', cwd: path.join(__dirname, '..') });
    console.log('✅ Formatting completed!');
  } catch (error) {
    console.error('❌ Formatting failed:', error.message);
  }

  // Run linter auto-fix
  console.log('\n🔧 Running ESLint auto-fix...');
  try {
    execSync('npm run lint:fix', { stdio: 'inherit', cwd: path.join(__dirname, '..') });
    console.log('✅ Auto-fix completed!');
  } catch (error) {
    console.log('⚠️  Some issues could not be auto-fixed.');
  }

  console.log('\n🎉 Code style standardization completed!');
  console.log('\n📚 Next steps:');
  console.log('1. Review the changes made');
  console.log('2. Run tests to ensure nothing is broken');
  console.log('3. Commit the style improvements');
  console.log('4. Refer to CODE_STYLE_GUIDE.md for ongoing standards');
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  getAllFiles,
  fixImportPaths,
  fixConsoleStatements,
};
