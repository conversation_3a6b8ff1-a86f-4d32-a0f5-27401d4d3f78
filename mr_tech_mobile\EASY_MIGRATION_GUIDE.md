# 🚀 Easy Services Migration - Test That Your App Keeps Working!

## 🎯 **Simple Firebase Console Method (Recommended)**

Since the Dart script had compilation issues, here's the **easiest way** to run the migration:

### **Step 1: Open Firebase Console**
1. Go to [Firebase Console](https://console.firebase.google.com)
2. Select your Mr. Tech project
3. Click **Firestore Database** in the sidebar
4. Click **Data** tab

### **Step 2: Open JavaScript Console**
1. In your browser, press **F12** (or **Cmd+Option+I** on Mac)
2. Go to the **Console** tab
3. You'll see the JavaScript console where you can run code

### **Step 3: Co<PERSON> and Paste the Migration Script**
Copy this entire script and paste it into the browser console:

```javascript
// SERVICES MIGRATION SCRIPT - COPY AND PASTE THIS ENTIRE BLOCK
console.log('🚀 SERVICES MIGRATION SCRIPT (Firebase Console)');
console.log('═'.repeat(50));
console.log('This script will add snake_case fields to your existing services');
console.log('Your original camelCase fields will be PRESERVED');
console.log('');

async function migrateServicesInConsole() {
  try {
    // Get services collection
    const servicesSnapshot = await firebase.firestore().collection('services').get();
    
    if (servicesSnapshot.empty) {
      console.log('❌ No services found in Firestore');
      return;
    }

    console.log('📊 BEFORE MIGRATION - Current Services:');
    console.log(`   Total Services: ${servicesSnapshot.docs.length}`);
    
    let hasSnakeCase = 0;
    let hasCamelCaseOnly = 0;
    
    servicesSnapshot.docs.forEach(doc => {
      const data = doc.data();
      const hasSnakeCaseFields = data.base_price !== undefined || 
                                 data.is_active !== undefined || 
                                 data.estimated_duration !== undefined;
      
      if (hasSnakeCaseFields) {
        hasSnakeCase++;
      } else {
        hasCamelCaseOnly++;
      }
    });
    
    console.log(`   - With snake_case fields: ${hasSnakeCase}`);
    console.log(`   - CamelCase only: ${hasCamelCaseOnly}`);
    console.log(`   - Migration progress: ${servicesSnapshot.docs.length > 0 ? Math.round(hasSnakeCase / servicesSnapshot.docs.length * 100) : 0}%`);
    console.log('');
    
    console.log('🔄 STARTING MIGRATION...');
    console.log('');
    
    let migrated = 0;
    let alreadyMigrated = 0;
    let errors = 0;
    
    for (const doc of servicesSnapshot.docs) {
      try {
        const data = doc.data();
        const updates = {};
        let needsUpdate = false;
        
        // Add snake_case fields
        if (data.basePrice !== undefined && data.base_price === undefined) {
          updates.base_price = data.basePrice;
          needsUpdate = true;
        }
        
        if (data.isActive !== undefined && data.is_active === undefined) {
          updates.is_active = data.isActive;
          needsUpdate = true;
        }
        
        if (data.estimatedDuration !== undefined && data.estimated_duration === undefined) {
          updates.estimated_duration = data.estimatedDuration;
          needsUpdate = true;
        }
        
        if (data.imageUrl !== undefined && data.image_url === undefined) {
          updates.image_url = data.imageUrl;
          needsUpdate = true;
        }
        
        if (data.created_at === undefined && data.createdAt === undefined) {
          updates.created_at = firebase.firestore.FieldValue.serverTimestamp();
          needsUpdate = true;
        } else if (data.createdAt !== undefined && data.created_at === undefined) {
          updates.created_at = data.createdAt;
          needsUpdate = true;
        }
        
        if (data.updated_at === undefined) {
          updates.updated_at = firebase.firestore.FieldValue.serverTimestamp();
          needsUpdate = true;
        }
        
        if (needsUpdate) {
          await doc.ref.update(updates);
          migrated++;
          
          let serviceName = 'Unknown';
          if (typeof data.name === 'string') {
            serviceName = data.name;
          } else if (data.name && typeof data.name === 'object') {
            serviceName = data.name.en || data.name.ar || Object.values(data.name)[0] || 'Unknown';
          }
          
          console.log(`   ✅ Migrated: ${serviceName}`);
          const fieldList = Object.keys(updates).join(', ');
          console.log(`      Added: ${fieldList}`);
        } else {
          alreadyMigrated++;
          console.log(`   ⏭️ Already migrated: ${doc.id}`);
        }
        
      } catch (error) {
        errors++;
        console.log(`   ❌ Error migrating ${doc.id}: ${error.message}`);
      }
    }
    
    console.log('');
    console.log('📊 MIGRATION SUMMARY:');
    console.log(`   - Total services: ${servicesSnapshot.docs.length}`);
    console.log(`   - Migrated: ${migrated}`);
    console.log(`   - Already migrated: ${alreadyMigrated}`);
    console.log(`   - Errors: ${errors}`);
    
    console.log('');
    console.log('🎉 MIGRATION COMPLETED SUCCESSFULLY!');
    console.log('✅ All services now have both camelCase AND snake_case fields');
    console.log('✅ Your app will work with both field formats');
    console.log('✅ ServiceModel will prefer snake_case but fallback to camelCase');
    console.log('✅ No data was lost or removed');
    
  } catch (error) {
    console.log(`❌ ERROR: ${error.message}`);
  }
}

// RUN THE MIGRATION
migrateServicesInConsole();
```

### **Step 4: Press Enter**
After pasting, press **Enter** to run the script. You'll see the migration progress in real-time.

### **Step 5: Test Your App**
```bash
cd mr_tech_mobile
flutter run
```

1. Open the services screen
2. Verify all services load normally
3. Check debug console for field usage logs
4. **Your app should work perfectly!**

---

## 🛡️ **What This Does (Safe!)**

### **Before Migration:**
```json
{
  "basePrice": 200,
  "isActive": true,
  "estimatedDuration": 90
}
```

### **After Migration:**
```json
{
  "basePrice": 200,           // ✅ Preserved
  "isActive": true,           // ✅ Preserved
  "estimatedDuration": 90,    // ✅ Preserved
  "base_price": 200,          // ✅ Added
  "is_active": true,          // ✅ Added
  "estimated_duration": 90    // ✅ Added
}
```

---

## 🎯 **Expected Results**

| Test | Result |
|------|--------|
| **Services Load** | ✅ Perfect |
| **App Crashes** | ❌ None |
| **Field Reading** | ✅ Both formats work |
| **Data Loss** | ❌ None |
| **Compatibility** | ✅ 100% maintained |

---

## 🚨 **If You Need to Rollback**

If anything goes wrong (it won't!), you can remove the snake_case fields:

```javascript
// ROLLBACK SCRIPT - Only if needed
firebase.firestore().collection('services').get().then(snapshot => {
  snapshot.forEach(doc => {
    doc.ref.update({
      'base_price': firebase.firestore.FieldValue.delete(),
      'is_active': firebase.firestore.FieldValue.delete(),
      'estimated_duration': firebase.firestore.FieldValue.delete(),
      'image_url': firebase.firestore.FieldValue.delete(),
      'created_at': firebase.firestore.FieldValue.delete(),
      'updated_at': firebase.firestore.FieldValue.delete()
    });
  });
});
```

---

## ✅ **Ready to Prove Your App Keeps Working?**

**Run the migration script above to test live that field naming changes are completely safe!** 🎉

This will be the **definitive proof** that our migration strategy works perfectly in production. 