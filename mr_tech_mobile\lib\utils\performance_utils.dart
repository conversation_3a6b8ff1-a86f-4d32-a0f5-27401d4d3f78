import 'package:flutter/foundation.dart';
import 'dart:async';

/// Performance utilities for optimizing app startup and runtime performance
class PerformanceUtils {
  static const bool _enableDebugPrints = kDebugMode;
  static const Duration _debounceDelay = Duration(milliseconds: 300);
  static final Map<String, Timer> _debounceTimers = {};

  /// Optimized debug print that only prints in debug mode
  static void debugPrint(String message) {
    if (_enableDebugPrints) {
      print(message);
    }
  }

  /// Debounced function execution to prevent excessive calls
  static void debounce(String key, VoidCallback callback) {
    _debounceTimers[key]?.cancel();
    _debounceTimers[key] = Timer(_debounceDelay, callback);
  }

  /// Execute a function with error handling and performance logging
  static Future<T?> executeWithPerformanceLogging<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    final stopwatch = Stopwatch()..start();

    try {
      final result = await operation();
      stopwatch.stop();

      if (_enableDebugPrints) {
        print('$operationName completed in ${stopwatch.elapsedMilliseconds}ms');
      }

      return result;
    } catch (e) {
      stopwatch.stop();
      debugPrint(
        '$operationName failed after ${stopwatch.elapsedMilliseconds}ms: $e',
      );
      return null;
    }
  }

  /// Batch multiple async operations to reduce overhead
  static Future<List<T?>> batchOperations<T>(
    List<Future<T> Function()> operations,
  ) async {
    return await Future.wait(
      operations.map(
        (op) => op().catchError((e) {
          debugPrint('Batch operation failed: $e');
          return null;
        }),
      ),
    );
  }

  /// Throttle function execution to prevent excessive calls
  static Timer? _throttleTimer;
  static void throttle(Duration duration, VoidCallback callback) {
    if (_throttleTimer?.isActive ?? false) return;

    _throttleTimer = Timer(duration, callback);
  }

  /// Clean up performance utilities
  static void dispose() {
    for (final timer in _debounceTimers.values) {
      timer.cancel();
    }
    _debounceTimers.clear();
    _throttleTimer?.cancel();
  }

  /// Memory optimization - force garbage collection in debug mode
  static void optimizeMemory() {
    if (kDebugMode) {
      // Force garbage collection to free up memory
      // This is only useful in debug mode for testing
      Future.delayed(const Duration(milliseconds: 100), () {
        // Trigger GC by creating and discarding objects
        final temp = List.generate(1000, (i) => i);
        temp.clear();
      });
    }
  }

  /// Check if device has sufficient resources for heavy operations
  static bool shouldPerformHeavyOperation() {
    // In a real app, you might check available memory, battery level, etc.
    // For now, we'll just check if we're in debug mode
    return kDebugMode || DateTime.now().millisecondsSinceEpoch % 2 == 0;
  }

  /// Delay execution to let UI thread catch up
  static Future<void> yieldToUI() async {
    await Future.delayed(const Duration(microseconds: 1));
  }

  /// Execute operation with UI yield points for better responsiveness
  static Future<T?> executeWithUIYields<T>(
    Future<T> Function() operation,
  ) async {
    try {
      await yieldToUI();
      final result = await operation();
      await yieldToUI();
      return result;
    } catch (e) {
      debugPrint('Operation with UI yields failed: $e');
      return null;
    }
  }
}

/// Mixin for widgets that need performance optimization
mixin PerformanceOptimizedWidget {
  /// Debounced setState to prevent excessive rebuilds
  void debouncedSetState(VoidCallback fn, [String? key]) {
    PerformanceUtils.debounce(key ?? runtimeType.toString(), fn);
  }

  /// Throttled operation execution
  void throttledOperation(Duration duration, VoidCallback operation) {
    PerformanceUtils.throttle(duration, operation);
  }
}

/// Extension on Future for performance utilities
extension FuturePerformance<T> on Future<T> {
  /// Add timeout and error handling to futures
  Future<T?> withPerformanceHandling([Duration? timeout]) {
    return this.timeout(timeout ?? const Duration(seconds: 10)).catchError((e) {
      PerformanceUtils.debugPrint('Future operation failed: $e');
      return null;
    });
  }
}
