import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';

/// Comprehensive navigation manager for improved navigation and state management
/// Provides centralized navigation control, route management, and state persistence
class NavigationManager {
  // Private constructor to prevent instantiation
  NavigationManager._();

  static NavigationManager? _instance;
  static NavigationManager get instance => _instance ??= NavigationManager._();

  // Navigation state
  final GlobalKey<NavigatorState> _navigatorKey = GlobalKey<NavigatorState>();
  final List<RouteInfo> _routeHistory = [];
  final Map<String, dynamic> _routeArguments = {};
  final Map<String, GlobalKey<NavigatorState>> _nestedNavigators = {};
  
  // State management
  final Map<String, dynamic> _persistentState = {};
  final StreamController<NavigationEvent> _navigationEvents = StreamController.broadcast();
  
  // Route guards
  final List<RouteGuard> _routeGuards = [];
  
  // Back button handling
  bool _canPop = true;
  VoidCallback? _customBackHandler;

  /// Initialize navigation manager
  static void initialize() {
    instance._setupBackButtonHandler();
    debugPrint('NavigationManager initialized');
  }

  /// Get navigator key for MaterialApp
  GlobalKey<NavigatorState> get navigatorKey => _navigatorKey;

  /// Get current context
  BuildContext? get currentContext => _navigatorKey.currentContext;

  /// Get navigation events stream
  Stream<NavigationEvent> get navigationEvents => _navigationEvents.stream;

  /// Navigate to a route with enhanced features
  Future<T?> navigateTo<T extends Object?>(
    String routeName, {
    Object? arguments,
    bool replace = false,
    bool clearStack = false,
    Map<String, dynamic>? state,
    Duration? transition,
    RouteTransitionType transitionType = RouteTransitionType.platform,
  }) async {
    final context = currentContext;
    if (context == null) {
      debugPrint('NavigationManager: No context available for navigation');
      return null;
    }

    // Check route guards
    final canNavigate = await _checkRouteGuards(routeName, arguments);
    if (!canNavigate) {
      debugPrint('NavigationManager: Navigation blocked by route guard');
      return null;
    }

    // Store arguments and state
    if (arguments != null) {
      _routeArguments[routeName] = arguments;
    }
    if (state != null) {
      _persistentState[routeName] = state;
    }

    // Create route info
    final routeInfo = RouteInfo(
      name: routeName,
      arguments: arguments,
      timestamp: DateTime.now(),
    );

    try {
      T? result;

      if (clearStack) {
        // Clear entire navigation stack
        result = await Navigator.of(context).pushNamedAndRemoveUntil(
          routeName,
          (route) => false,
          arguments: arguments,
        );
        _routeHistory.clear();
      } else if (replace) {
        // Replace current route
        result = await Navigator.of(context).pushReplacementNamed(
          routeName,
          arguments: arguments,
        );
        if (_routeHistory.isNotEmpty) {
          _routeHistory.removeLast();
        }
      } else {
        // Push new route
        result = await Navigator.of(context).pushNamed(
          routeName,
          arguments: arguments,
        );
      }

      // Add to history
      _routeHistory.add(routeInfo);

      // Emit navigation event
      _navigationEvents.add(NavigationEvent(
        type: NavigationEventType.navigated,
        routeName: routeName,
        arguments: arguments,
      ));

      return result;
    } catch (e) {
      debugPrint('NavigationManager: Navigation error: $e');
      _navigationEvents.add(NavigationEvent(
        type: NavigationEventType.error,
        routeName: routeName,
        error: e.toString(),
      ));
      return null;
    }
  }

  /// Navigate back with enhanced features
  Future<bool> navigateBack<T extends Object?>([T? result]) async {
    final context = currentContext;
    if (context == null) return false;

    if (!_canPop) {
      _customBackHandler?.call();
      return true;
    }

    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop(result);
      
      // Remove from history
      if (_routeHistory.isNotEmpty) {
        final removedRoute = _routeHistory.removeLast();
        _navigationEvents.add(NavigationEvent(
          type: NavigationEventType.popped,
          routeName: removedRoute.name,
        ));
      }
      
      return true;
    }

    return false;
  }

  /// Navigate to route and clear stack
  Future<T?> navigateAndClearStack<T extends Object?>(
    String routeName, {
    Object? arguments,
  }) async {
    return navigateTo<T>(
      routeName,
      arguments: arguments,
      clearStack: true,
    );
  }

  /// Navigate with custom transition
  Future<T?> navigateWithTransition<T extends Object?>(
    Widget page, {
    RouteTransitionType transitionType = RouteTransitionType.slide,
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
  }) async {
    final context = currentContext;
    if (context == null) return null;

    final route = _createCustomRoute<T>(
      page,
      transitionType: transitionType,
      duration: duration,
      curve: curve,
    );

    return Navigator.of(context).push(route);
  }

  /// Add route guard
  void addRouteGuard(RouteGuard guard) {
    _routeGuards.add(guard);
  }

  /// Remove route guard
  void removeRouteGuard(RouteGuard guard) {
    _routeGuards.remove(guard);
  }

  /// Check if can navigate to route
  Future<bool> _checkRouteGuards(String routeName, Object? arguments) async {
    for (final guard in _routeGuards) {
      final canNavigate = await guard.canNavigate(routeName, arguments);
      if (!canNavigate) {
        return false;
      }
    }
    return true;
  }

  /// Set custom back button handler
  void setCustomBackHandler(VoidCallback? handler) {
    _customBackHandler = handler;
    _canPop = handler == null;
  }

  /// Setup system back button handler
  void _setupBackButtonHandler() {
    SystemChannels.platform.setMethodCallHandler((call) async {
      if (call.method == 'SystemNavigator.pop') {
        final handled = await navigateBack();
        if (!handled) {
          SystemNavigator.pop();
        }
      }
    });
  }

  /// Get route arguments
  T? getRouteArguments<T>(String routeName) {
    return _routeArguments[routeName] as T?;
  }

  /// Get persistent state
  T? getPersistentState<T>(String routeName) {
    return _persistentState[routeName] as T?;
  }

  /// Set persistent state
  void setPersistentState(String routeName, dynamic state) {
    _persistentState[routeName] = state;
  }

  /// Clear persistent state
  void clearPersistentState(String routeName) {
    _persistentState.remove(routeName);
  }

  /// Get navigation history
  List<RouteInfo> get navigationHistory => List.unmodifiable(_routeHistory);

  /// Clear navigation history
  void clearHistory() {
    _routeHistory.clear();
  }

  /// Register nested navigator
  void registerNestedNavigator(String key, GlobalKey<NavigatorState> navigatorKey) {
    _nestedNavigators[key] = navigatorKey;
  }

  /// Get nested navigator
  GlobalKey<NavigatorState>? getNestedNavigator(String key) {
    return _nestedNavigators[key];
  }

  /// Create custom route with transition
  PageRoute<T> _createCustomRoute<T>(
    Widget page, {
    required RouteTransitionType transitionType,
    required Duration duration,
    required Curve curve,
  }) {
    return PageRouteBuilder<T>(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        switch (transitionType) {
          case RouteTransitionType.slide:
            return SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(1.0, 0.0),
                end: Offset.zero,
              ).animate(CurvedAnimation(parent: animation, curve: curve)),
              child: child,
            );
          case RouteTransitionType.fade:
            return FadeTransition(
              opacity: CurvedAnimation(parent: animation, curve: curve),
              child: child,
            );
          case RouteTransitionType.scale:
            return ScaleTransition(
              scale: CurvedAnimation(parent: animation, curve: curve),
              child: child,
            );
          case RouteTransitionType.rotation:
            return RotationTransition(
              turns: CurvedAnimation(parent: animation, curve: curve),
              child: child,
            );
          case RouteTransitionType.platform:
          default:
            return child;
        }
      },
    );
  }

  /// Dispose resources
  void dispose() {
    _navigationEvents.close();
    _routeHistory.clear();
    _routeArguments.clear();
    _persistentState.clear();
    _nestedNavigators.clear();
    _routeGuards.clear();
  }
}

/// Route information for navigation history
class RouteInfo {
  final String name;
  final Object? arguments;
  final DateTime timestamp;

  const RouteInfo({
    required this.name,
    this.arguments,
    required this.timestamp,
  });

  @override
  String toString() {
    return 'RouteInfo(name: $name, arguments: $arguments, timestamp: $timestamp)';
  }
}

/// Navigation event for monitoring navigation changes
class NavigationEvent {
  final NavigationEventType type;
  final String routeName;
  final Object? arguments;
  final String? error;

  const NavigationEvent({
    required this.type,
    required this.routeName,
    this.arguments,
    this.error,
  });
}

/// Navigation event types
enum NavigationEventType {
  navigated,
  popped,
  error,
}

/// Route transition types
enum RouteTransitionType {
  platform,
  slide,
  fade,
  scale,
  rotation,
}

/// Route guard interface
abstract class RouteGuard {
  Future<bool> canNavigate(String routeName, Object? arguments);
}

/// Authentication route guard
class AuthRouteGuard implements RouteGuard {
  final bool Function() isAuthenticated;
  final List<String> protectedRoutes;
  final String loginRoute;

  const AuthRouteGuard({
    required this.isAuthenticated,
    required this.protectedRoutes,
    required this.loginRoute,
  });

  @override
  Future<bool> canNavigate(String routeName, Object? arguments) async {
    if (protectedRoutes.contains(routeName) && !isAuthenticated()) {
      // Redirect to login
      NavigationManager.instance.navigateTo(loginRoute);
      return false;
    }
    return true;
  }
}
