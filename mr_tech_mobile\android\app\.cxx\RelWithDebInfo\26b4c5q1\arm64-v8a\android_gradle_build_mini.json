{"buildFiles": ["D:\\Ibrahim\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Ibrahim\\mr.tech.v2\\mr_tech_mobile\\android\\app\\.cxx\\RelWithDebInfo\\26b4c5q1\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Ibrahim\\mr.tech.v2\\mr_tech_mobile\\android\\app\\.cxx\\RelWithDebInfo\\26b4c5q1\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}