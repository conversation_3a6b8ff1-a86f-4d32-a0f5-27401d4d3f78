/**
 * Base API Service Class
 * Provides standardized patterns for all API services with:
 * - Consistent error handling
 * - Retry logic
 * - Logging
 * - Type safety
 * - Response standardization
 */

import { logger } from '../utils/logger';
import type { 
  ApiResponse, 
  AppError, 
  RetryConfig,
  ServiceResponse 
} from '../types/common';
import { ErrorCode } from '../types/enums';
import { isNumber, isObject, isString } from '../utils/typeGuards';

export interface BaseApiServiceConfig {
  serviceName: string;
  enableLogging?: boolean;
  enableRetry?: boolean;
  retryConfig?: RetryConfig;
  timeout?: number;
}

export abstract class BaseApiService {
  protected config: Required<BaseApiServiceConfig>;

  constructor(config: BaseApiServiceConfig) {
    this.config = {
      enableLogging: true,
      enableRetry: true,
      retryConfig: {
        maxAttempts: 3,
        baseDelay: 1000,
        maxDelay: 5000,
        exponentialBackoff: true,
      },
      timeout: 30000,
      ...config,
    };
  }

  /**
   * Create a standardized error response
   */
  protected createError(
    code: ErrorCode,
    message: string,
    details?: Record<string, unknown>
  ): AppError {
    return {
      code,
      message,
      details,
      timestamp: new Date(),
      context: this.config.serviceName,
    };
  }

  /**
   * Create a standardized service response
   */
  protected createResponse<T>(
    success: boolean,
    data?: T,
    error?: AppError
  ): ServiceResponse<T> {
    return {
      success,
      data,
      error,
      metadata: {
        service: this.config.serviceName,
        timestamp: new Date().toISOString(),
      },
    };
  }

  /**
   * Log service operations
   */
  protected log(
    level: 'info' | 'warn' | 'error',
    message: string,
    data?: Record<string, unknown>
  ): void {
    if (this.config.enableLogging) {
      logger[level](`[${this.config.serviceName}] ${message}`, data);
    }
  }

  /**
   * Execute operation with retry logic
   */
  protected async executeWithRetry<T>(
    operation: () => Promise<T>,
    operationName: string,
    customRetryConfig?: Partial<RetryConfig>
  ): Promise<T> {
    if (!this.config.enableRetry) {
      return operation();
    }

    const retryConfig = { ...this.config.retryConfig, ...customRetryConfig };
    let lastError: Error;
    
    for (let attempt = 1; attempt <= retryConfig.maxAttempts; attempt++) {
      try {
        const result = await operation();
        if (attempt > 1) {
          this.log('info', `${operationName} succeeded on attempt ${attempt}`);
        }
        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        // Check if error is retryable
        if (!this.isRetryableError(lastError) || attempt === retryConfig.maxAttempts) {
          this.log('error', `${operationName} failed after ${attempt} attempts`, {
            error: lastError.message,
            isRetryable: this.isRetryableError(lastError),
          });
          break;
        }
        
        const delay = this.calculateDelay(attempt, retryConfig);
        this.log('warn', `${operationName} failed on attempt ${attempt}, retrying...`, {
          error: lastError.message,
          nextAttemptIn: delay,
          attemptsRemaining: retryConfig.maxAttempts - attempt,
        });
        
        await this.sleep(delay);
      }
    }
    
    throw lastError!;
  }

  /**
   * Determine if an error is retryable
   */
  protected isRetryableError(error: Error): boolean {
    const retryablePatterns = [
      /network/i,
      /timeout/i,
      /connection/i,
      /temporary/i,
      /rate.?limit/i,
      /server.?error/i,
      /503/,
      /502/,
      /500/,
    ];

    const nonRetryablePatterns = [
      /unauthorized/i,
      /forbidden/i,
      /not.?found/i,
      /bad.?request/i,
      /validation/i,
      /401/,
      /403/,
      /404/,
      /400/,
    ];

    const errorMessage = error.message.toLowerCase();

    // Check non-retryable patterns first
    if (nonRetryablePatterns.some(pattern => pattern.test(errorMessage))) {
      return false;
    }

    // Check retryable patterns
    return retryablePatterns.some(pattern => pattern.test(errorMessage));
  }

  /**
   * Calculate delay for retry attempts
   */
  protected calculateDelay(attempt: number, config: RetryConfig): number {
    if (!config.exponentialBackoff) {
      return config.baseDelay;
    }

    const exponentialDelay = config.baseDelay * Math.pow(2, attempt - 1);
    const jitter = Math.random() * 0.1 * exponentialDelay; // Add 10% jitter
    const delay = exponentialDelay + jitter;

    return Math.min(delay, config.maxDelay);
  }

  /**
   * Sleep utility for retry delays
   */
  protected sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Validate required parameters
   */
  protected validateRequired(
    params: Record<string, unknown>,
    requiredFields: string[]
  ): AppError | null {
    const missingFields: string[] = [];

    for (const field of requiredFields) {
      const value = params[field];
      if (value === undefined || value === null || value === '') {
        missingFields.push(field);
      }
    }

    if (missingFields.length > 0) {
      return this.createError(
        ErrorCode.VALIDATION_REQUIRED_FIELD,
        `Missing required fields: ${missingFields.join(', ')}`,
        { missingFields, providedFields: Object.keys(params) }
      );
    }

    return null;
  }

  /**
   * Validate parameter types
   */
  protected validateTypes(
    params: Record<string, unknown>,
    typeValidations: Record<string, (value: unknown) => boolean>
  ): AppError | null {
    const invalidFields: string[] = [];

    for (const [field, validator] of Object.entries(typeValidations)) {
      const value = params[field];
      if (value !== undefined && !validator(value)) {
        invalidFields.push(field);
      }
    }

    if (invalidFields.length > 0) {
      return this.createError(
        ErrorCode.VALIDATION_INVALID_FORMAT,
        `Invalid field types: ${invalidFields.join(', ')}`,
        { invalidFields, providedTypes: this.getFieldTypes(params) }
      );
    }

    return null;
  }

  /**
   * Get field types for debugging
   */
  private getFieldTypes(params: Record<string, unknown>): Record<string, string> {
    const types: Record<string, string> = {};
    for (const [key, value] of Object.entries(params)) {
      types[key] = typeof value;
    }
    return types;
  }

  /**
   * Sanitize input data
   */
  protected sanitizeInput<T extends Record<string, unknown>>(
    input: T,
    allowedFields: (keyof T)[]
  ): Partial<T> {
    const sanitized: Partial<T> = {};
    
    for (const field of allowedFields) {
      if (input[field] !== undefined) {
        sanitized[field] = input[field];
      }
    }
    
    return sanitized;
  }

  /**
   * Handle API response and convert to ServiceResponse
   */
  protected handleApiResponse<T>(
    response: ApiResponse<T>,
    operationName: string
  ): ServiceResponse<T> {
    if (response.success && response.data !== undefined) {
      this.log('info', `${operationName} completed successfully`);
      return this.createResponse(true, response.data);
    } else {
      const error = this.createError(
        ErrorCode.NETWORK_SERVER_ERROR,
        response.error || 'Unknown API error',
        { response }
      );
      this.log('error', `${operationName} failed`, { error });
      return this.createResponse(false, undefined, error);
    }
  }

  /**
   * Create timeout promise for operations
   */
  protected createTimeoutPromise<T>(timeoutMs?: number): Promise<T> {
    const timeout = timeoutMs || this.config.timeout;
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Operation timed out after ${timeout}ms`));
      }, timeout);
    });
  }

  /**
   * Execute operation with timeout
   */
  protected async executeWithTimeout<T>(
    operation: Promise<T>,
    timeoutMs?: number
  ): Promise<T> {
    return Promise.race([
      operation,
      this.createTimeoutPromise<T>(timeoutMs),
    ]);
  }
}
