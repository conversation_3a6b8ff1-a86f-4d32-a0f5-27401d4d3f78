import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

// Performance-optimized Vite configuration
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  build: {
    // Enable source maps for production debugging
    sourcemap: true,

    // Optimize chunk size
    chunkSizeWarningLimit: 1000,

    rollupOptions: {
      output: {
        // Manual chunk splitting for better caching
        manualChunks: {
          // Vendor chunks
          'react-vendor': ['react', 'react-dom', 'react-router-dom'],
          'firebase-vendor': [
            'firebase/app',
            'firebase/auth',
            'firebase/firestore',
            'firebase/storage',
            'firebase/functions',
          ],
          'ui-vendor': [
            '@radix-ui/react-dialog',
            '@radix-ui/react-dropdown-menu',
            '@radix-ui/react-select',
            '@radix-ui/react-tabs',
            '@radix-ui/react-toast',
            '@radix-ui/react-tooltip',
            'lucide-react',
          ],
          'chart-vendor': ['recharts', 'd3-scale', 'd3-array'],
          'form-vendor': ['react-hook-form', 'zod'],
          'utility-vendor': ['date-fns', 'lodash', 'dompurify'],

          // App chunks
          auth: [
            './src/services/authService',
            './src/contexts/AuthContext',
            './src/pages/LoginPage',
            './src/components/auth/LoginForm',
          ],
          chat: [
            './src/services/chatService',
            './src/pages/ChatPage',
            './src/components/chat/ChatWindow',
            './src/components/chat/ChatList',
            './src/components/chat/ChatMessage',
            './src/components/chat/ChatInput',
          ],
          dashboard: ['./src/pages/DashboardPage', './src/services/performanceService'],
          payments: ['./src/pages/PaymentsPage', './src/services/paymentService'],
          'user-management': [
            './src/pages/UserManagementPage',
            './src/pages/TechniciansPage',
            './src/pages/AdminManagementPage',
            './src/services/userService',
            './src/services/technicianService',
          ],
          requests: ['./src/pages/RequestsPage', './src/services/requestService'],
          services: ['./src/pages/ServicesPage', './src/services/serviceService'],
        },

        // Optimize chunk naming
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId
            ? chunkInfo.facadeModuleId.split('/').pop()?.replace('.tsx', '').replace('.ts', '')
            : 'chunk';
          return `js/${facadeModuleId}-[hash].js`;
        },

        // Optimize asset naming
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name?.split('.') || [];
          const ext = info[info.length - 1];

          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext || '')) {
            return 'img/[name]-[hash][extname]';
          }

          if (/css/i.test(ext || '')) {
            return 'css/[name]-[hash][extname]';
          }

          return 'assets/[name]-[hash][extname]';
        },
      },
    },

    // Enable minification
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.log in production
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug'],
      },
      mangle: {
        safari10: true,
      },
    },

    // Target modern browsers for better optimization
    target: 'es2020',
  },

  // Optimize dependencies
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'firebase/app',
      'firebase/auth',
      'firebase/firestore',
      'firebase/storage',
      'firebase/functions',
      'date-fns',
      'lucide-react',
      'recharts',
      'react-hook-form',
      'zod',
      'dompurify',
    ],
    exclude: [
      // Exclude large dependencies that should be loaded dynamically
    ],
  },

  // Development server optimizations
  server: {
    hmr: {
      overlay: false, // Disable error overlay for better performance
    },
  },

  // Preview server optimizations
  preview: {
    port: 4173,
    strictPort: true,
  },
});
