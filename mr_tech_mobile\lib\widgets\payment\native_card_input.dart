import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../text_styles.dart';

/// Native card input widget with proper formatting and validation
class NativeCardInput extends StatefulWidget {
  final Function(Map<String, String>) onCardDataChanged;
  final bool enabled;

  const NativeCardInput({
    super.key,
    required this.onCardDataChanged,
    this.enabled = true,
  });

  @override
  State<NativeCardInput> createState() => _NativeCardInputState();
}

class _NativeCardInputState extends State<NativeCardInput>
    with TickerProviderStateMixin {
  final _cardNumberController = TextEditingController();
  final _expiryController = TextEditingController();
  final _cvvController = TextEditingController();
  final _holderNameController = TextEditingController();

  final _cardNumberFocus = FocusNode();
  final _expiryFocus = FocusNode();
  final _cvvFocus = FocusNode();
  final _holderNameFocus = FocusNode();

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  String _cardType = '';
  bool _isCardValid = false;
  bool _isExpiryValid = false;
  bool _isCvvValid = false;
  bool _isHolderNameValid = false;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _cardNumberController.addListener(_onCardNumberChanged);
    _expiryController.addListener(_onExpiryChanged);
    _cvvController.addListener(_onCvvChanged);
    _holderNameController.addListener(_onHolderNameChanged);

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _cardNumberController.dispose();
    _expiryController.dispose();
    _cvvController.dispose();
    _holderNameController.dispose();
    _cardNumberFocus.dispose();
    _expiryFocus.dispose();
    _cvvFocus.dispose();
    _holderNameFocus.dispose();
    super.dispose();
  }

  void _onCardNumberChanged() {
    final cardNumber = _cardNumberController.text.replaceAll(' ', '');
    setState(() {
      _cardType = _getCardType(cardNumber);
      _isCardValid = _validateCardNumber(cardNumber);
    });
    _notifyCardDataChanged();
  }

  void _onExpiryChanged() {
    setState(() {
      _isExpiryValid = _validateExpiry(_expiryController.text);
    });
    _notifyCardDataChanged();
  }

  void _onCvvChanged() {
    setState(() {
      _isCvvValid = _validateCvv(_cvvController.text);
    });
    _notifyCardDataChanged();
  }

  void _onHolderNameChanged() {
    setState(() {
      _isHolderNameValid = _validateHolderName(_holderNameController.text);
    });
    _notifyCardDataChanged();
  }

  void _notifyCardDataChanged() {
    widget.onCardDataChanged({
      'cardNumber': _cardNumberController.text.replaceAll(' ', ''),
      'expiry': _expiryController.text,
      'cvv': _cvvController.text,
      'holderName': _holderNameController.text,
      'cardType': _cardType,
      'isValid': (_isCardValid && _isExpiryValid && _isCvvValid && _isHolderNameValid).toString(),
    });
  }

  String _getCardType(String cardNumber) {
    if (cardNumber.startsWith('4')) return 'visa';
    if (cardNumber.startsWith('5') || cardNumber.startsWith('2')) return 'mastercard';
    if (cardNumber.startsWith('3')) return 'amex';
    return '';
  }

  bool _validateCardNumber(String cardNumber) {
    if (cardNumber.length < 13 || cardNumber.length > 19) return false;
    
    // Luhn algorithm
    int sum = 0;
    bool alternate = false;
    
    for (int i = cardNumber.length - 1; i >= 0; i--) {
      int digit = int.parse(cardNumber[i]);
      
      if (alternate) {
        digit *= 2;
        if (digit > 9) digit -= 9;
      }
      
      sum += digit;
      alternate = !alternate;
    }
    
    return sum % 10 == 0;
  }

  bool _validateExpiry(String expiry) {
    if (expiry.length != 5) return false;
    
    final parts = expiry.split('/');
    if (parts.length != 2) return false;
    
    final month = int.tryParse(parts[0]);
    final year = int.tryParse('20${parts[1]}');
    
    if (month == null || year == null) return false;
    if (month < 1 || month > 12) return false;
    
    final now = DateTime.now();
    final expireDate = DateTime(year, month);
    
    return expireDate.isAfter(now);
  }

  bool _validateCvv(String cvv) {
    return cvv.length >= 3 && cvv.length <= 4;
  }

  bool _validateHolderName(String name) {
    return name.trim().length >= 2;
  }

  Widget _buildCardIcon(String cardType) {
    IconData icon;
    Color color;
    
    switch (cardType) {
      case 'visa':
        icon = Icons.credit_card;
        color = Colors.blue;
        break;
      case 'mastercard':
        icon = Icons.credit_card;
        color = Colors.red;
        break;
      case 'amex':
        icon = Icons.credit_card;
        color = Colors.green;
        break;
      default:
        icon = Icons.credit_card;
        color = Colors.grey;
    }
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      child: Icon(icon, color: color, size: 24),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Card(
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(color: Colors.grey.shade300),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: colorScheme.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.credit_card,
                        color: colorScheme.primary,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Card Information',
                      style: AppTextStyles.headingSmall(
                        context,
                        color: colorScheme.onSurface,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // Card Number
                TextFormField(
                  controller: _cardNumberController,
                  focusNode: _cardNumberFocus,
                  enabled: widget.enabled,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    _CardNumberInputFormatter(),
                  ],
                  decoration: InputDecoration(
                    labelText: 'Card Number',
                    hintText: '1234 5678 9012 3456',
                    prefixIcon: _buildCardIcon(_cardType),
                    suffixIcon: _isCardValid
                        ? Icon(Icons.check_circle, color: Colors.green.shade600, size: 20)
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: colorScheme.primary, width: 2),
                    ),
                  ),
                  onFieldSubmitted: (_) => _expiryFocus.requestFocus(),
                ),
                const SizedBox(height: 16),

                // Expiry and CVV Row
                Row(
                  children: [
                    // Expiry Date
                    Expanded(
                      child: TextFormField(
                        controller: _expiryController,
                        focusNode: _expiryFocus,
                        enabled: widget.enabled,
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          _ExpiryDateInputFormatter(),
                        ],
                        decoration: InputDecoration(
                          labelText: 'Expiry Date',
                          hintText: 'MM/YY',
                          prefixIcon: Icon(Icons.calendar_today, size: 20),
                          suffixIcon: _isExpiryValid
                              ? Icon(Icons.check_circle, color: Colors.green.shade600, size: 20)
                              : null,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: colorScheme.primary, width: 2),
                          ),
                        ),
                        onFieldSubmitted: (_) => _cvvFocus.requestFocus(),
                      ),
                    ),
                    const SizedBox(width: 16),

                    // CVV
                    Expanded(
                      child: TextFormField(
                        controller: _cvvController,
                        focusNode: _cvvFocus,
                        enabled: widget.enabled,
                        keyboardType: TextInputType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          LengthLimitingTextInputFormatter(4),
                        ],
                        obscureText: true,
                        decoration: InputDecoration(
                          labelText: 'CVV',
                          hintText: '123',
                          prefixIcon: Icon(Icons.security, size: 20),
                          suffixIcon: _isCvvValid
                              ? Icon(Icons.check_circle, color: Colors.green.shade600, size: 20)
                              : null,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: colorScheme.primary, width: 2),
                          ),
                        ),
                        onFieldSubmitted: (_) => _holderNameFocus.requestFocus(),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Cardholder Name
                TextFormField(
                  controller: _holderNameController,
                  focusNode: _holderNameFocus,
                  enabled: widget.enabled,
                  textCapitalization: TextCapitalization.words,
                  decoration: InputDecoration(
                    labelText: 'Cardholder Name',
                    hintText: 'John Doe',
                    prefixIcon: Icon(Icons.person, size: 20),
                    suffixIcon: _isHolderNameValid
                        ? Icon(Icons.check_circle, color: Colors.green.shade600, size: 20)
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: Colors.grey.shade300),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(color: colorScheme.primary, width: 2),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Input formatter for card number with automatic spacing
class _CardNumberInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text.replaceAll(' ', '');
    
    if (text.length > 16) {
      return oldValue;
    }
    
    final buffer = StringBuffer();
    for (int i = 0; i < text.length; i++) {
      buffer.write(text[i]);
      if ((i + 1) % 4 == 0 && i + 1 != text.length) {
        buffer.write(' ');
      }
    }
    
    final formatted = buffer.toString();
    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}

/// Input formatter for expiry date with MM/YY format
class _ExpiryDateInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text.replaceAll('/', '');
    
    if (text.length > 4) {
      return oldValue;
    }
    
    if (text.length >= 2) {
      final month = text.substring(0, 2);
      final year = text.length > 2 ? text.substring(2) : '';
      final formatted = '$month/$year';
      
      return TextEditingValue(
        text: formatted,
        selection: TextSelection.collapsed(offset: formatted.length),
      );
    }
    
    return TextEditingValue(
      text: text,
      selection: TextSelection.collapsed(offset: text.length),
    );
  }
}