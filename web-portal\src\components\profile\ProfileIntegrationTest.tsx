import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '../../components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../components/ui/card';
import { Alert, AlertDescription } from '../../components/ui/alert';
import { AlertCircle, CheckCircle, Loader2, RefreshCw } from 'lucide-react';
import { profileService } from '../../services/profileService';
import technicianService from '../../services/technicianService';

interface TestResult {
  step: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  timestamp?: Date;
}

interface ProfileIntegrationTestProps {
  userId: string;
  userRole: 'technician' | 'admin' | 'user';
}

const ProfileIntegrationTest: React.FC<ProfileIntegrationTestProps> = ({ userId, userRole }) => {
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);

  const addTestResult = (step: string, status: 'success' | 'error', message: string) => {
    setTestResults((prev) => [
      ...prev,
      {
        step,
        status,
        message,
        timestamp: new Date(),
      },
    ]);
  };

  const runIntegrationTest = async () => {
    setIsRunning(true);
    setTestResults([]);

    try {
      // Test 1: Verify user profile exists
      addTestResult('User Profile Check', 'pending', 'Checking user profile...');

      try {
        const userProfile = await profileService.getUserProfile(userId);
        addTestResult(
          'User Profile Check',
          'success',
          `User profile found: ${userProfile.name} (${userProfile.email})`,
        );
      } catch (error) {
        addTestResult('User Profile Check', 'error', `Failed to fetch user profile: ${error}`);
        return;
      }

      // Test 2: If technician, verify technician profile exists
      if (userRole === 'technician') {
        addTestResult('Technician Profile Check', 'pending', 'Checking technician profile...');

        try {
          const technicianProfile = await technicianService.getById(userId);
          if (technicianProfile) {
            addTestResult(
              'Technician Profile Check',
              'success',
              `Technician profile found: ${technicianProfile.name} with ${technicianProfile.specialties.length} specialties`,
            );
          } else {
            addTestResult('Technician Profile Check', 'error', 'Technician profile not found');
            return;
          }
        } catch (error) {
          addTestResult(
            'Technician Profile Check',
            'error',
            `Failed to fetch technician profile: ${error}`,
          );
          return;
        }
      }

      // Test 3: Test profile picture upload simulation
      addTestResult(
        'Profile Picture Integration',
        'pending',
        'Testing profile picture integration...',
      );

      try {
        // Simulate a profile picture URL update
        const testImageUrl = 'https://via.placeholder.com/150/0000FF/FFFFFF?text=Test';

        // Update user profile
        await profileService.updateUserProfile(userId, {
          photo_url: testImageUrl,
        });

        // If technician, also update technician profile
        if (userRole === 'technician') {
          await profileService.updateTechnicianProfile(userId, {
            photo_url: testImageUrl,
          });
        }

        addTestResult(
          'Profile Picture Integration',
          'success',
          'Profile picture URL updated in both collections successfully',
        );
      } catch (error) {
        addTestResult(
          'Profile Picture Integration',
          'error',
          `Failed to update profile picture: ${error}`,
        );
        return;
      }

      // Test 4: Verify data consistency
      addTestResult('Data Consistency Check', 'pending', 'Verifying data consistency...');

      try {
        const userProfile = await profileService.getUserProfile(userId);

        if (userRole === 'technician') {
          const technicianProfile = await technicianService.getById(userId);

          if (userProfile.photo_url === technicianProfile?.photo_url) {
            addTestResult(
              'Data Consistency Check',
              'success',
              'Profile picture URLs are consistent between users and technicians collections',
            );
          } else {
            addTestResult(
              'Data Consistency Check',
              'error',
              'Profile picture URLs are inconsistent between collections',
            );
          }
        } else {
          addTestResult(
            'Data Consistency Check',
            'success',
            'User profile data is consistent (non-technician user)',
          );
        }
      } catch (error) {
        addTestResult(
          'Data Consistency Check',
          'error',
          `Failed to verify data consistency: ${error}`,
        );
      }

      // Test 5: Mobile app compatibility check
      addTestResult('Mobile App Compatibility', 'pending', 'Checking mobile app compatibility...');

      try {
        if (userRole === 'technician') {
          const technicianProfile = await technicianService.getById(userId);

          // Check if all required fields for mobile app are present
          const requiredFields = ['name', 'photo_url', 'specialties', 'is_available', 'status'];
          const missingFields = requiredFields.filter(
            (field) => technicianProfile && !(field in technicianProfile),
          );

          if (missingFields.length === 0) {
            addTestResult(
              'Mobile App Compatibility',
              'success',
              'All required fields for mobile app integration are present',
            );
          } else {
            addTestResult(
              'Mobile App Compatibility',
              'error',
              `Missing required fields: ${missingFields.join(', ')}`,
            );
          }
        } else {
          addTestResult(
            'Mobile App Compatibility',
            'success',
            'Mobile app compatibility check not applicable for non-technician users',
          );
        }
      } catch (error) {
        addTestResult(
          'Mobile App Compatibility',
          'error',
          `Failed to check mobile app compatibility: ${error}`,
        );
      }
    } catch (error) {
      addTestResult(
        'Integration Test',
        'error',
        `Unexpected error during integration test: ${error}`,
      );
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className='h-4 w-4 text-green-600' />;
      case 'error':
        return <AlertCircle className='h-4 w-4 text-red-600' />;
      case 'pending':
        return <Loader2 className='h-4 w-4 text-blue-600 animate-spin' />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return 'text-green-800 bg-green-50 border-green-200';
      case 'error':
        return 'text-red-800 bg-red-50 border-red-200';
      case 'pending':
        return 'text-blue-800 bg-blue-50 border-blue-200';
    }
  };

  return (
    <Card className='w-full max-w-2xl'>
      <CardHeader>
        <CardTitle className='flex items-center gap-2'>
          <RefreshCw className='h-5 w-5' />
          Profile Integration Test
        </CardTitle>
        <CardDescription>
          Test the integration between web portal profile management and mobile app display
        </CardDescription>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div className='flex justify-between items-center'>
          <div>
            <p className='text-sm text-muted-foreground'>User ID: {userId}</p>
            <p className='text-sm text-muted-foreground'>Role: {userRole}</p>
          </div>
          <Button onClick={runIntegrationTest} disabled={isRunning} className='min-w-[120px]'>
            {isRunning ? (
              <>
                <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                Testing...
              </>
            ) : (
              <>
                <RefreshCw className='h-4 w-4 mr-2' />
                Run Test
              </>
            )}
          </Button>
        </div>

        {testResults.length > 0 && (
          <div className='space-y-2'>
            <h4 className='font-medium'>Test Results:</h4>
            {testResults.map((result, index) => (
              <Alert key={index} className={getStatusColor(result.status)}>
                <div className='flex items-start gap-2'>
                  {getStatusIcon(result.status)}
                  <div className='flex-1'>
                    <div className='font-medium'>{result.step}</div>
                    <AlertDescription className='mt-1'>{result.message}</AlertDescription>
                    {result.timestamp && (
                      <p className='text-xs opacity-70 mt-1'>
                        {result.timestamp.toLocaleTimeString()}
                      </p>
                    )}
                  </div>
                </div>
              </Alert>
            ))}
          </div>
        )}

        {testResults.length > 0 && !isRunning && (
          <div className='pt-4 border-t'>
            <div className='flex justify-between text-sm'>
              <span>Total Tests: {testResults.length}</span>
              <span className='text-green-600'>
                Passed: {testResults.filter((r) => r.status === 'success').length}
              </span>
              <span className='text-red-600'>
                Failed: {testResults.filter((r) => r.status === 'error').length}
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ProfileIntegrationTest;
