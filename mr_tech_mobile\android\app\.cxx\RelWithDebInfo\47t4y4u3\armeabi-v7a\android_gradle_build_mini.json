{"buildFiles": ["D:\\Ibrahim\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Ibrahim\\mr.tech.v2\\mr_tech_mobile\\android\\app\\.cxx\\RelWithDebInfo\\47t4y4u3\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Ibrahim\\mr.tech.v2\\mr_tech_mobile\\android\\app\\.cxx\\RelWithDebInfo\\47t4y4u3\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}