import 'package:flutter/material.dart';
import 'dart:ui' as ui;
import '../l10n/translations.dart';
import 'package:shared_preferences/shared_preferences.dart';

class TranslationService extends ChangeNotifier {
  // Singleton instance
  static final TranslationService _instance = TranslationService._internal();

  factory TranslationService() => _instance;

  TranslationService._internal() {
    _loadLanguagePreference();
  }

  // Current locale - will be set based on device language or saved preference
  Locale _currentLocale = const Locale('en'); // Temporary default

  // Get current locale
  Locale get currentLocale => _currentLocale;

  // Set current locale
  set currentLocale(Locale locale) {
    if (locale.languageCode != _currentLocale.languageCode) {
      _currentLocale = locale;
      _saveLanguagePreference(locale.languageCode);
      notifyListeners();
    }
  }

  // Get device's default language
  String _getDeviceLanguage() {
    try {
      // Get the device's locale
      final deviceLocale = ui.PlatformDispatcher.instance.locale;
      final deviceLanguageCode = deviceLocale.languageCode;

      debugPrint('Device language detected: $deviceLanguageCode');

      // Check if the device language is supported
      final supportedLanguageCodes =
          supportedLocales.map((l) => l.languageCode).toList();

      if (supportedLanguageCodes.contains(deviceLanguageCode)) {
        debugPrint('Device language $deviceLanguageCode is supported');
        return deviceLanguageCode;
      } else {
        debugPrint(
          'Device language $deviceLanguageCode not supported, falling back to English',
        );
        return 'en'; // Fallback to English if device language not supported
      }
    } catch (e) {
      debugPrint(
        'Error detecting device language: $e, falling back to English',
      );
      return 'en'; // Fallback to English on error
    }
  }

  // Load language preference from SharedPreferences
  Future<void> _loadLanguagePreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      String languageCode;

      // Check if user has previously saved a language preference
      if (prefs.containsKey('language_code')) {
        languageCode = prefs.getString('language_code')!;
        debugPrint('Using saved language preference: $languageCode');
      } else {
        // First time - use device language
        languageCode = _getDeviceLanguage();
        debugPrint('First time launch - using device language: $languageCode');
        // Save the detected language as the user's preference
        await prefs.setString('language_code', languageCode);
      }

      _currentLocale = Locale(languageCode);
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading language preference: $e');
      // Fallback to device language detection
      final deviceLanguage = _getDeviceLanguage();
      _currentLocale = Locale(deviceLanguage);
      notifyListeners();
    }
  }

  // Save language preference to SharedPreferences
  Future<void> _saveLanguagePreference(String languageCode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('language_code', languageCode);
      debugPrint('Saved language preference: $languageCode');
    } catch (e) {
      debugPrint('Error saving language preference: $e');
    }
  }

  // Check if current locale is RTL
  bool get isRtl => _currentLocale.languageCode == 'ar';

  // Check if the app is in RTL mode based on context
  bool isRtlContext(BuildContext context) {
    return _currentLocale.languageCode == 'ar';
  }

  // Get text direction based on current locale
  TextDirection get textDirection =>
      isRtl ? TextDirection.rtl : TextDirection.ltr;

  // Translation method
  String translate(String key) {
    const translations = Translations.translations;
    final languageCode = _currentLocale.languageCode;

    // If the language has translations for this key, return it
    if (translations.containsKey(languageCode) &&
        translations[languageCode]!.containsKey(key)) {
      return translations[languageCode]![key]!;
    }

    // Fallback to English if available
    if (languageCode != 'en' &&
        translations.containsKey('en') &&
        translations['en']!.containsKey(key)) {
      return translations['en']![key]!;
    }

    // If no translation found, return the key itself
    return key;
  }

  // Toggle between English and Arabic
  Future<void> toggleLanguage() async {
    _currentLocale =
        _currentLocale.languageCode == 'en'
            ? const Locale('ar')
            : const Locale('en');

    await _saveLanguagePreference(_currentLocale.languageCode);
    notifyListeners();
  }

  // Get supported locales
  List<Locale> get supportedLocales => [
    const Locale('ar', 'SA'), // Arabic
    const Locale('en', 'US'), // English
  ];
}
