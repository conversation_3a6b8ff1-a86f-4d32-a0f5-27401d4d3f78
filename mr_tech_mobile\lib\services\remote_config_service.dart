import 'dart:async';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';

/// Service for managing Firebase Remote Config
/// Handles secure storage of sensitive configuration values like API keys
class RemoteConfigService {
  static final RemoteConfigService _instance = RemoteConfigService._internal();
  factory RemoteConfigService() => _instance;
  RemoteConfigService._internal();

  FirebaseRemoteConfig? _remoteConfig;
  bool _isInitialized = false;
  final Completer<void> _initCompleter = Completer<void>();

  /// Default values for Remote Config parameters
  static const Map<String, dynamic> _defaults = {
    'paymob_secret_key': '',
    'paymob_public_key': '',
    'paymob_integration_id': '',
    'paymob_api_key': '',
    'payment_mode': 'paymob',
    'config_version': '1.0.0',
  };

  /// Initialize Remote Config with default values and fetch settings
  Future<void> initialize() async {
    if (_isInitialized) {
      return _initCompleter.future;
    }

    try {
      _remoteConfig = FirebaseRemoteConfig.instance;
      
      // Set configuration settings
      await _remoteConfig!.setConfigSettings(
        RemoteConfigSettings(
          fetchTimeout: const Duration(seconds: 10),
          minimumFetchInterval: kDebugMode 
            ? const Duration(seconds: 10)  // Quick refresh in debug
            : const Duration(hours: 1),    // Production refresh interval
        ),
      );

      // Set default values
      await _remoteConfig!.setDefaults(_defaults);

      // Initial fetch and activate
      await _fetchAndActivate();

      _isInitialized = true;
      _initCompleter.complete();

      debugPrint('✅ RemoteConfigService initialized successfully');
      if (kDebugMode) {
        await _debugPrintConfigStatus();
      }

    } catch (e) {
      debugPrint('❌ Error initializing RemoteConfigService: $e');
      _isInitialized = true; // Mark as initialized to prevent retry loops
      _initCompleter.complete();
    }
  }

  /// Ensure the service is initialized before use
  Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
    return _initCompleter.future;
  }

  /// Fetch and activate remote config values
  Future<bool> _fetchAndActivate() async {
    try {
      final fetchResult = await _remoteConfig!.fetchAndActivate();
      debugPrint('Remote Config fetch result: $fetchResult');
      return fetchResult;
    } catch (e) {
      debugPrint('Error fetching remote config: $e');
      return false;
    }
  }

  /// Get a string value from Remote Config
  Future<String> getString(String key) async {
    await _ensureInitialized();
    
    try {
      final value = _remoteConfig!.getString(key);
      if (kDebugMode && key.contains('paymob')) {
        debugPrint('Remote Config - $key: ${value.isNotEmpty ? "✓ Found" : "✗ Empty"}');
      }
      return value;
    } catch (e) {
      debugPrint('Error getting Remote Config value for $key: $e');
      return _defaults[key]?.toString() ?? '';
    }
  }

  /// Get an integer value from Remote Config
  Future<int> getInt(String key) async {
    await _ensureInitialized();
    
    try {
      return _remoteConfig!.getInt(key);
    } catch (e) {
      debugPrint('Error getting Remote Config int value for $key: $e');
      return _defaults[key] as int? ?? 0;
    }
  }

  /// Get a boolean value from Remote Config
  Future<bool> getBool(String key) async {
    await _ensureInitialized();
    
    try {
      return _remoteConfig!.getBool(key);
    } catch (e) {
      debugPrint('Error getting Remote Config bool value for $key: $e');
      return _defaults[key] as bool? ?? false;
    }
  }

  /// Force fetch new values from Remote Config
  Future<bool> forceFetch() async {
    await _ensureInitialized();
    
    try {
      debugPrint('🔄 Force fetching Remote Config values...');
      final result = await _fetchAndActivate();
      
      if (result && kDebugMode) {
        await _debugPrintConfigStatus();
      }
      
      return result;
    } catch (e) {
      debugPrint('Error force fetching remote config: $e');
      return false;
    }
  }

  /// Get Paymob configuration from Remote Config
  Future<Map<String, String>> getPaymobConfig() async {
    await _ensureInitialized();

    try {
      final config = {
        'secret_key': await getString('paymob_secret_key'),
        'public_key': await getString('paymob_public_key'),
        'integration_id': await getString('paymob_integration_id'),
        'api_key': await getString('paymob_api_key'),
        'payment_mode': await getString('payment_mode'),
      };

      // Remove empty values
      config.removeWhere((key, value) => value.isEmpty);

      debugPrint('📡 Remote Config Paymob keys loaded: ${config.keys.length}/5');
      
      return config;
    } catch (e) {
      debugPrint('Error getting Paymob config from Remote Config: $e');
      return {};
    }
  }

  /// Check if Remote Config has valid Paymob configuration
  Future<bool> hasValidPaymobConfig() async {
    final config = await getPaymobConfig();
    
    final hasSecretKey = config['secret_key']?.isNotEmpty == true;
    final hasPublicKey = config['public_key']?.isNotEmpty == true;
    final hasIntegrationId = config['integration_id']?.isNotEmpty == true;
    
    return hasSecretKey && hasPublicKey && hasIntegrationId;
  }

  /// Debug print configuration status
  Future<void> _debugPrintConfigStatus() async {
    if (!kDebugMode) return;

    debugPrint('\n======= REMOTE CONFIG STATUS =======');
    debugPrint('Remote Config initialized: $_isInitialized');
    debugPrint('Last fetch time: ${_remoteConfig!.lastFetchTime}');
    debugPrint('Last fetch status: ${_remoteConfig!.lastFetchStatus}');
    
    // Check Paymob configuration without exposing values
    final paymobConfig = await getPaymobConfig();
    debugPrint('Paymob Remote Config keys available: ${paymobConfig.keys.length}');
    
    for (final key in paymobConfig.keys) {
      final value = paymobConfig[key]!;
      debugPrint('  $key: ${value.isNotEmpty ? "✓ (${value.length} chars)" : "✗ Empty"}');
    }
    
    debugPrint('===================================\n');
  }

  /// Get configuration info for debugging
  Future<Map<String, dynamic>> getConfigInfo() async {
    await _ensureInitialized();
    
    return {
      'initialized': _isInitialized,
      'last_fetch_time': _remoteConfig!.lastFetchTime.toIso8601String(),
      'last_fetch_status': _remoteConfig!.lastFetchStatus.toString(),
      'has_valid_paymob_config': await hasValidPaymobConfig(),
    };
  }
}