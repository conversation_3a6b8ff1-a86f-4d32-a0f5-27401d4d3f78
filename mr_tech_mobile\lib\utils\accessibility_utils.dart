import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/semantics.dart';

/// Accessibility utilities for improved app accessibility
/// Provides comprehensive accessibility features and WCAG compliance tools
class AccessibilityUtils {
  // Private constructor to prevent instantiation
  AccessibilityUtils._();

  static bool _isInitialized = false;
  static bool _screenReaderEnabled = false;
  static bool _highContrastEnabled = false;
  static double _textScaleFactor = 1.0;
  static bool _reduceAnimations = false;

  /// Initialize accessibility utilities
  static void initialize() {
    if (_isInitialized) return;

    _detectAccessibilitySettings();
    _setupAccessibilityListeners();

    _isInitialized = true;
    debugPrint('AccessibilityUtils initialized');
  }

  /// Detect current accessibility settings
  static void _detectAccessibilitySettings() {
    try {
      // Check if screen reader is enabled
      _screenReaderEnabled = SemanticsBinding.instance.accessibilityFeatures.accessibleNavigation;
      
      // Check if high contrast is enabled
      _highContrastEnabled = SemanticsBinding.instance.accessibilityFeatures.highContrast;
      
      // Check if animations should be reduced
      _reduceAnimations = SemanticsBinding.instance.accessibilityFeatures.disableAnimations;
      
      debugPrint('Accessibility settings detected:');
      debugPrint('  Screen reader: $_screenReaderEnabled');
      debugPrint('  High contrast: $_highContrastEnabled');
      debugPrint('  Reduce animations: $_reduceAnimations');
      
    } catch (e) {
      debugPrint('Error detecting accessibility settings: $e');
    }
  }

  /// Setup accessibility listeners
  static void _setupAccessibilityListeners() {
    // Listen for accessibility changes
    SemanticsBinding.instance.addObserver(_AccessibilityObserver());
  }

  /// Create an accessible widget with proper semantics
  static Widget makeAccessible({
    required Widget child,
    required String label,
    String? hint,
    String? value,
    bool isButton = false,
    bool isHeader = false,
    bool isLink = false,
    bool isImage = false,
    bool excludeSemantics = false,
    VoidCallback? onTap,
    bool increasedTouchTarget = false,
  }) {
    if (excludeSemantics) {
      return ExcludeSemantics(child: child);
    }

    Widget accessibleChild = child;

    // Increase touch target for better accessibility
    if (increasedTouchTarget) {
      accessibleChild = _increaseTouchTarget(accessibleChild);
    }

    // Add tap functionality if provided
    if (onTap != null) {
      accessibleChild = GestureDetector(
        onTap: onTap,
        child: accessibleChild,
      );
    }

    // Wrap with semantics
    return Semantics(
      label: label,
      hint: hint,
      value: value,
      button: isButton,
      header: isHeader,
      link: isLink,
      image: isImage,
      child: accessibleChild,
    );
  }

  /// Increase touch target size for better accessibility
  static Widget _increaseTouchTarget(Widget child) {
    return Container(
      constraints: const BoxConstraints(
        minWidth: 48.0,
        minHeight: 48.0,
      ),
      child: child,
    );
  }

  /// Create an accessible button with proper semantics
  static Widget accessibleButton({
    required Widget child,
    required String label,
    required VoidCallback onPressed,
    String? hint,
    bool enabled = true,
    ButtonStyle? style,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      button: true,
      enabled: enabled,
      child: ElevatedButton(
        onPressed: enabled ? onPressed : null,
        style: style,
        child: child,
      ),
    );
  }

  /// Create an accessible text field with proper semantics
  static Widget accessibleTextField({
    required TextEditingController controller,
    required String label,
    String? hint,
    String? errorText,
    bool obscureText = false,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    void Function(String)? onChanged,
    Widget? suffixIcon,
    Widget? prefixIcon,
    int? maxLines = 1,
    bool enabled = true,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      textField: true,
      child: TextFormField(
        controller: controller,
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          errorText: errorText,
          suffixIcon: suffixIcon,
          prefixIcon: prefixIcon,
          border: const OutlineInputBorder(),
        ),
        obscureText: obscureText,
        keyboardType: keyboardType,
        validator: validator,
        onChanged: onChanged,
        maxLines: maxLines,
        enabled: enabled,
      ),
    );
  }

  /// Create an accessible image with proper semantics
  static Widget accessibleImage({
    required ImageProvider image,
    required String semanticLabel,
    String? tooltip,
    double? width,
    double? height,
    BoxFit? fit,
  }) {
    Widget imageWidget = Image(
      image: image,
      width: width,
      height: height,
      fit: fit,
      semanticLabel: semanticLabel,
    );

    if (tooltip != null) {
      imageWidget = Tooltip(
        message: tooltip,
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  /// Create an accessible list with proper semantics
  static Widget accessibleList({
    required List<Widget> children,
    required String label,
    String? hint,
    ScrollController? controller,
    bool shrinkWrap = false,
    ScrollPhysics? physics,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      child: ListView(
        controller: controller,
        shrinkWrap: shrinkWrap,
        physics: physics,
        children: children,
      ),
    );
  }

  /// Announce text to screen readers
  static void announceToScreenReader(String message) {
    if (_screenReaderEnabled) {
      SemanticsService.announce(message, TextDirection.ltr);
    }
  }

  /// Check if high contrast mode is enabled
  static bool get isHighContrastEnabled => _highContrastEnabled;

  /// Check if screen reader is enabled
  static bool get isScreenReaderEnabled => _screenReaderEnabled;

  /// Check if animations should be reduced
  static bool get shouldReduceAnimations => _reduceAnimations;

  /// Get current text scale factor
  static double get textScaleFactor => _textScaleFactor;

  /// Get accessible color with proper contrast
  static Color getAccessibleColor({
    required Color foreground,
    required Color background,
    double minContrastRatio = 4.5,
  }) {
    final contrastRatio = _calculateContrastRatio(foreground, background);
    
    if (contrastRatio >= minContrastRatio) {
      return foreground;
    }

    // Adjust color to meet contrast requirements
    return _adjustColorForContrast(
      foreground,
      background,
      minContrastRatio,
    );
  }

  /// Calculate contrast ratio between two colors
  static double _calculateContrastRatio(Color color1, Color color2) {
    final luminance1 = _calculateLuminance(color1);
    final luminance2 = _calculateLuminance(color2);
    
    final lighter = luminance1 > luminance2 ? luminance1 : luminance2;
    final darker = luminance1 > luminance2 ? luminance2 : luminance1;
    
    return (lighter + 0.05) / (darker + 0.05);
  }

  /// Calculate relative luminance of a color
  static double _calculateLuminance(Color color) {
    final r = _linearizeColorComponent(color.red / 255.0);
    final g = _linearizeColorComponent(color.green / 255.0);
    final b = _linearizeColorComponent(color.blue / 255.0);
    
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  /// Linearize color component for luminance calculation
  static double _linearizeColorComponent(double component) {
    if (component <= 0.03928) {
      return component / 12.92;
    } else {
      return ((component + 0.055) / 1.055).pow(2.4);
    }
  }

  /// Adjust color to meet contrast requirements
  static Color _adjustColorForContrast(
    Color foreground,
    Color background,
    double minContrastRatio,
  ) {
    // Simple implementation: darken or lighten the foreground color
    final backgroundLuminance = _calculateLuminance(background);
    
    if (backgroundLuminance > 0.5) {
      // Light background, darken foreground
      return _darkenColor(foreground, minContrastRatio);
    } else {
      // Dark background, lighten foreground
      return _lightenColor(foreground, minContrastRatio);
    }
  }

  /// Darken a color
  static Color _darkenColor(Color color, double factor) {
    final hsl = HSLColor.fromColor(color);
    final darkened = hsl.withLightness((hsl.lightness * (1 - factor * 0.1)).clamp(0.0, 1.0));
    return darkened.toColor();
  }

  /// Lighten a color
  static Color _lightenColor(Color color, double factor) {
    final hsl = HSLColor.fromColor(color);
    final lightened = hsl.withLightness((hsl.lightness + factor * 0.1).clamp(0.0, 1.0));
    return lightened.toColor();
  }

  /// Create focus-aware widget
  static Widget focusAware({
    required Widget child,
    required FocusNode focusNode,
    String? semanticLabel,
    VoidCallback? onFocusChange,
  }) {
    return Focus(
      focusNode: focusNode,
      onFocusChange: onFocusChange,
      child: Builder(
        builder: (context) {
          final isFocused = focusNode.hasFocus;
          
          return Container(
            decoration: isFocused
                ? BoxDecoration(
                    border: Border.all(
                      color: Theme.of(context).colorScheme.primary,
                      width: 2.0,
                    ),
                    borderRadius: BorderRadius.circular(4.0),
                  )
                : null,
            child: Semantics(
              label: semanticLabel,
              focused: isFocused,
              child: child,
            ),
          );
        },
      ),
    );
  }

  /// Get accessibility metrics
  static Map<String, dynamic> getAccessibilityMetrics() {
    return {
      'isInitialized': _isInitialized,
      'screenReaderEnabled': _screenReaderEnabled,
      'highContrastEnabled': _highContrastEnabled,
      'textScaleFactor': _textScaleFactor,
      'reduceAnimations': _reduceAnimations,
    };
  }
}

/// Accessibility observer for monitoring changes
class _AccessibilityObserver extends SemanticsObserver {
  @override
  void didChangeAccessibilityFeatures() {
    AccessibilityUtils._detectAccessibilitySettings();
  }
}

/// Extension for double to support power operation
extension DoubleExtension on double {
  double pow(double exponent) {
    return dart.math.pow(this, exponent).toDouble();
  }
}

// Import dart:math for pow function
import 'dart:math' as dart.math;

/// Keyboard navigation utilities for improved accessibility
class KeyboardNavigationUtils {
  // Private constructor to prevent instantiation
  KeyboardNavigationUtils._();

  /// Create a keyboard navigable widget
  static Widget keyboardNavigable({
    required Widget child,
    required FocusNode focusNode,
    VoidCallback? onActivate,
    String? semanticLabel,
    bool autofocus = false,
  }) {
    return Focus(
      focusNode: focusNode,
      autofocus: autofocus,
      onKeyEvent: (node, event) {
        if (event is KeyDownEvent) {
          // Handle Enter and Space key activation
          if (event.logicalKey == LogicalKeyboardKey.enter ||
              event.logicalKey == LogicalKeyboardKey.space) {
            onActivate?.call();
            return KeyEventResult.handled;
          }
        }
        return KeyEventResult.ignored;
      },
      child: Builder(
        builder: (context) {
          final isFocused = focusNode.hasFocus;

          return Container(
            decoration: isFocused
                ? BoxDecoration(
                    border: Border.all(
                      color: Theme.of(context).colorScheme.primary,
                      width: 2.0,
                    ),
                    borderRadius: BorderRadius.circular(4.0),
                  )
                : null,
            child: Semantics(
              label: semanticLabel,
              focused: isFocused,
              child: child,
            ),
          );
        },
      ),
    );
  }

  /// Create a focus traversal group for managing focus order
  static Widget focusTraversalGroup({
    required Widget child,
    required List<FocusNode> focusNodes,
    TraversalPolicy? policy,
  }) {
    return FocusTraversalGroup(
      policy: policy ?? OrderedTraversalPolicy(),
      child: child,
    );
  }

  /// Create a skip link for keyboard navigation
  static Widget skipLink({
    required String label,
    required FocusNode targetFocus,
    required BuildContext context,
  }) {
    return Positioned(
      top: -100, // Hidden by default
      left: 0,
      child: Focus(
        onFocusChange: (hasFocus) {
          // Show skip link when focused
        },
        child: ElevatedButton(
          onPressed: () {
            targetFocus.requestFocus();
          },
          child: Text(label),
        ),
      ),
    );
  }
}
