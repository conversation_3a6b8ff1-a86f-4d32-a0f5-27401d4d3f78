import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/font_manager.dart';

class AppTheme {
  // Private constructor to prevent instantiation
  AppTheme._();
  
  // Color palette - Updated with new brand colors
  static const Color _primaryColor = Color(0xFF4335A7);  // Deep purple
  static const Color _secondaryColor = Color(0xFF80C4E9); // Light blue
  static const Color _tertiaryColor = Color(0xFFFF7F3E);  // Orange
  static const Color _accentColor = Color(0xFFFF7F3E);   // Orange (same as tertiary)
  
  static const Color _successColor = Color(0xFF2E7D32);  // Emerald green
  static const Color _warningColor = Color(0xFFE65100);  // Deep amber
  static const Color _errorColor = Color(0xFFC62828);    // Ruby red
  static const Color _infoColor = Color(0xFF0277BD);     // Deep blue
  
  // Neutrals - Updated with warm undertones to match new palette
  static const Color _surfaceColor = Color(0xFFFFFFFF);  // White (was warm cream)
  static const Color _backgroundLightColor = Color(0xFFFFFFFF); // White (was warm cream)
  static const Color _backgroundDarkColor = Color(0xFF121212); // Rich black
  
  static const Color _textPrimaryLightColor = Color(0xFF1A1A1A); // Near black
  static const Color _textSecondaryLightColor = Color(0xFF4D4D4D); // Dark gray
  static const Color _textTertiaryLightColor = Color(0xFF666666); // Medium gray
  
  static const Color _textPrimaryDarkColor = Color(0xFFF5F5F5); // Warm white
  static const Color _textSecondaryDarkColor = Color(0xFFCCCCCC); // Light gray
  static const Color _textTertiaryDarkColor = Color(0xFF999999); // Medium gray
  
  // Gradients for premium feel - Updated with new colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [_primaryColor, Color(0xFF352A85)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient accentGradient = LinearGradient(
    colors: [_accentColor, Color(0xFFE86A2D)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient successGradient = LinearGradient(
    colors: [_successColor, Color(0xFF1B5E20)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Shadows for depth - Enhanced for more dramatic effect
  static List<BoxShadow> get subtleShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(0.05),
      blurRadius: 12,
      offset: const Offset(0, 2),
      spreadRadius: 0,
    ),
  ];
  
  static List<BoxShadow> get mediumShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(0.08),
      blurRadius: 20,
      offset: const Offset(0, 5),
      spreadRadius: -2,
    ),
    BoxShadow(
      color: Colors.black.withOpacity(0.03),
      blurRadius: 4,
      offset: const Offset(0, 1),
      spreadRadius: 0,
    ),
  ];
  
  static List<BoxShadow> get strongShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(0.15),
      blurRadius: 30,
      offset: const Offset(0, 10),
      spreadRadius: -5,
    ),
    BoxShadow(
      color: Colors.black.withOpacity(0.07),
      blurRadius: 8,
      offset: const Offset(0, 3),
      spreadRadius: -1,
    ),
  ];
  
  // Rounded corners - Refined for elegance
  static const BorderRadius smallRadius = BorderRadius.all(Radius.circular(6));
  static const BorderRadius mediumRadius = BorderRadius.all(Radius.circular(12));
  static const BorderRadius largeRadius = BorderRadius.all(Radius.circular(18));
  static const BorderRadius extraLargeRadius = BorderRadius.all(Radius.circular(24));
  static const BorderRadius circularRadius = BorderRadius.all(Radius.circular(100));
  
  // Animation durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 350);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // Spacing scale - 8px grid system for consistent spacing
  static const double spaceXS = 4.0;   // 0.5 * 8px - For very tight spacing
  static const double spaceS = 8.0;    // 1 * 8px - Base unit
  static const double spaceM = 16.0;   // 2 * 8px - Standard spacing
  static const double spaceL = 24.0;   // 3 * 8px - Large spacing
  static const double spaceXL = 32.0;  // 4 * 8px - Extra large spacing
  static const double space2XL = 48.0; // 6 * 8px - Section spacing
  static const double space3XL = 64.0; // 8 * 8px - Page spacing
  static const double space4XL = 80.0; // 10 * 8px - Large section spacing
  static const double space5XL = 96.0; // 12 * 8px - Maximum spacing

  // Component-specific spacing constants
  static const double cardPadding = spaceM;           // 16px
  static const double cardMargin = spaceS;            // 8px
  static const double sectionSpacing = spaceL;       // 24px
  static const double pageHorizontalPadding = spaceM; // 16px
  static const double pageVerticalPadding = spaceL;   // 24px
  static const double buttonPadding = spaceM;         // 16px
  static const double inputPadding = spaceM;          // 16px
  static const double listItemSpacing = spaceS;      // 8px
  static const double iconSpacing = spaceS;          // 8px
  static const double chipSpacing = spaceXS;         // 4px
  
  // Generate ThemeData for light theme
  static ThemeData lightTheme(BuildContext context) {
    // Create the color scheme for light theme
    final colorScheme = ColorScheme.light(
      primary: _primaryColor,
      secondary: _secondaryColor,
      tertiary: _tertiaryColor,
      surface: _surfaceColor,
      error: _errorColor,
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onTertiary: Colors.white,
      onSurface: _textPrimaryLightColor,
      onError: Colors.white,
    );
    
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      primaryColor: _primaryColor,
      scaffoldBackgroundColor: _surfaceColor,
      canvasColor: _backgroundLightColor,
      fontFamily: FontManager.getSecondaryFontFamily(context),
      colorScheme: colorScheme,
      
      // Text theme with sophisticated typography
      textTheme: FontManager.createTextTheme(context, colorScheme),
      
      // Card theme - Enhanced with subtle border and refined styling
      cardTheme: CardThemeData(
        elevation: 0,
        color: _backgroundLightColor,
        shape: RoundedRectangleBorder(
          borderRadius: mediumRadius,
          side: BorderSide(
            color: _textTertiaryLightColor.withOpacity(0.1),
            width: 1,
          ),
        ),
        clipBehavior: Clip.antiAlias,
        margin: EdgeInsets.all(cardMargin),
        shadowColor: Colors.black.withOpacity(0.06),
      ),
      
      // App bar theme - Modernized with more elegant typography
      appBarTheme: AppBarTheme(
        elevation: 0,
        backgroundColor: Colors.transparent,
        centerTitle: false,
        titleTextStyle: FontManager.appBarTitleStyle(context, _textPrimaryLightColor),
        iconTheme: IconThemeData(color: _textPrimaryLightColor),
        toolbarHeight: 64,
      ),
      
      // Button themes - Refined with more sophisticated shapes and shadows
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 0,
          backgroundColor: _primaryColor,
          foregroundColor: Colors.white,
          textStyle: FontManager.buttonTextStyle(context, Colors.white),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          shape: RoundedRectangleBorder(borderRadius: mediumRadius),
          minimumSize: const Size(0, 52),
          shadowColor: _primaryColor.withOpacity(0.3),
        ),
      ),
      
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: _primaryColor,
          textStyle: FontManager.buttonTextStyle(context, _primaryColor),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          shape: RoundedRectangleBorder(borderRadius: mediumRadius),
          side: BorderSide(color: _primaryColor.withOpacity(0.6), width: 1.5),
          minimumSize: const Size(0, 52),
        ),
      ),
      
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: _primaryColor,
          textStyle: FontManager.buttonTextStyle(context, _primaryColor),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          shape: RoundedRectangleBorder(borderRadius: mediumRadius),
          minimumSize: const Size(0, 40),
        ),
      ),
      
      // Input decoration theme - Enhanced with more sophisticated styling
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: _backgroundLightColor,
        contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
        border: OutlineInputBorder(
          borderRadius: mediumRadius,
          borderSide: BorderSide(color: _textTertiaryLightColor.withOpacity(0.3), width: 1),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: mediumRadius,
          borderSide: BorderSide(color: _textTertiaryLightColor.withOpacity(0.3), width: 1),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: mediumRadius,
          borderSide: BorderSide(color: _primaryColor, width: 1.5),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: mediumRadius,
          borderSide: BorderSide(color: _errorColor, width: 1),
        ),
        labelStyle: FontManager.inputTextStyle(context, _textSecondaryLightColor),
        hintStyle: FontManager.inputTextStyle(context, _textTertiaryLightColor),
        floatingLabelStyle: TextStyle(
          fontFamily: FontManager.getSecondaryFontFamily(context),
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: _primaryColor,
        ),
      ),
      
      // Bottom navigation bar theme - Enhanced for more polished appearance
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: _backgroundLightColor,
        selectedItemColor: _primaryColor,
        unselectedItemColor: _textTertiaryLightColor,
        selectedLabelStyle: FontManager.navigationTextStyle(context, _primaryColor, selected: true),
        unselectedLabelStyle: FontManager.navigationTextStyle(context, _textTertiaryLightColor),
        elevation: 8,
        type: BottomNavigationBarType.fixed,
      ),
      
      // Dialog theme
      dialogTheme: DialogThemeData(
        backgroundColor: _backgroundLightColor,
        shape: RoundedRectangleBorder(borderRadius: largeRadius),
        elevation: 16,
        titleTextStyle: TextStyle(
          fontFamily: FontManager.getPrimaryFontFamily(context),
          fontSize: 22,
          fontWeight: FontWeight.w600,
          color: _textPrimaryLightColor,
          letterSpacing: -0.25,
        ),
        contentTextStyle: TextStyle(
          fontFamily: FontManager.getSecondaryFontFamily(context),
          fontSize: 16,
          fontWeight: FontWeight.w400,
          color: _textPrimaryLightColor,
          letterSpacing: 0.15,
          height: 1.5,
        ),
      ),
    );
  }
  
  // Generate ThemeData for dark theme
  static ThemeData darkTheme(BuildContext context) {
    // Enhanced dark color scheme with better contrast and accessibility
    final colorScheme = ColorScheme.dark(
      primary: _primaryColor,
      secondary: _secondaryColor,
      tertiary: _tertiaryColor,
      surface: const Color(0xFF1E293B), // Slate-800
      surfaceContainerHighest: const Color(0xFF334155), // Slate-700
      surfaceContainer: const Color(0xFF475569), // Slate-600
      surfaceContainerHigh: const Color(0xFF64748B), // Slate-500
      surfaceContainerLow: const Color(0xFF0F172A), // Slate-900
      background: _backgroundDarkColor,
      error: _errorColor,
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onTertiary: Colors.white,
      onSurface: _textPrimaryDarkColor,
      onSurfaceVariant: _textSecondaryDarkColor,
      onBackground: _textPrimaryDarkColor,
      onError: Colors.white,
      outline: _textTertiaryDarkColor,
      outlineVariant: _textTertiaryDarkColor.withOpacity(0.3),
      shadow: Colors.black.withOpacity(0.8),
      scrim: Colors.black.withOpacity(0.9),
    );

    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primaryColor: _primaryColor,
      scaffoldBackgroundColor: _backgroundDarkColor,
      canvasColor: const Color(0xFF1E293B),
      fontFamily: FontManager.getSecondaryFontFamily(context),
      colorScheme: colorScheme,

      // Text theme with sophisticated typography for dark mode
      textTheme: FontManager.createTextTheme(context, colorScheme),

      // Card theme - Enhanced for dark mode with proper contrast
      cardTheme: CardThemeData(
        elevation: 0,
        color: const Color(0xFF1E293B),
        shape: RoundedRectangleBorder(
          borderRadius: mediumRadius,
          side: BorderSide(
            color: _textTertiaryDarkColor.withOpacity(0.2),
            width: 1,
          ),
        ),
        clipBehavior: Clip.antiAlias,
        margin: EdgeInsets.all(cardMargin),
        shadowColor: Colors.black.withOpacity(0.3),
      ),

      // App bar theme - Enhanced for dark mode
      appBarTheme: AppBarTheme(
        elevation: 0,
        backgroundColor: Colors.transparent,
        centerTitle: false,
        titleTextStyle: FontManager.appBarTitleStyle(context, _textPrimaryDarkColor),
        iconTheme: IconThemeData(color: _textPrimaryDarkColor),
        toolbarHeight: 64,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.light,
          statusBarBrightness: Brightness.dark,
        ),
      ),

      // Button themes - Enhanced for dark mode with proper contrast
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: 0,
          backgroundColor: _primaryColor,
          foregroundColor: Colors.white,
          textStyle: FontManager.buttonTextStyle(context, Colors.white),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          shape: RoundedRectangleBorder(borderRadius: mediumRadius),
          minimumSize: const Size(0, 52),
          shadowColor: _primaryColor.withOpacity(0.4),
        ),
      ),

      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: _primaryColor,
          textStyle: FontManager.buttonTextStyle(context, _primaryColor),
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
          shape: RoundedRectangleBorder(borderRadius: mediumRadius),
          side: BorderSide(color: _primaryColor.withOpacity(0.8), width: 1.5),
          minimumSize: const Size(0, 52),
        ),
      ),

      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: _primaryColor,
          textStyle: FontManager.buttonTextStyle(context, _primaryColor),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          shape: RoundedRectangleBorder(borderRadius: mediumRadius),
          minimumSize: const Size(0, 40),
        ),
      ),

      // Input decoration theme - Enhanced for dark mode
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: const Color(0xFF334155), // Darker surface for inputs
        contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
        border: OutlineInputBorder(
          borderRadius: mediumRadius,
          borderSide: BorderSide(color: _textTertiaryDarkColor.withOpacity(0.4), width: 1),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: mediumRadius,
          borderSide: BorderSide(color: _textTertiaryDarkColor.withOpacity(0.4), width: 1),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: mediumRadius,
          borderSide: BorderSide(color: _primaryColor, width: 1.5),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: mediumRadius,
          borderSide: BorderSide(color: _errorColor, width: 1),
        ),
        labelStyle: FontManager.inputTextStyle(context, _textSecondaryDarkColor),
        hintStyle: FontManager.inputTextStyle(context, _textTertiaryDarkColor),
        floatingLabelStyle: TextStyle(
          fontFamily: FontManager.getSecondaryFontFamily(context),
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: _primaryColor,
        ),
      ),

      // Bottom navigation bar theme - Enhanced for dark mode
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: const Color(0xFF1E293B),
        selectedItemColor: _primaryColor,
        unselectedItemColor: _textTertiaryDarkColor,
        selectedLabelStyle: FontManager.navigationTextStyle(context, _primaryColor, selected: true),
        unselectedLabelStyle: FontManager.navigationTextStyle(context, _textTertiaryDarkColor),
        elevation: 8,
        type: BottomNavigationBarType.fixed,
      ),

      // Dialog theme - Enhanced for dark mode
      dialogTheme: DialogThemeData(
        backgroundColor: const Color(0xFF1E293B),
        shape: RoundedRectangleBorder(borderRadius: largeRadius),
        elevation: 16,
        titleTextStyle: TextStyle(
          fontFamily: FontManager.getPrimaryFontFamily(context),
          fontSize: 22,
          fontWeight: FontWeight.w600,
          color: _textPrimaryDarkColor,
          letterSpacing: -0.25,
        ),
        contentTextStyle: TextStyle(
          fontFamily: FontManager.getSecondaryFontFamily(context),
          fontSize: 16,
          fontWeight: FontWeight.w400,
          color: _textPrimaryDarkColor,
          letterSpacing: 0.15,
          height: 1.5,
        ),
      ),

      // Divider theme for dark mode
      dividerTheme: DividerThemeData(
        color: _textTertiaryDarkColor.withOpacity(0.2),
        thickness: 1,
        space: 1,
      ),

      // Switch theme for dark mode
      switchTheme: SwitchThemeData(
        thumbColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return _primaryColor;
          }
          return _textTertiaryDarkColor;
        }),
        trackColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return _primaryColor.withOpacity(0.3);
          }
          return _textTertiaryDarkColor.withOpacity(0.2);
        }),
      ),

      // Checkbox theme for dark mode
      checkboxTheme: CheckboxThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return _primaryColor;
          }
          return Colors.transparent;
        }),
        checkColor: WidgetStateProperty.all(Colors.white),
        side: BorderSide(color: _textTertiaryDarkColor.withOpacity(0.6), width: 2),
      ),

      // Radio theme for dark mode
      radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return _primaryColor;
          }
          return _textTertiaryDarkColor.withOpacity(0.6);
        }),
      ),
    );
  }

  // Helper methods for consistent styling across the app
  
  // Card decorations
  static BoxDecoration get cardDecoration => BoxDecoration(
    color: _backgroundLightColor,
    borderRadius: mediumRadius,
    boxShadow: subtleShadow,
    border: Border.all(
      color: _textTertiaryLightColor.withOpacity(0.1),
      width: 1,
    ),
  );
  
  static BoxDecoration get elevatedCardDecoration => BoxDecoration(
    color: _backgroundLightColor,
    borderRadius: mediumRadius,
    boxShadow: mediumShadow,
  );
  
  // Gradient decorations
  static BoxDecoration get primaryGradientDecoration => BoxDecoration(
    gradient: primaryGradient,
    borderRadius: mediumRadius,
    boxShadow: mediumShadow,
  );
  
  static BoxDecoration get accentGradientDecoration => BoxDecoration(
    gradient: accentGradient,
    borderRadius: mediumRadius,
    boxShadow: mediumShadow,
  );
  
  // Status indicator decorations
  static BoxDecoration statusIndicator(Color color) => BoxDecoration(
    color: color.withOpacity(0.1),
    borderRadius: circularRadius,
    border: Border.all(
      color: color.withOpacity(0.6),
      width: 1.5,
    ),
  );
  
  // Luxurious container decoration
  static BoxDecoration get luxuryContainer => BoxDecoration(
    color: _backgroundLightColor,
    borderRadius: mediumRadius,
    boxShadow: subtleShadow,
    border: Border.all(
      color: _accentColor.withOpacity(0.2),
      width: 1.5,
    ),
  );
  
  // Frosted glass effect
  static BoxDecoration get frostedGlass => BoxDecoration(
    color: Colors.white.withOpacity(0.15),
    borderRadius: mediumRadius,
    border: Border.all(
      color: Colors.white.withOpacity(0.2),
      width: 1,
    ),
    boxShadow: [
      BoxShadow(
        color: Colors.black.withOpacity(0.05),
        blurRadius: 20,
        spreadRadius: -5,
      ),
    ],
  );

  // Dark mode specific decorations
  static BoxDecoration get darkCardDecoration => BoxDecoration(
    color: const Color(0xFF1E293B),
    borderRadius: mediumRadius,
    boxShadow: [
      BoxShadow(
        color: Colors.black.withOpacity(0.3),
        blurRadius: 12,
        offset: const Offset(0, 2),
        spreadRadius: 0,
      ),
    ],
    border: Border.all(
      color: _textTertiaryDarkColor.withOpacity(0.2),
      width: 1,
    ),
  );

  static BoxDecoration get darkElevatedCardDecoration => BoxDecoration(
    color: const Color(0xFF1E293B),
    borderRadius: mediumRadius,
    boxShadow: [
      BoxShadow(
        color: Colors.black.withOpacity(0.4),
        blurRadius: 20,
        offset: const Offset(0, 5),
        spreadRadius: -2,
      ),
      BoxShadow(
        color: Colors.black.withOpacity(0.2),
        blurRadius: 4,
        offset: const Offset(0, 1),
        spreadRadius: 0,
      ),
    ],
  );

  // Dark mode frosted glass effect
  static BoxDecoration get darkFrostedGlass => BoxDecoration(
    color: Colors.black.withOpacity(0.3),
    borderRadius: mediumRadius,
    border: Border.all(
      color: _textTertiaryDarkColor.withOpacity(0.3),
      width: 1,
    ),
    boxShadow: [
      BoxShadow(
        color: Colors.black.withOpacity(0.2),
        blurRadius: 20,
        spreadRadius: -5,
      ),
    ],
  );

  // Context-aware decorations that adapt to theme
  static BoxDecoration adaptiveCardDecoration(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? darkCardDecoration : cardDecoration;
  }

  static BoxDecoration adaptiveElevatedCardDecoration(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? darkElevatedCardDecoration : elevatedCardDecoration;
  }

  static BoxDecoration adaptiveFrostedGlass(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? darkFrostedGlass : frostedGlass;
  }

  // Enhanced shadows for dark mode
  static List<BoxShadow> get darkSubtleShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(0.3),
      blurRadius: 12,
      offset: const Offset(0, 2),
      spreadRadius: 0,
    ),
  ];

  static List<BoxShadow> get darkMediumShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(0.4),
      blurRadius: 20,
      offset: const Offset(0, 5),
      spreadRadius: -2,
    ),
    BoxShadow(
      color: Colors.black.withOpacity(0.2),
      blurRadius: 4,
      offset: const Offset(0, 1),
      spreadRadius: 0,
    ),
  ];

  static List<BoxShadow> get darkStrongShadow => [
    BoxShadow(
      color: Colors.black.withOpacity(0.6),
      blurRadius: 30,
      offset: const Offset(0, 10),
      spreadRadius: -5,
    ),
    BoxShadow(
      color: Colors.black.withOpacity(0.3),
      blurRadius: 8,
      offset: const Offset(0, 3),
      spreadRadius: -1,
    ),
  ];

  // Context-aware shadows
  static List<BoxShadow> adaptiveSubtleShadow(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? darkSubtleShadow : subtleShadow;
  }

  static List<BoxShadow> adaptiveMediumShadow(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? darkMediumShadow : mediumShadow;
  }

  static List<BoxShadow> adaptiveStrongShadow(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return isDark ? darkStrongShadow : strongShadow;
  }
}