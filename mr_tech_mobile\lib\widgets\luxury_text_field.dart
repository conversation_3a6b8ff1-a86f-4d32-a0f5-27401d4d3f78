import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/app_theme.dart';

/// A luxury-styled text field with customizable appearance and behaviors.
/// 
/// This widget provides a consistent, premium look for text inputs throughout the app
/// with support for various input types, validation, and styling options.
class LuxuryTextField extends StatefulWidget {
  final String? label;
  final String? hint;
  final String? initialValue;
  final TextEditingController? controller;
  final bool obscureText;
  final TextInputType keyboardType;
  final TextInputAction textInputAction;
  final int? maxLines;
  final int? minLines;
  final int? maxLength;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onEditingComplete;
  final ValueChanged<String>? onFieldSubmitted;
  final FormFieldValidator<String>? validator;
  final List<TextInputFormatter>? inputFormatters;
  final bool enabled;
  final bool readOnly;
  final bool autofocus;
  final FocusNode? focusNode;
  final Widget? prefix;
  final Widget? suffix;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final VoidCallback? onSuffixIconTap;
  final VoidCallback? onPrefixIconTap;
  final EdgeInsetsGeometry? contentPadding;
  final Color? fillColor;
  final Color? borderColor;
  final Color? focusedBorderColor;
  final BorderRadius? borderRadius;
  final String? errorText;
  final String? helperText;
  final bool showCounter;
  final TextStyle? style;
  final TextStyle? labelStyle;
  final TextStyle? hintStyle;
  final TextCapitalization textCapitalization;
  final bool enableSuggestions;
  final bool autocorrect;
  final AutovalidateMode autovalidateMode;
  final TextStyle? helperStyle;
  final TextStyle? errorStyle;
  final bool expands;
  final bool showCursor;
  final VoidCallback? onTap;
  final bool filled;
  final double? cursorHeight;
  final Color? cursorColor;
  final TextDirection? textDirection;
  
  const LuxuryTextField({
    super.key,
    this.label,
    this.hint,
    this.initialValue,
    this.controller,
    this.obscureText = false,
    this.keyboardType = TextInputType.text,
    this.textInputAction = TextInputAction.next,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.onChanged,
    this.onEditingComplete,
    this.onFieldSubmitted,
    this.validator,
    this.inputFormatters,
    this.enabled = true,
    this.readOnly = false,
    this.autofocus = false,
    this.focusNode,
    this.prefix,
    this.suffix,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconTap,
    this.onPrefixIconTap,
    this.contentPadding,
    this.fillColor,
    this.borderColor,
    this.focusedBorderColor,
    this.borderRadius,
    this.errorText,
    this.helperText,
    this.showCounter = false,
    this.style,
    this.labelStyle,
    this.hintStyle,
    this.textCapitalization = TextCapitalization.none,
    this.enableSuggestions = true,
    this.autocorrect = true,
    this.autovalidateMode = AutovalidateMode.disabled,
    this.helperStyle,
    this.errorStyle,
    this.expands = false,
    this.showCursor = true,
    this.onTap,
    this.filled = true,
    this.cursorHeight,
    this.cursorColor,
    this.textDirection,
  });
  
  @override
  State<LuxuryTextField> createState() => _LuxuryTextFieldState();
}

class _LuxuryTextFieldState extends State<LuxuryTextField> {
  bool _passwordVisible = false;
  late TextEditingController _controller;
  bool _isFocused = false;
  
  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController(text: widget.initialValue);
    
    // Set up focus listener if we have a focusNode
    if (widget.focusNode != null) {
      widget.focusNode!.addListener(_handleFocusChange);
    }
  }
  
  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    
    if (widget.focusNode != null) {
      widget.focusNode!.removeListener(_handleFocusChange);
    }
    
    super.dispose();
  }
  
  void _handleFocusChange() {
    if (widget.focusNode != null) {
      setState(() {
        _isFocused = widget.focusNode!.hasFocus;
      });
    }
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final radius = widget.borderRadius ?? AppTheme.mediumRadius;
    
    // Determine if we need to show a toggle for password visibility
    final bool isPassword = widget.obscureText && widget.suffixIcon == null;
    
    return TextFormField(
      controller: _controller,
      initialValue: widget.controller != null ? null : widget.initialValue,
      obscureText: isPassword ? !_passwordVisible : widget.obscureText,
      keyboardType: widget.keyboardType,
      textInputAction: widget.textInputAction,
      maxLines: widget.obscureText ? 1 : widget.maxLines,
      minLines: widget.minLines,
      maxLength: widget.maxLength,
      onChanged: widget.onChanged,
      onEditingComplete: widget.onEditingComplete,
      onFieldSubmitted: widget.onFieldSubmitted,
      validator: widget.validator,
      inputFormatters: widget.inputFormatters,
      enabled: widget.enabled,
      readOnly: widget.readOnly,
      autofocus: widget.autofocus,
      focusNode: widget.focusNode,
      textCapitalization: widget.textCapitalization,
      enableSuggestions: widget.enableSuggestions,
      autocorrect: widget.autocorrect,
      autovalidateMode: widget.autovalidateMode,
      expands: widget.expands,
      showCursor: widget.showCursor,
      onTap: widget.onTap,
      cursorHeight: widget.cursorHeight,
      cursorColor: widget.cursorColor ?? theme.colorScheme.primary,
      textDirection: widget.textDirection,
      style: widget.style ?? theme.textTheme.bodyLarge,
      decoration: InputDecoration(
        labelText: widget.label,
        hintText: widget.hint,
        errorText: widget.errorText,
        helperText: widget.helperText,
        filled: widget.filled,
        fillColor: widget.fillColor ?? theme.cardTheme.color,
        contentPadding: widget.contentPadding ?? 
            EdgeInsets.symmetric(horizontal: 20, vertical: widget.maxLines != null && widget.maxLines! > 1 ? 20 : 16),
        border: OutlineInputBorder(
          borderRadius: radius,
          borderSide: BorderSide(
            color: widget.borderColor ?? theme.dividerTheme.color ?? Colors.black12,
            width: 1.0,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: radius,
          borderSide: BorderSide(
            color: widget.borderColor ?? theme.dividerTheme.color ?? Colors.black12,
            width: 1.0,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: radius,
          borderSide: BorderSide(
            color: widget.focusedBorderColor ?? theme.colorScheme.primary,
            width: 1.5,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: radius,
          borderSide: BorderSide(
            color: theme.colorScheme.error,
            width: 1.0,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: radius,
          borderSide: BorderSide(
            color: theme.colorScheme.error,
            width: 1.5,
          ),
        ),
        prefixIcon: widget.prefixIcon != null
            ? IconButton(
                icon: Icon(widget.prefixIcon),
                onPressed: widget.onPrefixIconTap,
                color: _isFocused
                    ? theme.colorScheme.primary
                    : theme.iconTheme.color?.withOpacity(0.7),
              )
            : widget.prefix,
        suffixIcon: isPassword
            ? IconButton(
                icon: Icon(
                  _passwordVisible ? Icons.visibility_off : Icons.visibility,
                  color: _isFocused
                      ? theme.colorScheme.primary
                      : theme.iconTheme.color?.withOpacity(0.7),
                ),
                onPressed: () {
                  setState(() {
                    _passwordVisible = !_passwordVisible;
                  });
                },
              )
            : widget.suffixIcon != null
                ? IconButton(
                    icon: Icon(widget.suffixIcon),
                    onPressed: widget.onSuffixIconTap,
                    color: _isFocused
                        ? theme.colorScheme.primary
                        : theme.iconTheme.color?.withOpacity(0.7),
                  )
                : widget.suffix,
        labelStyle: widget.labelStyle ?? theme.inputDecorationTheme.labelStyle,
        hintStyle: widget.hintStyle ?? theme.inputDecorationTheme.hintStyle,
        helperStyle: widget.helperStyle,
        errorStyle: widget.errorStyle,
        counterText: widget.showCounter ? null : '',
        floatingLabelBehavior: FloatingLabelBehavior.auto,
      ),
    );
  }
}

/// A luxury-styled search field
class LuxurySearchField extends StatelessWidget {
  final TextEditingController? controller;
  final String hint;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onClear;
  final VoidCallback? onSubmitted;
  final FocusNode? focusNode;
  final bool autofocus;
  final Color? fillColor;
  final BorderRadius? borderRadius;
  
  const LuxurySearchField({
    super.key,
    this.controller,
    this.hint = 'Search...',
    this.onChanged,
    this.onClear,
    this.onSubmitted,
    this.focusNode,
    this.autofocus = false,
    this.fillColor,
    this.borderRadius,
  });
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final radius = borderRadius ?? BorderRadius.circular(30);
    
    return Container(
      height: 48,
      decoration: BoxDecoration(
        borderRadius: radius,
        color: fillColor ?? theme.cardTheme.color,
        boxShadow: AppTheme.subtleShadow,
      ),
      child: TextField(
        controller: controller,
        focusNode: focusNode,
        autofocus: autofocus,
        decoration: InputDecoration(
          hintText: hint,
          prefixIcon: Icon(
            Icons.search,
            color: theme.iconTheme.color?.withOpacity(0.6),
          ),
          suffixIcon: controller != null && controller!.text.isNotEmpty
              ? IconButton(
                  icon: Icon(
                    Icons.clear,
                    color: theme.iconTheme.color?.withOpacity(0.6),
                    size: 20,
                  ),
                  onPressed: () {
                    controller!.clear();
                    if (onClear != null) onClear!();
                    if (onChanged != null) onChanged!('');
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: radius,
            borderSide: BorderSide.none,
          ),
          contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
          filled: true,
          fillColor: fillColor ?? theme.cardTheme.color,
        ),
        style: theme.textTheme.bodyMedium,
        textInputAction: TextInputAction.search,
        onChanged: onChanged,
        onSubmitted: (_) {
          if (onSubmitted != null) onSubmitted!();
        },
      ),
    );
  }
}

/// A dropdown field with luxury styling
class LuxuryDropdown<T> extends StatelessWidget {
  final String? label;
  final T? value;
  final List<DropdownMenuItem<T>> items;
  final ValueChanged<T?>? onChanged;
  final String? hint;
  final Widget? prefix;
  final IconData? prefixIcon;
  final FormFieldValidator<T>? validator;
  final bool isExpanded;
  final bool isDense;
  final Color? fillColor;
  final Color? borderColor;
  final BorderRadius? borderRadius;
  final EdgeInsetsGeometry? contentPadding;
  final AutovalidateMode? autovalidateMode;
  
  const LuxuryDropdown({
    super.key,
    this.label,
    required this.items,
    this.value,
    this.onChanged,
    this.hint,
    this.prefix,
    this.prefixIcon,
    this.validator,
    this.isExpanded = true,
    this.isDense = false,
    this.fillColor,
    this.borderColor,
    this.borderRadius,
    this.contentPadding,
    this.autovalidateMode,
  });
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final radius = borderRadius ?? AppTheme.mediumRadius;
    
    return DropdownButtonFormField<T>(
      value: value,
      items: items,
      onChanged: onChanged,
      validator: validator,
      isExpanded: isExpanded,
      isDense: isDense,
      icon: Icon(Icons.arrow_drop_down, color: theme.iconTheme.color),
      autovalidateMode: autovalidateMode,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        filled: true,
        fillColor: fillColor ?? theme.cardTheme.color,
        contentPadding: contentPadding ?? 
            EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        border: OutlineInputBorder(
          borderRadius: radius,
          borderSide: BorderSide(
            color: borderColor ?? theme.dividerTheme.color ?? Colors.black12,
            width: 1.0,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: radius,
          borderSide: BorderSide(
            color: borderColor ?? theme.dividerTheme.color ?? Colors.black12,
            width: 1.0,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: radius,
          borderSide: BorderSide(
            color: theme.colorScheme.primary,
            width: 1.5,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: radius,
          borderSide: BorderSide(
            color: theme.colorScheme.error,
            width: 1.0,
          ),
        ),
        prefixIcon: prefixIcon != null
            ? Icon(prefixIcon, color: theme.iconTheme.color?.withOpacity(0.7))
            : prefix,
      ),
    );
  }
} 