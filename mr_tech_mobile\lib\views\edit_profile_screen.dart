import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../services/translation_service.dart';
import '../services/anydesk_service.dart';
import '../models/user_model.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:url_launcher/url_launcher.dart';
import '../utils/network_image_handler.dart';
import 'delete_account_screen.dart';
import 'welcome_screen.dart';
import '../widgets/text_styles.dart';

class EditProfileScreen extends StatefulWidget {
  const EditProfileScreen({super.key});

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final _displayNameController = TextEditingController();
  final _phoneNumberController = TextEditingController();
  final _anydesk_idController = TextEditingController();

  bool _isLoading = true;
  bool _isSaving = false;
  UserModel? _user;

  // Services
  late AuthService _authService;
  late TranslationService _translationService;
  final AnyDeskService _anydeskService = AnyDeskService();

  @override
  void initState() {
    super.initState();
    _authService = Provider.of<AuthService>(context, listen: false);
    _translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );

    _loadUserData();
  }

  @override
  void dispose() {
    _displayNameController.dispose();
    _phoneNumberController.dispose();
    _anydesk_idController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    setState(() => _isLoading = true);

    try {
      final user = await _authService.getCurrentUserDetails();

      if (user != null) {
        setState(() {
          _user = user;
          _displayNameController.text = user.displayName ?? '';
          _phoneNumberController.text = user.phoneNumber ?? '';
          _anydesk_idController.text = user.anydesk_id ?? '';
        });
      }
    } catch (e) {
      debugPrint('Error loading user data: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${_translate('Error loading profile')}: ${e.toString()}',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isSaving = true);

    try {
      // Update profile in Firebase Auth and Firestore
      await _authService.updateUserDetails(
        displayName: _displayNameController.text,
        phoneNumber: _phoneNumberController.text,
        anydesk_id:
            _anydesk_idController.text.isEmpty
                ? null
                : _anydesk_idController.text,
      );

      // Mark AnyDesk setup as completed if ID is provided
      if (_anydesk_idController.text.isNotEmpty) {
        await _anydeskService.saveAnyDeskSetupCompleted(true);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_translate('Profile updated successfully')),
            backgroundColor: Colors.green,
          ),
        );

        // Refresh user data
        await _loadUserData();
      }
    } catch (e) {
      debugPrint('Error saving profile: $e');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${_translate('Error updating profile')}: ${e.toString()}',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSaving = false);
      }
    }
  }

  Future<void> _openAnyDeskWebsite() async {
    final Uri url = Uri.parse('https://anydesk.com/downloads');
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_translate('Could not open AnyDesk website')),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Helper method to translate strings
  String _translate(String key) {
    return _translationService.translate(key);
  }

  // Helper method to build text fields with consistent styling
  Widget _buildTextField({
    required TextEditingController controller,
    required String labelText,
    required IconData prefixIcon,
    String? helperText,
    Widget? suffixIcon,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    final colorScheme = Theme.of(context).colorScheme;

    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      validator: validator,
      style: AppTextStyles.bodyMedium(context),
      decoration: InputDecoration(
        labelText: labelText,
        helperText: helperText,
        prefixIcon: Icon(prefixIcon, color: colorScheme.primary),
        suffixIcon: suffixIcon,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.error),
        ),
        filled: true,
        fillColor: colorScheme.surface,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: CustomScrollView(
        slivers: [
          // Floating header
          SliverAppBar(
            expandedHeight: 80,
            floating: true,
            pinned: false,
            snap: true,
            elevation: 0,
            backgroundColor: colorScheme.surface,
            leading: BackButton(color: colorScheme.onSurface),
            flexibleSpace: FlexibleSpaceBar(
              background: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 10,
                  ),
                  child: Row(
                    children: [
                      const SizedBox(width: 56), // Space for back button
                      Expanded(
                        child: Text(
                          _translate('Edit Profile'),
                          style: AppTextStyles.headingMedium(
                            context,
                            color: colorScheme.onSurface,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(width: 56), // Balance the layout
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Main content
          _isLoading
              ? const SliverFillRemaining(
                child: Center(child: CircularProgressIndicator()),
              )
              : SliverToBoxAdapter(
                child: Form(
                  key: _formKey,
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Profile picture and basic info
                        _buildProfileHeader(),
                        const SizedBox(height: 32),

                        _buildPersonalInfoSection(),
                        const SizedBox(height: 32),

                        _buildAnyDeskSection(),
                        const SizedBox(height: 40),

                        // Save button
                        SizedBox(
                          width: double.infinity,
                          height: 52,
                          child: FilledButton(
                            onPressed: _isSaving ? null : _saveProfile,
                            style: FilledButton.styleFrom(
                              backgroundColor: colorScheme.primary,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              elevation: 0,
                            ),
                            child:
                                _isSaving
                                    ? SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        valueColor:
                                            AlwaysStoppedAnimation<Color>(
                                              Colors.white,
                                            ),
                                      ),
                                    )
                                    : Text(
                                      _translate('Save Changes'),
                                      style: AppTextStyles.buttonMedium(
                                        context,
                                        color: Colors.white,
                                      ),
                                    ),
                          ),
                        ),

                        const SizedBox(height: 40),

                        // Account deletion section
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.red.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey.shade300),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.warning_amber_rounded,
                                    color: Colors.red.shade700,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    _translate('Danger Zone'),
                                    style: AppTextStyles.headingSmall(
                                      context,
                                      color: Colors.red.shade700,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 12),
                              Text(
                                _translate(
                                  'Deleting your account is permanent and cannot be undone. All your data will be removed.',
                                ),
                                style: AppTextStyles.bodyMedium(
                                  context,
                                  color: Colors.red.shade900.withOpacity(0.8),
                                ),
                              ),
                              const SizedBox(height: 16),
                              SizedBox(
                                width: double.infinity,
                                child: OutlinedButton.icon(
                                  onPressed: () {
                                    Navigator.of(context).push(
                                      MaterialPageRoute(
                                        builder:
                                            (context) =>
                                                const DeleteAccountScreen(),
                                      ),
                                    );
                                  },
                                  icon: const Icon(Icons.delete_forever),
                                  label: Text(_translate('Delete Account')),
                                  style: OutlinedButton.styleFrom(
                                    foregroundColor: Colors.red.shade700,
                                    side: BorderSide(
                                      color: Colors.red.shade700,
                                    ),
                                    padding: const EdgeInsets.symmetric(
                                      vertical: 12,
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Logout button
                        SizedBox(
                          width: double.infinity,
                          child: OutlinedButton.icon(
                            onPressed: _logout,
                            icon: Icon(Icons.logout, color: colorScheme.error),
                            label: Text(
                              _translate('Logout'),
                              style: AppTextStyles.buttonMedium(
                                context,
                                color: colorScheme.error,
                              ),
                            ),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: colorScheme.error,
                              side: BorderSide(color: Colors.grey.shade300),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                          ),
                        ),

                        const SizedBox(height: 100), // Space for safe area
                      ],
                    ),
                  ),
                ),
              ),
        ],
      ),
    );
  }

  Widget _buildProfileHeader() {
    final User? firebaseUser = FirebaseAuth.instance.currentUser;
    final colorScheme = Theme.of(context).colorScheme;

    // Get initials for avatar if no image is available
    String initials = '';
    if (_displayNameController.text.isNotEmpty) {
      initials =
          _displayNameController.text
              .split(' ')
              .map((name) => name.isNotEmpty ? name[0] : '')
              .take(2)
              .join('')
              .toUpperCase();
    }

    return Column(
      children: [
        // Simple profile picture with clean border
        Center(
          child: Stack(
            alignment: Alignment.center,
            children: [
              // Avatar
              Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.grey.shade300, width: 2),
                ),
                child: NetworkImageCircleAvatarExtension.withNetworkImage(
                  context: context,
                  imageUrl: firebaseUser?.photoURL,
                  initials: initials,
                  radius: 50,
                  backgroundColor: colorScheme.surfaceContainerHighest,
                  initialsStyle: AppTextStyles.displaySmall(
                    context,
                    color: colorScheme.primary,
                  ),
                ),
              ),
              // Edit indicator
              Positioned(
                bottom: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: colorScheme.primary,
                    shape: BoxShape.circle,
                    border: Border.all(color: colorScheme.surface, width: 2),
                  ),
                  child: const Icon(
                    Icons.camera_alt_rounded,
                    size: 14,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // User name display
        Center(
          child: Text(
            _displayNameController.text.isEmpty
                ? _translate('Your Name')
                : _displayNameController.text,
            style: AppTextStyles.headingMedium(
              context,
              color: colorScheme.onSurface,
            ),
          ),
        ),

        // Email display
        Center(
          child: Padding(
            padding: const EdgeInsets.only(top: 4),
            child: Text(
              firebaseUser?.email ?? _user?.email ?? '',
              style: AppTextStyles.bodyMedium(
                context,
                color: colorScheme.onSurfaceVariant,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPersonalInfoSection() {
    final colorScheme = Theme.of(context).colorScheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Padding(
          padding: const EdgeInsets.only(left: 4, bottom: 16),
          child: Text(
            _translate('Personal Information'),
            style: AppTextStyles.headingSmall(
              context,
              color: colorScheme.onSurface,
            ),
          ),
        ),

        // Name field
        _buildTextField(
          controller: _displayNameController,
          labelText: _translate('Full Name'),
          prefixIcon: Icons.person_outline,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return _translate('Name is required');
            }
            return null;
          },
        ),
        const SizedBox(height: 20),

        // Phone number field
        _buildTextField(
          controller: _phoneNumberController,
          labelText: _translate('Phone Number'),
          prefixIcon: Icons.phone,
          keyboardType: TextInputType.phone,
        ),
      ],
    );
  }

  Widget _buildAnyDeskSection() {
    final colorScheme = Theme.of(context).colorScheme;
    final anyDeskColor = Colors.blue.shade700;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Padding(
          padding: const EdgeInsets.only(left: 4, bottom: 16),
          child: Text(
            _translate('Remote Support Settings'),
            style: AppTextStyles.headingSmall(
              context,
              color: colorScheme.onSurface,
            ),
          ),
        ),

        // AnyDesk ID field
        _buildTextField(
          controller: _anydesk_idController,
          labelText: _translate('AnyDesk ID'),
          helperText: _translate('Required for remote support sessions'),
          prefixIcon: Icons.desktop_mac,
          keyboardType: TextInputType.number,
          suffixIcon: IconButton(
            icon: Icon(Icons.help_outline, color: anyDeskColor),
            onPressed: () => _showAnyDeskInfo(),
          ),
        ),
        const SizedBox(height: 20),

        // Download AnyDesk button
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _openAnyDeskWebsite,
            icon: Icon(Icons.download, color: anyDeskColor),
            label: Text(
              _translate('Download AnyDesk'),
              style: AppTextStyles.buttonMedium(context, color: anyDeskColor),
            ),
            style: OutlinedButton.styleFrom(
              side: BorderSide(color: Colors.grey.shade300),
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        const SizedBox(height: 20),

        // Info card
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(Icons.info_outline, color: anyDeskColor, size: 20),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  _translate(
                    'AnyDesk must be installed on your computer (not your mobile device). Our technicians will use this ID to connect to your computer during scheduled sessions.',
                  ),
                  style: AppTextStyles.bodyMedium(
                    context,
                    color: Colors.blue.shade900,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showAnyDeskInfo() {
    final colorScheme = Theme.of(context).colorScheme;
    final anyDeskColor = Colors.blue.shade700;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
              side: BorderSide(color: Colors.grey.shade300),
            ),
            title: Text(
              _translate('AnyDesk Information'),
              style: AppTextStyles.headingSmall(context),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _translate('How to find your AnyDesk ID:'),
                  style: AppTextStyles.labelMedium(
                    context,
                    color: colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 16),
                _buildAnyDeskStep(
                  '1',
                  _translate(
                    'Install AnyDesk on your computer (not your mobile device)',
                  ),
                  anyDeskColor,
                ),
                const SizedBox(height: 8),
                _buildAnyDeskStep(
                  '2',
                  _translate('Open the AnyDesk application on your computer'),
                  anyDeskColor,
                ),
                const SizedBox(height: 8),
                _buildAnyDeskStep(
                  '3',
                  _translate(
                    'Your AnyDesk ID is shown at the top of the application',
                  ),
                  anyDeskColor,
                ),
                const SizedBox(height: 8),
                _buildAnyDeskStep(
                  '4',
                  _translate(
                    'It\'s a 9-digit number, usually separated by spaces',
                  ),
                  anyDeskColor,
                ),
                const SizedBox(height: 20),
                SizedBox(
                  width: double.infinity,
                  child: FilledButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      _openAnyDeskWebsite();
                    },
                    icon: const Icon(Icons.download),
                    label: Text(_translate('Download AnyDesk')),
                    style: FilledButton.styleFrom(
                      backgroundColor: anyDeskColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      elevation: 0,
                    ),
                  ),
                ),
              ],
            ),
            actions: [
              OutlinedButton(
                onPressed: () => Navigator.pop(context),
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: Colors.grey.shade300),
                ),
                child: Text(
                  _translate('Close'),
                  style: AppTextStyles.buttonMedium(
                    context,
                    color: anyDeskColor,
                  ),
                ),
              ),
            ],
          ),
    );
  }

  Widget _buildAnyDeskStep(String number, String text, Color color) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            shape: BoxShape.circle,
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: Center(
            child: Text(
              number,
              style: AppTextStyles.bodySmall(context, color: color),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            text,
            style: AppTextStyles.bodyMedium(context, color: Colors.black87),
          ),
        ),
      ],
    );
  }

  // Add logout functionality
  void _logout() async {
    // Show confirmation dialog
    final shouldLogout = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
              side: BorderSide(color: Colors.grey.shade300),
            ),
            title: Text(_translate('Logout')),
            content: Text(_translate('Are you sure you want to logout?')),
            actions: [
              OutlinedButton(
                onPressed: () => Navigator.pop(context, false),
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: Colors.grey.shade300),
                ),
                child: Text(_translate('Cancel')),
              ),
              FilledButton(
                onPressed: () => Navigator.pop(context, true),
                style: FilledButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.error,
                  elevation: 0,
                ),
                child: Text(_translate('Logout')),
              ),
            ],
          ),
    );

    // Proceed with logout if user confirmed
    if (shouldLogout == true) {
      try {
        // Show loading indicator
        showDialog(
          context: context,
          barrierDismissible: false,
          builder:
              (context) => const Center(child: CircularProgressIndicator()),
        );

        // Logout using auth service
        final authService = Provider.of<AuthService>(context, listen: false);
        await authService.signOut();

        // Navigate to welcome screen
        if (mounted) {
          // Pop the loading dialog
          Navigator.pop(context);

          // Navigate to welcome screen and clear the navigation stack
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(
              builder:
                  (context) => WelcomeScreen(
                    onLocaleChanged: (locale) {
                      // Update locale
                      Provider.of<TranslationService>(context, listen: false)
                          .currentLocale = locale;
                    },
                    onAuthenticationComplete: () {},
                  ),
            ),
            (route) => false, // clear the entire stack
          );
        }
      } catch (e) {
        // Pop the loading dialog if still showing
        if (mounted && Navigator.canPop(context)) {
          Navigator.pop(context);
        }

        // Show error
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              '${_translate('Error logging out')}: ${e.toString()}',
            ),
            behavior: SnackBarBehavior.floating,
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
