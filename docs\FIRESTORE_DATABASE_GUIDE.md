# Firestore Database Implementation Guide - Mr. Tech Mobile App

## Overview

This guide documents the **actual Firestore database structure** used in the Mr. Tech mobile application. The database follows a hybrid approach with both snake_case and camelCase field naming for compatibility between the mobile app and web portal.

## Database Architecture

The application uses **Firebase Firestore** as the primary database with the following key characteristics:
- **Multi-platform compatibility**: Supports both mobile app (Flutter) and web portal
- **Dual field naming**: Uses both snake_case and camelCase for cross-platform compatibility
- **Real-time updates**: Implements Firestore listeners for live data synchronization
- **Caching layer**: Implements local caching for improved performance
- **Security rules**: Comprehensive security rules for data protection

## Core Collections

### 1. `users` Collection

**Purpose**: Stores user profiles and authentication data

**Document Structure**:
```typescript
{
  // User identification
  email: string,
  display_name: string,
  phone_number?: string,
  photo_url?: string,
  
  // Profile information
  address?: string,
  city?: string,
  country?: string,
  anydesk_id?: string,
  
  // User state
  is_complete: boolean,
  is_onboarded: boolean,
  email_verified: boolean,
  preferred_language: string, // 'en' | 'ar'
  
  // Timestamps
  created_at: Timestamp,
  updated_at: Timestamp,
  
  // Notification preferences
  notification_preferences: {
    push_notifications: boolean,
    email_notifications: boolean,
    sms_notifications: boolean,
    marketing_notifications: boolean
  }
}
```

**Key Features**:
- Supports both `display_name` and `full_name` fields for compatibility
- Handles multilingual preferences (`preferred_language`)
- Nested notification preferences object
- AnyDesk integration support

**Security Rules**:
- Users can read/update their own profile
- Admins can read all profiles
- Role changes restricted to admins only

### 2. `service_requests` Collection

**Purpose**: Core collection for service requests and support tickets

**Document Structure**:
```typescript
{
  // Request identification
  customer_id: string,
  service_id: string,
  service_name: string,
  service_description: string,
  customer_issue: string,
  
  // Assignment
  technician_id?: string,
  technician_name?: string,
  anydesk_id?: string,
  
  // Status and workflow
  status: 'payment_pending' | 'pending' | 'approved' | 'inProgress' | 'in_progress' | 'completed' | 'cancelled' | 'refused',
  amount: number,
  is_paid: boolean,
  
  // Chat system
  chat_active: boolean,
  chatActive: boolean, // Compatibility field
  session_active: boolean,
  
  // Timestamps
  created_at: Timestamp,
  updated_at: Timestamp,
  scheduled_time?: Timestamp,
  session_start_time?: Timestamp,
  session_end_time?: Timestamp,
  session_duration?: number, // in minutes
  
  // Review system
  customer_rated?: boolean,
  rating?: number,
  review_comment?: string,
  
  // Notifications
  customer_fcm_token?: string,
  customerFcmToken?: string, // Compatibility field
  
  // Visibility
  is_visible: boolean
}
```

**Key Features**:
- Dual field naming (`chat_active` + `chatActive`) for web/mobile compatibility
- Comprehensive status workflow
- Built-in session management
- FCM token storage for notifications
- Support for both snake_case and camelCase customer ID fields

**Status Flow**:
1. `payment_pending` → Payment required
2. `pending` → Awaiting technician assignment
3. `approved` → Technician assigned
4. `inProgress`/`in_progress` → Work in progress
5. `completed` → Service completed
6. `cancelled`/`refused` → Request terminated

### 3. `services` Collection

**Purpose**: Available services catalog with multilingual support

**Document Structure**:
```typescript
{
  // Service identification
  name: string | {[languageCode: string]: string}, // Multilingual support
  description: string | {[languageCode: string]: string},
  category: string,
  
  // Pricing and duration
  base_price: number,
  estimated_duration: number, // in minutes
  
  // Display
  image_url: string,
  active: boolean,
  
  // Metadata
  metadata?: {
    icon?: string,
    popularityIndex?: number,
    [key: string]: any
  },
  
  // Timestamps
  created_at: Timestamp,
  updated_at: Timestamp
}
```

**Key Features**:
- **Multilingual support**: Names and descriptions can be objects with language codes
- Category-based organization
- Popularity tracking via metadata
- Flexible metadata system for additional properties

**Default Categories**:
- `os` - Operating System support
- `productivity` - Office applications
- `hardware` - Hardware setup/troubleshooting
- `network` - Network configuration
- `security` - Security and antivirus
- `data` - Data recovery services

### 4. `technicians` Collection

**Purpose**: Technician profiles and availability

**Document Structure**:
```typescript
{
  // Basic information
  email: string,
  name: string,
  photo_url?: string,
  phone_number?: string,
  
  // Skills and experience
  specialties: string[], // Array of specialization areas
  rating: number,
  completed_requests: number,
  active_requests: number,
  
  // Availability
  status: 'active' | 'offline' | 'busy' | 'onLeave',
  is_available: boolean,
  
  // Timestamps
  created_at: Timestamp,
  updated_at: Timestamp
}
```

**Status Types**:
- `active` - Available for new requests
- `offline` - Not currently available
- `busy` - Working on existing requests
- `onLeave` - Temporarily unavailable

### 5. `admins` Collection

**Purpose**: Administrator access control

**Document Structure**:
```typescript
{
  email: string,
  name: string,
  role: 'admin',
  permissions: string[],
  created_at: Timestamp,
  updated_at: Timestamp
}
```

**Special Documents**:
- `first-admin-marker` - Prevents multiple first admin creation

### 6. `reviews` Collection

**Purpose**: Customer reviews and ratings

**Document Structure**:
```typescript
{
  // Reference IDs
  request_id: string,
  customer_id: string,
  customer_name: string,
  technician_id: string,
  service_id: string,
  
  // Review content
  rating: number, // 1-5 scale
  comment: string,
  
  // Timestamp
  created_at: Timestamp
}
```

**Key Features**:
- Links to specific service request
- Customer and technician association
- 5-star rating system

### 7. `notifications` Collection

**Purpose**: User notifications and alerts

**Document Structure**:
```typescript
{
  // Target user
  userId: string,
  
  // Notification content
  title: string,
  body: string,
  type: string, // 'request_update', 'payment', 'system', etc.
  
  // Related data
  requestId?: string,
  
  // State
  read: boolean,
  deleted: boolean,
  
  // Timestamp
  createdAt: Timestamp
}
```

**Notification Types**:
- `request_update` - Service request status changes
- `payment` - Payment-related notifications
- `system` - System announcements
- `chat` - Chat message notifications

### 8. `device_tokens` Collection

**Purpose**: FCM token management for push notifications

**Document Structure**:
```typescript
{
  userId: string,
  token: string,
  platform: 'android' | 'ios',
  appVersion: string,
  createdAt: Timestamp,
  updatedAt: Timestamp,
  isActive: boolean
}
```

## Subcollections

### 1. `service_requests/{requestId}/messages`

**Purpose**: Chat messages for service requests

**Document Structure**:
```typescript
{
  // Message identification
  request_id: string,
  sender_type: 'customer' | 'technician' | 'system',
  sender_id: string,
  
  // Message content
  message_type: 'text' | 'image' | 'file' | 'system',
  content: string,
  file_url?: string,
  
  // State
  read: boolean,
  
  // Timestamp
  created_at: Timestamp,
  
  // Compatibility fields (camelCase)
  senderType: 'customer' | 'technician' | 'system',
  senderId: string,
  messageType: 'text' | 'image' | 'file' | 'system',
  fileUrl?: string,
  createdAt: Timestamp
}
```

**Key Features**:
- Dual field naming for compatibility
- Support for text, image, file, and system messages
- Read status tracking
- Sender type validation

### 2. `users/{userId}/notifications`

**Purpose**: User-specific notifications (nested structure)

**Document Structure**: Same as main notifications collection but scoped to specific user

## Security Rules Summary

### Authentication Requirements
- All operations require authentication (`request.auth != null`)
- User identity validation for data access
- Role-based access control (Admin, Technician, Customer)

### Key Security Features

1. **User Data Protection**:
   - Users can only access their own data
   - Admins have full access
   - Role changes restricted to admins

2. **Request Access Control**:
   - Customers can only see their own requests
   - Technicians see assigned requests
   - Admins have full access

3. **Chat Security**:
   - Messages only allowed when chat is active
   - Sender validation (customer/technician must be participants)
   - System messages allowed regardless of chat status

4. **Notification Security**:
   - Users can only read their own notifications
   - Admins and technicians can create notifications
   - Update restrictions (only read/deleted flags)

## Database Indexes

### Composite Indexes

1. **Service Requests**:
   - `customer_id + created_at (desc)` - User's requests chronologically
   - `customer_id + status + created_at (desc)` - Filtered user requests
   - `status + created_at (desc)` - Admin dashboard queries
   - `chat_active + customer_id + updated_at (desc)` - Active chats

2. **Notifications**:
   - `userId + deleted + createdAt (desc)` - User notifications
   - `userId + createdAt (desc)` - All user notifications
   - `read + createdAt (desc)` - Unread notifications

3. **Messages**:
   - `message_type + sender_type + created_at (desc)` - Message filtering
   - Per-request message ordering handled by subcollection

4. **Reviews**:
   - `technicianId + createdAt (desc)` - Technician reviews

5. **Services**:
   - `category + price (asc)` - Service browsing

## Performance Optimizations

### Caching Strategy
- **Service data**: 15-minute cache (relatively static)
- **User profiles**: 15-minute cache with force refresh option
- **Requests**: 5-minute cache (frequently updated)
- **Cache invalidation**: Manual cache clearing on updates

### Query Optimizations
- **Pagination**: Implemented for large result sets
- **Composite indexes**: Optimized for common query patterns
- **Limit clauses**: Prevent excessive data retrieval
- **Field selection**: Minimize data transfer when possible

### Real-time Updates
- **Stream subscriptions**: For active requests and chat messages
- **Connection management**: Proper cleanup of listeners
- **Error handling**: Fallback to cached data on connection issues

## Data Consistency

### Dual Field Naming
The database maintains both snake_case and camelCase versions of critical fields:

```typescript
// Example in service_requests
{
  chat_active: true,
  chatActive: true,        // Compatibility field
  customer_id: "user123",
  customerId: "user123",   // Compatibility field
}
```

### Field Mapping
- `customer_id` ↔ `customerId`
- `chat_active` ↔ `chatActive`
- `created_at` ↔ `createdAt`
- `updated_at` ↔ `updatedAt`
- `sender_type` ↔ `senderType`
- `message_type` ↔ `messageType`

## Migration Considerations

### Backward Compatibility
- All new fields added with both naming conventions
- Existing queries support both field names
- Gradual migration strategy for legacy data

### Data Validation
- Server-side validation through security rules
- Client-side validation in models
- Type safety through TypeScript interfaces

## Monitoring and Analytics

### Collection Usage Tracking
- Request volume and patterns
- User engagement metrics
- Service popularity analysis
- Technician performance metrics

### Error Handling
- Comprehensive error logging
- Fallback mechanisms for data access
- Cache recovery strategies
- Connection state management

---

This guide reflects the **actual implementation** in the Mr. Tech mobile application as of the current codebase analysis. The database structure is designed for scalability, performance, and cross-platform compatibility. 