import { logger } from './logger';
import type { ComponentPerformanceData } from '../types/common';

/**
 * Performance monitoring utilities for tracking app performance
 */

interface PerformanceMetrics {
  loadTime: number;
  domContentLoaded: number;
  firstContentfulPaint?: number;
  largestContentfulPaint?: number;
  firstInputDelay?: number;
  cumulativeLayoutShift?: number;
  timeToInteractive?: number;
}

interface ChunkLoadMetrics {
  chunkName: string;
  loadTime: number;
  size?: number;
  cached: boolean;
}

interface ComponentLoadMetrics {
  componentName: string;
  loadTime: number;
  renderTime: number;
  propKeys?: string[];
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics = {
    loadTime: 0,
    domContentLoaded: 0,
  };

  private chunkMetrics: ChunkLoadMetrics[] = [];
  private componentMetrics: ComponentLoadMetrics[] = [];
  private observers: PerformanceObserver[] = [];

  constructor() {
    this.initializeObservers();
    this.trackPageLoad();
  }

  /**
   * Initialize performance observers
   */
  private initializeObservers(): void {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
      return;
    }

    try {
      // Observe paint metrics
      const paintObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.name === 'first-contentful-paint') {
            this.metrics.firstContentfulPaint = entry.startTime;
          }
        }
      });
      paintObserver.observe({ entryTypes: ['paint'] });
      this.observers.push(paintObserver);

      // Observe largest contentful paint
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        this.metrics.largestContentfulPaint = lastEntry.startTime;
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      this.observers.push(lcpObserver);

      // Observe first input delay
      const fidObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          this.metrics.firstInputDelay = (entry as any).processingStart - entry.startTime;
        }
      });
      fidObserver.observe({ entryTypes: ['first-input'] });
      this.observers.push(fidObserver);

      // Observe layout shifts
      const clsObserver = new PerformanceObserver((list) => {
        let clsValue = 0;
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
          }
        }
        this.metrics.cumulativeLayoutShift = clsValue;
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
      this.observers.push(clsObserver);

      // Observe navigation timing
      const navigationObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          const navEntry = entry as PerformanceNavigationTiming;
          this.metrics.loadTime = navEntry.loadEventEnd - navEntry.navigationStart;
          this.metrics.domContentLoaded =
            navEntry.domContentLoadedEventEnd - navEntry.navigationStart;
          this.metrics.timeToInteractive = navEntry.domInteractive - navEntry.navigationStart;
        }
      });
      navigationObserver.observe({ entryTypes: ['navigation'] });
      this.observers.push(navigationObserver);
    } catch (error) {
      logger.warn('Failed to initialize performance observers', { error });
    }
  }

  /**
   * Track page load performance
   */
  private trackPageLoad(): void {
    if (typeof window === 'undefined') return;

    window.addEventListener('load', () => {
      setTimeout(() => {
        this.reportMetrics();
      }, 0);
    });

    // Track when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        this.metrics.domContentLoaded = performance.now();
      });
    } else {
      this.metrics.domContentLoaded = performance.now();
    }
  }

  /**
   * Track chunk loading performance
   */
  trackChunkLoad(chunkName: string, startTime: number, cached: boolean = false): void {
    const loadTime = performance.now() - startTime;

    const metric: ChunkLoadMetrics = {
      chunkName,
      loadTime,
      cached,
    };

    this.chunkMetrics.push(metric);

    logger.info('Chunk loaded', {
      chunk: chunkName,
      loadTime: Math.round(loadTime),
      cached,
    });

    // Report slow chunk loads
    if (loadTime > 1000) {
      logger.warn('Slow chunk load detected', metric);
    }
  }

  /**
   * Track component loading and rendering performance
   */
  trackComponentLoad(
    componentName: string,
    loadTime: number,
    renderTime: number,
    props?: Record<string, unknown>,
  ): void {
    const metric: ComponentLoadMetrics = {
      componentName,
      loadTime,
      renderTime,
      propKeys: props ? Object.keys(props) : undefined, // Only track prop keys for privacy
    };

    this.componentMetrics.push(metric);

    logger.debug('Component loaded', {
      component: componentName,
      loadTime: Math.round(loadTime),
      renderTime: Math.round(renderTime),
    });

    // Report slow component loads
    if (loadTime > 500) {
      logger.warn('Slow component load detected', metric);
    }
  }

  /**
   * Track memory usage
   */
  trackMemoryUsage(): void {
    if (
      typeof window === 'undefined' ||
      !('performance' in window) ||
      !(performance as any).memory
    ) {
      return;
    }

    const memory = (performance as any).memory;
    const memoryInfo = {
      usedJSHeapSize: Math.round(memory.usedJSHeapSize / 1024 / 1024), // MB
      totalJSHeapSize: Math.round(memory.totalJSHeapSize / 1024 / 1024), // MB
      jsHeapSizeLimit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024), // MB
    };

    logger.info('Memory usage', memoryInfo);

    // Warn if memory usage is high
    if (memoryInfo.usedJSHeapSize > 100) {
      logger.warn('High memory usage detected', memoryInfo);
    }
  }

  /**
   * Get current performance metrics
   */
  getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  /**
   * Get chunk loading metrics
   */
  getChunkMetrics(): ChunkLoadMetrics[] {
    return [...this.chunkMetrics];
  }

  /**
   * Get component loading metrics
   */
  getComponentMetrics(): ComponentLoadMetrics[] {
    return [...this.componentMetrics];
  }

  /**
   * Report all performance metrics
   */
  reportMetrics(): void {
    const metrics = this.getMetrics();

    logger.info('Performance metrics', {
      loadTime: Math.round(metrics.loadTime),
      domContentLoaded: Math.round(metrics.domContentLoaded),
      firstContentfulPaint: metrics.firstContentfulPaint
        ? Math.round(metrics.firstContentfulPaint)
        : undefined,
      largestContentfulPaint: metrics.largestContentfulPaint
        ? Math.round(metrics.largestContentfulPaint)
        : undefined,
      firstInputDelay: metrics.firstInputDelay ? Math.round(metrics.firstInputDelay) : undefined,
      cumulativeLayoutShift: metrics.cumulativeLayoutShift
        ? Math.round(metrics.cumulativeLayoutShift * 1000) / 1000
        : undefined,
      timeToInteractive: metrics.timeToInteractive
        ? Math.round(metrics.timeToInteractive)
        : undefined,
    });

    // Track memory usage
    this.trackMemoryUsage();

    // Report chunk metrics summary
    if (this.chunkMetrics.length > 0) {
      const totalChunkLoadTime = this.chunkMetrics.reduce(
        (sum, metric) => sum + metric.loadTime,
        0,
      );
      const cachedChunks = this.chunkMetrics.filter((metric) => metric.cached).length;

      logger.info('Chunk loading summary', {
        totalChunks: this.chunkMetrics.length,
        cachedChunks,
        totalLoadTime: Math.round(totalChunkLoadTime),
        averageLoadTime: Math.round(totalChunkLoadTime / this.chunkMetrics.length),
      });
    }
  }

  /**
   * Clean up observers
   */
  cleanup(): void {
    this.observers.forEach((observer) => observer.disconnect());
    this.observers = [];
  }
}

// Create singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Export types
export type { PerformanceMetrics, ChunkLoadMetrics, ComponentLoadMetrics };
