import React, { useCallback, useEffect, useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { AlertCircle, Clock, Star, TrendingUp, Users } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { useAsyncOperation } from '../hooks/useErrorHandler';
import ErrorDisplay from '../components/error/ErrorDisplay';
import { SkeletonCard, SkeletonList, SkeletonStats } from '../components/ui/skeleton';

// Import services
import requestService from '../services/requestService';
import technicianService from '../services/technicianService';
import { FirebaseService } from '../services/firebaseService';

// Import types
import { type RequestModel, RequestStatus } from '../types/request';
import { Timestamp } from 'firebase/firestore';

// Create review service
const reviewService = new FirebaseService('reviews');

// Helper function to convert Timestamp to Date
const toDate = (timestamp: Date | Timestamp | undefined | null): Date => {
  if (!timestamp) return new Date(0);
  if (timestamp instanceof Date) return timestamp;
  if (timestamp instanceof Timestamp) return timestamp.toDate();
  return new Date(0);
};

interface DashboardStats {
  activeRequests: number;
  totalTechnicians: number;
  completedToday: number;
  avgResponseTime: string;
  customerSatisfaction: number;
  totalReviews: number;
}

interface RecentActivity {
  id: string;
  type: 'request' | 'completion' | 'assignment';
  title: string;
  description: string;
  timestamp: Date;
}

interface RecentReview {
  id: string;
  customerName: string;
  technicianName: string;
  rating: number;
  comment: string;
  createdAt: Date;
}

const DashboardPage: React.FC = () => {
  const { user } = useAuth();

  // State management
  const [stats, setStats] = useState<DashboardStats>({
    activeRequests: 0,
    totalTechnicians: 0,
    completedToday: 0,
    avgResponseTime: '0 min',
    customerSatisfaction: 0,
    totalReviews: 0,
  });
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [recentReviews, setRecentReviews] = useState<RecentReview[]>([]);

  // Use async operation hook for data loading
  const dashboardDataOperation = useAsyncOperation<{
    stats: DashboardStats;
    activity: RecentActivity[];
    reviews: RecentReview[];
  }>();

  // Helper function to get today's date range
  const getTodayRange = () => {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);
    return { startOfDay, endOfDay };
  };

  // Helper function to format date for display
  const formatTimeAgo = (date: Date): string => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes} min ago`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
  };

  // Fetch dashboard statistics
  const fetchDashboardStats = useCallback(async (): Promise<DashboardStats> => {
    try {
      const { startOfDay, endOfDay } = getTodayRange();

      // Fetch all requests
      const allRequests = await requestService.getAll();

      // Fetch all technicians
      const allTechnicians = await technicianService.getAll();

      // Fetch recent reviews
      const allReviews = await reviewService.getAll();

      // Calculate active requests (pending, approved, in_progress)
      const activeRequests = allRequests.filter((request) =>
        [RequestStatus.PENDING, RequestStatus.APPROVED, RequestStatus.IN_PROGRESS].includes(
          request.status,
        ),
      ).length;

      // Calculate completed requests today
      const completedToday = allRequests.filter((request) => {
        if (request.status !== RequestStatus.COMPLETED) return false;
        const updatedAt = toDate(request.updated_at);
        return updatedAt >= startOfDay && updatedAt < endOfDay;
      }).length;

      // Calculate average response time (mock for now, would need proper tracking)
      const avgResponseTime = '24 min'; // TODO: Implement proper response time calculation

      // Calculate customer satisfaction from reviews
      let customerSatisfaction = 0;
      let totalReviews = 0;

      if (allReviews.length > 0) {
        const totalRating = allReviews.reduce(
          (sum: number, review: any) => sum + (review.rating || 0),
          0,
        );
        customerSatisfaction = Math.round((totalRating / allReviews.length) * 10) / 10;
        totalReviews = allReviews.length;
      }

      return {
        activeRequests,
        totalTechnicians: allTechnicians.length,
        completedToday,
        avgResponseTime,
        customerSatisfaction,
        totalReviews,
      };
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      throw error;
    }
  }, []);

  // Generate recent activity from requests
  const generateRecentActivity = (requests: RequestModel[]): RecentActivity[] => {
    const activities: RecentActivity[] = [];

    // Sort requests by updated date
    const sortedRequests = [...requests]
      .filter((request) => request.updated_at)
      .sort((a, b) => {
        const dateA = toDate(a.updated_at);
        const dateB = toDate(b.updated_at);
        return dateB.getTime() - dateA.getTime();
      })
      .slice(0, 5); // Get latest 5 activities

    sortedRequests.forEach((request) => {
      const updatedAt = toDate(request.updated_at);

      if (request.status === RequestStatus.PENDING && !request.technician_id) {
        activities.push({
          id: request.id,
          type: 'request',
          title: 'New request from customer',
          description: `${request.service_name || 'Service request'}`,
          timestamp: updatedAt,
        });
      } else if (request.status === RequestStatus.COMPLETED) {
        activities.push({
          id: request.id,
          type: 'completion',
          title: 'Service completed',
          description: `${request.service_name || 'Service'} - ${request.technician_name || 'Technician'}`,
          timestamp: updatedAt,
        });
      } else if (request.status === RequestStatus.APPROVED && request.technician_id) {
        activities.push({
          id: request.id,
          type: 'assignment',
          title: 'Request assigned',
          description: `Assigned to ${request.technician_name || 'technician'}`,
          timestamp: updatedAt,
        });
      }
    });

    return activities;
  };

  // Fetch recent reviews
  const fetchRecentReviews = async (): Promise<RecentReview[]> => {
    try {
      const allReviews = await reviewService.getAll({
        orderBy: { field: 'created_at', direction: 'desc' },
        limit: 5,
      });

      return allReviews.map(
        (review: any): RecentReview => ({
          id: review.id,
          customerName: review.customer_name || 'Customer',
          technicianName: review.technician_name || 'Technician',
          rating: review.rating || 0,
          comment: review.comment || '',
          createdAt: toDate(review.created_at),
        }),
      );
    } catch (error) {
      console.error('Error fetching recent reviews:', error);
      return [];
    }
  };

  // Load all dashboard data
  const loadDashboardData = useCallback(async () => {
    const result = await dashboardDataOperation.execute(
      async () => {
        // Fetch stats
        const dashboardStats = await fetchDashboardStats();

        // Fetch requests for activity generation
        const allRequests = await requestService.getAll();
        const activity = generateRecentActivity(allRequests);

        // Fetch recent reviews
        let reviews: RecentReview[] = [];
        if (user?.role === 'admin') {
          reviews = await fetchRecentReviews();
        }

        return {
          stats: dashboardStats,
          activity,
          reviews,
        };
      },
      {
        onSuccess: (data) => {
          setStats(data.stats);
          setRecentActivity(data.activity);
          setRecentReviews(data.reviews);
        },
      },
    );

    return result;
  }, [user?.role, fetchDashboardStats, dashboardDataOperation.execute]);

  // Load data on component mount
  useEffect(() => {
    loadDashboardData();
  }, [loadDashboardData]);

  // Display loading state
  if (dashboardDataOperation.isLoading) {
    return (
      <div className='space-y-6'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>Dashboard</h1>
          <p className='text-muted-foreground mt-2'>Loading dashboard data...</p>
        </div>

        {/* Stats skeleton */}
        <SkeletonStats cards={4} />

        {/* Recent activity skeleton */}
        <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-7'>
          <SkeletonCard className='md:col-span-4' showHeader={true} />
          <SkeletonCard className='md:col-span-3' showHeader={true} />
        </div>

        {/* Recent reviews skeleton (admin only) */}
        {user?.role === 'admin' && <SkeletonList items={3} showAvatar={true} />}
      </div>
    );
  }

  // Display error state
  if (dashboardDataOperation.isError && dashboardDataOperation.error) {
    return (
      <div className='space-y-6'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>Dashboard</h1>
          <p className='text-muted-foreground mt-2'>Welcome back, {user?.name}</p>
        </div>
        <ErrorDisplay
          error={dashboardDataOperation.error}
          title='Failed to Load Dashboard'
          description="We couldn't load your dashboard data. Please try again."
          showRetry={true}
          onRetry={loadDashboardData}
          type='server'
          severity='medium'
        />
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      <div>
        <h1 className='text-3xl font-bold tracking-tight'>Dashboard</h1>
        <p className='text-muted-foreground mt-2'>Welcome back, {user?.name}</p>
      </div>

      {/* Stats Grid */}
      <div className='grid gap-4 sm:grid-cols-2 lg:grid-cols-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Active Requests</CardTitle>
            <AlertCircle className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.activeRequests}</div>
            <p className='text-xs text-muted-foreground'>Pending, approved & in progress</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>
              {user?.role === 'admin' ? 'Total Technicians' : 'Completed Today'}
            </CardTitle>
            <Users className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {user?.role === 'admin' ? stats.totalTechnicians : stats.completedToday}
            </div>
            <p className='text-xs text-muted-foreground'>
              {user?.role === 'admin' ? 'Registered technicians' : 'Services completed today'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Avg Response Time</CardTitle>
            <Clock className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{stats.avgResponseTime}</div>
            <p className='text-xs text-muted-foreground'>Average technician response</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Customer Satisfaction</CardTitle>
            <Star className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='flex items-center space-x-2'>
              <div className='text-2xl font-bold'>
                {stats.customerSatisfaction > 0 ? stats.customerSatisfaction.toFixed(1) : 'N/A'}
              </div>
              {stats.customerSatisfaction > 0 && (
                <div className='flex items-center ml-2'>
                  {[1, 2, 3, 4, 5].map((star) => (
                    <Star
                      key={star}
                      className={`h-4 w-4 ${
                        star <= Math.round(stats.customerSatisfaction)
                          ? 'text-yellow-400 fill-yellow-400'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
              )}
            </div>
            <p className='text-xs text-muted-foreground'>
              {stats.totalReviews > 0 ? `Based on ${stats.totalReviews} reviews` : 'No reviews yet'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
          <CardDescription>
            {user?.role === 'admin' ? 'Latest system activities' : 'Your recent service requests'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            {recentActivity.length === 0 ? (
              <div className='text-center py-8'>
                <TrendingUp className='h-12 w-12 text-muted-foreground mx-auto mb-4' />
                <p className='text-muted-foreground'>No recent activity</p>
              </div>
            ) : (
              recentActivity.map((activity) => (
                <div
                  key={activity.id}
                  className='flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4'
                >
                  <Badge
                    variant={
                      activity.type === 'request'
                        ? 'default'
                        : activity.type === 'completion'
                          ? 'secondary'
                          : 'outline'
                    }
                    className='w-fit'
                  >
                    {activity.type === 'request'
                      ? 'New'
                      : activity.type === 'completion'
                        ? 'Completed'
                        : 'Assigned'}
                  </Badge>
                  <div className='flex-1 space-y-1'>
                    <p className='text-sm font-medium leading-none'>{activity.title}</p>
                    <p className='text-sm text-muted-foreground'>{activity.description}</p>
                  </div>
                  <div className='text-sm text-muted-foreground sm:text-right'>
                    {formatTimeAgo(activity.timestamp)}
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Recent Reviews */}
      {user?.role === 'admin' && (
        <Card>
          <CardHeader>
            <CardTitle>Recent Reviews</CardTitle>
            <CardDescription>Latest customer feedback</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              {recentReviews.length === 0 ? (
                <div className='text-center py-8'>
                  <Star className='h-12 w-12 text-muted-foreground mx-auto mb-4' />
                  <p className='text-muted-foreground'>No reviews yet</p>
                  <p className='text-sm text-muted-foreground mt-2'>
                    Reviews will appear here once customers rate completed services
                  </p>
                </div>
              ) : (
                recentReviews.map((review) => (
                  <div
                    key={review.id}
                    className='flex flex-col sm:flex-row sm:items-start gap-3 sm:gap-4 p-4 bg-muted/50 rounded-lg'
                  >
                    <div className='flex items-center'>
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star
                          key={star}
                          className={`h-4 w-4 ${
                            star <= review.rating
                              ? 'text-yellow-400 fill-yellow-400'
                              : 'text-gray-300'
                          }`}
                        />
                      ))}
                      <span className='ml-2 text-sm font-medium'>{review.rating}/5</span>
                    </div>
                    <div className='flex-1 space-y-2'>
                      <p className='text-sm font-medium leading-none'>{review.customerName}</p>
                      {review.comment && (
                        <p className='text-sm text-muted-foreground italic'>"{review.comment}"</p>
                      )}
                      <p className='text-xs text-muted-foreground'>
                        Technician: {review.technicianName} • {formatTimeAgo(review.createdAt)}
                      </p>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Actions */}
      <div>
        <h2 className='text-lg font-semibold mb-4'>Quick Actions</h2>
        <div className='grid gap-4 sm:grid-cols-2 lg:grid-cols-3'>
          <Card
            className='cursor-pointer hover:bg-accent hover:shadow-md transition-all duration-200 group'
            onClick={() => (window.location.href = '/requests')}
            role='button'
            tabIndex={0}
            onKeyDown={(e) => e.key === 'Enter' && (window.location.href = '/requests')}
          >
            <CardContent className='flex items-center p-4'>
              <AlertCircle className='h-5 w-5 mr-3 text-primary group-hover:scale-110 transition-transform' />
              <span className='font-medium'>View All Requests</span>
            </CardContent>
          </Card>

          {user?.role === 'admin' && (
            <>
              <Card
                className='cursor-pointer hover:bg-accent hover:shadow-md transition-all duration-200 group'
                onClick={() => (window.location.href = '/technicians')}
                role='button'
                tabIndex={0}
                onKeyDown={(e) => e.key === 'Enter' && (window.location.href = '/technicians')}
              >
                <CardContent className='flex items-center p-4'>
                  <Users className='h-5 w-5 mr-3 text-primary group-hover:scale-110 transition-transform' />
                  <span className='font-medium'>Manage Technicians</span>
                </CardContent>
              </Card>

              <Card
                className='cursor-pointer hover:bg-accent hover:shadow-md transition-all duration-200 group'
                onClick={() => loadDashboardData()}
                role='button'
                tabIndex={0}
                onKeyDown={(e) => e.key === 'Enter' && loadDashboardData()}
              >
                <CardContent className='flex items-center p-4'>
                  <TrendingUp className='h-5 w-5 mr-3 text-primary group-hover:scale-110 transition-transform' />
                  <span className='font-medium'>Refresh Data</span>
                </CardContent>
              </Card>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
