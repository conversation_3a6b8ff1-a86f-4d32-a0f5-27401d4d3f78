# RTL Support Guidelines for Mr.Tech Mobile App

This document outlines the standards and best practices for implementing Right-to-Left (RTL) support in the Mr.Tech mobile app.

## Overview

The Mr.Tech app fully supports both LTR (Left-to-Right, English) and RTL (Right-to-Left, Arabic) layouts. All UI components and screens are designed to adapt automatically based on the current locale.

## Key Implementation Details

### 1. Directionality

The app applies RTL directionality at two levels:

- **App-level** - In `main.dart`, we wrap the entire app in a `Directionality` widget:
  ```dart
  builder: (context, child) {
    return Directionality(
      textDirection: _translationService.textDirection,
      child: child!,
    );
  }
  ```

- **Navigation-level** - In `main_navigation.dart`, we add another `Directionality` wrapper to ensure proper RTL handling:
  ```dart
  return Directionality(
    textDirection: isRtl ? TextDirection.rtl : TextDirection.ltr,
    child: Scaffold(...)
  );
  ```

### 2. TranslationService

The `TranslationService` provides the following RTL-related properties:

- `bool isRtl` - Returns true when the current locale is Arabic
- `TextDirection textDirection` - Returns the appropriate `TextDirection` based on the current locale

### 3. RTL-Aware Components

For custom layouts, consider the following guidelines:

- **Avoid Explicit TextDirection:** Don't manually set `textDirection` property on Rows, Columns or other widgets
- **Use Directionality:** For custom widgets or sections, use the Directionality widget if necessary
- **Mirrored Positioning:** When using Positioned widgets, adjust left/right values based on isRtl:
  ```dart
  Positioned(
    right: isRtl ? null : 0,
    left: isRtl ? 0 : null,
    child: yourWidget,
  )
  ```

### 4. Animations

For slide animations that should respect RTL layout:

```dart
SlideTransition(
  position: Tween<Offset>(
    begin: Offset(isRtl ? -0.5 : 0.5, 0),
    end: Offset.zero,
  ).animate(animation),
  child: yourWidget,
)
```

## Testing RTL Support

To thoroughly test RTL support:

1. Toggle between English and Arabic using the language selector
2. Verify all screens render correctly in both directions
3. Test navigation flows in both language modes
4. Ensure animations work properly in both directions
5. Verify text alignment and truncation is correct

## Common Issues and Solutions

- **Text Alignment:** Use `textAlign: TextAlign.start` instead of `TextAlign.left` for proper RTL support
- **Icons:** Some icons may need to be mirrored in RTL mode using `Transform.scale(scaleX: isRtl ? -1 : 1)`
- **Padding/Margin:** Use `EdgeInsetsDirectional` instead of `EdgeInsets` for RTL-aware padding
- **ListView:** Set `reverse: isRtl` for horizontal lists that should respect reading direction

Following these guidelines will ensure a consistent user experience for both Arabic and English users. 