import React, { type ReactNode, createContext, useContext, useEffect, useState } from 'react';
import authService from '../services/authService';
import sessionService from '../services/sessionService';
import { type LoginCredentials, type SignupData, type User, type UserRole } from '../types/auth';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  login: (credentials: LoginCredentials & { rememberMe?: boolean }) => Promise<void>;
  signup: (data: SignupData) => Promise<void>;
  logout: () => Promise<void>;
  logoutAllDevices: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  clearError: () => void;
  hasRole: (roles: UserRole[]) => boolean;
  updateProfile: (data: Partial<User>) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Initialize session service
    sessionService.initialize();

    // Subscribe to auth state changes
    const unsubscribe = authService.onAuthStateChange((user) => {
      setUser(user);
      setLoading(false);

      // Update session activity when user is detected
      if (user) {
        sessionService.updateSessionActivity(user.uid);
      }
    });

    return unsubscribe;
  }, []);

  const login = async (credentials: LoginCredentials & { rememberMe?: boolean }) => {
    try {
      setError(null);
      setLoading(true);
      const user = await authService.login(credentials);
      setUser(user);
    } catch (err: any) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const signup = async (data: SignupData) => {
    try {
      setError(null);
      setLoading(true);
      const newUser = await authService.signup(data);
      // Don't set the new user as current user
      // Admin remains logged in after creating new accounts
    } catch (err: any) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    try {
      setError(null);
      setLoading(true);
      await authService.logout();
      setUser(null);
    } catch (err: any) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const logoutAllDevices = async () => {
    try {
      setError(null);
      setLoading(true);
      await authService.logoutAllDevices();
      setUser(null);
    } catch (err: any) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const resetPassword = async (email: string) => {
    try {
      setError(null);
      await authService.resetPassword(email);
    } catch (err: any) {
      setError(err.message);
      throw err;
    }
  };

  const clearError = () => {
    setError(null);
  };

  const hasRole = (roles: UserRole[]) => {
    return authService.hasRole(user, roles);
  };

  const updateProfile = async (data: Partial<User>) => {
    if (!user) throw new Error('No user logged in');

    try {
      setError(null);
      await authService.updateUserProfile(user.uid, data);
      // Refresh user data
      const updatedUser = await authService.getCurrentUser();
      if (updatedUser) {
        setUser(updatedUser);
      }
    } catch (err: any) {
      setError(err.message);
      throw err;
    }
  };

  const value: AuthContextType = {
    user,
    loading,
    error,
    login,
    signup,
    logout,
    logoutAllDevices,
    resetPassword,
    clearError,
    hasRole,
    updateProfile,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export default AuthContext;
