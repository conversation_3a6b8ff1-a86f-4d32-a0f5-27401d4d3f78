<?php
/**
 * Mr.Tech Contact Form Handler
 * Processes form submissions from the landing page and sends email notifications
 */

// Set error reporting for debugging (remove in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define variables and set to empty values
$name = $email = $subject = $message = '';
$response = ['success' => false, 'message' => ''];

// Process only POST requests
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Validate and sanitize inputs
    $name = filter_input(INPUT_POST, 'name', FILTER_SANITIZE_STRING);
    $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
    $subject = filter_input(INPUT_POST, 'subject', FILTER_SANITIZE_STRING);
    $message = filter_input(INPUT_POST, 'message', FILTER_SANITIZE_STRING);
    
    // Validate email
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $response['message'] = 'Invalid email format';
        outputResponse($response);
        exit;
    }
    
    // Check for empty fields
    if (empty($name) || empty($email) || empty($message)) {
        $response['message'] = 'Please fill all required fields';
        outputResponse($response);
        exit;
    }
    
    // Set recipient email (change to your email address)
    $to = '<EMAIL>';
    
    // Set email subject
    $emailSubject = !empty($subject) ? "Mr.Tech Contact: $subject" : "Mr.Tech Contact Form Submission";
    
    // Build email content
    $emailContent = "Name: $name\n";
    $emailContent .= "Email: $email\n";
    $emailContent .= "Subject: $subject\n\n";
    $emailContent .= "Message:\n$message\n";
    
    // Set headers
    $headers = "From: $email\r\n";
    $headers .= "Reply-To: $email\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion();
    
    // Attempt to send email
    try {
        if (mail($to, $emailSubject, $emailContent, $headers)) {
            $response['success'] = true;
            $response['message'] = 'Thank you! Your message has been sent.';
        } else {
            $response['message'] = 'Sorry, there was an error sending your message.';
        }
    } catch (Exception $e) {
        $response['message'] = 'Error: ' . $e->getMessage();
    }
    
    // Output response
    outputResponse($response);
    exit;
}

/**
 * Output JSON response and set appropriate headers
 * 
 * @param array $data Response data
 */
function outputResponse($data) {
    header('Content-Type: application/json');
    echo json_encode($data);
}
?> 