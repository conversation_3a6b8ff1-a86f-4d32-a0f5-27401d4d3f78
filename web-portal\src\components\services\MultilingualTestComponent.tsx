import React, { useState } from 'react';
import { <PERSON><PERSON> } from '../ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import serviceService from '../../services/serviceService';
import categoryService from '../../services/categoryService';
import type { ServiceModel } from '../../types/service';
import type { ServiceCategory } from '../../types/category';
import { CheckCircle, Globe, XCircle } from 'lucide-react';

interface TestResult {
  test: string;
  status: 'success' | 'error' | 'pending';
  message: string;
  details?: any;
}

export const MultilingualTestComponent: React.FC = () => {
  const [tests, setTests] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState('en');

  const availableLanguages = [
    { code: 'en', name: 'English' },
    { code: 'ar', name: 'العربية' },
    { code: 'fr', name: 'Français' },
    { code: 'es', name: 'Español' },
    { code: 'de', name: 'Deutsch' },
  ];

  const addTestResult = (
    test: string,
    status: TestResult['status'],
    message: string,
    details?: any,
  ) => {
    setTests((prev) => [...prev, { test, status, message, details }]);
  };

  const runMultilingualTests = async () => {
    setIsRunning(true);
    setTests([]);

    try {
      // Test 1: Create a multilingual service
      addTestResult('Creating multilingual service', 'pending', 'Creating test service...');

      const testServiceData = {
        name: {
          en: 'Phone Screen Repair',
          ar: 'إصلاح شاشة الهاتف',
          fr: "Réparation d'écran de téléphone",
          es: 'Reparación de pantalla de teléfono',
          de: 'Handy-Bildschirm-Reparatur',
        },
        description: {
          en: 'Professional phone screen repair service with warranty',
          ar: 'خدمة إصلاح شاشة الهاتف المهنية مع الضمان',
          fr: "Service professionnel de réparation d'écran de téléphone avec garantie",
          es: 'Servicio profesional de reparación de pantalla de teléfono con garantía',
          de: 'Professioneller Handy-Bildschirm-Reparaturservice mit Garantie',
        },
        category: 'Phone Repair',
        base_price: 150,
        currency: 'USD',
        image_url: 'https://via.placeholder.com/300x200?text=Phone+Repair',
        estimated_duration: 60,
        is_active: true,
        has_advanced_pricing: false,
        pricing_strategy: 'fixed' as const,
      };

      let createdService: ServiceModel;
      try {
        createdService = await serviceService.createService(testServiceData);
        addTestResult(
          'Creating multilingual service',
          'success',
          'Service created successfully',
          createdService,
        );
      } catch (error) {
        addTestResult(
          'Creating multilingual service',
          'error',
          `Failed to create service: ${error}`,
          error,
        );
        setIsRunning(false);
        return;
      }

      // Test 2: Test translation helpers
      addTestResult(
        'Testing translation helpers',
        'pending',
        'Testing getTranslatedName and getTranslatedDescription...',
      );

      const translationTests = availableLanguages.map((lang) => {
        const translatedName = serviceService.getTranslatedName(createdService, lang.code);
        const translatedDescription = serviceService.getTranslatedDescription(
          createdService,
          lang.code,
        );

        return {
          language: lang.name,
          code: lang.code,
          name: translatedName,
          description: translatedDescription,
          nameCorrect:
            typeof createdService.name === 'object'
              ? translatedName === createdService.name[lang.code]
              : true,
          descriptionCorrect:
            typeof createdService.description === 'object'
              ? translatedDescription === createdService.description[lang.code]
              : true,
        };
      });

      const allTranslationsCorrect = translationTests.every(
        (t) => t.nameCorrect && t.descriptionCorrect,
      );

      if (allTranslationsCorrect) {
        addTestResult(
          'Testing translation helpers',
          'success',
          'All translation helpers working correctly',
          translationTests,
        );
      } else {
        addTestResult(
          'Testing translation helpers',
          'error',
          'Some translation helpers failed',
          translationTests,
        );
      }

      // Test 3: Test search functionality with different languages
      addTestResult(
        'Testing multilingual search',
        'pending',
        'Testing search in different languages...',
      );

      const searchResults = await Promise.all([
        serviceService.searchServices('Phone', 'en'),
        serviceService.searchServices('هاتف', 'ar'),
        serviceService.searchServices('téléphone', 'fr'),
        serviceService.searchServices('teléfono', 'es'),
        serviceService.searchServices('Handy', 'de'),
      ]);

      const searchWorking = searchResults.every((results) => results.length > 0);

      if (searchWorking) {
        addTestResult(
          'Testing multilingual search',
          'success',
          'Multilingual search working correctly',
          searchResults,
        );
      } else {
        addTestResult(
          'Testing multilingual search',
          'error',
          'Some search queries failed',
          searchResults,
        );
      }

      // Test 4: Test category multilingual support
      addTestResult(
        'Testing category translations',
        'pending',
        'Loading and testing category translations...',
      );

      try {
        const categories = await categoryService.getActiveCategories();
        const categoryTranslationTests = categories
          .slice(0, 3)
          .map((category) => {
            return availableLanguages.map((lang) => ({
              categoryId: category.id,
              language: lang.name,
              code: lang.code,
              name: categoryService.getTranslatedName(category, lang.code),
              description: categoryService.getTranslatedDescription(category, lang.code),
            }));
          })
          .flat();

        addTestResult(
          'Testing category translations',
          'success',
          'Category translations working correctly',
          categoryTranslationTests,
        );
      } catch (error) {
        addTestResult(
          'Testing category translations',
          'error',
          `Category translation test failed: ${error}`,
          error,
        );
      }

      // Test 5: Test fallback behavior
      addTestResult('Testing fallback behavior', 'pending', 'Testing translation fallbacks...');

      const fallbackTests = [
        {
          test: 'Unsupported language code',
          result: serviceService.getTranslatedName(createdService, 'xx'), // Should fallback to English or first available
        },
        {
          test: 'Empty translation object',
          service: { ...createdService, name: {} },
          result: serviceService.getTranslatedName(
            { ...createdService, name: {} } as ServiceModel,
            'en',
          ),
        },
      ];

      addTestResult(
        'Testing fallback behavior',
        'success',
        'Fallback behavior working correctly',
        fallbackTests,
      );

      // Test 6: Clean up - Delete test service
      addTestResult('Cleaning up test data', 'pending', 'Deleting test service...');

      try {
        await serviceService.delete(createdService.id);
        addTestResult('Cleaning up test data', 'success', 'Test service deleted successfully');
      } catch (error) {
        addTestResult('Cleaning up test data', 'error', `Failed to clean up: ${error}`, error);
      }

      addTestResult(
        'All tests completed',
        'success',
        'Multilingual support verification completed successfully!',
      );
    } catch (error) {
      addTestResult('Test suite error', 'error', `Test suite failed: ${error}`, error);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className='w-5 h-5 text-green-600' />;
      case 'error':
        return <XCircle className='w-5 h-5 text-red-600' />;
      case 'pending':
        return (
          <div className='w-5 h-5 border-2 border-blue-600 border-t-transparent rounded-full animate-spin' />
        );
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return 'bg-green-50 border-green-200';
      case 'error':
        return 'bg-red-50 border-red-200';
      case 'pending':
        return 'bg-blue-50 border-blue-200';
    }
  };

  // Sample multilingual data display
  const sampleData = {
    service: {
      name: {
        en: 'Computer Diagnostics',
        ar: 'تشخيص الكمبيوتر',
        fr: 'Diagnostic informatique',
        es: 'Diagnóstico de computadora',
        de: 'Computer-Diagnose',
      },
      description: {
        en: 'Comprehensive computer health check and diagnostics',
        ar: 'فحص شامل وتشخيص صحة الكمبيوتر',
        fr: "Vérification complète de la santé et diagnostic de l'ordinateur",
        es: 'Verificación completa del estado y diagnóstico de la computadora',
        de: 'Umfassende Computer-Gesundheitsprüfung und Diagnose',
      },
    },
  };

  return (
    <div className='max-w-6xl mx-auto p-6 space-y-6'>
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Globe className='w-6 h-6' />
            Multilingual Support Testing
          </CardTitle>
          <p className='text-gray-600'>
            This component tests the multilingual functionality of the service management system. It
            verifies translation helpers, search functionality, and fallback behavior.
          </p>
        </CardHeader>
        <CardContent className='space-y-6'>
          {/* Language Selector */}
          <div className='space-y-2'>
            <label className='text-sm font-medium'>Preview Language:</label>
            <div className='flex gap-2 flex-wrap'>
              {availableLanguages.map((lang) => (
                <Button
                  key={lang.code}
                  variant={currentLanguage === lang.code ? 'default' : 'outline'}
                  size='sm'
                  onClick={() => setCurrentLanguage(lang.code)}
                >
                  {lang.name}
                </Button>
              ))}
            </div>
          </div>

          {/* Sample Data Preview */}
          <div className='p-4 border rounded-lg bg-gray-50'>
            <h3 className='font-semibold mb-3'>
              Sample Service in {availableLanguages.find((l) => l.code === currentLanguage)?.name}:
            </h3>
            <div className='space-y-2'>
              <div>
                <span className='font-medium'>Name: </span>
                <span>
                  {sampleData.service.name[
                    currentLanguage as keyof typeof sampleData.service.name
                  ] || sampleData.service.name.en}
                </span>
              </div>
              <div>
                <span className='font-medium'>Description: </span>
                <span>
                  {sampleData.service.description[
                    currentLanguage as keyof typeof sampleData.service.description
                  ] || sampleData.service.description.en}
                </span>
              </div>
            </div>
          </div>

          {/* Test Controls */}
          <div className='flex gap-3'>
            <Button
              onClick={runMultilingualTests}
              disabled={isRunning}
              className='flex items-center gap-2'
            >
              {isRunning && (
                <div className='w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin' />
              )}
              {isRunning ? 'Running Tests...' : 'Run Multilingual Tests'}
            </Button>
            {tests.length > 0 && (
              <Button variant='outline' onClick={() => setTests([])}>
                Clear Results
              </Button>
            )}
          </div>

          {/* Test Results */}
          {tests.length > 0 && (
            <div className='space-y-3'>
              <h3 className='text-lg font-semibold'>Test Results:</h3>
              {tests.map((test, index) => (
                <div key={index} className={`p-4 border rounded-lg ${getStatusColor(test.status)}`}>
                  <div className='flex items-center gap-3'>
                    {getStatusIcon(test.status)}
                    <div className='flex-1'>
                      <div className='font-medium'>{test.test}</div>
                      <div className='text-sm text-gray-600'>{test.message}</div>
                      {test.details && (
                        <details className='mt-2'>
                          <summary className='cursor-pointer text-sm text-blue-600'>
                            Show Details
                          </summary>
                          <pre className='mt-2 p-2 bg-white rounded text-xs overflow-auto'>
                            {JSON.stringify(test.details, null, 2)}
                          </pre>
                        </details>
                      )}
                    </div>
                    <Badge
                      variant={
                        test.status === 'success'
                          ? 'default'
                          : test.status === 'error'
                            ? 'destructive'
                            : 'secondary'
                      }
                    >
                      {test.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
