import { Timestamp, addDoc, collection, getDoc, getDocs, limit, query } from 'firebase/firestore';
import { db } from '../config/firebase';
import type { PaymentTransaction } from '../types/payment';
import logger from './logger';

/**
 * Add test payment data to Firestore for testing the payments page
 */
export const addTestPaymentData = async () => {
  try {
    const testTransactions: Omit<PaymentTransaction, 'id'>[] = [
      {
        paymobTransactionId: '12345678',
        requestId: 'req_test_001',
        customerId: 'customer_001',
        amount: 150.0,
        currency: 'EGP',
        status: 'completed',
        paymentMethod: 'paymob',
        merchantOrderId: 'order_001',
        integrationId: '1164997',
        isLive: false,
        errorOccurred: false,
        txnResponseCode: 'APPROVED',
        dataMessage: 'Transaction completed successfully',
        createdAt: new Date(),
        processedAt: new Date(),
        callbackData: {
          obj: {
            id: 12345678,
            success: true,
            amount_cents: 15000,
            currency: 'EGP',
          },
        },
      },
      {
        paymobTransactionId: '12345679',
        requestId: 'req_test_002',
        customerId: 'customer_002',
        amount: 200.0,
        currency: 'EGP',
        status: 'failed',
        paymentMethod: 'paymob',
        merchantOrderId: 'order_002',
        integrationId: '1164997',
        isLive: false,
        errorOccurred: true,
        txnResponseCode: 'DECLINED',
        dataMessage: 'Insufficient funds',
        createdAt: new Date(Date.now() - 86400000), // 1 day ago
        processedAt: new Date(Date.now() - 86400000),
        callbackData: {
          obj: {
            id: 12345679,
            success: false,
            amount_cents: 20000,
            currency: 'EGP',
          },
        },
      },
      {
        paymobTransactionId: '12345680',
        requestId: 'req_test_003',
        customerId: 'customer_003',
        amount: 75.5,
        currency: 'EGP',
        status: 'pending',
        paymentMethod: 'paymob',
        merchantOrderId: 'order_003',
        integrationId: '1164997',
        isLive: true,
        errorOccurred: false,
        txnResponseCode: 'PENDING',
        dataMessage: 'Transaction is being processed',
        createdAt: new Date(Date.now() - 3600000), // 1 hour ago
        processedAt: new Date(Date.now() - 3600000),
        callbackData: {
          obj: {
            id: 12345680,
            success: false,
            amount_cents: 7550,
            currency: 'EGP',
          },
        },
      },
    ];

    logger.info('Adding test payment transactions...', undefined, 'TEST');

    for (const transaction of testTransactions) {
      const docRef = await addDoc(collection(db, 'payment_transactions'), {
        ...transaction,
        createdAt: Timestamp.fromDate(transaction.createdAt),
        processedAt: Timestamp.fromDate(transaction.processedAt),
      });
      logger.info('Added test transaction', { id: docRef.id }, 'TEST');
    }

    logger.info('✅ Test payment data added successfully!', undefined, 'TEST');
    return true;
  } catch (error) {
    console.error('❌ Error adding test payment data:', error);
    throw error;
  }
};

/**
 * Migrate payment responses to payment transactions
 */
export const migratePaymentResponses = async () => {
  try {
    logger.info('🔄 Migrating payment responses to payment transactions...', undefined, 'TEST');

    const { getDocs, query, collection, doc, setDoc, Timestamp } = await import(
      'firebase/firestore'
    );

    // Get all payment responses
    const responsesQuery = query(collection(db, 'payment_responses'));
    const responsesSnapshot = await getDocs(responsesQuery);

    logger.info('📋 Found payment responses to migrate', { count: responsesSnapshot.size }, 'TEST');

    for (const responseDoc of responsesSnapshot.docs) {
      const responseData = responseDoc.data();
      const transactionId = responseData.transactionId;

      if (!transactionId) {
        logger.warn('⚠️ Skipping response without transaction ID', { id: responseDoc.id }, 'TEST');
        continue;
      }

      const paymentTransactionId = `paymob_${transactionId}`;

      // Check if transaction already exists
      const transactionRef = doc(db, 'payment_transactions', paymentTransactionId);
      const existingTransaction = await getDoc(transactionRef);

      if (!existingTransaction.exists()) {
        // Create new transaction from response data
        const paymentTransaction = {
          id: paymentTransactionId,
          paymobTransactionId: transactionId.toString(),
          requestId: responseData.merchantOrderId?.startsWith('mr_tech_')
            ? responseData.merchantOrderId
            : undefined,
          amount: responseData.amountCents ? parseFloat(responseData.amountCents) / 100 : 0,
          currency: responseData.currency || 'EGP',
          status:
            responseData.paymentStatus ||
            (responseData.success === 'true' ? 'completed' : 'failed'),
          paymentMethod: 'paymob',
          merchantOrderId: responseData.merchantOrderId,
          integrationId: responseData.integrationId,
          isLive: true,
          errorOccurred: responseData.errorOccurred === 'true',
          txnResponseCode: responseData.txnResponseCode,
          dataMessage: responseData.dataMessage,
          createdAt: responseData.createdAt || Timestamp.now(),
          processedAt: responseData.createdAt || Timestamp.now(),
          responseReceived: true,
          responseReceivedAt: responseData.createdAt || Timestamp.now(),
          callbackData: { responseParams: responseData.responseParams },
          migratedFromResponse: true,
          migratedAt: Timestamp.now(),
        };

        await setDoc(transactionRef, paymentTransaction);
        logger.info('✅ Migrated transaction', { id: paymentTransactionId }, 'TEST');
      } else {
        logger.info('⏭️ Transaction already exists', { id: paymentTransactionId }, 'TEST');
      }
    }

    logger.info('✅ Payment response migration completed!', undefined, 'TEST');
    return true;
  } catch (error) {
    console.error('❌ Error migrating payment responses:', error);
    throw error;
  }
};

/**
 * Check Firebase connection and permissions
 */
export const testFirebaseConnection = async () => {
  try {
    logger.info('Testing Firebase connection...', undefined, 'TEST');

    // Try to read from both collections
    const transactionsQuery = query(collection(db, 'payment_transactions'), limit(10));
    const responsesQuery = query(collection(db, 'payment_responses'), limit(10));

    const [transactionsSnapshot, responsesSnapshot] = await Promise.all([
      getDocs(transactionsQuery),
      getDocs(responsesQuery),
    ]);

    logger.info('✅ Firebase connection successful!', undefined, 'TEST');
    logger.info('📊 Found payment transactions', { count: transactionsSnapshot.size }, 'TEST');
    logger.info('📋 Found payment responses', { count: responsesSnapshot.size }, 'TEST');

    if (transactionsSnapshot.empty && responsesSnapshot.empty) {
      logger.info('📝 No payment data found. You may need to:', undefined, 'TEST');
      logger.info('1. Make a test payment from the mobile app', undefined, 'TEST');
      logger.info('2. Or run addTestPaymentData() to add test data', undefined, 'TEST');
    } else {
      if (transactionsSnapshot.size > 0) {
        logger.info('💳 Sample transaction data:', undefined, 'TEST');
        transactionsSnapshot.docs.slice(0, 2).forEach((doc) => {
          const data = doc.data();
          logger.info(
            'Transaction sample',
            { id: doc.id, status: data.status, amount: data.amount, currency: data.currency },
            'TEST',
          );
        });
      }

      if (responsesSnapshot.size > 0) {
        logger.info('📨 Sample response data:', undefined, 'TEST');
        responsesSnapshot.docs.slice(0, 2).forEach((doc) => {
          const data = doc.data();
          logger.info(
            'Response sample',
            { id: doc.id, status: data.paymentStatus, amount: data.amountCents },
            'TEST',
          );
        });

        if (responsesSnapshot.size > 0 && transactionsSnapshot.size === 0) {
          logger.info(
            '🔄 Found payment responses but no transactions. Run migratePaymentResponses() to migrate them.',
            undefined,
            'TEST',
          );
        }
      }
    }

    return true;
  } catch (error) {
    console.error('❌ Firebase connection failed:', error);
    throw error;
  }
};
