const fs = require('fs');
const { execSync } = require('child_process');
const path = require('path');
const readline = require('readline');

// Path to save the service account key
const keyPath = path.join(__dirname, '..', 'firebase-admin-key.json');

// Function to generate the service account key
async function generateServiceAccountKey() {
  try {
    console.log('Generating Firebase Admin SDK service account key...');
    
    // Get the project ID from .firebaserc
    const firebaseRcPath = path.join(__dirname, '..', '.firebaserc');
    const firebaseRcContent = fs.readFileSync(firebaseRcPath, 'utf8');
    const projectIdMatch = firebaseRcContent.match(/"default":\s*"([^"]+)"/);
    
    if (!projectIdMatch) {
      console.error('Could not find project ID in .firebaserc');
      process.exit(1);
    }
    
    const projectId = projectIdMatch[1];
    console.log(`Using Firebase project: ${projectId}`);
    
    // Check if user is logged in to Firebase
    try {
      const accountInfo = execSync('firebase login:list --json', { encoding: 'utf8' });
      const parsedInfo = JSON.parse(accountInfo);
      
      if (!parsedInfo.length) {
        console.log('No Firebase account logged in. Please login first:');
        execSync('firebase login', { stdio: 'inherit' });
      } else {
        console.log(`Logged in as: ${parsedInfo[0].user.email}`);
      }
    } catch (error) {
      console.log('Error checking login status, attempting to login:');
      execSync('firebase login', { stdio: 'inherit' });
    }
    
    // Since automatic service account creation might not work, provide manual instructions
    console.log('\nPlease follow these manual steps to create a service account key:');
    console.log(`1. Go to Firebase Console: https://console.firebase.google.com/project/${projectId}/settings/serviceaccounts/adminsdk`);
    console.log('2. Click "Generate new private key"');
    console.log(`3. Save the JSON file as "firebase-admin-key.json" in the directory: ${path.resolve(__dirname, '..')}`);
    console.log('4. Then run the update_services_with_arabic.js script manually after placing the key file.');
    
    // Create a readline interface for user input
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
    
    // Ask user if they want to check for the key file now
    rl.question('\nHave you downloaded and placed the key file? (y/n): ', (answer) => {
      if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
        // Check if the file exists
        if (fs.existsSync(keyPath)) {
          console.log('Service account key found! Continuing with the update script...');
          rl.close();
          // Continue with the next script
          try {
            console.log('\nUpdating services with Arabic translations...');
            execSync('node scripts/update_services_with_arabic.js', { 
              stdio: 'inherit',
              cwd: path.resolve(__dirname, '..')
            });
            console.log('Process completed successfully!');
          } catch (error) {
            console.error('Error running update script:', error.message);
          }
        } else {
          console.log(`Service account key not found at ${keyPath}`);
          console.log('Please make sure to save the key file with the correct name and location.');
          rl.close();
          process.exit(1);
        }
      } else {
        console.log('Please run the update_services_with_arabic.js script manually after placing the key file.');
        rl.close();
        process.exit(0);
      }
    });
  } catch (error) {
    console.error('Error in generate_admin_key script:', error.message);
    process.exit(1);
  }
}

// Run the function
generateServiceAccountKey(); 