import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/technician_model.dart';
import 'package:flutter/foundation.dart';

class TechnicianService {
  // Singleton instance
  static final TechnicianService _instance = TechnicianService._internal();
  factory TechnicianService() => _instance;
  TechnicianService._internal();
  
  // Firestore instance
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  // Check if there are technicians available
  Future<bool> areTechniciansAvailable() async {
    try {
      // Query for technicians with active status and is_available set to true
      final querySnapshot = await _firestore
          .collection('technicians')
          .where('status', isEqualTo: 'active')
          .where('is_available', isEqualTo: true)
          .limit(1) // Only need one to confirm availability
          .get();
      
      return querySnapshot.docs.isNotEmpty;
    } catch (e) {
      debugPrint('Error checking technician availability: $e');
      return false; // Default to false on error
    }
  }
  
  // Get active technician count
  Future<int> getActiveTechnicianCount() async {
    try {
      // Query for technicians with active status and is_available set to true
      final querySnapshot = await _firestore
          .collection('technicians')
          .where('status', isEqualTo: 'active')
          .where('is_available', isEqualTo: true)
          .get();
      
      return querySnapshot.docs.length;
    } catch (e) {
      debugPrint('Error getting technician count: $e');
      return 0; // Default to 0 on error
    }
  }
  
  // Get technician by ID
  Future<TechnicianModel?> getTechnicianById(String technicianId) async {
    try {
      final docSnapshot = await _firestore
          .collection('technicians')
          .doc(technicianId)
          .get();
      
      if (docSnapshot.exists) {
        return TechnicianModel.fromFirestore(docSnapshot);
      }
      
      return null;
    } catch (e) {
      debugPrint('Error getting technician: $e');
      return null;
    }
  }
} 