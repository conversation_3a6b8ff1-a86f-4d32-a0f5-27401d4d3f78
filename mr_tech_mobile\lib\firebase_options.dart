// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  // IMPORTANT: Replace these placeholder values with the actual values from your Firebase project.
  // Run 'flutterfire configure' to generate the proper Firebase configuration.
  
  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'YOUR-ACTUAL-API-KEY-HERE',
    appId: 'YOUR-ACTUAL-APP-ID-HERE',
    messagingSenderId: 'YOUR-ACTUAL-MESSAGING-SENDER-ID-HERE',
    projectId: 'YOUR-ACTUAL-PROJECT-ID-HERE',
    authDomain: 'YOUR-ACTUAL-AUTH-DOMAIN-HERE',
    storageBucket: 'YOUR-ACTUAL-STORAGE-BUCKET-HERE',
    measurementId: 'YOUR-ACTUAL-MEASUREMENT-ID-HERE',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBY5ltbF50h2GNbhe2obqm55Oa6RmxxRBY',
    appId: '1:586312365224:android:8861c3bf21fbb2d1bffe9d',
    messagingSenderId: '586312365224',
    projectId: 'stopnow-be6b7',
    databaseURL: 'https://stopnow-be6b7-default-rtdb.europe-west1.firebasedatabase.app',
    storageBucket: 'stopnow-be6b7.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCOvPVd7KIKVJ0BlbSTFDSw70gx8_Xo62M',
    appId: '1:586312365224:ios:803532683120900fbffe9d',
    messagingSenderId: '586312365224',
    projectId: 'stopnow-be6b7',
    databaseURL: 'https://stopnow-be6b7-default-rtdb.europe-west1.firebasedatabase.app',
    storageBucket: 'stopnow-be6b7.firebasestorage.app',
    androidClientId: '586312365224-baq0oo19uvfchj7908q65lgraivuet9e.apps.googleusercontent.com',
    iosClientId: '586312365224-i4cvc9fsiv5aoo36ujfdbjq1r1fdeevc.apps.googleusercontent.com',
    iosBundleId: 'com.maximummedia.mrtech',
  );

}