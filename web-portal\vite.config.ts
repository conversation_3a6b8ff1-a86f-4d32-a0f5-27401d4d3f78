import path from 'path';
import tailwindcss from '@tailwindcss/vite';
import react from '@vitejs/plugin-react';
import { defineConfig, loadEnv } from 'vite';

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // Load env file based on mode in the web-portal directory
  const env = loadEnv(mode, path.resolve(__dirname), 'VITE_');

  // Log env loading in development
  if (mode === 'development') {
    console.log('Vite env loading:', {
      mode,
      hasApiKey: !!env.VITE_FIREBASE_API_KEY,
      envKeys: Object.keys(env),
      envPath: path.resolve(__dirname),
    });
  }

  return {
    plugins: [react(), tailwindcss()],
    server: {
      host: '0.0.0.0', // Bind to all network interfaces
      port: 5173, // Default Vite port
      strictPort: false, // Allow port fallback if 5173 is busy
      hmr: {
        overlay: false, // Disable error overlay for better performance
      },
    },
    preview: {
      host: '0.0.0.0', // Also bind preview server to all interfaces
      port: 4173, // Default preview port
      strictPort: false,
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
      },
    },
    build: {
      // Enable source maps for production debugging
      sourcemap: mode === 'development',

      // Optimize chunk size
      chunkSizeWarningLimit: 1000,

      rollupOptions: {
        output: {
          // Manual chunk splitting for better caching
          manualChunks: {
            // Vendor chunks
            'react-vendor': ['react', 'react-dom', 'react-router-dom'],
            'firebase-vendor': [
              'firebase/app',
              'firebase/auth',
              'firebase/firestore',
              'firebase/storage',
              'firebase/functions',
            ],
            'ui-vendor': [
              '@radix-ui/react-dialog',
              '@radix-ui/react-dropdown-menu',
              '@radix-ui/react-select',
              '@radix-ui/react-tabs',
              '@radix-ui/react-toast',
              '@radix-ui/react-avatar',
              '@radix-ui/react-checkbox',
              '@radix-ui/react-collapsible',
              '@radix-ui/react-label',
              '@radix-ui/react-progress',
              '@radix-ui/react-scroll-area',
              '@radix-ui/react-separator',
              '@radix-ui/react-slot',
              'lucide-react',
            ],
            'chart-vendor': ['recharts'],
            'form-vendor': ['react-hook-form', 'zod'],
            'utility-vendor': ['date-fns', 'dompurify'],
          },
        },
      },

      // Enable minification
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: mode === 'production', // Remove console.log in production
          drop_debugger: true,
          pure_funcs: mode === 'production' ? ['console.log', 'console.info', 'console.debug'] : [],
        },
      },

      // Target modern browsers for better optimization
      target: 'es2020',
    },

    // Optimize dependencies
    optimizeDeps: {
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        'firebase/app',
        'firebase/auth',
        'firebase/firestore',
        'firebase/storage',
        'firebase/functions',
        'date-fns',
        'lucide-react',
        'recharts',
        'react-hook-form',
        'zod',
        'dompurify',
      ],
    },

    // Only expose VITE_ prefixed env vars
    define: {
      'import.meta.env.VITE_FIREBASE_API_KEY': JSON.stringify(env.VITE_FIREBASE_API_KEY),
      'import.meta.env.VITE_FIREBASE_AUTH_DOMAIN': JSON.stringify(env.VITE_FIREBASE_AUTH_DOMAIN),
      'import.meta.env.VITE_FIREBASE_PROJECT_ID': JSON.stringify(env.VITE_FIREBASE_PROJECT_ID),
      'import.meta.env.VITE_FIREBASE_STORAGE_BUCKET': JSON.stringify(
        env.VITE_FIREBASE_STORAGE_BUCKET,
      ),
      'import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID': JSON.stringify(
        env.VITE_FIREBASE_MESSAGING_SENDER_ID,
      ),
      'import.meta.env.VITE_FIREBASE_APP_ID': JSON.stringify(env.VITE_FIREBASE_APP_ID),
      'import.meta.env.VITE_FIREBASE_DATABASE_URL': JSON.stringify(env.VITE_FIREBASE_DATABASE_URL),
    },
  };
});
