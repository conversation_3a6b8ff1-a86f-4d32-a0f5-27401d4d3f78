import {
  type QueryConstraint,
  Timestamp,
  limit,
  orderBy,
  startAfter,
  where,
} from 'firebase/firestore';
import { FirebaseService, type QueryOptions } from './firebaseService';
import {
  type CreateRequestInput,
  type RequestFilters,
  type RequestModel,
  RequestStatus,
  type RequestsResponse,
  type UpdateRequestInput,
} from '../types/request';
import { normalizeStatus, webToMobileStatus } from '../utils/statusMapping';
import { fcmNotificationService } from './fcmNotificationService';

class RequestService extends FirebaseService<RequestModel> {
  constructor() {
    super('service_requests');
  }

  // Transform raw Firebase data to RequestModel with proper field mapping
  private transformRequestData(rawData: any): RequestModel {
    // Handle status mapping explicitly to ensure compatibility
    let status = normalizeStatus(rawData.status || 'pending');

    // Special handling for in-progress status which can appear in multiple formats
    if (
      rawData.status === 'inProgress' ||
      rawData.status === 'in-progress' ||
      rawData.status === 'in_progress'
    ) {
      status = RequestStatus.IN_PROGRESS;
    }

    return {
      id: rawData.id || '',
      customer_id: rawData.customer_id || rawData.customerId || '',
      service_id: rawData.service_id || rawData.serviceId || '',
      service_name: rawData.service_name || rawData.serviceName || '',
      service_description: rawData.service_description || rawData.serviceDescription || '',
      customer_issue: rawData.customer_issue || rawData.customerIssue || '',
      anydesk_id: rawData.anydesk_id || rawData.anydeskId,
      technician_id: rawData.technician_id,
      technician_name: rawData.technician_name,
      status,
      amount: rawData.amount || 0,
      is_paid: rawData.is_paid ?? rawData.isPaid ?? false,
      chat_active: rawData.chat_active ?? rawData.chatActive ?? false,
      session_active: rawData.session_active ?? rawData.sessionActive ?? false,
      created_at: rawData.created_at || rawData.createdAt || new Date(),
      updated_at: rawData.updated_at || rawData.updatedAt,
      scheduled_time: rawData.scheduled_time || rawData.scheduledTime,
      session_start_time: rawData.session_start_time || rawData.sessionStartTime,
      session_end_time: rawData.session_end_time || rawData.sessionEndTime,
      session_duration: rawData.session_duration || rawData.sessionDuration,
      customer_rated: rawData.customer_rated ?? rawData.customerRated,
      rating: rawData.rating,
      review_comment: rawData.review_comment || rawData.reviewComment,
      customer_fcm_token: rawData.customer_fcm_token || rawData.customerFcmToken,
      is_visible: rawData.is_visible ?? rawData.isVisible ?? true,
      customer_name: rawData.customer_name || rawData.customerName,
      customer_email: rawData.customer_email || rawData.customerEmail,
      // Legacy fields for compatibility
      customerName: rawData.customerName || rawData.customer_name,
      customerEmail: rawData.customerEmail || rawData.customer_email,
      customerFcmToken: rawData.customerFcmToken || rawData.customer_fcm_token,
      customerId: rawData.customerId || rawData.customer_id,
      customerIssue: rawData.customerIssue || rawData.customer_issue,
      serviceId: rawData.serviceId || rawData.service_id,
      serviceName: rawData.serviceName || rawData.service_name,
      serviceDescription: rawData.serviceDescription || rawData.service_description,
      createdAt: rawData.createdAt || rawData.created_at,
      updatedAt: rawData.updatedAt || rawData.updated_at,
      isPaid: rawData.isPaid ?? rawData.is_paid,
      isVisible: rawData.isVisible ?? rawData.is_visible,
      chatActive: rawData.chatActive ?? rawData.chat_active,
      sessionActive: rawData.sessionActive ?? rawData.session_active,
    };
  }

  // Override getAll to apply transformation
  async getAll(options?: QueryOptions): Promise<RequestModel[]> {
    try {
      const constraints: any[] = [];

      // Add where constraints
      if (options?.where) {
        options.where.forEach((w) => {
          constraints.push(where(w.field, w.operator, w.value));
        });
      }

      // Add orderBy constraint
      if (options?.orderBy) {
        constraints.push(orderBy(options.orderBy.field, options.orderBy.direction));
      }

      // Add pagination constraints
      if (options?.limit) {
        constraints.push(limit(options.limit));
      }

      if (options?.startAfter) {
        constraints.push(startAfter(options.startAfter));
      }

      // Use our query method which applies transformation
      return this.query(constraints);
    } catch (error) {
      console.error('Error getting all requests:', error);
      throw error;
    }
  }

  // Override getById to apply transformation
  async getById(id: string): Promise<RequestModel | null> {
    const rawRequest = await super.getById(id);
    if (!rawRequest) return null;
    return this.transformRequestData(rawRequest);
  }

  // Override query to apply transformation
  async query(constraints: any[]): Promise<RequestModel[]> {
    const rawRequests = await super.query(constraints);
    return rawRequests.map((request) => this.transformRequestData(request));
  }

  // Get requests by customer ID
  async getByCustomerId(customerId: string, options?: QueryOptions): Promise<RequestModel[]> {
    const constraints: QueryConstraint[] = [
      where('customer_id', '==', customerId),
      where('is_visible', '!=', false),
    ];

    if (options?.orderBy) {
      constraints.push(orderBy(options.orderBy.field, options.orderBy.direction));
    } else {
      constraints.push(orderBy('created_at', 'desc'));
    }

    const rawRequests = await super.query(constraints);
    return rawRequests.map((request) => this.transformRequestData(request));
  }

  // Get requests by technician ID
  async getByTechnicianId(technicianId: string, options?: QueryOptions): Promise<RequestModel[]> {
    const constraints: QueryConstraint[] = [
      where('technician_id', '==', technicianId),
      where('is_visible', '!=', false),
    ];

    if (options?.orderBy) {
      constraints.push(orderBy(options.orderBy.field, options.orderBy.direction));
    } else {
      constraints.push(orderBy('created_at', 'desc'));
    }

    return this.query(constraints);
  }

  // Get requests by status
  async getByStatus(status: RequestStatus, options?: QueryOptions): Promise<RequestModel[]> {
    const constraints: QueryConstraint[] = [
      where('status', '==', status),
      where('is_visible', '!=', false),
    ];

    if (options?.orderBy) {
      constraints.push(orderBy(options.orderBy.field, options.orderBy.direction));
    } else {
      constraints.push(orderBy('created_at', 'desc'));
    }

    return this.query(constraints);
  }

  // Get pending requests (not assigned to any technician)
  async getPendingRequests(options?: QueryOptions): Promise<RequestModel[]> {
    const constraints: QueryConstraint[] = [
      where('status', '==', RequestStatus.PENDING),
      where('technician_id', '==', null),
      where('is_visible', '!=', false),
    ];

    if (options?.orderBy) {
      constraints.push(orderBy(options.orderBy.field, options.orderBy.direction));
    } else {
      constraints.push(orderBy('created_at', 'asc')); // Oldest first
    }

    const rawRequests = await super.query(constraints);
    return rawRequests.map((request) => this.transformRequestData(request));
  }

  // Get active requests (in progress)
  async getActiveRequests(options?: QueryOptions): Promise<RequestModel[]> {
    const constraints: QueryConstraint[] = [
      where('status', 'in', [RequestStatus.APPROVED, RequestStatus.IN_PROGRESS]),
      where('is_visible', '!=', false),
    ];

    if (options?.orderBy) {
      constraints.push(orderBy(options.orderBy.field, options.orderBy.direction));
    } else {
      constraints.push(orderBy('created_at', 'desc'));
    }

    const rawRequests = await super.query(constraints);
    return rawRequests.map((request) => this.transformRequestData(request));
  }

  // Create a new request
  async createRequest(data: CreateRequestInput): Promise<RequestModel> {
    const requestData = {
      ...data,
      status: RequestStatus.PAYMENT_PENDING,
      is_paid: false,
      chat_active: false,
      session_active: false,
      is_visible: true,
      created_at: Timestamp.now(),
    };

    return this.create(requestData as Omit<RequestModel, 'id'>);
  }

  // Override update method to handle status conversion for mobile app compatibility
  async update(id: string, data: Partial<RequestModel>): Promise<RequestModel> {
    // Convert status to mobile app format if present
    const updateData = { ...data };
    if (updateData.status) {
      // Store status in mobile app format for compatibility
      (updateData as any).status = webToMobileStatus(updateData.status);
    }

    return super.update(id, updateData);
  }

  // Update request status
  async updateStatus(id: string, status: RequestStatus): Promise<RequestModel> {
    // Get current request data to access customer info and technician name
    const currentRequest = await this.getById(id);
    if (!currentRequest) {
      throw new Error(`Request not found: ${id}`);
    }

    const updateData: Partial<RequestModel> = { status };

    // Handle status-specific updates
    switch (status) {
      case RequestStatus.IN_PROGRESS:
        if (!updateData.session_start_time) {
          updateData.session_start_time = Timestamp.now();
        }
        updateData.session_active = true;
        break;

      case RequestStatus.COMPLETED:
        if (!updateData.session_end_time) {
          updateData.session_end_time = Timestamp.now();
        }
        updateData.session_active = false;
        updateData.chat_active = false;
        break;

      case RequestStatus.CANCELLED:
      case RequestStatus.REFUSED:
        updateData.session_active = false;
        updateData.chat_active = false;
        break;
    }

    // Update the request
    const updatedRequest = await this.update(id, updateData);

    // Send FCM notifications for status changes
    await this.sendStatusChangeNotification(updatedRequest, status);

    return updatedRequest;
  }

  // Send FCM notification for status changes
  private async sendStatusChangeNotification(
    request: RequestModel,
    newStatus: RequestStatus,
  ): Promise<void> {
    try {
      const technicianName = request.technician_name || 'Technician';

      switch (newStatus) {
        case RequestStatus.APPROVED:
          await fcmNotificationService.sendRequestApprovedNotification(request, technicianName);

          // Automatically initialize chat when request is approved
          try {
            const { chatService } = await import('./chatService');
            await chatService.initializeChat(request.id, technicianName);
            console.warn(`Chat initialized for approved request: ${request.id}`);
          } catch (chatError) {
            console.error('Error initializing chat for approved request:', chatError);
            // Don't throw error to avoid breaking the status update
          }
          break;

        case RequestStatus.IN_PROGRESS:
          await fcmNotificationService.sendSessionStartedNotification(request, technicianName);
          break;

        case RequestStatus.COMPLETED:
          await fcmNotificationService.sendRequestCompletedNotification(request, technicianName);
          break;

        // No notifications for other status changes
        default:
          break;
      }
    } catch (error) {
      console.error('Error sending status change notification:', error);
      // Don't throw error to avoid breaking the status update
    }
  }

  // Assign technician to request
  async assignTechnician(
    requestId: string,
    technicianId: string,
    technicianName: string,
  ): Promise<RequestModel> {
    return this.update(requestId, {
      technician_id: technicianId,
      technician_name: technicianName,
      status: RequestStatus.APPROVED,
    });
  }

  // Update payment status
  async updatePaymentStatus(id: string, isPaid: boolean): Promise<RequestModel> {
    const updateData: Partial<RequestModel> = {
      is_paid: isPaid,
    };

    // If payment is completed and status is payment_pending, update to pending
    if (isPaid) {
      const request = await this.getById(id);
      if (request?.status === RequestStatus.PAYMENT_PENDING) {
        updateData.status = RequestStatus.PENDING;
      }
    }

    return this.update(id, updateData);
  }

  // Toggle chat active status
  async toggleChatActive(id: string, isActive: boolean): Promise<RequestModel> {
    return this.update(id, { chat_active: isActive });
  }

  // Add rating and review
  async addRating(id: string, rating: number, reviewComment?: string): Promise<RequestModel> {
    return this.update(id, {
      customer_rated: true,
      rating,
      review_comment: reviewComment,
    });
  }

  // Calculate session duration
  async calculateSessionDuration(id: string): Promise<RequestModel> {
    const request = await this.getById(id);
    if (!request) {
      throw new Error('Request not found');
    }

    if (request.session_start_time && request.session_end_time) {
      const startTime = this.toDate(request.session_start_time);
      const endTime = this.toDate(request.session_end_time);

      if (startTime && endTime) {
        const durationInMinutes = Math.round(
          (endTime.getTime() - startTime.getTime()) / (1000 * 60),
        );

        return this.update(id, { session_duration: durationInMinutes });
      }
    }

    return request;
  }

  // Get paginated requests with filters
  async getRequestsPaginated(
    filters: RequestFilters = {},
    page: number = 1,
    pageSize: number = 20,
  ): Promise<RequestsResponse> {
    try {
      const constraints: QueryConstraint[] = [where('is_visible', '!=', false)];

      // Apply filters
      if (filters.status && filters.status.length > 0) {
        constraints.push(where('status', 'in', filters.status));
      }

      if (filters.technician_id) {
        constraints.push(where('technician_id', '==', filters.technician_id));
      }

      if (filters.customer_id) {
        constraints.push(where('customer_id', '==', filters.customer_id));
      }

      if (filters.is_paid !== undefined) {
        constraints.push(where('is_paid', '==', filters.is_paid));
      }

      if (filters.dateRange) {
        const startTimestamp = Timestamp.fromDate(filters.dateRange.start);
        const endTimestamp = Timestamp.fromDate(filters.dateRange.end);
        constraints.push(where('created_at', '>=', startTimestamp));
        constraints.push(where('created_at', '<=', endTimestamp));
      }

      if (filters.amountRange) {
        constraints.push(where('amount', '>=', filters.amountRange.min));
        constraints.push(where('amount', '<=', filters.amountRange.max));
      }

      // Add ordering and pagination
      constraints.push(orderBy('created_at', 'desc'));
      constraints.push(limit(pageSize + 1)); // Get one extra to check if there are more

      // Calculate offset for pagination
      const offset = (page - 1) * pageSize;
      if (offset > 0) {
        // For pagination, we need to get the starting document
        // This is a simplified approach - in production, you'd want to use cursor-based pagination
        const skipConstraints = [
          ...constraints.slice(0, -1), // Remove the limit
          limit(offset),
        ];
        const skipResults = await super.query(skipConstraints);
        if (skipResults.length > 0) {
          const lastDoc = skipResults[skipResults.length - 1];
          constraints.push(startAfter(lastDoc));
        }
      }

      const rawRequests = await super.query(constraints);
      const hasMore = rawRequests.length > pageSize;
      const requests = rawRequests
        .slice(0, pageSize)
        .map((request) => this.transformRequestData(request));

      // Get total count (simplified - in production, you'd want to cache this or use a counter)
      const totalConstraints = constraints.filter(
        (c) => c.type !== 'limit' && c.type !== 'startAfter',
      );
      const allRequests = await super.query(totalConstraints);
      const total = allRequests.length;

      return {
        requests,
        total,
        page,
        limit: pageSize,
        hasMore,
      };
    } catch (error) {
      console.error('Error getting paginated requests:', error);
      throw error;
    }
  }

  // Search requests with pagination
  async searchRequests(
    searchTerm: string,
    filters: RequestFilters = {},
    page: number = 1,
    pageSize: number = 20,
  ): Promise<RequestsResponse> {
    // For search, we'll get all requests and filter in memory
    // In production, you'd want to use a search service like Algolia
    const allRequestsResponse = await this.getRequestsPaginated(filters, 1, 1000);
    const searchResults = allRequestsResponse.requests.filter((request) => {
      const searchLower = searchTerm.toLowerCase();
      return (
        request.id.toLowerCase().includes(searchLower) ||
        request.customer_issue.toLowerCase().includes(searchLower) ||
        request.customer_name?.toLowerCase().includes(searchLower) ||
        request.customer_email?.toLowerCase().includes(searchLower) ||
        request.technician_name?.toLowerCase().includes(searchLower) ||
        (typeof request.service_name === 'string' &&
          request.service_name.toLowerCase().includes(searchLower))
      );
    });

    const total = searchResults.length;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedResults = searchResults.slice(startIndex, endIndex);

    return {
      requests: paginatedResults,
      total,
      page,
      limit: pageSize,
      hasMore: endIndex < total,
    };
  }
}

// Export singleton instance
export default new RequestService();
