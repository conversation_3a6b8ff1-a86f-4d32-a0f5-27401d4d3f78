import 'package:flutter/foundation.dart';

/// Field Migration Monitor
/// Tracks field naming usage patterns during the migration process
/// This helps ensure the migration is proceeding safely
class FieldMigrationMonitor {
  static final Map<String, int> _snakeCaseUsage = {};
  static final Map<String, int> _camelCaseUsage = {};
  static final Map<String, int> _missingFields = {};

  /// Log when a field is accessed using snake_case
  static void logSnakeCaseUsage(String fieldName, String documentId) {
    if (kDebugMode) {
      _snakeCaseUsage[fieldName] = (_snakeCaseUsage[fieldName] ?? 0) + 1;
      debugPrint(
        'MIGRATION: Using snake_case for $fieldName in doc $documentId',
      );
    }
  }

  /// Log when a field is accessed using camelCase fallback
  static void logCamelCaseFallback(String fieldName, String documentId) {
    if (kDebugMode) {
      _camelCaseUsage[fieldName] = (_camelCaseUsage[fieldName] ?? 0) + 1;
      debugPrint(
        'MIGRATION: Using camelCase fallback for $fieldName in doc $documentId',
      );
    }
  }

  /// Log when a field is completely missing
  static void logMissingField(String fieldName, String documentId) {
    if (kDebugMode) {
      _missingFields[fieldName] = (_missingFields[fieldName] ?? 0) + 1;
      debugPrint('MIGRATION: Missing field $fieldName in doc $documentId');
    }
  }

  /// Get migration statistics
  static Map<String, dynamic> getStats() {
    return {
      'snake_case_usage': Map.from(_snakeCaseUsage),
      'camel_case_fallbacks': Map.from(_camelCaseUsage),
      'missing_fields': Map.from(_missingFields),
      'total_snake_case': _snakeCaseUsage.values.fold(0, (a, b) => a + b),
      'total_camel_case': _camelCaseUsage.values.fold(0, (a, b) => a + b),
      'total_missing': _missingFields.values.fold(0, (a, b) => a + b),
    };
  }

  /// Check if migration is ready for next phase
  static bool isMigrationReady() {
    final totalCamelCase = _camelCaseUsage.values.fold(0, (a, b) => a + b);
    final totalMissing = _missingFields.values.fold(0, (a, b) => a + b);

    // Ready if less than 5% of field accesses use camelCase fallback
    // and no critical fields are missing
    return totalCamelCase < 50 && totalMissing == 0;
  }

  /// Print migration status report
  static void printReport() {
    if (kDebugMode) {
      final stats = getStats();
      debugPrint('=== FIELD MIGRATION REPORT ===');
      debugPrint('Snake case usage: ${stats['total_snake_case']}');
      debugPrint('CamelCase fallbacks: ${stats['total_camel_case']}');
      debugPrint('Missing fields: ${stats['total_missing']}');
      debugPrint('Migration ready: ${isMigrationReady()}');
      debugPrint('==============================');
    }
  }

  /// Clear all statistics (useful for testing)
  static void clearStats() {
    _snakeCaseUsage.clear();
    _camelCaseUsage.clear();
    _missingFields.clear();
  }

  /// Validate document has required snake_case fields
  static bool validateDocument(
    Map<String, dynamic> data,
    List<String> requiredFields,
  ) {
    bool isValid = true;

    for (final field in requiredFields) {
      if (data[field] == null) {
        logMissingField(field, data['id']?.toString() ?? 'unknown');
        isValid = false;
      }
    }

    return isValid;
  }

  /// Helper to safely get field value with monitoring
  static T? getFieldValue<T>(
    Map<String, dynamic> data,
    String snakeField,
    String camelField,
    String documentId,
  ) {
    if (data[snakeField] != null) {
      logSnakeCaseUsage(snakeField, documentId);
      return data[snakeField] as T?;
    } else if (data[camelField] != null) {
      logCamelCaseFallback(camelField, documentId);
      return data[camelField] as T?;
    } else {
      logMissingField(snakeField, documentId);
      return null;
    }
  }
}
