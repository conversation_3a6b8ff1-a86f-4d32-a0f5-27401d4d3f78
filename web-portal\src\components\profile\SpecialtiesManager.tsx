import React, { useState } from 'react';
import { Plus, Tag, X } from 'lucide-react';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Badge } from '../../components/ui/badge';
import { Card, CardContent } from '../../components/ui/card';

interface SpecialtiesManagerProps {
  specialties: string[];
  onSpecialtiesChange: (specialties: string[]) => void;
  maxSpecialties?: number;
}

// Predefined specialty suggestions
const SPECIALTY_SUGGESTIONS = [
  'Computer Repair',
  'Network Setup',
  'Software Installation',
  'Hardware Troubleshooting',
  'Data Recovery',
  'Virus Removal',
  'System Optimization',
  'Printer Setup',
  'Mobile Device Repair',
  'Smart Home Setup',
  'Security System Installation',
  'Audio/Video Setup',
  'Gaming Setup',
  'Business IT Support',
  'Cloud Services',
  'Backup Solutions',
  'Remote Support',
  'Training & Consultation',
];

const SpecialtiesManager: React.FC<SpecialtiesManagerProps> = ({
  specialties,
  onSpecialtiesChange,
  maxSpecialties = 10,
}) => {
  const [newSpecialty, setNewSpecialty] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);

  const addSpecialty = (specialty: string) => {
    const trimmedSpecialty = specialty.trim();

    if (!trimmedSpecialty) return;

    // Check if specialty already exists (case-insensitive)
    if (specialties.some((s) => s.toLowerCase() === trimmedSpecialty.toLowerCase())) {
      return;
    }

    // Check max limit
    if (specialties.length >= maxSpecialties) {
      return;
    }

    onSpecialtiesChange([...specialties, trimmedSpecialty]);
    setNewSpecialty('');
    setShowSuggestions(false);
  };

  const removeSpecialty = (specialtyToRemove: string) => {
    onSpecialtiesChange(specialties.filter((s) => s !== specialtyToRemove));
  };

  const handleInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addSpecialty(newSpecialty);
    }
  };

  const filteredSuggestions = SPECIALTY_SUGGESTIONS.filter(
    (suggestion) =>
      !specialties.includes(suggestion) &&
      suggestion.toLowerCase().includes(newSpecialty.toLowerCase()),
  ).slice(0, 6); // Show max 6 suggestions

  return (
    <div className='space-y-4'>
      {/* Current Specialties */}
      {specialties.length > 0 && (
        <div className='space-y-2'>
          <p className='text-sm font-medium'>Current Specialties:</p>
          <div className='flex flex-wrap gap-2'>
            {specialties.map((specialty, index) => (
              <Badge key={index} variant='secondary' className='flex items-center gap-1 px-3 py-1'>
                <Tag className='h-3 w-3' />
                {specialty}
                <Button
                  variant='ghost'
                  size='sm'
                  className='h-4 w-4 p-0 ml-1 hover:bg-destructive hover:text-destructive-foreground'
                  onClick={() => removeSpecialty(specialty)}
                >
                  <X className='h-3 w-3' />
                </Button>
              </Badge>
            ))}
          </div>
        </div>
      )}

      {/* Add New Specialty */}
      <div className='space-y-2'>
        <p className='text-sm font-medium'>
          Add Specialty {specialties.length > 0 && `(${specialties.length}/${maxSpecialties})`}
        </p>

        <div className='flex gap-2'>
          <div className='flex-1 relative'>
            <Input
              value={newSpecialty}
              onChange={(e) => {
                setNewSpecialty(e.target.value);
                setShowSuggestions(e.target.value.length > 0);
              }}
              onKeyPress={handleInputKeyPress}
              onFocus={() => setShowSuggestions(newSpecialty.length > 0)}
              placeholder='Enter a specialty...'
              disabled={specialties.length >= maxSpecialties}
            />

            {/* Suggestions Dropdown */}
            {showSuggestions && filteredSuggestions.length > 0 && (
              <Card className='absolute top-full left-0 right-0 z-10 mt-1 max-h-48 overflow-y-auto'>
                <CardContent className='p-2'>
                  <div className='space-y-1'>
                    {filteredSuggestions.map((suggestion, index) => (
                      <Button
                        key={index}
                        variant='ghost'
                        size='sm'
                        className='w-full justify-start text-left h-auto py-2'
                        onClick={() => addSpecialty(suggestion)}
                      >
                        <Tag className='h-3 w-3 mr-2' />
                        {suggestion}
                      </Button>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          <Button
            onClick={() => addSpecialty(newSpecialty)}
            disabled={!newSpecialty.trim() || specialties.length >= maxSpecialties}
            size='sm'
          >
            <Plus className='h-4 w-4' />
          </Button>
        </div>

        {specialties.length >= maxSpecialties && (
          <p className='text-xs text-muted-foreground'>
            Maximum number of specialties reached ({maxSpecialties})
          </p>
        )}
      </div>

      {/* Popular Specialties */}
      {specialties.length < maxSpecialties && (
        <div className='space-y-2'>
          <p className='text-sm font-medium'>Popular Specialties:</p>
          <div className='flex flex-wrap gap-2'>
            {SPECIALTY_SUGGESTIONS.filter((suggestion) => !specialties.includes(suggestion))
              .slice(0, 8)
              .map((suggestion, index) => (
                <Button
                  key={index}
                  variant='outline'
                  size='sm'
                  className='h-auto py-1 px-2 text-xs'
                  onClick={() => addSpecialty(suggestion)}
                >
                  <Plus className='h-3 w-3 mr-1' />
                  {suggestion}
                </Button>
              ))}
          </div>
        </div>
      )}

      {/* Help Text */}
      <div className='text-xs text-muted-foreground space-y-1'>
        <p>• Add up to {maxSpecialties} specialties to help customers find you</p>
        <p>• Use specific terms that describe your technical expertise</p>
        <p>• Popular specialties will appear in search results more often</p>
      </div>
    </div>
  );
};

export default SpecialtiesManager;
