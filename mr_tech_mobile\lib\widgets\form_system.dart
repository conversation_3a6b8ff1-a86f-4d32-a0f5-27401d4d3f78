import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/app_theme.dart';
import '../utils/spacing.dart';
import '../utils/typography.dart';

/// Comprehensive form system for consistent form styling throughout the app
/// Provides standardized form components following Material 3 design principles
class FormSystem {
  // Private constructor to prevent instantiation
  FormSystem._();

  /// Standard text input field
  static Widget textField(
    BuildContext context, {
    String? label,
    String? hint,
    String? helperText,
    String? errorText,
    TextEditingController? controller,
    ValueChanged<String>? onChanged,
    VoidCallback? onTap,
    bool readOnly = false,
    bool obscureText = false,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    Widget? prefixIcon,
    Widget? suffixIcon,
    int? maxLines = 1,
    int? maxLength,
    bool enabled = true,
    EdgeInsetsGeometry? margin,
  }) {
    final theme = Theme.of(context);
    
    return Container(
      margin: margin ?? Spacing.verticalPaddingS,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (label != null) ...[
            Text(
              label,
              style: Typography.labelLarge(context),
            ),
            Spacing.verticalXS,
          ],
          TextFormField(
            controller: controller,
            onChanged: onChanged,
            onTap: onTap,
            readOnly: readOnly,
            obscureText: obscureText,
            keyboardType: keyboardType,
            inputFormatters: inputFormatters,
            maxLines: maxLines,
            maxLength: maxLength,
            enabled: enabled,
            style: Typography.bodyMedium(context),
            decoration: InputDecoration(
              hintText: hint,
              prefixIcon: prefixIcon,
              suffixIcon: suffixIcon,
              filled: true,
              fillColor: theme.brightness == Brightness.dark
                ? theme.colorScheme.surfaceContainer
                : theme.colorScheme.surface,
              contentPadding: Spacing.inputPadding,
              border: OutlineInputBorder(
                borderRadius: AppTheme.smallRadius,
                borderSide: BorderSide(
                  color: theme.colorScheme.outline.withOpacity(0.2),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: AppTheme.smallRadius,
                borderSide: BorderSide(
                  color: theme.colorScheme.outline.withOpacity(0.2),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: AppTheme.smallRadius,
                borderSide: BorderSide(
                  color: theme.colorScheme.primary,
                  width: 2,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: AppTheme.smallRadius,
                borderSide: BorderSide(
                  color: theme.colorScheme.error,
                  width: 2,
                ),
              ),
              focusedErrorBorder: OutlineInputBorder(
                borderRadius: AppTheme.smallRadius,
                borderSide: BorderSide(
                  color: theme.colorScheme.error,
                  width: 2,
                ),
              ),
              disabledBorder: OutlineInputBorder(
                borderRadius: AppTheme.smallRadius,
                borderSide: BorderSide(
                  color: theme.colorScheme.outline.withOpacity(0.1),
                ),
              ),
              errorText: errorText,
              helperText: helperText,
              helperStyle: Typography.labelSmall(context).subtle(context),
              errorStyle: Typography.labelSmall(context).copyWith(
                color: theme.colorScheme.error,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Dropdown field
  static Widget dropdown<T>(
    BuildContext context, {
    String? label,
    String? hint,
    String? helperText,
    String? errorText,
    required T? value,
    required List<DropdownMenuItem<T>> items,
    required ValueChanged<T?> onChanged,
    Widget? prefixIcon,
    bool enabled = true,
    EdgeInsetsGeometry? margin,
  }) {
    final theme = Theme.of(context);
    
    return Container(
      margin: margin ?? Spacing.verticalPaddingS,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (label != null) ...[
            Text(
              label,
              style: Typography.labelLarge(context),
            ),
            Spacing.verticalXS,
          ],
          DropdownButtonFormField<T>(
            value: value,
            items: items,
            onChanged: enabled ? onChanged : null,
            style: Typography.bodyMedium(context),
            decoration: InputDecoration(
              hintText: hint,
              prefixIcon: prefixIcon,
              filled: true,
              fillColor: theme.brightness == Brightness.dark
                ? theme.colorScheme.surfaceContainer
                : theme.colorScheme.surface,
              contentPadding: Spacing.inputPadding,
              border: OutlineInputBorder(
                borderRadius: AppTheme.smallRadius,
                borderSide: BorderSide(
                  color: theme.colorScheme.outline.withOpacity(0.2),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: AppTheme.smallRadius,
                borderSide: BorderSide(
                  color: theme.colorScheme.outline.withOpacity(0.2),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: AppTheme.smallRadius,
                borderSide: BorderSide(
                  color: theme.colorScheme.primary,
                  width: 2,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: AppTheme.smallRadius,
                borderSide: BorderSide(
                  color: theme.colorScheme.error,
                  width: 2,
                ),
              ),
              errorText: errorText,
              helperText: helperText,
              helperStyle: Typography.labelSmall(context).subtle(context),
              errorStyle: Typography.labelSmall(context).copyWith(
                color: theme.colorScheme.error,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Checkbox field
  static Widget checkbox(
    BuildContext context, {
    required String label,
    required bool value,
    required ValueChanged<bool?> onChanged,
    String? subtitle,
    bool enabled = true,
    EdgeInsetsGeometry? margin,
  }) {
    return Container(
      margin: margin ?? Spacing.verticalPaddingS,
      child: CheckboxListTile(
        title: Text(
          label,
          style: Typography.bodyMedium(context),
        ),
        subtitle: subtitle != null 
          ? Text(
              subtitle,
              style: Typography.labelSmall(context).subtle(context),
            )
          : null,
        value: value,
        onChanged: enabled ? onChanged : null,
        controlAffinity: ListTileControlAffinity.leading,
        contentPadding: EdgeInsets.zero,
        dense: true,
      ),
    );
  }

  /// Radio button group
  static Widget radioGroup<T>(
    BuildContext context, {
    required String label,
    required T? value,
    required List<RadioOption<T>> options,
    required ValueChanged<T?> onChanged,
    bool enabled = true,
    EdgeInsetsGeometry? margin,
  }) {
    return Container(
      margin: margin ?? Spacing.verticalPaddingS,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: Typography.labelLarge(context),
          ),
          Spacing.verticalS,
          ...options.map((option) => RadioListTile<T>(
            title: Text(
              option.label,
              style: Typography.bodyMedium(context),
            ),
            subtitle: option.subtitle != null 
              ? Text(
                  option.subtitle!,
                  style: Typography.labelSmall(context).subtle(context),
                )
              : null,
            value: option.value,
            groupValue: value,
            onChanged: enabled ? onChanged : null,
            contentPadding: EdgeInsets.zero,
            dense: true,
          )),
        ],
      ),
    );
  }

  /// Switch field
  static Widget switchField(
    BuildContext context, {
    required String label,
    required bool value,
    required ValueChanged<bool> onChanged,
    String? subtitle,
    bool enabled = true,
    EdgeInsetsGeometry? margin,
  }) {
    return Container(
      margin: margin ?? Spacing.verticalPaddingS,
      child: SwitchListTile(
        title: Text(
          label,
          style: Typography.bodyMedium(context),
        ),
        subtitle: subtitle != null 
          ? Text(
              subtitle,
              style: Typography.labelSmall(context).subtle(context),
            )
          : null,
        value: value,
        onChanged: enabled ? onChanged : null,
        contentPadding: EdgeInsets.zero,
        dense: true,
      ),
    );
  }

  /// Primary button
  static Widget primaryButton(
    BuildContext context, {
    required String text,
    required VoidCallback? onPressed,
    Widget? icon,
    bool isLoading = false,
    EdgeInsetsGeometry? margin,
    double? width,
  }) {
    return Container(
      margin: margin ?? Spacing.verticalPaddingS,
      width: width,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          padding: Spacing.buttonPadding,
          shape: RoundedRectangleBorder(
            borderRadius: AppTheme.smallRadius,
          ),
          minimumSize: Size(0, 48),
        ),
        child: isLoading
          ? SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).colorScheme.onPrimary,
                ),
              ),
            )
          : Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (icon != null) ...[
                  icon,
                  Spacing.horizontalS,
                ],
                Text(
                  text,
                  style: Typography.buttonText(context).copyWith(
                    color: Theme.of(context).colorScheme.onPrimary,
                  ),
                ),
              ],
            ),
      ),
    );
  }

  /// Secondary button
  static Widget secondaryButton(
    BuildContext context, {
    required String text,
    required VoidCallback? onPressed,
    Widget? icon,
    bool isLoading = false,
    EdgeInsetsGeometry? margin,
    double? width,
  }) {
    return Container(
      margin: margin ?? Spacing.verticalPaddingS,
      width: width,
      child: OutlinedButton(
        onPressed: isLoading ? null : onPressed,
        style: OutlinedButton.styleFrom(
          padding: Spacing.buttonPadding,
          shape: RoundedRectangleBorder(
            borderRadius: AppTheme.smallRadius,
          ),
          minimumSize: Size(0, 48),
        ),
        child: isLoading
          ? SizedBox(
              height: 20,
              width: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).colorScheme.primary,
                ),
              ),
            )
          : Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (icon != null) ...[
                  icon,
                  Spacing.horizontalS,
                ],
                Text(
                  text,
                  style: Typography.buttonText(context),
                ),
              ],
            ),
      ),
    );
  }

  /// Text button
  static Widget textButton(
    BuildContext context, {
    required String text,
    required VoidCallback? onPressed,
    Widget? icon,
    EdgeInsetsGeometry? margin,
  }) {
    return Container(
      margin: margin ?? Spacing.verticalPaddingS,
      child: TextButton(
        onPressed: onPressed,
        style: TextButton.styleFrom(
          padding: Spacing.buttonPadding,
          shape: RoundedRectangleBorder(
            borderRadius: AppTheme.smallRadius,
          ),
          minimumSize: Size(0, 48),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) ...[
              icon,
              Spacing.horizontalS,
            ],
            Text(
              text,
              style: Typography.buttonText(context),
            ),
          ],
        ),
      ),
    );
  }
}

/// Radio option model
class RadioOption<T> {
  final T value;
  final String label;
  final String? subtitle;

  const RadioOption({
    required this.value,
    required this.label,
    this.subtitle,
  });
}

/// Form validation utilities
class FormValidation {
  // Private constructor to prevent instantiation
  FormValidation._();

  /// Validates required fields
  static String? required(String? value, {String? fieldName}) {
    if (value == null || value.trim().isEmpty) {
      return '${fieldName ?? 'This field'} is required';
    }
    return null;
  }

  /// Validates email format
  static String? email(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Email is required';
    }

    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
    if (!emailRegex.hasMatch(value.trim())) {
      return 'Please enter a valid email address';
    }

    return null;
  }

  /// Validates phone number format
  static String? phone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Phone number is required';
    }

    final phoneRegex = RegExp(r'^\+?[\d\s\-\(\)]{10,}$');
    if (!phoneRegex.hasMatch(value.trim())) {
      return 'Please enter a valid phone number';
    }

    return null;
  }

  /// Validates password strength
  static String? password(String? value, {int minLength = 8}) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }

    if (value.length < minLength) {
      return 'Password must be at least $minLength characters long';
    }

    if (!RegExp(r'[A-Z]').hasMatch(value)) {
      return 'Password must contain at least one uppercase letter';
    }

    if (!RegExp(r'[a-z]').hasMatch(value)) {
      return 'Password must contain at least one lowercase letter';
    }

    if (!RegExp(r'[0-9]').hasMatch(value)) {
      return 'Password must contain at least one number';
    }

    return null;
  }

  /// Validates password confirmation
  static String? confirmPassword(String? value, String? originalPassword) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your password';
    }

    if (value != originalPassword) {
      return 'Passwords do not match';
    }

    return null;
  }

  /// Validates minimum length
  static String? minLength(String? value, int minLength, {String? fieldName}) {
    if (value == null || value.trim().isEmpty) {
      return '${fieldName ?? 'This field'} is required';
    }

    if (value.trim().length < minLength) {
      return '${fieldName ?? 'This field'} must be at least $minLength characters long';
    }

    return null;
  }

  /// Validates maximum length
  static String? maxLength(String? value, int maxLength, {String? fieldName}) {
    if (value != null && value.trim().length > maxLength) {
      return '${fieldName ?? 'This field'} must be no more than $maxLength characters long';
    }

    return null;
  }

  /// Validates numeric input
  static String? numeric(String? value, {String? fieldName}) {
    if (value == null || value.trim().isEmpty) {
      return '${fieldName ?? 'This field'} is required';
    }

    if (double.tryParse(value.trim()) == null) {
      return '${fieldName ?? 'This field'} must be a valid number';
    }

    return null;
  }

  /// Validates URL format
  static String? url(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'URL is required';
    }

    final urlRegex = RegExp(r'^https?:\/\/[^\s]+$');
    if (!urlRegex.hasMatch(value.trim())) {
      return 'Please enter a valid URL';
    }

    return null;
  }

  /// Combines multiple validators
  static String? Function(String?) combine(List<String? Function(String?)> validators) {
    return (String? value) {
      for (final validator in validators) {
        final result = validator(value);
        if (result != null) {
          return result;
        }
      }
      return null;
    };
  }
}
