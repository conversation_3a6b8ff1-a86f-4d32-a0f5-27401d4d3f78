import React, { useState } from 'react';
import {
  Calendar,
  Download,
  Eye,
  FileText,
  Filter,
  Grid,
  HardDrive,
  Image as ImageIcon,
  List,
  Search,
  Trash2,
  User,
} from 'lucide-react';
import { But<PERSON> } from './button';
import { Input } from './input';
import { Card, CardContent } from './card';
import { Badge } from './badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from './dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './select';
import { cn } from '../../lib/utils';
import fileUploadService from '../../services/fileUploadService.js';

export interface FileItem {
  id: string;
  fileName: string;
  fileUrl: string;
  fileSize: number;
  fileType: string;
  uploadedAt: Date;
  uploadedBy: string;
  uploadedByName?: string;
  category?: string;
  tags?: string[];
}

interface FileGalleryProps {
  files: FileItem[];
  onFileDelete?: (fileId: string) => void;
  onFileDownload?: (file: FileItem) => void;
  canDelete?: boolean;
  viewMode?: 'grid' | 'list';
  showSearch?: boolean;
  showFilter?: boolean;
  className?: string;
}

const FileGallery: React.FC<FileGalleryProps> = ({
  files,
  onFileDelete,
  onFileDownload,
  canDelete = false,
  viewMode: initialViewMode = 'grid',
  showSearch = true,
  showFilter = true,
  className,
}) => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>(initialViewMode);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [previewFile, setPreviewFile] = useState<FileItem | null>(null);

  // Filter and search files
  const filteredFiles = files.filter((file) => {
    const matchesSearch =
      file.fileName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      file.uploadedByName?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesFilter =
      filterType === 'all' || fileUploadService.getFileTypeCategory(file.fileType) === filterType;

    return matchesSearch && matchesFilter;
  });

  const handleDownload = (file: FileItem) => {
    if (onFileDownload) {
      onFileDownload(file);
    } else {
      const link = document.createElement('a');
      link.href = file.fileUrl;
      link.download = file.fileName;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handlePreview = (file: FileItem) => {
    if (fileUploadService.isImageUrl(file.fileUrl)) {
      setPreviewFile(file);
    } else {
      window.open(file.fileUrl, '_blank');
    }
  };

  const handleDelete = (fileId: string) => {
    if (window.confirm('Are you sure you want to delete this file?')) {
      onFileDelete?.(fileId);
    }
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return <ImageIcon className='h-5 w-5 text-blue-500' />;
    }
    return <FileText className='h-5 w-5 text-gray-500' />;
  };

  const getFileTypeColor = (fileType: string) => {
    const category = fileUploadService.getFileTypeCategory(fileType);
    const colors = {
      image: 'bg-blue-100 text-blue-800',
      document: 'bg-green-100 text-green-800',
      other: 'bg-gray-100 text-gray-800',
    };
    return colors[category];
  };

  const renderGridView = () => (
    <div className='grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4'>
      {filteredFiles.map((file) => (
        <Card key={file.id} className='group hover:shadow-md transition-shadow'>
          <CardContent className='p-4'>
            <div className='aspect-square bg-gray-50 rounded-lg mb-3 flex items-center justify-center overflow-hidden'>
              {fileUploadService.isImageUrl(file.fileUrl) ? (
                <img
                  src={file.fileUrl}
                  alt={file.fileName}
                  className='w-full h-full object-cover cursor-pointer'
                  onClick={() => handlePreview(file)}
                />
              ) : (
                <div
                  className='flex flex-col items-center cursor-pointer'
                  onClick={() => handlePreview(file)}
                >
                  {getFileIcon(file.fileType)}
                  <span className='text-xs text-gray-500 mt-2 text-center'>
                    {fileUploadService.getFileTypeName(file.fileType)}
                  </span>
                </div>
              )}
            </div>

            <div className='space-y-2'>
              <h3 className='font-medium text-sm truncate' title={file.fileName}>
                {file.fileName}
              </h3>

              <div className='flex items-center justify-between'>
                <Badge
                  variant='secondary'
                  className={cn('text-xs', getFileTypeColor(file.fileType))}
                >
                  {fileUploadService.getFileTypeCategory(file.fileType)}
                </Badge>
                <span className='text-xs text-gray-500'>
                  {fileUploadService.formatFileSize(file.fileSize)}
                </span>
              </div>

              <div className='flex items-center gap-1 text-xs text-gray-500'>
                <User className='h-3 w-3' />
                <span className='truncate'>{file.uploadedByName || 'Unknown'}</span>
              </div>

              <div className='flex items-center gap-1 text-xs text-gray-500'>
                <Calendar className='h-3 w-3' />
                <span>{file.uploadedAt.toLocaleDateString()}</span>
              </div>

              <div className='flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity'>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={() => handlePreview(file)}
                  className='h-8 w-8 p-0'
                >
                  <Eye className='h-4 w-4' />
                </Button>
                <Button
                  variant='ghost'
                  size='sm'
                  onClick={() => handleDownload(file)}
                  className='h-8 w-8 p-0'
                >
                  <Download className='h-4 w-4' />
                </Button>
                {canDelete && (
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={() => handleDelete(file.id)}
                    className='h-8 w-8 p-0 text-red-600 hover:text-red-700'
                  >
                    <Trash2 className='h-4 w-4' />
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  const renderListView = () => (
    <div className='space-y-2'>
      {filteredFiles.map((file) => (
        <Card key={file.id} className='hover:shadow-sm transition-shadow'>
          <CardContent className='p-4'>
            <div className='flex items-center gap-4'>
              <div className='flex-shrink-0'>{getFileIcon(file.fileType)}</div>

              <div className='flex-1 min-w-0'>
                <div className='flex items-center gap-2 mb-1'>
                  <h3 className='font-medium text-sm truncate'>{file.fileName}</h3>
                  <Badge
                    variant='secondary'
                    className={cn('text-xs', getFileTypeColor(file.fileType))}
                  >
                    {fileUploadService.getFileTypeCategory(file.fileType)}
                  </Badge>
                </div>

                <div className='flex items-center gap-4 text-xs text-gray-500'>
                  <div className='flex items-center gap-1'>
                    <HardDrive className='h-3 w-3' />
                    <span>{fileUploadService.formatFileSize(file.fileSize)}</span>
                  </div>
                  <div className='flex items-center gap-1'>
                    <User className='h-3 w-3' />
                    <span>{file.uploadedByName || 'Unknown'}</span>
                  </div>
                  <div className='flex items-center gap-1'>
                    <Calendar className='h-3 w-3' />
                    <span>{file.uploadedAt.toLocaleDateString()}</span>
                  </div>
                </div>
              </div>

              <div className='flex items-center gap-1'>
                <Button variant='ghost' size='sm' onClick={() => handlePreview(file)}>
                  <Eye className='h-4 w-4' />
                </Button>
                <Button variant='ghost' size='sm' onClick={() => handleDownload(file)}>
                  <Download className='h-4 w-4' />
                </Button>
                {canDelete && (
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={() => handleDelete(file.id)}
                    className='text-red-600 hover:text-red-700'
                  >
                    <Trash2 className='h-4 w-4' />
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );

  return (
    <div className={cn('space-y-4', className)}>
      {/* Controls */}
      {(showSearch || showFilter) && (
        <div className='flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between'>
          <div className='flex flex-col sm:flex-row gap-2 flex-1'>
            {showSearch && (
              <div className='relative flex-1 max-w-sm'>
                <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
                <Input
                  placeholder='Search files...'
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className='pl-10'
                />
              </div>
            )}

            {showFilter && (
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className='w-40'>
                  <Filter className='h-4 w-4 mr-2' />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value='all'>All Files</SelectItem>
                  <SelectItem value='image'>Images</SelectItem>
                  <SelectItem value='document'>Documents</SelectItem>
                  <SelectItem value='other'>Other</SelectItem>
                </SelectContent>
              </Select>
            )}
          </div>

          <div className='flex items-center gap-2'>
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size='sm'
              onClick={() => setViewMode('grid')}
            >
              <Grid className='h-4 w-4' />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size='sm'
              onClick={() => setViewMode('list')}
            >
              <List className='h-4 w-4' />
            </Button>
          </div>
        </div>
      )}

      {/* File Count */}
      <div className='text-sm text-gray-500'>
        {filteredFiles.length} of {files.length} files
      </div>

      {/* Files */}
      {filteredFiles.length === 0 ? (
        <div className='text-center py-12 text-gray-500'>
          <FileText className='h-12 w-12 mx-auto mb-4 text-gray-300' />
          <p>No files found</p>
          {searchQuery && <p className='text-sm mt-2'>Try adjusting your search or filters</p>}
        </div>
      ) : viewMode === 'grid' ? (
        renderGridView()
      ) : (
        renderListView()
      )}

      {/* Image Preview Dialog */}
      {previewFile && fileUploadService.isImageUrl(previewFile.fileUrl) && (
        <Dialog open={!!previewFile} onOpenChange={() => setPreviewFile(null)}>
          <DialogContent className='max-w-4xl'>
            <DialogHeader>
              <DialogTitle>{previewFile.fileName}</DialogTitle>
            </DialogHeader>
            <div className='flex justify-center'>
              <img
                src={previewFile.fileUrl}
                alt={previewFile.fileName}
                className='max-w-full max-h-[70vh] object-contain rounded-lg'
              />
            </div>
            <div className='flex justify-center gap-2 mt-4'>
              <Button variant='outline' onClick={() => handleDownload(previewFile)}>
                <Download className='h-4 w-4 mr-2' />
                Download
              </Button>
              <Button variant='outline' onClick={() => window.open(previewFile.fileUrl, '_blank')}>
                <Eye className='h-4 w-4 mr-2' />
                Open in New Tab
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default FileGallery;
