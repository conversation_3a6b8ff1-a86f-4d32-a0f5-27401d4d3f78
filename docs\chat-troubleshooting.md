# Chat Troubleshooting Guide

This document provides guidance on troubleshooting chat-related issues in the Mr.Tech application, focusing particularly on permission issues between the web portal and mobile app.

## Common Issues

### 1. "User is not authorized to send messages in this chat" Error

This error typically occurs when:
- The chat status fields are inconsistent across platforms
- The user doesn't have the correct role assigned
- The Firestore security rules are not synchronized

## Chat Status Fields

For cross-platform compatibility, we use multiple field names to represent the same data. For chat status, we maintain the following fields in both Firestore and Realtime Database:

- `chat_active` (snake_case)
- `chatActive` (camelCase)
- `has_active_chat` (snake_case legacy field)

**All three fields must have the same value (true or false) for proper operation.**

## Fixing Chat Status Issues

### Option 1: Run the Fix Script

#### Web Portal
1. Navigate to the web-portal directory
2. Run the fix script:
   ```bash
   npm run fix-chat-active
   ```

#### Mobile App
1. Navigate to the mobile app directory
2. Run the fix script:
   ```bash
   flutter run -d chrome lib/scripts/fix_chat_active.dart
   ```

### Option 2: Manual Fix

1. Open Firebase Console
2. Navigate to Firestore Database
3. Check the `service_requests` collection
4. For each document, ensure the three chat status fields have consistent values

## Firestore Security Rules

The security rules must also be synchronized between platforms. If chat messages work on one platform but not the other, the issue might be with the security rules.

### Synchronizing Security Rules

Run the deploy script from the web portal directory:

```bash
cd web-portal
bash deploy-rules.sh
```

This script:
1. Updates the merged rules file
2. Copies the rules to the mobile app directory
3. Deploys the rules to Firebase

## Debugging Chat Permissions

When debugging chat permissions, check:

1. **User Role**
   - Is the user authenticated?
   - Does the user have the correct role (admin, technician, or customer)?
   
2. **Request Relationship**
   - Is the user the customer or technician assigned to this request?
   - For admins, do they have the admin role properly set in Firestore?
   
3. **Chat Status Fields**
   - Are all three chat status fields (`chat_active`, `chatActive`, and `has_active_chat`) consistent?
   
4. **Security Rules**
   - Are the security rules properly deployed and synchronized?
   - Do the rules allow the action being attempted?

## Recent Updates

We've made the following improvements to address chat permission issues:

1. **Updated Chat Status Management**
   - All three field names are now updated simultaneously
   - Check all field names when reading chat status
   
2. **Enhanced Admin Detection**
   - Improved the admin role detection
   - Added support for technician role fallback
   
3. **Normalized Security Rules**
   - Ensured consistent rules between web and mobile
   - Created a deployment script to keep rules synchronized

4. **Enhanced Error Handling**
   - Better error messages for debugging chat issues
   - More detailed logging when permission errors occur

## Testing Chat Functionality

After fixing issues, test the chat functionality on both platforms:

1. **Web Portal**
   - Log in as a technician and navigate to a service request
   - Verify you can send and receive messages
   
2. **Mobile App**
   - Log in as a customer with an active service request
   - Verify you can send and receive messages

## Additional Resources

- [Firebase Security Rules Documentation](https://firebase.google.com/docs/firestore/security/get-started)
- [Firestore Data Model](https://firebase.google.com/docs/firestore/data-model)
- [Flutter Firebase Authentication](https://firebase.flutter.dev/docs/auth/usage)