import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';

/// A minimal set of Firebase options for fast initialization.
/// 
/// This class provides just the essential Firebase configuration needed
/// for initial app startup. It excludes any configurations that might
/// slow down initialization.
class MinimalFirebaseOptions {
  /// Creates minimal Firebase options from the full options.
  static FirebaseOptions fromFullOptions() {
    final fullOptions = DefaultFirebaseOptions.currentPlatform;
    
    // Return a minimal version with only essential properties
    return FirebaseOptions(
      apiKey: fullOptions.apiKey,
      appId: fullOptions.appId,
      messagingSenderId: fullOptions.messagingSenderId,
      projectId: fullOptions.projectId,
    );
  }
} 