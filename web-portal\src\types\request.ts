import type { Timestamp } from 'firebase/firestore';

export enum RequestStatus {
  PAYMENT_PENDING = 'payment_pending',
  PENDING = 'pending',
  APPROVED = 'approved',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  REFUSED = 'refused',
}

export interface RequestModel {
  id: string;
  customer_id: string;
  service_id: string;
  service_name: string | { en: string; ar: string };
  service_description: string | { en: string; ar: string };
  customer_issue: string;
  anydesk_id?: string;
  technician_id?: string;
  technician_name?: string;
  status: RequestStatus;
  amount: number;
  is_paid: boolean;
  chat_active: boolean;
  session_active: boolean;
  created_at: Timestamp | Date;
  updated_at?: Timestamp | Date;
  scheduled_time?: Timestamp | Date;
  session_start_time?: Timestamp | Date;
  session_end_time?: Timestamp | Date;
  session_duration?: number; // in minutes
  customer_rated?: boolean;
  rating?: number;
  review_comment?: string;
  customer_fcm_token?: string;
  is_visible?: boolean;

  // Additional fields from Firestore
  customer_name?: string;
  customer_email?: string;
  customerName?: string; // Legacy field name
  customerEmail?: string; // Legacy field name
  customerFcmToken?: string; // Legacy field name
  customerId?: string; // Legacy field name
  customerIssue?: string; // Legacy field name
  serviceId?: string; // Legacy field name
  serviceName?: string | { en: string; ar: string }; // Legacy field name
  serviceDescription?: string | { en: string; ar: string }; // Legacy field name
  createdAt?: Timestamp | Date; // Legacy field name
  updatedAt?: Timestamp | Date; // Legacy field name
  isPaid?: boolean; // Legacy field name
  isVisible?: boolean; // Legacy field name
  chatActive?: boolean; // Legacy field name
  sessionActive?: boolean; // Legacy field name
}

export interface CreateRequestInput {
  customer_id: string;
  service_id: string;
  service_name: string;
  service_description: string;
  customer_issue: string;
  amount: number;
  anydesk_id?: string;
  scheduled_time?: Date;
  customer_fcm_token?: string;
}

export interface UpdateRequestInput {
  technician_id?: string;
  technician_name?: string;
  status?: RequestStatus;
  is_paid?: boolean;
  chat_active?: boolean;
  session_active?: boolean;
  scheduled_time?: Date;
  session_start_time?: Date;
  session_end_time?: Date;
  session_duration?: number;
  customer_rated?: boolean;
  rating?: number;
  review_comment?: string;
}

// Pagination interfaces for requests
export interface RequestFilters {
  status?: RequestStatus[];
  technician_id?: string;
  customer_id?: string;
  is_paid?: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
  amountRange?: {
    min: number;
    max: number;
  };
  search?: string;
}

export interface RequestsResponse {
  requests: RequestModel[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}
