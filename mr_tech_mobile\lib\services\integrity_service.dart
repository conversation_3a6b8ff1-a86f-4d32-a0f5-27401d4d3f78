import 'package:flutter/foundation.dart';
import 'package:flutter_security_checker/flutter_security_checker.dart';

/// Service for checking device and app integrity
class IntegrityService {
  // Singleton instance
  static final IntegrityService _instance = IntegrityService._internal();
  factory IntegrityService() => _instance;
  
  IntegrityService._internal();
  
  /// Check if the device is rooted or jailbroken
  /// Returns true if the device is compromised
  Future<bool> isDeviceRooted() async {
    try {
      // In debug mode, always return false to allow development
      if (kDebugMode) {
        debugPrint('[IntegrityService] Debug mode: Skipping root detection');
        return false;
      }
      
      final bool isRooted = await FlutterSecurityChecker.isRooted;
      if (isRooted) {
        debugPrint('[IntegrityService] WARNING: Device appears to be rooted/jailbroken');
      }
      return isRooted;
    } catch (e) {
      debugPrint('[IntegrityService] Error checking root status: $e');
      // If we can't check, assume it's not rooted for better user experience
      // but log the error
      return false;
    }
  }
  
  /// Check if the app is running on a real device (not an emulator)
  Future<bool> isRealDevice() async {
    try {
      final bool isReal = await FlutterSecurityChecker.isRealDevice;
      if (!isReal) {
        debugPrint('[IntegrityService] WARNING: App is running on an emulator');
      }
      return isReal;
    } catch (e) {
      debugPrint('[IntegrityService] Error checking device type: $e');
      // If we can't check, assume it's a real device
      return true;
    }
  }
  
  /// Check if the app was installed from an official store
  /// Returns true if installed correctly, false if sideloaded or in debug mode
  Future<bool> isInstalledFromStore() async {
    try {
      // In debug mode, always return true to allow development
      if (kDebugMode) {
        debugPrint('[IntegrityService] Debug mode: Skipping store verification');
        return true;
      }
      
      final bool isCorrectlyInstalled = await FlutterSecurityChecker.hasCorrectlyInstalled;
      if (!isCorrectlyInstalled) {
        debugPrint('[IntegrityService] WARNING: App was not installed from official store');
      }
      return isCorrectlyInstalled;
    } catch (e) {
      debugPrint('[IntegrityService] Error checking installation source: $e');
      // If we can't check, assume it's correctly installed
      return true;
    }
  }
  
  /// Perform a complete security check of the device and app
  /// Returns a map with the results of all checks
  Future<Map<String, bool>> performFullSecurityCheck() async {
    final Map<String, bool> results = {
      'isRooted': false,
      'isRealDevice': true,
      'isInstalledFromStore': true,
    };
    
    try {
      results['isRooted'] = await isDeviceRooted();
      results['isRealDevice'] = await isRealDevice();
      results['isInstalledFromStore'] = await isInstalledFromStore();
    } catch (e) {
      debugPrint('[IntegrityService] Error during full security check: $e');
    }
    
    return results;
  }
  
  /// Check if the device meets security requirements
  /// Returns true if the device is secure enough to run the app
  Future<bool> isDeviceSecure() async {
    // In debug mode, always return true to allow development
    if (kDebugMode) {
      return true;
    }
    
    final securityResults = await performFullSecurityCheck();
    
    // Device is considered insecure if it's rooted or not installed from store
    final bool isSecure = !securityResults['isRooted']! && 
                          securityResults['isInstalledFromStore']!;
    
    return isSecure;
  }
} 