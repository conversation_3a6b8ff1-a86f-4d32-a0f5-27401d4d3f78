#!/bin/bash

# Deploy Payment Callback Functions to Firebase
# This script deploys the Paymob payment callback functions

echo "🚀 Deploying Payment Callback Functions..."

# For Windows users: Use this batch file instead
# Create deploy-payment-callbacks.bat with the same commands

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "❌ Firebase CLI is not installed. Please install it first:"
    echo "npm install -g firebase-tools"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Please run this script from the firebase-config/functions directory"
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Deploy only the payment callback functions
echo "🔄 Deploying payment callback functions..."
firebase deploy --only functions:paymobTransactionProcessed,functions:paymobTransactionResponse

if [ $? -eq 0 ]; then
    echo "✅ Payment callback functions deployed successfully!"
    echo ""
    echo "📋 Your callback URLs are:"
    echo "🔔 Transaction Processed Callback: https://us-central1-stopnow-be6b7.cloudfunctions.net/paymobTransactionProcessed"
    echo "🔄 Transaction Response Callback: https://us-central1-stopnow-be6b7.cloudfunctions.net/paymobTransactionResponse"
    echo ""
    echo "📝 Next steps:"
    echo "1. Go to your Paymob dashboard"
    echo "2. Navigate to Developers > Payment Integrations"
    echo "3. Select Test mode"
    echo "4. Choose Integration ID (1164997)"
    echo "5. Click Edit and update the callback URLs with the URLs above"
    echo "6. Click Submit"
    echo ""
    echo "🧪 Test the payment flow from your mobile app to verify everything works!"
else
    echo "❌ Deployment failed. Please check the error messages above."
    exit 1
fi
