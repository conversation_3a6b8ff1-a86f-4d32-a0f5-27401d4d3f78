# ===================================
# MR.TECH.V2 - Complete Project .gitignore
# ===================================

# ===== NODE.JS / NPM =====
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity
*.tgz
package-lock.json.bak

# ===== ENVIRONMENT VARIABLES =====
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env~

# ===== BUILD OUTPUTS =====
/build
/dist
build/
dist/
out/

# ===== FIREBASE =====
.firebase/
firebase-debug.log*
firebase-debug.*.log*
ui-debug.log
.runtimeconfig.json
firebase-export-*
.firebaserc.local

# ===== FLUTTER / DART (Mobile App) =====
# Build outputs
mr_tech_mobile/build/
mr_tech_mobile/.dart_tool/
mr_tech_mobile/.packages
mr_tech_mobile/.pub-cache/
mr_tech_mobile/.pub/
mr_tech_mobile/pubspec.lock

# Flutter/Dart specific
mr_tech_mobile/.flutter-plugins
mr_tech_mobile/.flutter-plugins-dependencies
mr_tech_mobile/.metadata
mr_tech_mobile/ios/Flutter/flutter_export_environment.sh
mr_tech_mobile/ios/Runner.xcworkspace/
mr_tech_mobile/android/.gradle/
mr_tech_mobile/android/app/debug/
mr_tech_mobile/android/app/profile/
mr_tech_mobile/android/app/release/
mr_tech_mobile/android/gradle/
mr_tech_mobile/android/gradlew
mr_tech_mobile/android/gradlew.bat
mr_tech_mobile/android/local.properties
mr_tech_mobile/android/key.properties

# ===== WEB PORTAL (React/Vite) =====
web-portal/node_modules/
web-portal/dist/
web-portal/build/
web-portal/.env
web-portal/.env.local
web-portal/.env.development.local
web-portal/.env.test.local
web-portal/.env.production.local

# ===== FIREBASE CONFIG =====
firebase-config/node_modules/

# ===== LANDING PAGE =====
landing-page/node_modules/

# ===== LOGS =====
logs/
*.log
dev-debug.log
firebase-debug.log
ui-debug.log

# ===== TESTING =====
coverage/
.nyc_output
/coverage

# ===== EDITOR / IDE =====
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.cursor/
.roo/
.roomodes
.taskmaster/
.windsurfrules

# ===== OS SPECIFIC =====
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# ===== TEMPORARY FILES =====
*.tmp
*.temp
*.swp
*.swo
*~

# ===== SECURITY / SENSITIVE =====
mr_tech_mobile/firebase-admin-key.json
firebase-admin-key.json
*.pem
*.key
*.p12
*.keystore
*.jks
.taskmasterconfig

# Keystore and environment files with secrets
mr_tech_mobile/android/key.properties
mr_tech_mobile/android/local.properties
mr_tech_mobile/.env
web-portal/.env
firebase-config/.env
.env

# API Keys and tokens
**/google-services.json
**/GoogleService-Info.plist
**/*api*key*
**/*secret*
**/*token*

# ===== CACHE =====
.cache/
.parcel-cache/
.eslintcache

# ===== MISC =====
.dataconnect
tasks.json
tasks/
nul
*.pid
*.seed
*.pid.lock
.lock-wscript
lib-cov
.grunt
bower_components
.node_repl_history
/.pnp
.pnp.js

# ===== PYTHON (if any backend scripts) =====
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
/mr_tech_backend/venv

# ===== FUNCTIONS =====
/functions
