@echo off
REM Deploy Payment Callback Functions to Firebase
REM This script deploys the Paymob payment callback functions

echo 🚀 Deploying Payment Callback Functions...

REM Check if Firebase CLI is installed
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Firebase CLI is not installed. Please install it first:
    echo npm install -g firebase-tools
    pause
    exit /b 1
)

REM Check if we're in the right directory
if not exist "package.json" (
    echo ❌ Please run this script from the firebase-config/functions directory
    pause
    exit /b 1
)

REM Install dependencies
echo 📦 Installing dependencies...
npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

REM Deploy only the payment callback functions
echo 🔄 Deploying payment callback functions...
firebase deploy --only functions:paymobTransactionProcessed,functions:paymobTransactionResponse

if %errorlevel% equ 0 (
    echo ✅ Payment callback functions deployed successfully!
    echo.
    echo 📋 Your callback URLs are:
    echo 🔔 Transaction Processed Callback: https://us-central1-stopnow-be6b7.cloudfunctions.net/paymobTransactionProcessed
    echo 🔄 Transaction Response Callback: https://us-central1-stopnow-be6b7.cloudfunctions.net/paymobTransactionResponse
    echo.
    echo 📝 Next steps:
    echo 1. Go to your Paymob dashboard
    echo 2. Navigate to Developers ^> Payment Integrations
    echo 3. Select Test mode
    echo 4. Choose Integration ID ^(1164997^)
    echo 5. Click Edit and update the callback URLs with the URLs above
    echo 6. Click Submit
    echo.
    echo 🧪 Test the payment flow from your mobile app to verify everything works!
) else (
    echo ❌ Deployment failed. Please check the error messages above.
    pause
    exit /b 1
)

pause
