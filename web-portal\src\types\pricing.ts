import type { Timestamp } from 'firebase/firestore';

// Supported currencies
export type Currency = 'USD' | 'EUR' | 'GBP' | 'AED' | 'SAR' | 'QAR' | 'KWD' | 'BHD';

// Pricing strategies
export type PricingStrategy = 'fixed' | 'tiered' | 'time_based' | 'distance_based' | 'dynamic';

// Time-based pricing periods
export type TimePeriod = 'business_hours' | 'after_hours' | 'weekends' | 'holidays' | 'emergency';

// Geographic zones for location-based pricing
export interface GeographicZone {
  id: string;
  name: string;
  multiplier: number; // Price multiplier for this zone
  boundaries?: {
    lat: number;
    lng: number;
    radius: number; // in kilometers
  }[];
}

// Price tier configuration
export interface PriceTier {
  id: string;
  name: string | Record<string, string>; // Multilingual
  description?: string | Record<string, string>;
  base_price: number;
  currency: Currency;
  features: string[]; // What's included in this tier
  is_popular?: boolean; // For UI highlighting
  sort_order: number;
}

// Time-based pricing rules
export interface TimePricingRule {
  period: TimePeriod;
  multiplier: number; // Price multiplier
  days_of_week?: number[]; // 0-6, Sunday = 0
  start_time?: string; // HH:MM format
  end_time?: string; // HH:MM format
  is_active: boolean;
}

// Distance-based pricing
export interface DistancePricing {
  base_radius: number; // Free radius in km
  price_per_km: number; // Additional price per km
  max_distance?: number; // Maximum service distance
  currency: Currency;
}

// Discount and promotion configuration
export interface Discount {
  id: string;
  name: string | Record<string, string>;
  type: 'percentage' | 'fixed_amount' | 'buy_one_get_one';
  value: number; // Percentage (0-100) or fixed amount
  currency?: Currency; // Required for fixed_amount type
  min_order_value?: number;
  max_discount_amount?: number;
  valid_from: Date | Timestamp;
  valid_until: Date | Timestamp;
  usage_limit?: number;
  used_count: number;
  applicable_services?: string[]; // Service IDs
  applicable_categories?: string[]; // Category names
  user_restrictions?: {
    new_users_only?: boolean;
    user_ids?: string[]; // Specific users only
    exclude_user_ids?: string[]; // Exclude specific users
  };
  is_active: boolean;
  created_at: Date | Timestamp;
  updated_at?: Date | Timestamp;
}

// Tax configuration
export interface TaxConfiguration {
  id: string;
  name: string;
  rate: number; // Tax rate as percentage (e.g., 5 for 5%)
  type: 'vat' | 'sales_tax' | 'service_tax' | 'custom';
  applicable_regions: string[]; // Geographic regions where this tax applies
  is_inclusive: boolean; // Whether tax is included in displayed price
  is_active: boolean;
}

// Main service pricing configuration
export interface ServicePricing {
  id: string;
  service_id: string;
  strategy: PricingStrategy;

  // Fixed pricing (simple)
  base_price?: number;
  currency: Currency;

  // Tiered pricing
  tiers?: PriceTier[];

  // Time-based pricing
  time_rules?: TimePricingRule[];

  // Distance-based pricing
  distance_pricing?: DistancePricing;

  // Geographic pricing
  geographic_zones?: GeographicZone[];
  default_zone_multiplier: number;

  // Tax configuration
  tax_configuration_ids: string[];

  // Discounts
  applicable_discount_ids?: string[];

  // Emergency/rush pricing
  emergency_multiplier: number;

  // Minimum and maximum prices
  min_price?: number;
  max_price?: number;

  // Additional metadata
  metadata?: Record<string, any>;

  // Audit fields
  is_active: boolean;
  created_at: Date | Timestamp;
  updated_at?: Date | Timestamp;
}

// Calculated price result
export interface PriceCalculation {
  base_price: number;
  currency: Currency;

  // Applied modifiers
  time_multiplier?: number;
  distance_multiplier?: number;
  zone_multiplier?: number;
  emergency_multiplier?: number;

  // Discounts applied
  discounts_applied: {
    discount_id: string;
    discount_name: string;
    amount: number;
    type: 'percentage' | 'fixed_amount';
  }[];

  // Tax calculations
  taxes: {
    tax_id: string;
    tax_name: string;
    rate: number;
    amount: number;
    is_inclusive: boolean;
  }[];

  // Final pricing breakdown
  subtotal: number; // Before taxes and discounts
  total_discount: number;
  price_after_discount: number;
  total_tax: number;
  final_price: number;

  // Additional info
  tier_selected?: string; // For tiered pricing
  calculation_breakdown: string[]; // Human-readable breakdown
}

// Input for price calculation
export interface PriceCalculationInput {
  service_id: string;

  // Location context
  customer_location?: {
    lat: number;
    lng: number;
  };

  // Time context
  requested_datetime?: Date;
  is_emergency?: boolean;

  // User context
  user_id?: string;
  is_new_user?: boolean;

  // Service specific
  selected_tier_id?: string; // For tiered pricing
  estimated_duration?: number; // May affect pricing

  // Applied discounts
  discount_codes?: string[];
}

// Default currency rates for conversion (should be updated from external API)
export interface CurrencyRate {
  from_currency: Currency;
  to_currency: Currency;
  rate: number;
  last_updated: Date | Timestamp;
}

// Input types for creating/updating pricing
export interface CreateServicePricingInput {
  service_id: string;
  strategy: PricingStrategy;
  base_price?: number;
  currency: Currency;
  tiers?: Omit<PriceTier, 'id'>[];
  time_rules?: TimePricingRule[];
  distance_pricing?: DistancePricing;
  geographic_zones?: GeographicZone[];
  default_zone_multiplier?: number;
  tax_configuration_ids?: string[];
  emergency_multiplier?: number;
  min_price?: number;
  max_price?: number;
  metadata?: Record<string, any>;
  is_active?: boolean;
}

export interface UpdateServicePricingInput {
  strategy?: PricingStrategy;
  base_price?: number;
  currency?: Currency;
  tiers?: PriceTier[];
  time_rules?: TimePricingRule[];
  distance_pricing?: DistancePricing;
  geographic_zones?: GeographicZone[];
  default_zone_multiplier?: number;
  tax_configuration_ids?: string[];
  emergency_multiplier?: number;
  min_price?: number;
  max_price?: number;
  metadata?: Record<string, any>;
  is_active?: boolean;
}

// Configuration presets for common pricing strategies
export const PRICING_PRESETS = {
  SIMPLE_FIXED: {
    strategy: 'fixed' as PricingStrategy,
    emergency_multiplier: 1.5,
    default_zone_multiplier: 1.0,
  },
  TIME_BASED: {
    strategy: 'time_based' as PricingStrategy,
    time_rules: [
      { period: 'business_hours' as TimePeriod, multiplier: 1.0, is_active: true },
      { period: 'after_hours' as TimePeriod, multiplier: 1.3, is_active: true },
      { period: 'weekends' as TimePeriod, multiplier: 1.2, is_active: true },
      { period: 'holidays' as TimePeriod, multiplier: 1.5, is_active: true },
      { period: 'emergency' as TimePeriod, multiplier: 2.0, is_active: true },
    ],
    emergency_multiplier: 2.0,
    default_zone_multiplier: 1.0,
  },
  TIERED: {
    strategy: 'tiered' as PricingStrategy,
    emergency_multiplier: 1.5,
    default_zone_multiplier: 1.0,
  },
} as const;
