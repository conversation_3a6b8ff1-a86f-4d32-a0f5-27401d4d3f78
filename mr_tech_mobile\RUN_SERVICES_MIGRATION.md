# 🚀 Run Services Migration - Test Your App Keeps Working!

## 📋 **What This Does**

This migration will **ADD** snake_case fields to your existing services without removing anything:

### **Before Migration** (Your Current Database):
```json
{
  "basePrice": 200,
  "isActive": true,
  "estimatedDuration": 90
}
```

### **After Migration** (Both Formats Available):
```json
{
  "basePrice": 200,
  "isActive": true,
  "estimatedDuration": 90,
  "base_price": 200,
  "is_active": true,
  "estimated_duration": 90
}
```

## 🛡️ **Safety Guarantees**

- ✅ **No data removed** - Only adds snake_case fields
- ✅ **App keeps working** - ServiceModel reads both formats
- ✅ **Instant rollback** - Can remove snake_case fields anytime
- ✅ **Error handling** - Safe fallbacks if anything goes wrong

---

## 🎯 **METHOD 1: Standalone Script (Recommended)**

### **Step 1: Run the Migration Script**
```bash
cd mr_tech_mobile
dart run scripts/migrate_services.dart
```

### **Step 2: Follow the Prompts**
- <PERSON><PERSON><PERSON> will show current status
- Ask for confirmation before making changes
- Show detailed progress during migration
- Validate results after completion

### **Step 3: Test Your App**
```bash
flutter run
```
- Open services screen
- Check that all services load correctly
- Look at debug console for field usage logs

---

## 🎯 **METHOD 2: From Flutter App (Alternative)**

### **Option A: Add Test Widget to Your App**

1. **Temporarily add this to your main navigation:**
```dart
// In your main app, add a debug button
ElevatedButton(
  onPressed: () {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ServicesMigrationTestWidget(),
      ),
    );
  },
  child: Text('Test Services Migration'),
),
```

2. **Import the test file:**
```dart
import 'test_services_migration.dart';
```

3. **Run your app and tap the migration button**

### **Option B: Call Directly in Code**
```dart
// Add this to any button or initialization
await TestServicesMigration.runMigrationTest();
```

---

## 🔍 **What to Expect**

### **Console Output Example:**
```
🚀 STARTING SERVICES MIGRATION TEST
══════════════════════════════════════════════════════

📊 BEFORE MIGRATION - Current Services:
   Total Services: 6
   - With snake_case fields: 0
   - CamelCase only: 6
   - Migration progress: 0%

🔄 RUNNING MIGRATION...
   ✅ Migrated: Virus Removal
      Added: base_price, is_active, estimated_duration
   ✅ Migrated: System Optimization
      Added: base_price, is_active, estimated_duration

📊 AFTER MIGRATION - Updated Services:
   Total Services: 6
   - With snake_case fields: 6
   - CamelCase only: 0
   - Migration progress: 100%

🎉 MIGRATION COMPLETED SUCCESSFULLY!
✅ All services now have both camelCase AND snake_case fields
✅ Your app will work with both field formats
✅ ServiceModel will prefer snake_case but fallback to camelCase
```

### **App Behavior:**
- **Services load normally** ✅
- **No crashes** ✅
- **Debug logs show field usage** ✅
- **Both field formats work** ✅

---

## 🧪 **Testing Verification**

### **1. Before Migration:**
- App loads services using camelCase fields
- Console logs: "Using camelCase fallback for base_price"

### **2. After Migration:**
- App loads services using snake_case fields (preferred)
- Console logs show successful snake_case field usage
- No more fallback messages

### **3. Compatibility Test:**
- Temporarily remove snake_case fields from one service
- App should still work using camelCase fallback
- Re-add snake_case fields and verify preference

---

## 📊 **Expected Results**

| Test Case | Before Migration | After Migration |
|-----------|------------------|-----------------|
| **Field Reading** | camelCase only | snake_case preferred, camelCase fallback |
| **App Crashes** | None | None |
| **Service Loading** | ✅ Works | ✅ Works |
| **Database Size** | 100% | ~150% (dual fields) |
| **Performance** | Normal | Normal |
| **Compatibility** | Web portal only | Both mobile + web |

---

## 🚨 **If Something Goes Wrong**

### **Emergency Rollback:**
```bash
# Remove snake_case fields (keep camelCase)
# This script can be created if needed
dart run scripts/rollback_services.dart
```

### **Manual Field Removal:**
```javascript
// In Firebase Console, run this query
services.get().then(snapshot => {
  snapshot.forEach(doc => {
    doc.ref.update({
      'base_price': firebase.firestore.FieldValue.delete(),
      'is_active': firebase.firestore.FieldValue.delete(),
      'estimated_duration': firebase.firestore.FieldValue.delete()
    });
  });
});
```

---

## ✅ **Ready to Test?**

**Run this command to start:**
```bash
cd mr_tech_mobile
dart run scripts/migrate_services.dart
```

**This will prove that your app continues working during field naming changes!** 🎉 