import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/translation_service.dart';
import '../services/auth_service.dart';
import '../services/app_service.dart';
import 'main_navigation.dart';
import '../widgets/text_styles.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  final int _numPages = 3;
  bool _isLoading = false;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _completeOnboarding() async {
    setState(() => _isLoading = true);
    
    try {
      // Update user's onboarding status in Firestore
      final authService = Provider.of<AuthService>(context, listen: false);
      await authService.updateOnboardingStatus(true);
      
      // Also update onboarding status in AppService
      final appService = Provider.of<AppService>(context, listen: false);
      await appService.completeOnboarding();
      
      // Navigate to main navigation
      if (mounted) {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const MainNavigation()),
          (route) => false, // Remove all previous routes
        );
      }
    } catch (e) {
      // Show error
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  void _nextPage() {
    if (_currentPage < _numPages - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      // Complete onboarding
      _completeOnboarding();
    }
  }

  void _skipOnboarding() async {
    setState(() => _isLoading = true);
    
    try {
      // Update user's onboarding status in Firestore
      final authService = Provider.of<AuthService>(context, listen: false);
      await authService.updateOnboardingStatus(true);
      
      // Also update onboarding status in AppService
      final appService = Provider.of<AppService>(context, listen: false);
      await appService.completeOnboarding();
      
      // Navigate to main navigation
      if (mounted) {
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const MainNavigation()),
          (route) => false, // Remove all previous routes
        );
      }
    } catch (e) {
      // Show error
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final translationService = Provider.of<TranslationService>(context);
    final translate = translationService.translate;
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: SafeArea(
        child: Column(
          children: [
            // Skip button
            Align(
              alignment: Alignment.topRight,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: TextButton(
                  onPressed: _isLoading ? null : _skipOnboarding,
                  child: Text(
                    translate('Skip'),
                    style: AppTextStyles.buttonMedium(context, color: colorScheme.primary),
                  ),
                ),
              ),
            ),
            
            // Page view
            Expanded(
              child: PageView(
                controller: _pageController,
                onPageChanged: (int page) {
                  setState(() {
                    _currentPage = page;
                  });
                },
                children: [
                  _buildOnboardingPage(
                    icon: Icons.info_outline,
                    title: translate('How Mr.Tech Works'),
                    description: translate('Get instant remote tech support from certified technicians for Windows, Office, Printer issues, and more.'),
                    colorScheme: colorScheme,
                  ),
                  _buildOnboardingPage(
                    icon: Icons.computer,
                    title: translate('AnyDesk Setup'),
                    description: translate('When a technician accepts your request, you\'ll be guided to install AnyDesk for remote access to your computer.'),
                    colorScheme: colorScheme,
                  ),
                  _buildOnboardingPage(
                    icon: Icons.security,
                    title: translate('Your Safety First'),
                    description: translate('All our technicians are certified and verified. Sessions are secure and only start with your explicit permission.'),
                    colorScheme: colorScheme,
                  ),
                ],
              ),
            ),
            
            // Page indicator
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 24.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: _buildPageIndicator(colorScheme),
              ),
            ),
            
            // Next or Get Started button
            Padding(
              padding: const EdgeInsets.all(24.0),
              child: SizedBox(
                width: double.infinity,
                height: 52,
                child: FilledButton.icon(
                  onPressed: _isLoading ? null : _nextPage,
                  style: FilledButton.styleFrom(
                    backgroundColor: colorScheme.primary,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    elevation: 0,
                  ),
                  icon: _isLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : (_currentPage == _numPages - 1 
                          ? const Icon(Icons.check, size: 20)
                          : const Icon(Icons.arrow_forward, size: 20)),
                  label: Text(
                    _currentPage == _numPages - 1 
                        ? translate('Get Started') 
                        : translate('Next'),
                    style: AppTextStyles.buttonMedium(context, color: Colors.white),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOnboardingPage({
    required IconData icon,
    required String title,
    required String description,
    required ColorScheme colorScheme,
  }) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Simplified icon container without gradient or shadow
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: colorScheme.primary.withOpacity(0.1),
              shape: BoxShape.circle,
              border: Border.all(color: Colors.grey.shade300, width: 2),
            ),
            child: Icon(
              icon,
              size: 60,
              color: colorScheme.primary,
            ),
          ),
          const SizedBox(height: 40),
          Text(
            title,
            style: AppTextStyles.headingLarge(context, color: colorScheme.onSurface),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          Text(
            description,
            style: AppTextStyles.bodyLarge(
              context,
              color: colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  List<Widget> _buildPageIndicator(ColorScheme colorScheme) {
    List<Widget> indicators = [];
    for (int i = 0; i < _numPages; i++) {
      indicators.add(
        AnimatedContainer(
          duration: const Duration(milliseconds: 150),
          width: _currentPage == i ? 24.0 : 10.0,
          height: 10.0,
          margin: const EdgeInsets.symmetric(horizontal: 4.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.0),
            color: _currentPage == i
                ? colorScheme.primary
                : Colors.grey.shade300,
          ),
        ),
      );
    }
    return indicators;
  }
} 