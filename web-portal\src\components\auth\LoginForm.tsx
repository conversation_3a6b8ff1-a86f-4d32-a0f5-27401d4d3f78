import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { AlertCircle, Eye, EyeOff, LogIn } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { type LoginCredentials } from '../../types/auth';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Checkbox } from '../ui/checkbox';
import { LoadingButton, StatusIndicator } from '../ui/loading-states';

type LoginFormData = LoginCredentials & { rememberMe?: boolean };

// Validation schema
const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  rememberMe: z.boolean().optional(),
});

const LoginForm: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { login, error, clearError } = useAuth();
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const from = location.state?.from?.pathname || '/dashboard';

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      rememberMe: false,
    },
  });

  const rememberMe = watch('rememberMe');

  const onSubmit = async (data: LoginFormData) => {
    try {
      setIsLoading(true);
      clearError();
      await login(data);
      navigate(from, { replace: true });
    } catch (err) {
      // Error is handled by context
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className='w-full max-w-md mx-auto'>
      <Card>
        <CardHeader className='text-center'>
          <CardTitle className='text-3xl font-bold'>Welcome Back</CardTitle>
          <CardDescription>Sign in to your account</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className='space-y-6'>
            {error && (
              <div className='bg-destructive/10 border border-destructive text-destructive px-4 py-3 rounded-md text-sm'>
                {error}
              </div>
            )}

            <div>
              <Label htmlFor='email'>
                Email Address <span className='text-destructive'>*</span>
              </Label>
              <Input
                {...register('email')}
                type='email'
                id='email'
                placeholder='<EMAIL>'
                autoComplete='email'
                disabled={isLoading}
                className={errors.email ? 'border-destructive focus-visible:ring-destructive' : ''}
                aria-invalid={errors.email ? 'true' : 'false'}
                aria-describedby={errors.email ? 'email-error' : undefined}
              />
              {errors.email && (
                <p
                  id='email-error'
                  className='mt-1 text-sm text-destructive flex items-center gap-1'
                >
                  <AlertCircle className='w-4 h-4' />
                  {errors.email.message}
                </p>
              )}
            </div>

            <div>
              <Label htmlFor='password'>
                Password <span className='text-destructive'>*</span>
              </Label>
              <div className='relative'>
                <Input
                  {...register('password')}
                  type={showPassword ? 'text' : 'password'}
                  id='password'
                  className={`pr-10 ${errors.password ? 'border-destructive focus-visible:ring-destructive' : ''}`}
                  placeholder='••••••••'
                  autoComplete='current-password'
                  disabled={isLoading}
                  aria-invalid={errors.password ? 'true' : 'false'}
                  aria-describedby={errors.password ? 'password-error' : undefined}
                />
                <button
                  type='button'
                  className='absolute inset-y-0 right-0 pr-3 flex items-center disabled:opacity-50'
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={isLoading}
                  aria-label={showPassword ? 'Hide password' : 'Show password'}
                >
                  {showPassword ? (
                    <EyeOff className='h-5 w-5 text-muted-foreground' />
                  ) : (
                    <Eye className='h-5 w-5 text-muted-foreground' />
                  )}
                </button>
              </div>
              {errors.password && (
                <p
                  id='password-error'
                  className='mt-1 text-sm text-destructive flex items-center gap-1'
                >
                  <AlertCircle className='w-4 h-4' />
                  {errors.password.message}
                </p>
              )}
            </div>

            <div className='flex items-center justify-between'>
              <div className='flex items-center space-x-2'>
                <Checkbox
                  id='rememberMe'
                  checked={rememberMe}
                  onCheckedChange={(checked) => {
                    setValue('rememberMe', checked as boolean);
                  }}
                />
                <label
                  htmlFor='rememberMe'
                  className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'
                >
                  Remember me
                </label>
              </div>
              <Link to='/forgot-password' className='text-sm text-primary hover:text-primary/80'>
                Forgot password?
              </Link>
            </div>

            <LoadingButton
              type='submit'
              isLoading={isLoading}
              loadingText='Signing in...'
              disabled={!watch('email') || !watch('password')}
              className='w-full'
            >
              <LogIn className='h-4 w-4 mr-2' />
              Sign In
            </LoadingButton>
          </form>

          <div className='mt-6 text-center'>
            <p className='text-sm text-muted-foreground'>For technicians and administrators only</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default LoginForm;
