import { type QueryConstraint, orderBy, where } from 'firebase/firestore';
import { FirebaseService, type QueryOptions } from './firebaseService';
import type { CreateCategoryInput, ServiceCategory, UpdateCategoryInput } from '../types/category';

class CategoryService extends FirebaseService<ServiceCategory> {
  constructor() {
    super('service_categories');
  }

  // Get active categories sorted by order
  async getActiveCategories(options?: QueryOptions): Promise<ServiceCategory[]> {
    const constraints: QueryConstraint[] = [where('is_active', '==', true)];

    if (options?.orderBy) {
      constraints.push(orderBy(options.orderBy.field, options.orderBy.direction));
    } else {
      constraints.push(orderBy('sort_order', 'asc'), orderBy('name', 'asc'));
    }

    return this.query(constraints);
  }

  // Get all categories including inactive ones
  async getAllCategories(): Promise<ServiceCategory[]> {
    const constraints: QueryConstraint[] = [orderBy('sort_order', 'asc'), orderBy('name', 'asc')];

    return this.query(constraints);
  }

  // Create a new category
  async createCategory(data: CreateCategoryInput): Promise<ServiceCategory> {
    const categoryData = {
      ...data,
      is_active: data.is_active ?? true,
      sort_order: data.sort_order ?? 999, // Default to end
    };

    return this.create(categoryData as Omit<ServiceCategory, 'id'>);
  }

  // Update category
  async updateCategory(id: string, data: UpdateCategoryInput): Promise<ServiceCategory> {
    return this.update(id, data);
  }

  // Toggle category active status
  async toggleActiveStatus(id: string): Promise<ServiceCategory> {
    const category = await this.getById(id);
    if (!category) {
      throw new Error('Category not found');
    }

    return this.update(id, { is_active: !category.is_active });
  }

  // Reorder categories
  async reorderCategories(categoryIds: string[]): Promise<void> {
    const updatePromises = categoryIds.map((id, index) =>
      this.update(id, { sort_order: index + 1 }),
    );

    await Promise.all(updatePromises);
  }

  // Get translated name
  getTranslatedName(category: ServiceCategory, languageCode: string): string {
    if (typeof category.name === 'string') {
      return category.name;
    }

    return (
      category.name[languageCode] ||
      category.name['en'] ||
      Object.values(category.name)[0] ||
      'Unnamed Category'
    );
  }

  // Get translated description
  getTranslatedDescription(category: ServiceCategory, languageCode: string): string {
    if (!category.description) return '';

    if (typeof category.description === 'string') {
      return category.description;
    }

    return (
      category.description[languageCode] ||
      category.description['en'] ||
      Object.values(category.description)[0] ||
      ''
    );
  }

  // Search categories by name or description
  async searchCategories(
    searchTerm: string,
    languageCode: string = 'en',
  ): Promise<ServiceCategory[]> {
    const categories = await this.getActiveCategories();

    const lowerSearchTerm = searchTerm.toLowerCase();

    return categories.filter((category) => {
      const name = this.getTranslatedName(category, languageCode).toLowerCase();
      const description = this.getTranslatedDescription(category, languageCode).toLowerCase();

      return name.includes(lowerSearchTerm) || description.includes(lowerSearchTerm);
    });
  }

  // Check if category name already exists
  async categoryNameExists(
    name: string | Record<string, string>,
    excludeId?: string,
  ): Promise<boolean> {
    const allCategories = await this.getAllCategories();

    return allCategories.some((category) => {
      if (excludeId && category.id === excludeId) return false;

      if (typeof name === 'string') {
        // Check against string names and all translation values
        if (typeof category.name === 'string') {
          return category.name.toLowerCase() === name.toLowerCase();
        } else {
          return Object.values(category.name).some(
            (translatedName: string) => translatedName.toLowerCase() === name.toLowerCase(),
          );
        }
      } else {
        // Check translation overlap
        if (typeof category.name === 'string') {
          return Object.values(name).some(
            (translatedName: string) =>
              translatedName.toLowerCase() === (category.name as string).toLowerCase(),
          );
        } else {
          const categoryNameRecord = category.name as Record<string, string>;
          return Object.entries(name).some(
            ([lang, translatedName]) =>
              categoryNameRecord[lang] &&
              categoryNameRecord[lang].toLowerCase() === translatedName.toLowerCase(),
          );
        }
      }
    });
  }

  // Get category statistics
  async getCategoryStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
  }> {
    const allCategories = await this.getAllCategories();
    const active = allCategories.filter((cat) => cat.is_active);

    return {
      total: allCategories.length,
      active: active.length,
      inactive: allCategories.length - active.length,
    };
  }
}

// Export singleton instance
export default new CategoryService();
