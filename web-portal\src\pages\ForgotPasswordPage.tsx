import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ArrowLeft } from 'lucide-react';
import authService from '../services/authService';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { useAuth } from '../contexts/AuthContext';

// Validation schema
const forgotPasswordSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>;

const ForgotPasswordPage: React.FC = () => {
  const { resetPassword } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ForgotPasswordFormData>({
    resolver: zodResolver(forgotPasswordSchema),
  });

  const onSubmit = async (data: ForgotPasswordFormData) => {
    try {
      setIsLoading(true);
      setError(null);
      await resetPassword(data.email);
      setIsSuccess(true);
      reset();
    } catch (err: any) {
      setError(err.message || 'Failed to send reset email. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className='min-h-screen flex items-center justify-center bg-background px-4 py-12 sm:px-6 lg:px-8'>
      <div className='w-full max-w-md'>
        <Card>
          <CardHeader className='text-center'>
            <CardTitle className='text-3xl font-bold'>Forgot Password</CardTitle>
            <CardDescription>
              {!isSuccess
                ? "Enter your email address and we'll send you a link to reset your password."
                : 'Check your email for password reset instructions.'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {!isSuccess ? (
              <form onSubmit={handleSubmit(onSubmit)} className='space-y-6'>
                {error && (
                  <div className='bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm'>
                    {error}
                  </div>
                )}

                <div>
                  <Label htmlFor='email'>Email Address</Label>
                  <Input
                    {...register('email')}
                    type='email'
                    id='email'
                    placeholder='<EMAIL>'
                    autoComplete='email'
                    disabled={isLoading}
                  />
                  {errors.email && (
                    <p className='mt-1 text-sm text-destructive'>{errors.email.message}</p>
                  )}
                </div>

                <Button type='submit' disabled={isLoading} className='w-full'>
                  {isLoading ? 'Sending...' : 'Send Reset Email'}
                </Button>

                <div className='text-center'>
                  <Link
                    to='/login'
                    className='text-sm text-blue-600 hover:text-blue-700 inline-flex items-center'
                  >
                    <ArrowLeft className='h-4 w-4 mr-1' />
                    Back to Login
                  </Link>
                </div>
              </form>
            ) : (
              <div className='space-y-6'>
                <div className='bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md text-sm'>
                  Password reset email sent successfully! Please check your inbox.
                </div>

                <p className='text-sm text-muted-foreground text-center'>
                  If you don't see the email, please check your spam folder.
                </p>

                <div className='text-center'>
                  <Link
                    to='/login'
                    className='text-sm text-blue-600 hover:text-blue-700 inline-flex items-center'
                  >
                    <ArrowLeft className='h-4 w-4 mr-1' />
                    Back to Login
                  </Link>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ForgotPasswordPage;
