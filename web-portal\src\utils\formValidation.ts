import { z } from 'zod';

// Common validation schemas
export const commonValidation = {
  email: z.string().email('Please enter a valid email address'),
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number'),
  phone: z
    .string()
    .regex(/^\+?[\d\s\-()]+$/, 'Please enter a valid phone number')
    .optional(),
  name: z
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must be less than 100 characters'),
  url: z.string().url('Please enter a valid URL').optional(),
  price: z.number().min(0, 'Price must be a positive number'),
  percentage: z
    .number()
    .min(0, 'Percentage must be at least 0')
    .max(100, 'Percentage cannot exceed 100'),
};

// Form validation helpers
export const formValidationHelpers = {
  // Check if field has error
  hasError: (errors: any, fieldName: string): boolean => {
    return !!errors[fieldName];
  },

  // Get error message
  getErrorMessage: (errors: any, fieldName: string): string | undefined => {
    return errors[fieldName]?.message;
  },

  // Get field props for validation styling
  getFieldProps: (errors: any, fieldName: string, isLoading = false) => ({
    className: errors[fieldName] ? 'border-destructive focus-visible:ring-destructive' : '',
    disabled: isLoading,
    'aria-invalid': errors[fieldName] ? 'true' : 'false',
    'aria-describedby': errors[fieldName] ? `${fieldName}-error` : undefined,
  }),

  // Get error message props for rendering
  getErrorMessageProps: (errors: any, fieldName: string) => {
    if (!errors[fieldName]) return null;

    return {
      id: `${fieldName}-error`,
      className: 'text-sm text-destructive flex items-center gap-1 mt-1',
      message: errors[fieldName].message,
    };
  },
};

// Service form validation schema
export const serviceFormSchema = z.object({
  category: z.string().min(1, 'Category is required'),
  base_price: z.number().min(0, 'Base price must be a positive number'),
  currency: z.enum(['USD', 'EUR', 'GBP', 'EGP'], {
    errorMap: () => ({ message: 'Please select a valid currency' }),
  }),
  image_url: z.string().url('Please enter a valid image URL').optional().or(z.literal('')),
  is_active: z.boolean(),
  estimated_duration: z.number().min(1, 'Duration must be at least 1 minute'),
  pricing_strategy: z.enum(['fixed', 'hourly', 'custom'], {
    errorMap: () => ({ message: 'Please select a valid pricing strategy' }),
  }),
  has_advanced_pricing: z.boolean(),
  metadata: z.record(z.any()).optional(),
});

// Technician form validation schema
export const technicianFormSchema = z.object({
  name: commonValidation.name,
  email: commonValidation.email,
  phone_number: commonValidation.phone,
  photo_url: commonValidation.url,
  specialties: z.array(z.string()).min(1, 'At least one specialty is required'),
  status: z.enum(['active', 'inactive', 'onLeave'], {
    errorMap: () => ({ message: 'Please select a valid status' }),
  }),
  is_available: z.boolean(),
  password: z.string().min(8, 'Password must be at least 8 characters').optional(),
  createUserAccount: z.boolean().optional(),
});

// Request form validation schema
export const requestFormSchema = z.object({
  customer_id: z.string().min(1, 'Customer is required'),
  service_id: z.string().min(1, 'Service is required'),
  service_name: z.string().min(1, 'Service name is required'),
  service_description: z.string().min(1, 'Service description is required'),
  customer_issue: z.string().min(10, 'Please describe the issue in at least 10 characters'),
  amount: commonValidation.price,
  anydesk_id: z.string().optional(),
  scheduled_time: z.date().optional(),
  customer_fcm_token: z.string().optional(),
});

// Chat message validation schema
export const chatMessageSchema = z.object({
  content: z.string().min(1, 'Message cannot be empty').max(1000, 'Message is too long'),
  messageType: z.enum(['text', 'image', 'file', 'system']),
  fileUrl: z.string().url('Invalid file URL').optional(),
});

// Form state management helpers
export const formStateHelpers = {
  // Create initial loading state
  createInitialState: () => ({
    isLoading: false,
    isSaving: false,
    saveSuccess: false,
    error: null,
  }),

  // Update loading state
  setLoading: (setState: any, isLoading: boolean) => {
    setState((prev: any) => ({ ...prev, isLoading }));
  },

  // Update saving state
  setSaving: (setState: any, isSaving: boolean) => {
    setState((prev: any) => ({ ...prev, isSaving, saveSuccess: false, error: null }));
  },

  // Set success state
  setSuccess: (setState: any, message?: string) => {
    setState((prev: any) => ({
      ...prev,
      isSaving: false,
      saveSuccess: true,
      error: null,
      successMessage: message,
    }));
  },

  // Set error state
  setError: (setState: any, error: string) => {
    setState((prev: any) => ({
      ...prev,
      isSaving: false,
      saveSuccess: false,
      error,
    }));
  },
};

// Validation messages
export const validationMessages = {
  required: 'This field is required',
  email: 'Please enter a valid email address',
  phone: 'Please enter a valid phone number',
  url: 'Please enter a valid URL',
  minLength: (min: number) => `Must be at least ${min} characters`,
  maxLength: (max: number) => `Must be less than ${max} characters`,
  positiveNumber: 'Must be a positive number',
  percentage: 'Must be between 0 and 100',
};
