import { useCallback, useEffect, useRef, useState } from 'react';
import type { RequestStatus } from '../types/request';
import { type RequestModel } from '../types/request';
import realtimeStatusService from '../services/realtimeStatusService';

/**
 * Hook for listening to real-time status changes for a specific request
 */
export function useRealtimeRequestStatus(requestId: string | null) {
  const [request, setRequest] = useState<RequestModel | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const unsubscribeRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    if (!requestId) {
      setRequest(null);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    // Set up real-time listener
    const unsubscribe = realtimeStatusService.listenToRequestStatus(requestId, (updatedRequest) => {
      setRequest(updatedRequest);
      setLoading(false);
      if (!updatedRequest) {
        setError('Request not found');
      }
    });

    unsubscribeRef.current = unsubscribe;

    // Cleanup function
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, [requestId]);

  return { request, loading, error };
}

/**
 * Hook for listening to real-time status changes for requests with specific status
 */
export function useRealtimeRequestsByStatus(status: RequestStatus) {
  const [requests, setRequests] = useState<RequestModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const unsubscribeRef = useRef<(() => void) | null>(null);
  const listenerIdRef = useRef<string>(`status_${status}_${Date.now()}`);

  useEffect(() => {
    setLoading(true);
    setError(null);

    // Set up real-time listener
    const unsubscribe = realtimeStatusService.listenToRequestsByStatus(
      status,
      (updatedRequests) => {
        setRequests(updatedRequests);
        setLoading(false);
      },
      listenerIdRef.current,
    );

    unsubscribeRef.current = unsubscribe;

    // Cleanup function
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, [status]);

  return { requests, loading, error };
}

/**
 * Hook for listening to real-time changes for all requests with optional filters
 */
export function useRealtimeRequests(filters?: {
  technicianId?: string;
  customerId?: string;
  statuses?: RequestStatus[];
}) {
  const [requests, setRequests] = useState<RequestModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const unsubscribeRef = useRef<(() => void) | null>(null);
  const listenerIdRef = useRef<string>(`all_requests_${Date.now()}`);

  // Create a stable reference for filters to avoid unnecessary re-renders
  const filtersRef = useRef(filters);
  const filtersString = JSON.stringify(filters);

  useEffect(() => {
    filtersRef.current = filters;
  }, [filters, filtersString]);

  useEffect(() => {
    setLoading(true);
    setError(null);

    // Set up real-time listener
    const unsubscribe = realtimeStatusService.listenToAllRequests(
      (updatedRequests) => {
        setRequests(updatedRequests);
        setLoading(false);
      },
      filtersRef.current,
      listenerIdRef.current,
    );

    unsubscribeRef.current = unsubscribe;

    // Cleanup function
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, [filtersString]);

  return { requests, loading, error };
}

/**
 * Hook for listening to real-time changes for technician's assigned requests
 */
export function useRealtimeTechnicianRequests(
  technicianId: string | null,
  statuses?: RequestStatus[],
) {
  const [requests, setRequests] = useState<RequestModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const unsubscribeRef = useRef<(() => void) | null>(null);

  // Create stable reference for statuses to avoid unnecessary re-renders
  const statusesString = JSON.stringify(statuses);

  useEffect(() => {
    if (!technicianId) {
      setRequests([]);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    // Set up real-time listener
    const unsubscribe = realtimeStatusService.listenToTechnicianRequests(
      technicianId,
      (updatedRequests) => {
        setRequests(updatedRequests);
        setLoading(false);
      },
      statuses,
    );

    unsubscribeRef.current = unsubscribe;

    // Cleanup function
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, [technicianId, statusesString, statuses]);

  return { requests, loading, error };
}

/**
 * Hook for listening to real-time changes for customer's requests
 */
export function useRealtimeCustomerRequests(customerId: string | null) {
  const [requests, setRequests] = useState<RequestModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const unsubscribeRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    if (!customerId) {
      setRequests([]);
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    // Set up real-time listener
    const unsubscribe = realtimeStatusService.listenToCustomerRequests(
      customerId,
      (updatedRequests) => {
        setRequests(updatedRequests);
        setLoading(false);
      },
    );

    unsubscribeRef.current = unsubscribe;

    // Cleanup function
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
        unsubscribeRef.current = null;
      }
    };
  }, [customerId]);

  return { requests, loading, error };
}

/**
 * Hook for managing real-time status service health and monitoring
 */
export function useRealtimeStatusHealth() {
  const [health, setHealth] = useState({
    isHealthy: true,
    activeListeners: 0,
    timestamp: new Date(),
  });
  const [listenerCounts, setListenerCounts] = useState({
    requests: 0,
    statuses: 0,
    global: 0,
    total: 0,
  });

  const updateHealth = useCallback(() => {
    const healthData = realtimeStatusService.healthCheck();
    const counts = realtimeStatusService.getActiveListenerCounts();

    setHealth(healthData);
    setListenerCounts(counts);
  }, []);

  const stopAllListeners = useCallback(() => {
    realtimeStatusService.stopAllListeners();
    updateHealth();
  }, [updateHealth]);

  useEffect(() => {
    // Update health on mount
    updateHealth();

    // Set up periodic health checks
    const interval = setInterval(updateHealth, 30000); // Every 30 seconds

    return () => {
      clearInterval(interval);
    };
  }, [updateHealth]);

  return {
    health,
    listenerCounts,
    updateHealth,
    stopAllListeners,
  };
}

/**
 * Hook for status change notifications
 */
export function useStatusChangeNotifications(
  requestId: string | null,
  onStatusChange?: (
    oldStatus: RequestStatus,
    newStatus: RequestStatus,
    request: RequestModel,
  ) => void,
) {
  const previousStatusRef = useRef<RequestStatus | null>(null);
  const { request } = useRealtimeRequestStatus(requestId);

  useEffect(() => {
    if (request && onStatusChange) {
      const currentStatus = request.status;
      const previousStatus = previousStatusRef.current;

      if (previousStatus && previousStatus !== currentStatus) {
        onStatusChange(previousStatus, currentStatus, request);
      }

      previousStatusRef.current = currentStatus;
    }
  }, [request, onStatusChange]);

  return { request };
}
