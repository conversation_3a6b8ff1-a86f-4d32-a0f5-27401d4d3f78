# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/
mr_tech_mobile/firebase-admin-key.json

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/
flutter_*.png
linked_*.ds
unlinked.ds
unlinked_spec.ds

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release
/android/app/google-services.json
/android/key.properties
*.jks
/android/.gradle
/android/local.properties
/android/**/GeneratedPluginRegistrant.*
/android/captures/
/android/gradlew
/android/gradlew.bat
/android/gradle/
/android/libs/
/android/app/gradle/wrapper/

# iOS
/ios/Flutter/.last_build_id
/ios/Flutter/App.framework
/ios/Flutter/Flutter.framework
/ios/Flutter/Generated.xcconfig
/ios/Flutter/app.flx
/ios/Flutter/app.zip
/ios/Flutter/flutter_assets/
/ios/ServiceDefinitions.json
/ios/Runner/GeneratedPluginRegistrant.*
/ios/GoogleService-Info.plist
/ios/Pods/
/ios/Runner.xcworkspace/
/ios/*.mode1v3
/ios/*.mode2v3
/ios/*.moved-aside
/ios/*.pbxuser
/ios/*.perspectivev3
/ios/*sync/
/ios/.sconsign.dblite
/ios/.tags*
/ios/.vagrant/
/ios/DerivedData/
/ios/Icon?
/ios/Pods/
/ios/profile
/ios/xcuserdata/

# macOS
/macos/Flutter/GeneratedPluginRegistrant.swift
/macos/Flutter/Flutter-Debug.xcconfig
/macos/Flutter/Flutter-Release.xcconfig
/macos/Flutter/Flutter-Profile.xcconfig

# Web
/web/favicon.png
/web/icons/Icon-*
/web/manifest.json

# Coverage
coverage/
*.lcov
.test_coverage.dart
*.coverage.json
*.info.json
*.coveralls.json

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# IDE related
.idea/
.vscode/
*.iml
*.swp
*.swo
.DS_Store
.metadata
pubspec.lock
.dart_gen/
.project

# Firebase
firebase_options.dart
google-services.json
GoogleService-Info.plist

# Temporary files
*.log
*.tmp
*.temp
temp/

# Generated files
.generated/
.fvm/flutter_sdk

# Performance benchmarks
lib/generated_plugin_registrant.dart
