# Security Setup Guide

This guide explains how to securely configure the Mr.Tech application after the security fixes.

## 🔐 Environment Variable Setup

### 1. Mobile App (Flutter)

**File: `mr_tech_mobile/android/local.properties`**
```properties
sdk.dir=C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk
flutter.sdk=C:\\flutter
flutter.buildMode=debug
flutter.versionName=1.0.4
flutter.versionCode=11

# Keystore environment variables for production builds
KEYSTORE_PASSWORD=your_actual_keystore_password
KEY_PASSWORD=your_actual_key_password  
KEY_ALIAS=mrtech
KEYSTORE_FILE=app/keystore/mrtech_keystore.jks
```

**File: `mr_tech_mobile/.env`**
```env
PAYMOB_API_KEY="your_actual_paymob_api_key"
PAYMOB_SECRET_KEY="your_actual_secret_key"
PAYMOB_PUBLIC_KEY="your_actual_public_key"
PAYMOB_INTEGRATION_ID="your_integration_id"
PAYMOB_AUTH_TOKEN=""
PAYMOB_TOKEN_EXPIRY=""
PAYMENT_MODE="paymob"
```

### 2. Web Portal (React)

**File: `web-portal/.env`**
```env
VITE_FIREBASE_API_KEY=AIzaSyBY5ltbF50h2GNbhe2obqm55Oa6RmxxRBY
VITE_FIREBASE_AUTH_DOMAIN=stopnow-be6b7.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=stopnow-be6b7
VITE_FIREBASE_STORAGE_BUCKET=stopnow-be6b7.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=586312365224
VITE_FIREBASE_APP_ID=1:586312365224:android:8861c3bf21fbb2d1bffe9d
VITE_FIREBASE_DATABASE_URL=https://stopnow-be6b7-default-rtdb.europe-west1.firebasedatabase.app

# Firebase Admin SDK (Backend only - keep secure)
FIREBASE_PRIVATE_KEY="your_actual_private_key"
FIREBASE_CLIENT_EMAIL=<EMAIL>
# ... other admin keys
```

## 🚀 Production Deployment

### CI/CD Environment Variables

For production deployments, set these environment variables in your CI/CD system:

#### GitHub Actions / Azure DevOps / GitLab CI
```yaml
env:
  KEYSTORE_PASSWORD: ${{ secrets.KEYSTORE_PASSWORD }}
  KEY_PASSWORD: ${{ secrets.KEY_PASSWORD }}
  PAYMOB_API_KEY: ${{ secrets.PAYMOB_API_KEY }}
  FIREBASE_PRIVATE_KEY: ${{ secrets.FIREBASE_PRIVATE_KEY }}
```

#### Docker
```dockerfile
ENV KEYSTORE_PASSWORD=$KEYSTORE_PASSWORD
ENV KEY_PASSWORD=$KEY_PASSWORD
ENV PAYMOB_API_KEY=$PAYMOB_API_KEY
```

## 🛡️ Security Best Practices

1. **Never commit actual secrets to version control**
2. **Use .env.example files as templates**
3. **Rotate API keys regularly**
4. **Use different Firebase projects for dev/staging/production**
5. **Enable Firebase App Check in production**
6. **Monitor for security vulnerabilities with `npm audit`**

## 📋 Deployment Checklist

- [ ] Copy `.env.example` to `.env` and fill in real values
- [ ] Copy `key.properties.example` to `key.properties` and fill in real values
- [ ] Copy `local.properties.example` to `local.properties` and fill in real values
- [ ] Verify `.gitignore` excludes all sensitive files
- [ ] Test build process with environment variables
- [ ] Run security audits: `npm audit` and `flutter analyze`
- [ ] Enable Firebase security rules in production mode

## 🔄 Migration from Old Setup

The new setup is backward compatible. Your existing builds will continue to work, but you should:

1. Update your build scripts to use environment variables
2. Remove hardcoded secrets from configuration files
3. Update deployment pipelines to inject secrets securely

## 🆘 Troubleshooting

### Build Fails with Missing Keystore
1. Ensure `local.properties` has correct environment variables
2. Verify keystore file exists at the specified path
3. Check that environment variables are properly exported

### Firebase Connection Issues
1. Verify Firebase configuration in `.env` files
2. Check that API keys are valid and not expired
3. Ensure Firebase project has proper permissions enabled