# Mr. Tech Web Portal - Product Requirements Document (PRD)
## Comprehensive System Recreation with React + Vite + ShadCN + Express.js

### **Project Overview**
Recreate the Mr. Tech web portal using modern technologies (React + Vite + ShadCN UI + Express.js backend) while maintaining full compatibility with the existing Firebase database and Flutter mobile app.

---

## **1. TECHNOLOGY STACK**

### **Frontend**
- **Framework**: React 18+ with TypeScript
- **Build Tool**: Vite
- **UI Library**: ShadCN UI (Tailwind CSS + Radix UI)
- **State Management**: Zustand or Redux Toolkit
- **Routing**: React Router DOM v6
- **HTTP Client**: Axios
- **Real-time**: Socket.io-client
- **Forms**: React Hook Form + Zod validation
- **Date Handling**: date-fns
- **Icons**: Lucide React
- **Charts**: Recharts or Chart.js

### **Backend**
- **Framework**: Express.js with TypeScript
- **Database**: Firebase Firestore + Realtime Database (existing)
- **Authentication**: Firebase Auth
- **File Storage**: Firebase Storage
- **Push Notifications**: Firebase Cloud Messaging (FCM)
- **Payment Integration**: Paymob (existing integration)
- **Real-time**: Socket.io
- **API Documentation**: Swagger/OpenAPI
- **Validation**: Zod
- **Logging**: Winston

### **Development & Deployment**
- **Package Manager**: pnpm
- **Linting**: ESLint + Prettier
- **Testing**: Vitest + React Testing Library
- **Deployment**: Vercel (frontend) + Railway/Render (backend)
- **Environment**: Docker for local development

---

## **2. DATABASE STRUCTURE**

### **Core Collections (Firestore)**

#### **Users Collection** (`users/{userId}`)
```typescript
interface User {
  id: string;
  email: string;
  displayName?: string;
  phoneNumber?: string;
  photoUrl?: string;
  address?: string;
  city?: string;
  country?: string;
  anydesk_id?: string;
  role?: 'admin' | 'technician' | 'customer';
  isComplete: boolean;
  isOnboarded: boolean;
  emailVerified: boolean;
  preferredLanguage: 'en' | 'ar';
  createdAt: Date;
  updatedAt?: Date;
  notificationPreferences: {
    chatMessages: boolean;
    requestUpdates: boolean;
    systemNotifications: boolean;
  };
}
```

#### **Service Requests Collection** (`service_requests/{requestId}`)
```typescript
interface ServiceRequest {
  id: string;
  customerId: string;
  serviceId: string;
  serviceName: string | { en: string; ar: string };
  serviceDescription: string | { en: string; ar: string };
  customerIssue: string;
  anydesk_id?: string;
  technicianId?: string;
  technicianName?: string;
  status: 'payment_pending' | 'pending' | 'approved' | 'inProgress' | 'completed' | 'cancelled' | 'refused';
  amount: number;
  isPaid: boolean;
  chatActive: boolean;
  sessionActive: boolean;
  createdAt: Date;
  updatedAt?: Date;
  scheduledTime?: Date;
  sessionStartTime?: Date;
  sessionEndTime?: Date;
  sessionDuration?: number; // minutes
  customerRated?: boolean;
  rating?: number;
  reviewComment?: string;
  customerFcmToken?: string;
}
```

#### **Services Collection** (`services/{serviceId}`)
```typescript
interface Service {
  id: string;
  name: { en: string; ar: string } | string;
  description: { en: string; ar: string } | string;
  category: string;
  basePrice: number;
  imageUrl: string;
  active: boolean;
  metadata?: Record<string, any>;
  estimatedDuration: number; // minutes
  createdAt: Date;
  updatedAt?: Date;
}
```

#### **Technicians Collection** (`technicians/{technicianId}`)
```typescript
interface Technician {
  id: string;
  email: string;
  name: string;
  photoUrl?: string;
  phoneNumber?: string;
  specialties: string[];
  status: 'active' | 'offline' | 'busy' | 'onLeave';
  rating: number;
  completedRequests: number;
  activeRequests: number;
  isAvailable: boolean;
  createdAt: Date;
  updatedAt?: Date;
}
```

#### **Chat Messages Subcollection** (`service_requests/{requestId}/messages/{messageId}`)
```typescript
interface ChatMessage {
  id: string;
  requestId: string;
  senderType: 'customer' | 'technician' | 'system';
  senderId: string;
  messageType: 'text' | 'image' | 'file' | 'system';
  content: string;
  fileUrl?: string;
  isRead: boolean;
  createdAt: Date;
}
```

#### **Device Tokens Collection** (`device_tokens/{tokenId}`)
```typescript
interface DeviceToken {
  id: string;
  userId: string;
  token: string;
  platform: 'android' | 'ios' | 'web';
  active: boolean;
  lastUpdated: Date;
}
```

#### **Notifications Collection** (`notifications/{notificationId}`)
```typescript
interface Notification {
  id: string;
  userId: string;
  title: string;
  body: string;
  type: 'chat_activation' | 'request_update' | 'system';
  requestId?: string;
  data?: Record<string, any>;
  read: boolean;
  delivered?: boolean;
  createdAt: Date;
}
```

#### **Payment Transactions Collection** (`payment_transactions/{transactionId}`)
```typescript
interface PaymentTransaction {
  id: string;
  requestId: string;
  customerId: string;
  amount: number;
  currency: 'EGP';
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  paymentMethod: 'paymob' | 'test';
  paymobTransactionId?: string;
  createdAt: Date;
  completedAt?: Date;
}
```

### **Real-time Database Structure (RTDB)**
```
- messages/
  - {requestId}/
    - {messageId}: ChatMessage
- chats/
  - {requestId}: { active: boolean, lastActivity: timestamp }
- online_status/
  - {userId}: { online: boolean, lastSeen: timestamp }
- sessions/
  - active/
    - {sessionId}: { requestId, technicianId, startTime }
```

---

## **3. CORE FEATURES & MODULES**

### **3.1 Authentication & Authorization**
- **Firebase Auth Integration**: Email/password, Google OAuth
- **Role-based Access Control**: Admin, Technician, Customer roles
- **Multi-collection Role Checking**: Support both dedicated collections and role field
- **Session Management**: JWT tokens, refresh tokens
- **Password Reset**: Email-based password recovery
- **Profile Management**: Update user information, photo upload

### **3.2 Dashboard & Analytics**
- **Overview Cards**: Request counts by status, revenue, technician stats
- **Real-time Metrics**: Active sessions, online technicians
- **Charts & Graphs**: 
  - Daily/weekly/monthly request trends
  - Service distribution pie chart
  - Technician performance metrics
  - Revenue analytics
- **Activity Feed**: Recent system activities
- **Quick Actions**: Create request, assign technician, view alerts

### **3.3 Request Management**
- **Request List View**: 
  - Filterable by status, technician, date range, service
  - Searchable by customer name, request ID, issue description
  - Sortable by date, priority, amount
  - Bulk actions (assign, update status)
- **Request Detail View**:
  - Complete request information display
  - Status management with workflow validation
  - Technician assignment/reassignment
  - Payment status tracking
  - Session management (start/stop AnyDesk sessions)
  - Chat integration
  - File attachments support
- **Request Creation**: Admin-initiated requests
- **Status Workflow**: 
  - `payment_pending` → `pending` → `approved` → `inProgress` → `completed`
  - Alternative paths: `cancelled`, `refused`

### **3.4 Chat System**
- **Real-time Messaging**: 
  - Text messages, file sharing, image uploads
  - Message status indicators (sent, delivered, read)
  - Typing indicators
  - Emoji support
- **Chat Management**:
  - Activate/deactivate chat for requests
  - Chat history persistence
  - Message search functionality
  - Export chat transcripts
- **Notifications**: 
  - Desktop notifications for new messages
  - FCM push notifications to mobile
  - Sound alerts
- **Multi-platform Sync**: Seamless sync with mobile app

### **3.5 Technician Management**
- **Technician Directory**:
  - List view with availability status
  - Performance metrics (rating, completed requests)
  - Specialties and skills management
- **Availability Management**:
  - Online/offline status tracking
  - Work schedule management
  - Break/leave management
- **Performance Analytics**:
  - Individual technician dashboards
  - Request completion rates
  - Customer satisfaction scores
  - Response time metrics
- **Assignment Logic**:
  - Manual assignment by admin
  - Auto-assignment based on availability and specialties
  - Workload balancing

### **3.6 Service Management**
- **Service Catalog**:
  - Multi-language support (Arabic/English)
  - Category organization
  - Pricing management
  - Service descriptions and images
- **Service Analytics**:
  - Popular services tracking
  - Revenue by service
  - Demand forecasting
- **Service Configuration**:
  - Estimated duration settings
  - Base pricing
  - Service availability

### **3.7 Customer Management**
- **Customer Directory**:
  - Customer profiles with contact information
  - Request history
  - Payment history
  - Communication preferences
- **Customer Support**:
  - Issue tracking
  - Communication logs
  - Satisfaction surveys
- **Customer Analytics**:
  - Customer lifetime value
  - Retention rates
  - Support ticket trends

### **3.8 Payment & Billing**
- **Payment Integration**:
  - Paymob payment gateway
  - Test mode for development
  - Payment status tracking
  - Refund management
- **Billing Management**:
  - Invoice generation
  - Payment history
  - Revenue reporting
  - Tax calculations
- **Financial Analytics**:
  - Revenue dashboards
  - Payment method analytics
  - Outstanding payments tracking

### **3.9 Notification System**
- **Multi-channel Notifications**:
  - In-app notifications
  - Email notifications
  - FCM push notifications
  - SMS notifications (future)
- **Notification Management**:
  - Notification templates
  - User preference management
  - Delivery tracking
  - Notification history
- **Real-time Updates**:
  - WebSocket connections for instant updates
  - Browser push notifications
  - Sound alerts

### **3.10 File Management**
- **File Upload/Download**:
  - Firebase Storage integration
  - Image optimization
  - File type validation
  - Size limits enforcement
- **File Organization**:
  - Request-based file organization
  - Chat attachment management
  - Profile photo management
- **Security**:
  - Access control based on user roles
  - Secure file URLs
  - Automatic cleanup of unused files

### **3.11 System Administration**
- **User Management**:
  - Create/edit/delete users
  - Role assignment
  - Account activation/deactivation
  - Bulk user operations
- **System Configuration**:
  - App settings management
  - Feature flags
  - Maintenance mode
  - System health monitoring
- **Audit Logging**:
  - User action tracking
  - System event logging
  - Security audit trails
  - Compliance reporting
- **Backup & Recovery**:
  - Data export functionality
  - System backup procedures
  - Disaster recovery planning

---

## **4. API DESIGN**

### **4.1 Authentication Endpoints**
```
POST /api/auth/login
POST /api/auth/logout
POST /api/auth/refresh
POST /api/auth/forgot-password
POST /api/auth/reset-password
GET  /api/auth/profile
PUT  /api/auth/profile
```

### **4.2 Request Management Endpoints**
```
GET    /api/requests                 # List requests with filters
POST   /api/requests                 # Create new request
GET    /api/requests/:id             # Get request details
PUT    /api/requests/:id             # Update request
DELETE /api/requests/:id             # Delete request
PUT    /api/requests/:id/status      # Update request status
PUT    /api/requests/:id/assign      # Assign technician
POST   /api/requests/:id/chat/start  # Start chat
POST   /api/requests/:id/session/start # Start AnyDesk session
POST   /api/requests/:id/session/end   # End session
```

### **4.3 Chat Endpoints**
```
GET    /api/chats/:requestId/messages    # Get chat messages
POST   /api/chats/:requestId/messages    # Send message
PUT    /api/chats/:requestId/read        # Mark messages as read
POST   /api/chats/:requestId/upload      # Upload file
GET    /api/chats/active                 # Get active chats
```

### **4.4 Technician Endpoints**
```
GET    /api/technicians              # List technicians
POST   /api/technicians              # Create technician
GET    /api/technicians/:id          # Get technician details
PUT    /api/technicians/:id          # Update technician
DELETE /api/technicians/:id          # Delete technician
PUT    /api/technicians/:id/status   # Update availability
GET    /api/technicians/:id/performance # Get performance metrics
```

### **4.5 Dashboard Endpoints**
```
GET /api/dashboard/overview          # Dashboard overview data
GET /api/dashboard/requests/counts   # Request counts by status
GET /api/dashboard/technicians/active # Active technicians
GET /api/dashboard/activities        # Recent activities
GET /api/dashboard/analytics/:period # Analytics for period
```

### **4.6 Notification Endpoints**
```
GET    /api/notifications            # Get user notifications
POST   /api/notifications            # Create notification
PUT    /api/notifications/:id/read   # Mark as read
DELETE /api/notifications/:id        # Delete notification
POST   /api/notifications/send       # Send push notification
```

---

## **5. USER INTERFACE DESIGN**

### **5.1 Design System (ShadCN UI)**
- **Color Palette**: Professional blue/gray theme with accent colors
- **Typography**: Inter font family, consistent sizing scale
- **Components**: 
  - Custom components built on Radix UI primitives
  - Data tables with sorting, filtering, pagination
  - Forms with validation and error handling
  - Modal dialogs and drawers
  - Toast notifications
  - Loading states and skeletons
- **Responsive Design**: Mobile-first approach, tablet and desktop optimized
- **Dark Mode**: Full dark mode support with theme switching
- **Accessibility**: WCAG 2.1 AA compliance

### **5.2 Layout Structure**
- **Sidebar Navigation**: Collapsible sidebar with role-based menu items
- **Top Bar**: User profile, notifications, search, theme toggle
- **Main Content**: Dynamic content area with breadcrumbs
- **Footer**: System status, version info, support links

### **5.3 Page Layouts**

#### **Dashboard Page**
- **Overview Cards**: Key metrics in card layout
- **Charts Section**: 2-column grid with interactive charts
- **Activity Feed**: Right sidebar with recent activities
- **Quick Actions**: Floating action buttons

#### **Request Management**
- **List View**: Data table with advanced filtering
- **Detail View**: Tabbed interface (Overview, Chat, Files, History)
- **Create/Edit**: Multi-step form with validation

#### **Chat Interface**
- **Chat List**: Left sidebar with active chats
- **Chat Window**: Center panel with message history
- **Chat Info**: Right sidebar with request details

#### **Technician Management**
- **Grid View**: Card-based technician directory
- **Detail View**: Profile information with performance metrics
- **Assignment Interface**: Drag-and-drop request assignment

---

## **6. REAL-TIME FEATURES**

### **6.1 WebSocket Integration**
- **Socket.io Implementation**: Bidirectional real-time communication
- **Event Types**:
  - `message:new` - New chat message
  - `request:updated` - Request status change
  - `technician:status` - Technician availability change
  - `session:started` - AnyDesk session started
  - `session:ended` - Session ended
  - `notification:new` - New notification

### **6.2 Real-time Updates**
- **Chat Messages**: Instant message delivery and read receipts
- **Request Status**: Live status updates across all users
- **Technician Availability**: Real-time availability tracking
- **Dashboard Metrics**: Live metric updates
- **Notifications**: Instant notification delivery

### **6.3 Offline Support**
- **Service Worker**: Cache critical resources
- **Offline Indicators**: Show connection status
- **Data Sync**: Sync changes when connection restored
- **Optimistic Updates**: Immediate UI updates with rollback on failure

---

## **7. SECURITY & COMPLIANCE**

### **7.1 Authentication & Authorization**
- **Firebase Auth**: Secure authentication with JWT tokens
- **Role-based Permissions**: Granular permission system
- **API Security**: Request validation, rate limiting
- **Session Management**: Secure session handling with refresh tokens

### **7.2 Data Protection**
- **HTTPS Only**: All communications encrypted
- **Input Validation**: Server-side validation for all inputs
- **SQL Injection Prevention**: Parameterized queries
- **XSS Protection**: Content Security Policy headers
- **CSRF Protection**: CSRF tokens for state-changing operations

### **7.3 Privacy & Compliance**
- **Data Encryption**: Sensitive data encrypted at rest
- **Audit Logging**: Complete audit trail of user actions
- **Data Retention**: Configurable data retention policies
- **GDPR Compliance**: Data export, deletion, and consent management

---

## **8. PAYMENT INTEGRATION**

### **8.1 Paymob Integration**
- **Payment Gateway**: Existing Paymob integration
- **Payment Methods**: Credit cards, mobile wallets
- **Test Mode**: Development testing capability
- **Transaction Tracking**: Complete payment lifecycle tracking
- **Refund Management**: Automated and manual refund processing

### **8.2 Payment Workflow**
- **Request Creation**: Payment pending status
- **Payment Processing**: Secure payment handling
- **Payment Confirmation**: Automatic status updates
- **Invoice Generation**: PDF invoice creation
- **Payment History**: Complete transaction records

---

## **9. NOTIFICATION SYSTEM**

### **9.1 Multi-channel Notifications**
- **In-app Notifications**: Real-time browser notifications
- **Email Notifications**: Transactional email sending
- **FCM Push Notifications**: Mobile app notifications
- **WebSocket Notifications**: Instant web app updates

### **9.2 Notification Types**
- **Chat Activation**: New chat started
- **Request Updates**: Status changes
- **Assignment Notifications**: Technician assignments
- **Payment Notifications**: Payment confirmations
- **System Alerts**: System maintenance, updates

---

## **10. MIGRATION STRATEGY**

### **10.1 Data Compatibility**
- **Firestore Structure**: Maintain existing database structure
- **API Compatibility**: Ensure mobile app continues to work
- **Field Name Support**: Support both camelCase and snake_case
- **Data Validation**: Comprehensive data integrity checks

### **10.2 Deployment Approach**
- **Parallel Deployment**: Run new system alongside old
- **Gradual Migration**: Phase-wise user migration
- **Feature Parity**: Ensure all features are available
- **Rollback Plan**: Quick rollback if issues arise

---

## **11. DEVELOPMENT PHASES**

### **Phase 1: Foundation (4 weeks)**
- Project setup and environment configuration
- Authentication system implementation
- Basic dashboard with overview metrics
- Database integration and API foundation

### **Phase 2: Core Features (4 weeks)**
- Request management system
- Technician management
- Basic chat functionality
- User management interface

### **Phase 3: Advanced Features (4 weeks)**
- Real-time chat with file sharing
- Advanced analytics and reporting
- Payment integration
- Notification system

### **Phase 4: Polish & Deployment (4 weeks)**
- UI/UX refinements
- Comprehensive testing
- Performance optimization
- Production deployment and migration

---

## **12. SUCCESS METRICS**

### **12.1 Performance Metrics**
- **Page Load Time**: < 2 seconds for initial load
- **API Response Time**: < 500ms for 95% of requests
- **Uptime**: 99.9% system availability
- **Error Rate**: < 0.1% error rate

### **12.2 User Experience Metrics**
- **User Satisfaction**: > 4.5/5 user satisfaction score
- **Task Completion Rate**: > 95% task completion rate
- **Support Tickets**: 50% reduction in support tickets
- **User Adoption**: > 90% user adoption rate

### **12.3 Business Metrics**
- **Request Processing Time**: 20% improvement in processing time
- **Technician Productivity**: 15% increase in completed requests
- **Customer Satisfaction**: > 4.0/5 customer satisfaction
- **Revenue Growth**: Support for 25% increase in request volume

---

This PRD provides a comprehensive blueprint for recreating the Mr. Tech web portal with modern technologies while maintaining full compatibility with the existing Firebase database and Flutter mobile app. 