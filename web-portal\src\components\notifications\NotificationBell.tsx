import React, { useState } from 'react';
import { Bell, BellRing, Check, Settings, Volume2, VolumeX, X } from 'lucide-react';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../ui/dropdown-menu';
import { ScrollArea } from '../ui/scroll-area';
import { useNotifications } from '../../contexts/NotificationContext';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '../../lib/utils';

const NotificationBell: React.FC = () => {
  const {
    notifications,
    unreadCount,
    settings,
    markAsRead,
    markAllAsRead,
    clearAllNotifications,
    updateSettings,
    testNotification,
  } = useNotifications();

  const [isOpen, setIsOpen] = useState(false);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'text-red-600 bg-red-50';
      case 'high':
        return 'text-orange-600 bg-orange-50';
      case 'medium':
        return 'text-blue-600 bg-blue-50';
      case 'low':
        return 'text-gray-600 bg-gray-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'urgent':
      case 'high':
        return <BellRing className='h-4 w-4' />;
      default:
        return <Bell className='h-4 w-4' />;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'request_created':
        return '🆕';
      case 'request_updated':
        return '🔄';
      case 'request_completed':
        return '✅';
      case 'request_cancelled':
        return '❌';
      case 'chat_message':
        return '💬';
      case 'payment_received':
        return '💰';
      case 'technician_assigned':
        return '👨‍🔧';
      case 'system_alert':
        return '⚠️';
      case 'maintenance_mode':
        return '🔧';
      default:
        return '📢';
    }
  };

  const toggleSounds = () => {
    updateSettings({ enableSounds: !settings.enableSounds });
  };

  const recentNotifications = notifications.slice(0, 10);

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant='ghost' size='icon' className='relative'>
          {unreadCount > 0 ? (
            <BellRing className='h-5 w-5 text-orange-600' />
          ) : (
            <Bell className='h-5 w-5' />
          )}
          {unreadCount > 0 && (
            <Badge
              variant='destructive'
              className='absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs'
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent align='end' className='w-80'>
        <div className='flex items-center justify-between p-2'>
          <DropdownMenuLabel className='p-0'>
            Notifications {unreadCount > 0 && `(${unreadCount})`}
          </DropdownMenuLabel>

          <div className='flex items-center gap-1'>
            <Button
              variant='ghost'
              size='icon'
              className='h-6 w-6'
              onClick={toggleSounds}
              title={settings.enableSounds ? 'Disable sounds' : 'Enable sounds'}
            >
              {settings.enableSounds ? (
                <Volume2 className='h-3 w-3' />
              ) : (
                <VolumeX className='h-3 w-3' />
              )}
            </Button>

            {unreadCount > 0 && (
              <Button
                variant='ghost'
                size='icon'
                className='h-6 w-6'
                onClick={markAllAsRead}
                title='Mark all as read'
              >
                <Check className='h-3 w-3' />
              </Button>
            )}

            {notifications.length > 0 && (
              <Button
                variant='ghost'
                size='icon'
                className='h-6 w-6'
                onClick={clearAllNotifications}
                title='Clear all notifications'
              >
                <X className='h-3 w-3' />
              </Button>
            )}
          </div>
        </div>

        <DropdownMenuSeparator />

        {recentNotifications.length === 0 ? (
          <div className='p-4 text-center text-muted-foreground'>
            <Bell className='h-8 w-8 mx-auto mb-2 opacity-50' />
            <p className='text-sm'>No notifications</p>
            <Button variant='outline' size='sm' className='mt-2' onClick={() => testNotification()}>
              Test Notification
            </Button>
          </div>
        ) : (
          <ScrollArea className='h-96'>
            <div className='space-y-1'>
              {recentNotifications.map((notification) => (
                <div
                  key={notification.id}
                  className={cn(
                    'p-3 hover:bg-gray-50 cursor-pointer border-l-2 transition-colors',
                    notification.read ? 'border-l-transparent' : 'border-l-blue-500 bg-blue-50/50',
                    getPriorityColor(notification.priority),
                  )}
                  onClick={() => {
                    if (!notification.read) {
                      markAsRead(notification.id);
                    }
                    setIsOpen(false);

                    // Navigate to relevant page if requestId exists
                    if (notification.requestId) {
                      window.location.href = `/requests/${notification.requestId}`;
                    }
                  }}
                >
                  <div className='flex items-start gap-3'>
                    <div className='flex-shrink-0 mt-0.5'>
                      <span className='text-lg'>{getTypeIcon(notification.type)}</span>
                    </div>

                    <div className='flex-1 min-w-0'>
                      <div className='flex items-center gap-2'>
                        <p className='text-sm font-medium truncate'>{notification.title}</p>
                        {!notification.read && (
                          <div className='w-2 h-2 bg-blue-600 rounded-full flex-shrink-0' />
                        )}
                      </div>

                      <p className='text-xs text-muted-foreground mt-1 line-clamp-2'>
                        {notification.message}
                      </p>

                      <div className='flex items-center justify-between mt-2'>
                        <span className='text-xs text-muted-foreground'>
                          {formatDistanceToNow(notification.timestamp, { addSuffix: true })}
                        </span>

                        <div className='flex items-center gap-1'>
                          {getPriorityIcon(notification.priority)}
                          <Badge variant='secondary' className='text-xs px-1 py-0'>
                            {notification.priority}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}

        {notifications.length > 10 && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              className='justify-center text-center cursor-pointer'
              onClick={() => {
                setIsOpen(false);
                // Navigate to notifications page
                window.location.href = '/notifications';
              }}
            >
              View all notifications ({notifications.length})
            </DropdownMenuItem>
          </>
        )}

        <DropdownMenuSeparator />
        <DropdownMenuItem
          className='justify-center text-center cursor-pointer'
          onClick={() => {
            setIsOpen(false);
            // Navigate to notification settings
            window.location.href = '/settings?tab=notifications';
          }}
        >
          <Settings className='h-4 w-4 mr-2' />
          Notification Settings
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default NotificationBell;
