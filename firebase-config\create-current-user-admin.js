// This script creates an admin document for the currently authenticated user
// Run this from the web portal console when logged in

const createCurrentUserAsAdmin = async () => {
  try {
    // Get current user
    const user = auth.currentUser;
    if (!user) {
      console.error('No user is currently authenticated');
      return;
    }

    console.log('Creating admin document for user:', user.uid);

    // Create admin document in users collection
    const userDocRef = doc(db, 'users', user.uid);
    await setDoc(userDocRef, {
      email: user.email,
      name: user.displayName || 'Admin User',
      role: 'admin',
      created_at: serverTimestamp(),
      updated_at: serverTimestamp(),
      is_active: true,
      phone: user.phoneNumber || '',
      photo_url: user.photoURL || ''
    });

    console.log('Admin user document created successfully!');
    console.log('You can now create technicians and perform admin operations.');
    
    // Optionally create the first-admin-marker to prevent others from becoming admin
    const markerRef = doc(db, 'admins', 'first-admin-marker');
    await setDoc(markerRef, {
      created_at: serverTimestamp(),
      created_by: user.uid
    });
    
    console.log('First admin marker created.');
    
  } catch (error) {
    console.error('Error creating admin user:', error);
  }
};

// Call the function
createCurrentUserAsAdmin();
