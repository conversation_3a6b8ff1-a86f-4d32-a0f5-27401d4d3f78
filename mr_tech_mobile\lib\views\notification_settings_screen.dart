import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/notification_preferences_model.dart';
import '../services/notification_service.dart';
import '../services/translation_service.dart';
import '../widgets/text_styles.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() =>
      _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState
    extends State<NotificationSettingsScreen> {
  late NotificationPreferencesModel _preferences;
  bool _isLoading = true;
  final NotificationService _notificationService = NotificationService();

  @override
  void initState() {
    super.initState();
    _loadPreferences();
  }

  Future<void> _loadPreferences() async {
    setState(() => _isLoading = true);

    // Initialize with current preferences from NotificationService
    _preferences = _notificationService.notificationPreferences;

    setState(() => _isLoading = false);
  }

  Future<void> _savePreferences() async {
    setState(() => _isLoading = true);

    try {
      await _notificationService.saveNotificationPreferences(_preferences);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_translate('preferences_saved')),
          backgroundColor: Theme.of(context).colorScheme.primary,
          behavior: SnackBarBehavior.floating,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_translate('error_saving_preferences')),
          backgroundColor: Theme.of(context).colorScheme.error,
          behavior: SnackBarBehavior.floating,
        ),
      );
      debugPrint('Error saving notification preferences: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  String _translate(String key) {
    return Provider.of<TranslationService>(
      context,
      listen: false,
    ).translate(key);
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: CustomScrollView(
        slivers: [
          // Floating header
          SliverAppBar(
            expandedHeight: 80,
            floating: true,
            pinned: false,
            snap: true,
            elevation: 0,
            backgroundColor: colorScheme.surface,
            flexibleSpace: FlexibleSpaceBar(
              background: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 10,
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.notifications_outlined,
                        color: colorScheme.onSurface,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        _translate('notification_settings'),
                        style: AppTextStyles.headingMedium(
                          context,
                          color: colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Main content
          SliverToBoxAdapter(
            child:
                _isLoading
                    ? const Padding(
                      padding: EdgeInsets.all(32),
                      child: Center(child: CircularProgressIndicator()),
                    )
                    : Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Global notifications toggle
                          _buildNotificationSection(colorScheme),

                          const SizedBox(height: 24),

                          _buildNotificationTypesSection(colorScheme),

                          const SizedBox(height: 24),

                          _buildAlertPreferencesSection(colorScheme),

                          const SizedBox(height: 32),

                          // Save button
                          FilledButton(
                            onPressed: _isLoading ? null : _savePreferences,
                            style: FilledButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                if (_isLoading) ...[
                                  const SizedBox(
                                    height: 20,
                                    width: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                ],
                                Text(
                                  _translate('save_changes'),
                                  style: AppTextStyles.labelLarge(
                                    context,
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationSection(ColorScheme colorScheme) {
    return Card(
      margin: EdgeInsets.zero,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade300),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.notifications_active,
                    color: colorScheme.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  _translate('push_notifications'),
                  style: AppTextStyles.headingSmall(
                    context,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildSwitchTile(
              title: _translate('enable_push_notifications'),
              subtitle: _translate('receive_notifications_on_device'),
              value: _preferences.pushNotifications,
              onChanged: (value) {
                setState(() {
                  _preferences = _preferences.copyWith(
                    pushNotifications: value,
                  );
                });
              },
              colorScheme: colorScheme,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationTypesSection(ColorScheme colorScheme) {
    return Card(
      margin: EdgeInsets.zero,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade300),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.category,
                    color: colorScheme.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  _translate('notification_types'),
                  style: AppTextStyles.headingSmall(
                    context,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              _translate('select_notifications_to_receive'),
              style: AppTextStyles.bodyMedium(
                context,
                color: colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 16),
            _buildSwitchTile(
              title: _translate('request_updates'),
              subtitle: _translate('notifications_about_request_status'),
              value: _preferences.requestUpdates,
              onChanged:
                  _preferences.pushNotifications
                      ? (value) {
                        setState(() {
                          _preferences = _preferences.copyWith(
                            requestUpdates: value,
                          );
                        });
                      }
                      : null,
              colorScheme: colorScheme,
            ),
            Divider(height: 1, color: Colors.grey.shade300),
            _buildSwitchTile(
              title: _translate('messages'),
              subtitle: _translate('notifications_about_new_messages'),
              value: _preferences.messageNotifications,
              onChanged:
                  _preferences.pushNotifications
                      ? (value) {
                        setState(() {
                          _preferences = _preferences.copyWith(
                            messageNotifications: value,
                          );
                        });
                      }
                      : null,
              colorScheme: colorScheme,
            ),
            Divider(height: 1, color: Colors.grey.shade300),
            _buildSwitchTile(
              title: _translate('payments'),
              subtitle: _translate('notifications_about_payments'),
              value: _preferences.paymentNotifications,
              onChanged:
                  _preferences.pushNotifications
                      ? (value) {
                        setState(() {
                          _preferences = _preferences.copyWith(
                            paymentNotifications: value,
                          );
                        });
                      }
                      : null,
              colorScheme: colorScheme,
            ),
            Divider(height: 1, color: Colors.grey.shade300),
            _buildSwitchTile(
              title: _translate('system'),
              subtitle: _translate('notifications_about_system_updates'),
              value: _preferences.systemNotifications,
              onChanged:
                  _preferences.pushNotifications
                      ? (value) {
                        setState(() {
                          _preferences = _preferences.copyWith(
                            systemNotifications: value,
                          );
                        });
                      }
                      : null,
              colorScheme: colorScheme,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAlertPreferencesSection(ColorScheme colorScheme) {
    return Card(
      margin: EdgeInsets.zero,
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade300),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.tune, color: colorScheme.primary, size: 20),
                ),
                const SizedBox(width: 12),
                Text(
                  _translate('alert_preferences'),
                  style: AppTextStyles.headingSmall(
                    context,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildSwitchTile(
              title: _translate('sound'),
              subtitle: _translate('play_sound_for_notifications'),
              value: _preferences.soundEnabled,
              onChanged:
                  _preferences.pushNotifications
                      ? (value) {
                        setState(() {
                          _preferences = _preferences.copyWith(
                            soundEnabled: value,
                          );
                        });
                      }
                      : null,
              colorScheme: colorScheme,
            ),
            Divider(height: 1, color: Colors.grey.shade300),
            _buildSwitchTile(
              title: _translate('vibration'),
              subtitle: _translate('vibrate_for_notifications'),
              value: _preferences.vibrationEnabled,
              onChanged:
                  _preferences.pushNotifications
                      ? (value) {
                        setState(() {
                          _preferences = _preferences.copyWith(
                            vibrationEnabled: value,
                          );
                        });
                      }
                      : null,
              colorScheme: colorScheme,
            ),
          ],
        ),
      ),
    );
  }

  // Simple switch tile
  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool)? onChanged,
    required ColorScheme colorScheme,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.labelLarge(
                    context,
                    color:
                        onChanged == null
                            ? colorScheme.onSurface.withOpacity(0.5)
                            : colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: AppTextStyles.bodySmall(
                    context,
                    color:
                        onChanged == null
                            ? colorScheme.onSurfaceVariant.withOpacity(0.5)
                            : colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          Switch.adaptive(
            value: value,
            onChanged: onChanged,
            activeColor: colorScheme.primary,
            activeTrackColor: colorScheme.primary.withOpacity(0.3),
            inactiveThumbColor: Colors.grey.shade400,
            inactiveTrackColor: Colors.grey.shade300,
          ),
        ],
      ),
    );
  }
}
