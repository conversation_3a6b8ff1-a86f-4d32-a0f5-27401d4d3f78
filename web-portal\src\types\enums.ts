/**
 * Application-specific enums
 * Centralized enum definitions for consistent typing throughout the app
 */

// User roles and permissions
export enum UserRole {
  ADMIN = 'admin',
  TECHNICIAN = 'technician',
  CUSTOMER = 'customer',
  SUPER_ADMIN = 'super_admin',
}

export enum Permission {
  READ_USERS = 'read_users',
  WRITE_USERS = 'write_users',
  DELETE_USERS = 'delete_users',
  READ_REQUESTS = 'read_requests',
  WRITE_REQUESTS = 'write_requests',
  DELETE_REQUESTS = 'delete_requests',
  READ_SERVICES = 'read_services',
  WRITE_SERVICES = 'write_services',
  DELETE_SERVICES = 'delete_services',
  READ_PAYMENTS = 'read_payments',
  WRITE_PAYMENTS = 'write_payments',
  READ_REPORTS = 'read_reports',
  MANAGE_SYSTEM = 'manage_system',
}

// Request statuses
export enum RequestStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
  ON_HOLD = 'on_hold',
  REJECTED = 'rejected',
}

// Payment statuses
export enum PaymentStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  REFUNDED = 'refunded',
  PARTIALLY_REFUNDED = 'partially_refunded',
}

// Payment methods
export enum PaymentMethod {
  CREDIT_CARD = 'credit_card',
  DEBIT_CARD = 'debit_card',
  BANK_TRANSFER = 'bank_transfer',
  CASH = 'cash',
  WALLET = 'wallet',
  INSTALLMENTS = 'installments',
}

// Service categories
export enum ServiceCategory {
  COMPUTER_REPAIR = 'computer_repair',
  MOBILE_REPAIR = 'mobile_repair',
  NETWORK_SETUP = 'network_setup',
  SOFTWARE_INSTALLATION = 'software_installation',
  DATA_RECOVERY = 'data_recovery',
  VIRUS_REMOVAL = 'virus_removal',
  HARDWARE_UPGRADE = 'hardware_upgrade',
  CONSULTATION = 'consultation',
  MAINTENANCE = 'maintenance',
  OTHER = 'other',
}

// Service types
export enum ServiceType {
  ON_SITE = 'on_site',
  REMOTE = 'remote',
  PICKUP_DELIVERY = 'pickup_delivery',
  IN_STORE = 'in_store',
}

// Technician statuses
export enum TechnicianStatus {
  AVAILABLE = 'available',
  BUSY = 'busy',
  OFFLINE = 'offline',
  ON_BREAK = 'on_break',
  IN_TRANSIT = 'in_transit',
}

// Chat message types
export enum ChatMessageType {
  TEXT = 'text',
  IMAGE = 'image',
  FILE = 'file',
  SYSTEM = 'system',
  LOCATION = 'location',
  VOICE = 'voice',
  VIDEO = 'video',
}

// Chat event types
export enum ChatEventType {
  MESSAGE_SENT = 'message_sent',
  MESSAGE_DELIVERED = 'message_delivered',
  MESSAGE_READ = 'message_read',
  USER_JOINED = 'user_joined',
  USER_LEFT = 'user_left',
  CHAT_ACTIVATED = 'chat_activated',
  CHAT_DEACTIVATED = 'chat_deactivated',
  TYPING_START = 'typing_start',
  TYPING_STOP = 'typing_stop',
}

// Notification types
export enum NotificationCategory {
  REQUEST_UPDATE = 'request_update',
  PAYMENT_UPDATE = 'payment_update',
  CHAT_MESSAGE = 'chat_message',
  SYSTEM_ALERT = 'system_alert',
  TECHNICIAN_ASSIGNMENT = 'technician_assignment',
  SERVICE_REMINDER = 'service_reminder',
  PROMOTION = 'promotion',
  MAINTENANCE = 'maintenance',
}

// File types
export enum FileType {
  IMAGE = 'image',
  DOCUMENT = 'document',
  VIDEO = 'video',
  AUDIO = 'audio',
  ARCHIVE = 'archive',
  OTHER = 'other',
}

// Error codes
export enum ErrorCode {
  // Authentication errors
  AUTH_INVALID_CREDENTIALS = 'AUTH_INVALID_CREDENTIALS',
  AUTH_TOKEN_EXPIRED = 'AUTH_TOKEN_EXPIRED',
  AUTH_INSUFFICIENT_PERMISSIONS = 'AUTH_INSUFFICIENT_PERMISSIONS',
  AUTH_ACCOUNT_DISABLED = 'AUTH_ACCOUNT_DISABLED',
  
  // Validation errors
  VALIDATION_REQUIRED_FIELD = 'VALIDATION_REQUIRED_FIELD',
  VALIDATION_INVALID_FORMAT = 'VALIDATION_INVALID_FORMAT',
  VALIDATION_OUT_OF_RANGE = 'VALIDATION_OUT_OF_RANGE',
  
  // Network errors
  NETWORK_CONNECTION_ERROR = 'NETWORK_CONNECTION_ERROR',
  NETWORK_TIMEOUT = 'NETWORK_TIMEOUT',
  NETWORK_SERVER_ERROR = 'NETWORK_SERVER_ERROR',
  
  // Business logic errors
  BUSINESS_INVALID_OPERATION = 'BUSINESS_INVALID_OPERATION',
  BUSINESS_RESOURCE_NOT_FOUND = 'BUSINESS_RESOURCE_NOT_FOUND',
  BUSINESS_DUPLICATE_RESOURCE = 'BUSINESS_DUPLICATE_RESOURCE',
  BUSINESS_QUOTA_EXCEEDED = 'BUSINESS_QUOTA_EXCEEDED',
  
  // File upload errors
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  FILE_INVALID_TYPE = 'FILE_INVALID_TYPE',
  FILE_UPLOAD_FAILED = 'FILE_UPLOAD_FAILED',
  
  // Unknown error
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

// Log levels
export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  FATAL = 'fatal',
}

// Environment types
export enum Environment {
  DEVELOPMENT = 'development',
  STAGING = 'staging',
  PRODUCTION = 'production',
  TEST = 'test',
}

// Sort directions
export enum SortDirection {
  ASC = 'asc',
  DESC = 'desc',
}

// Theme modes
export enum ThemeMode {
  LIGHT = 'light',
  DARK = 'dark',
  SYSTEM = 'system',
}

// Language codes
export enum Language {
  ENGLISH = 'en',
  ARABIC = 'ar',
}

// Currency codes
export enum Currency {
  USD = 'USD',
  EUR = 'EUR',
  EGP = 'EGP',
  SAR = 'SAR',
  AED = 'AED',
}

// Time zones
export enum TimeZone {
  UTC = 'UTC',
  CAIRO = 'Africa/Cairo',
  RIYADH = 'Asia/Riyadh',
  DUBAI = 'Asia/Dubai',
}

// Device types
export enum DeviceType {
  DESKTOP = 'desktop',
  LAPTOP = 'laptop',
  TABLET = 'tablet',
  MOBILE = 'mobile',
  SERVER = 'server',
  PRINTER = 'printer',
  ROUTER = 'router',
  OTHER = 'other',
}

// Operating systems
export enum OperatingSystem {
  WINDOWS = 'windows',
  MACOS = 'macos',
  LINUX = 'linux',
  ANDROID = 'android',
  IOS = 'ios',
  OTHER = 'other',
}

// Priority levels
export enum PriorityLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
  CRITICAL = 'critical',
}

// Status indicators
export enum StatusIndicator {
  ONLINE = 'online',
  OFFLINE = 'offline',
  AWAY = 'away',
  BUSY = 'busy',
  DO_NOT_DISTURB = 'do_not_disturb',
}

// Report types
export enum ReportType {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  QUARTERLY = 'quarterly',
  YEARLY = 'yearly',
  CUSTOM = 'custom',
}

// Export utility functions for enum validation
export const isValidEnum = <T extends Record<string, string>>(
  enumObject: T,
  value: unknown
): value is T[keyof T] => {
  return Object.values(enumObject).includes(value as T[keyof T]);
};

export const getEnumValues = <T extends Record<string, string>>(enumObject: T): T[keyof T][] => {
  return Object.values(enumObject);
};

export const getEnumKeys = <T extends Record<string, string>>(enumObject: T): (keyof T)[] => {
  return Object.keys(enumObject) as (keyof T)[];
};

// Enum validation functions
export const isUserRole = (value: unknown): value is UserRole => 
  isValidEnum(UserRole, value);

export const isRequestStatus = (value: unknown): value is RequestStatus => 
  isValidEnum(RequestStatus, value);

export const isPaymentStatus = (value: unknown): value is PaymentStatus => 
  isValidEnum(PaymentStatus, value);

export const isServiceCategory = (value: unknown): value is ServiceCategory => 
  isValidEnum(ServiceCategory, value);

export const isChatMessageType = (value: unknown): value is ChatMessageType => 
  isValidEnum(ChatMessageType, value);

export const isErrorCode = (value: unknown): value is ErrorCode => 
  isValidEnum(ErrorCode, value);
