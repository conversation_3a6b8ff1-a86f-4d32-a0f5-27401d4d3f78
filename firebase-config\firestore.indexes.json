{"indexes": [{"collectionGroup": "requests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "requests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "assignedTo", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "requests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clientId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "requests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "serviceType", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "reviews", "queryScope": "COLLECTION", "fields": [{"fieldPath": "technicianId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "reviews", "queryScope": "COLLECTION", "fields": [{"fieldPath": "technician_id", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "reviews", "queryScope": "COLLECTION", "fields": [{"fieldPath": "customer_id", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "reviews", "queryScope": "COLLECTION", "fields": [{"fieldPath": "rating", "order": "DESCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "reviews", "queryScope": "COLLECTION", "fields": [{"fieldPath": "verified", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "services", "queryScope": "COLLECTION", "fields": [{"fieldPath": "category", "order": "ASCENDING"}, {"fieldPath": "price", "order": "ASCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "user_id", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "user_id", "order": "ASCENDING"}, {"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "service_requests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "service_requests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "customer_id", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "service_requests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "customer_id", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "created_at", "order": "DESCENDING"}]}, {"collectionGroup": "service_requests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "customerId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "service_requests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "customerId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "admin_notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "forAllTechnicians", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "admin_notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "forAllTechnicians", "order": "ASCENDING"}, {"fieldPath": "read", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "admin_notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "read", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "read", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "service_requests", "queryScope": "COLLECTION", "fields": [{"fieldPath": "chat_active", "order": "ASCENDING"}, {"fieldPath": "customer_id", "order": "ASCENDING"}, {"fieldPath": "updated_at", "order": "DESCENDING"}]}, {"collectionGroup": "activities", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}], "fieldOverrides": []}