import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/translation_service.dart';
import '../services/database_service.dart';
import '../services/technician_service.dart';
import '../models/service_model.dart';
import '../models/request_model.dart';
import 'payment_screen.dart';
import '../utils/navigation_utils.dart';
import '../widgets/text_styles.dart';

class RequestReviewScreen extends StatefulWidget {
  final ServiceModel service;
  final String issueDescription;

  const RequestReviewScreen({
    super.key,
    required this.service,
    required this.issueDescription,
  });

  @override
  State<RequestReviewScreen> createState() => _RequestReviewScreenState();
}

class _RequestReviewScreenState extends State<RequestReviewScreen> {
  bool _isCreatingRequest = false;
  bool _isCheckingTechnicians = true;
  bool _techniciansAvailable = false;
  int _technicianCount = 0;
  final TechnicianService _technicianService = TechnicianService();

  @override
  void initState() {
    super.initState();
    _checkTechnicianAvailability();
  }

  Future<void> _checkTechnicianAvailability() async {
    try {
      final techniciansAvailable =
          await _technicianService.areTechniciansAvailable();
      final count = await _technicianService.getActiveTechnicianCount();

      if (mounted) {
        setState(() {
          _techniciansAvailable = techniciansAvailable;
          _technicianCount = count;
          _isCheckingTechnicians = false;
        });
      }
    } catch (e) {
      debugPrint('Error checking technician availability: $e');
      if (mounted) {
        setState(() {
          _techniciansAvailable = false;
          _isCheckingTechnicians = false;
        });
      }
    }
  }

  Future<void> _proceedToPayment(BuildContext context) async {
    // First check if technicians are available
    if (!_techniciansAvailable) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            _translate(
              'No technicians available at the moment. Please try again later.',
            ),
          ),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isCreatingRequest = true;
    });

    try {
      // Get database service
      final databaseService = Provider.of<DatabaseService>(
        context,
        listen: false,
      );

      final translationService = Provider.of<TranslationService>(
        context,
        listen: false,
      );

      final languageCode = translationService.currentLocale.languageCode;

      // Get translated names
      final serviceName = widget.service.getTranslatedName(languageCode);
      final serviceDescription = widget.service.getTranslatedDescription(
        languageCode,
      );

      // Create a temporary RequestModel with the necessary data
      final tempRequest = RequestModel(
        id: 'temp_id', // Will be replaced by Firestore document ID after payment
        customerId: databaseService.currentUserId ?? '',
        serviceId: widget.service.id,
        serviceName: serviceName,
        serviceDescription: serviceDescription,
        customerIssue: widget.issueDescription,
        status: RequestStatus.payment_pending,
        amount: widget.service.basePrice,
        isPaid: false,
        chatActive: false,
        createdAt: DateTime.now(),
      );

      // Navigate directly to payment screen with the temporary request
      if (mounted) {
        NavigationUtils.navigateWithBottomNav(
          context,
          PaymentScreen(
            request: tempRequest,
            serviceData: {
              'serviceId': widget.service.id,
              'serviceName': serviceName,
              'serviceDescription': serviceDescription,
              'customerIssue': widget.issueDescription,
              'amount': widget.service.basePrice,
            },
          ),
        );
      }
    } catch (e) {
      debugPrint('Error preparing payment: $e');
      // Show error message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            _translate('Error processing request. Please try again.'),
          ),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isCreatingRequest = false;
        });
      }
    }
  }

  String _translate(String key) {
    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );
    return translationService.translate(key);
  }

  Widget _buildAvailabilityBanner(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    Color bgColor;
    IconData statusIcon;
    String statusMessage;

    if (_isCheckingTechnicians) {
      bgColor = colorScheme.primary;
      statusIcon = Icons.hourglass_empty_rounded;
      statusMessage = _translate('Checking technician availability...');
    } else if (_techniciansAvailable) {
      bgColor = Colors.green.shade600;
      statusIcon = Icons.check_circle_outline_rounded;
      statusMessage = _translate(
        'Technicians available: {count} online',
      ).replaceAll('{count}', _technicianCount.toString());
    } else {
      bgColor = Colors.orange.shade700;
      statusIcon = Icons.warning_amber_rounded;
      statusMessage = _translate(
        'Limited availability: Wait times may be longer',
      );
    }

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              shape: BoxShape.circle,
              border: Border.all(color: Colors.grey.shade300),
            ),
            child:
                _isCheckingTechnicians
                    ? SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                    : Icon(statusIcon, color: Colors.white, size: 16),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              statusMessage,
              style: AppTextStyles.bodyMedium(context, color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );
    final languageCode = translationService.currentLocale.languageCode;

    // Get translated name and description from the service model
    final serviceName = widget.service.getTranslatedName(languageCode);

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(color: Colors.grey.shade300),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _translate('Request Summary'),
              style: AppTextStyles.headingSmall(
                context,
                color: colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),
            Divider(height: 1, color: Colors.grey.shade300),
            const SizedBox(height: 16),

            // Service details section
            _buildSummaryRow(
              _translate('Service'),
              serviceName,
              icon: Icons.miscellaneous_services_rounded,
              context: context,
            ),
            const SizedBox(height: 12),

            // Price section
            _buildSummaryRow(
              _translate('Price'),
              '${_translate('L.E')} ${widget.service.basePrice.toStringAsFixed(0)}',
              icon: Icons.attach_money_rounded,
              context: context,
              valueStyle: AppTextStyles.headingSmall(
                context,
                color: Colors.green.shade700,
              ),
            ),
            const SizedBox(height: 12),

            // Estimated time section
            _buildSummaryRow(
              _translate('Est. Duration'),
              '${widget.service.estimatedDuration} ${_translate('min')}',
              icon: Icons.access_time_rounded,
              context: context,
            ),
            const SizedBox(height: 20),

            // Issue description
            Text(
              _translate('Your Description'),
              style: AppTextStyles.buttonMedium(
                context,
                color: colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest.withOpacity(0.3),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Text(
                widget.issueDescription,
                style: AppTextStyles.bodyMedium(
                  context,
                  color: colorScheme.onSurface,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(
    String label,
    String value, {
    required BuildContext context,
    IconData? icon,
    bool isMultiline = false,
    TextStyle? valueStyle,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Row(
      crossAxisAlignment:
          isMultiline ? CrossAxisAlignment.start : CrossAxisAlignment.center,
      children: [
        if (icon != null) ...[
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Icon(icon, color: colorScheme.primary, size: 18),
          ),
          const SizedBox(width: 12),
        ],
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTextStyles.bodySmall(
                  context,
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style:
                    valueStyle ??
                    AppTextStyles.buttonMedium(
                      context,
                      color: colorScheme.onSurface,
                    ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildWhatHappensNextCard(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(color: Colors.grey.shade300),
      ),
      child: Container(
        decoration: BoxDecoration(
          color: colorScheme.primaryContainer.withOpacity(0.3),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.grey.shade300),
        ),
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: Icon(
                    Icons.info_outline_rounded,
                    color: colorScheme.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  _translate('What Happens Next'),
                  style: AppTextStyles.headingSmall(
                    context,
                    color: colorScheme.onSurface,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildStepItem('1', _translate('Complete payment securely')),
            _buildStepItem(
              '2',
              _translate('We assign an expert technician based on your issue'),
            ),
            _buildStepItem(
              '3',
              _translate('Technician contacts you via chat or call'),
            ),
            _buildStepItem(
              '4',
              _translate('Troubleshooting begins immediately'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStepItem(String number, String text) {
    final colorScheme = Theme.of(context).colorScheme;

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: colorScheme.primary.withOpacity(0.1),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Center(
              child: Text(
                number,
                style: AppTextStyles.buttonSmall(
                  context,
                  color: colorScheme.primary,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              text,
              style: AppTextStyles.bodyMedium(
                context,
                color: colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: 80,
            floating: true,
            pinned: false,
            snap: true,
            elevation: 0,
            backgroundColor: colorScheme.surface,
            leading: BackButton(color: colorScheme.onSurface),
            flexibleSpace: FlexibleSpaceBar(
              background: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 10,
                  ),
                  child: Row(
                    children: [
                      const SizedBox(width: 56), // Space for back button
                      Expanded(
                        child: Text(
                          _translate('Review Request'),
                          style: AppTextStyles.headingMedium(
                            context,
                            color: colorScheme.onSurface,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(width: 56), // Balance the layout
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Main content
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  _buildAvailabilityBanner(context),
                  const SizedBox(height: 16),
                  _buildSummaryCard(context),
                  const SizedBox(height: 16),
                  _buildWhatHappensNextCard(context),
                  const SizedBox(height: 100), // Space for bottom button
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: FilledButton.icon(
            onPressed:
                _isCreatingRequest ? null : () => _proceedToPayment(context),
            icon:
                _isCreatingRequest
                    ? Container()
                    : const Icon(Icons.payment_rounded, size: 20),
            label:
                _isCreatingRequest
                    ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                    : Text(
                      _translate('Proceed to Payment'),
                      style: AppTextStyles.buttonMedium(
                        context,
                        color: Colors.white,
                      ),
                    ),
            style: FilledButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              backgroundColor: colorScheme.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
