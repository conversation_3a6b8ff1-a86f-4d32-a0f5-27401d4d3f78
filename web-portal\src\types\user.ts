import type { Timestamp } from 'firebase/firestore';

// User roles matching the existing system
export type UserRole = 'admin' | 'technician' | 'customer';

// User status enum
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
  PENDING = 'pending',
}

// Base user interface with dual field naming support
export interface BaseUser {
  id: string;

  // Basic Information (dual naming for compatibility)
  email: string;
  name: string;
  display_name?: string; // snake_case
  displayName?: string; // camelCase

  // Contact Information
  phone_number?: string; // snake_case
  phoneNumber?: string; // camelCase
  photo_url?: string; // snake_case
  photoUrl?: string; // camelCase
  photoURL?: string; // legacy support

  // Location Information
  address?: string;
  city?: string;
  country?: string;

  // Account Status
  role: UserRole;
  status: UserStatus;
  is_active: boolean; // snake_case
  isActive?: boolean; // camelCase
  is_verified: boolean; // snake_case
  isVerified?: boolean; // camelCase
  email_verified: boolean; // snake_case
  emailVerified?: boolean; // camelCase

  // Onboarding
  is_onboarded: boolean; // snake_case
  isOnboarded?: boolean; // camelCase
  is_complete: boolean; // snake_case
  isComplete?: boolean; // camelCase

  // Preferences
  preferred_language: 'en' | 'ar'; // snake_case
  preferredLanguage?: 'en' | 'ar'; // camelCase

  // Timestamps
  created_at: Timestamp | Date; // snake_case
  createdAt?: Timestamp | Date; // camelCase
  updated_at?: Timestamp | Date; // snake_case
  updatedAt?: Timestamp | Date; // camelCase
  last_login?: Timestamp | Date; // snake_case
  lastLogin?: Timestamp | Date; // camelCase

  // Device and Session
  device_type?: 'web' | 'ios' | 'android'; // snake_case
  deviceType?: 'web' | 'ios' | 'android'; // camelCase
  fcm_token?: string; // snake_case
  fcmToken?: string; // camelCase

  // Security
  security?: {
    account_locked: boolean; // snake_case
    accountLocked?: boolean; // camelCase
    failed_login_attempts: number; // snake_case
    failedLoginAttempts?: number; // camelCase
    password_changed_at?: Timestamp | Date; // snake_case
    passwordChangedAt?: Timestamp | Date; // camelCase
    last_login_at?: Timestamp | Date; // snake_case
    lastLoginAt?: Timestamp | Date; // camelCase
  };

  // Notification Preferences
  notification_preferences?: {
    // snake_case
    chat_messages: boolean;
    request_updates: boolean;
    system_notifications: boolean;
    email_notifications: boolean;
    push_notifications: boolean;
  };
  notificationPreferences?: {
    // camelCase
    chatMessages: boolean;
    requestUpdates: boolean;
    systemNotifications: boolean;
    emailNotifications: boolean;
    pushNotifications: boolean;
  };
}

// Regular User (Customer) - extends BaseUser
export interface CustomerUser extends BaseUser {
  role: 'customer';

  // Customer-specific fields
  anydesk_id?: string;

  // Customer stats
  total_requests?: number; // snake_case
  totalRequests?: number; // camelCase
  completed_requests?: number; // snake_case
  completedRequests?: number; // camelCase

  // Customer preferences
  preferred_payment_method?: string; // snake_case
  preferredPaymentMethod?: string; // camelCase
}

// Technician User - extends BaseUser
export interface TechnicianUser extends BaseUser {
  role: 'technician';

  // Technician-specific fields
  specialties: string[];
  rating: number;
  completed_requests: number; // snake_case
  completedRequests?: number; // camelCase
  active_requests: number; // snake_case
  activeRequests?: number; // camelCase
  is_available: boolean; // snake_case
  isAvailable?: boolean; // camelCase

  // Technician status (different from user status)
  technician_status?: 'active' | 'offline' | 'busy' | 'onLeave'; // snake_case
  technicianStatus?: 'active' | 'offline' | 'busy' | 'onLeave'; // camelCase

  // Work schedule
  work_schedule?: {
    monday?: { start: string; end: string; available: boolean };
    tuesday?: { start: string; end: string; available: boolean };
    wednesday?: { start: string; end: string; available: boolean };
    thursday?: { start: string; end: string; available: boolean };
    friday?: { start: string; end: string; available: boolean };
    saturday?: { start: string; end: string; available: boolean };
    sunday?: { start: string; end: string; available: boolean };
  };
  workSchedule?: {
    monday?: { start: string; end: string; available: boolean };
    tuesday?: { start: string; end: string; available: boolean };
    wednesday?: { start: string; end: string; available: boolean };
    thursday?: { start: string; end: string; available: boolean };
    friday?: { start: string; end: string; available: boolean };
    saturday?: { start: string; end: string; available: boolean };
    sunday?: { start: string; end: string; available: boolean };
  };
}

// Admin User - extends BaseUser
export interface AdminUser extends BaseUser {
  role: 'admin';

  // Admin-specific fields
  permissions: string[];
  admin_level: 'super' | 'standard' | 'limited'; // snake_case
  adminLevel?: 'super' | 'standard' | 'limited'; // camelCase

  // Admin activity tracking
  last_action?: string; // snake_case
  lastAction?: string; // camelCase
  last_action_at?: Timestamp | Date; // snake_case
  lastActionAt?: Timestamp | Date; // camelCase
}

// Union type for all user types
export type User = CustomerUser | TechnicianUser | AdminUser;

// Input types for creating users
export interface CreateUserInput {
  email: string;
  name: string;
  password?: string;
  role: UserRole;
  phone_number?: string;
  photo_url?: string;
  address?: string;
  city?: string;
  country?: string;
  preferred_language?: 'en' | 'ar';

  // Role-specific fields
  specialties?: string[]; // for technicians
  permissions?: string[]; // for admins
  admin_level?: 'super' | 'standard' | 'limited'; // for admins
  anydesk_id?: string; // for customers
}

// Input types for updating users
export interface UpdateUserInput {
  name?: string;
  phone_number?: string;
  photo_url?: string;
  address?: string;
  city?: string;
  country?: string;
  preferred_language?: 'en' | 'ar';
  status?: UserStatus;
  is_active?: boolean;

  // Role-specific updates
  specialties?: string[]; // for technicians
  permissions?: string[]; // for admins
  admin_level?: 'super' | 'standard' | 'limited'; // for admins
  anydesk_id?: string; // for customers
  is_available?: boolean; // for technicians
  technician_status?: 'active' | 'offline' | 'busy' | 'onLeave'; // for technicians
}

// User search and filter options
export interface UserQueryOptions {
  role?: UserRole;
  status?: UserStatus;
  is_active?: boolean;
  search?: string;
  city?: string;
  country?: string;
  limit?: number;
  orderBy?: { field: string; direction: 'asc' | 'desc' };
}

// User statistics interface
export interface UserStats {
  total: number;
  active: number;
  inactive: number;
  suspended: number;
  pending: number;
  byRole: {
    customers: number;
    technicians: number;
    admins: number;
  };
  byLocation: Record<string, number>;
  recentSignups: number; // last 30 days
  totalRequests: number; // total number of requests across all customers
  customerStats: {
    withRequests: number; // customers with at least one request
    withAnydesk: number; // customers with AnyDesk ID configured
    verified: number; // verified customers
    onboarded: number; // onboarded customers
  };
}

// Activity log interface
export interface UserActivity {
  id: string;
  user_id: string;
  userId?: string;
  action: string;
  details?: string;
  ip_address?: string;
  ipAddress?: string;
  user_agent?: string;
  userAgent?: string;
  created_at: Timestamp | Date;
  createdAt?: Timestamp | Date;
}

// Audit log interface
export interface UserAuditLog {
  id: string;
  user_id: string;
  userId?: string;
  admin_id?: string;
  adminId?: string;
  action: 'create' | 'update' | 'delete' | 'suspend' | 'activate' | 'role_change';
  old_values?: Record<string, any>;
  oldValues?: Record<string, any>;
  new_values?: Record<string, any>;
  newValues?: Record<string, any>;
  reason?: string;
  created_at: Timestamp | Date;
  createdAt?: Timestamp | Date;
}
