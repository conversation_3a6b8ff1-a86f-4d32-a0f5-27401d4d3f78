const admin = require('firebase-admin');

// Initialize Firebase Admin SDK with service account
try {
  // Use the service account from the mobile app directory
  const serviceAccount = require('../mr_tech_mobile/firebase-admin-key.json');

  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: 'stopnow-be6b7',
    databaseURL: 'https://stopnow-be6b7-default-rtdb.europe-west1.firebasedatabase.app'
  });
  console.log('Firebase Admin SDK initialized successfully');
} catch (error) {
  console.log('Firebase already initialized or error:', error.message);
}

const db = admin.firestore();

/**
 * Test script to verify FCM notification flow
 * This script checks:
 * 1. If service requests have FCM tokens
 * 2. If user documents have FCM tokens
 * 3. If notifications are being created properly
 */

async function testFCMNotificationFlow() {
  console.log('🔍 Testing FCM Notification Flow...\n');

  try {
    // 1. Check service requests for FCM tokens
    console.log('1. Checking service requests for FCM tokens...');
    const serviceRequestsSnapshot = await db.collection('service_requests')
      .where('status', 'in', ['pending', 'approved', 'in-progress'])
      .limit(5)
      .get();

    if (serviceRequestsSnapshot.empty) {
      console.log('   ❌ No active service requests found');
    } else {
      serviceRequestsSnapshot.forEach(doc => {
        const data = doc.data();
        const hasSnakeCaseToken = !!data.customer_fcm_token;
        const hasCamelCaseToken = !!data.customerFcmToken;
        
        console.log(`   📋 Request ${doc.id}:`);
        console.log(`      - customer_fcm_token: ${hasSnakeCaseToken ? '✅ Present' : '❌ Missing'}`);
        console.log(`      - customerFcmToken: ${hasCamelCaseToken ? '✅ Present' : '❌ Missing'}`);
        console.log(`      - customer_id: ${data.customer_id || 'Missing'}`);
        console.log(`      - status: ${data.status}`);
        
        if (hasSnakeCaseToken) {
          console.log(`      - Token preview: ${data.customer_fcm_token.substring(0, 20)}...`);
        }
        console.log('');
      });
    }

    // 2. Check user documents for FCM tokens
    console.log('2. Checking user documents for FCM tokens...');
    const usersSnapshot = await db.collection('users').limit(5).get();

    if (usersSnapshot.empty) {
      console.log('   ❌ No users found');
    } else {
      usersSnapshot.forEach(doc => {
        const data = doc.data();
        const hasSnakeCaseToken = !!data.fcm_token;
        const hasCamelCaseToken = !!data.fcmToken;
        const hasDeviceTokens = Array.isArray(data.device_tokens) && data.device_tokens.length > 0;
        
        console.log(`   👤 User ${doc.id}:`);
        console.log(`      - fcm_token: ${hasSnakeCaseToken ? '✅ Present' : '❌ Missing'}`);
        console.log(`      - fcmToken: ${hasCamelCaseToken ? '✅ Present' : '❌ Missing'}`);
        console.log(`      - device_tokens: ${hasDeviceTokens ? '✅ Present' : '❌ Missing'}`);
        console.log(`      - email: ${data.email || 'Missing'}`);
        console.log('');
      });
    }

    // 3. Check recent notifications
    console.log('3. Checking recent notifications...');
    const notificationsSnapshot = await db.collection('notifications')
      .orderBy('created_at', 'desc')
      .limit(5)
      .get();

    if (notificationsSnapshot.empty) {
      console.log('   ❌ No notifications found');
    } else {
      notificationsSnapshot.forEach(doc => {
        const data = doc.data();
        console.log(`   🔔 Notification ${doc.id}:`);
        console.log(`      - user_id: ${data.user_id}`);
        console.log(`      - type: ${data.type}`);
        console.log(`      - title: ${data.title}`);
        console.log(`      - delivered: ${data.delivered ? '✅ Yes' : '❌ No'}`);
        console.log(`      - request_id: ${data.request_id || 'N/A'}`);
        console.log('');
      });
    }

    // 4. Check device_tokens collection
    console.log('4. Checking device_tokens collection...');
    const deviceTokensSnapshot = await db.collection('device_tokens')
      .where('active', '==', true)
      .limit(5)
      .get();

    if (deviceTokensSnapshot.empty) {
      console.log('   ❌ No active device tokens found');
    } else {
      deviceTokensSnapshot.forEach(doc => {
        const data = doc.data();
        console.log(`   📱 Device Token ${doc.id}:`);
        console.log(`      - userId: ${data.userId}`);
        console.log(`      - platform: ${data.platform}`);
        console.log(`      - active: ${data.active}`);
        console.log(`      - token preview: ${data.token ? data.token.substring(0, 20) + '...' : 'Missing'}`);
        console.log('');
      });
    }

    console.log('✅ FCM Notification Flow Test Complete!');

  } catch (error) {
    console.error('❌ Error testing FCM notification flow:', error);
  }
}

// Run the test
testFCMNotificationFlow().then(() => {
  process.exit(0);
}).catch(error => {
  console.error('Fatal error:', error);
  process.exit(1);
});
