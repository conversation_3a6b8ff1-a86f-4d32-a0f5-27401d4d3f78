import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import '../models/request_model.dart';
import '../utils/env_config.dart';
import 'request_service.dart';
import 'technician_service.dart';
import '../widgets/payment/native_payment_flow.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';

/// Native payment service that provides a native UI experience
/// instead of using WebView for payments
class NativePaymentService {
  // Singleton instance
  static final NativePaymentService _instance = NativePaymentService._internal();
  factory NativePaymentService() => _instance;
  NativePaymentService._internal();

  final RequestService _requestService = RequestService();
  final TechnicianService _technicianService = TechnicianService();
  final EnvConfig _envConfig = EnvConfig.instance;

  // V2 API configuration
  String? _secretKey;
  String? _publicKey;
  String? _integrationId;

  // V2 API endpoints
  static const String _baseUrl = 'https://accept.paymob.com';
  static const String _intentionEndpoint = '/v1/intention/';

  bool _isInitialized = false;

  /// Initialize the native payment service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Ensure environment config is initialized
      if (!_envConfig.isInitialized) {
        await _envConfig.initialize();
      }

      await _loadEnvironmentVariables();
      _isInitialized = true;

      debugPrint('NativePaymentService initialized successfully');
    } catch (e) {
      debugPrint('Error initializing NativePaymentService: $e');
      _isInitialized = true; // Still mark as initialized to avoid repeated attempts
    }
  }

  /// Load environment variables from EnvConfig
  Future<void> _loadEnvironmentVariables() async {
    try {
      _secretKey = await _envConfig.get('PAYMOB_SECRET_KEY');
      _publicKey = await _envConfig.get('PAYMOB_PUBLIC_KEY');
      _integrationId = await _envConfig.get('PAYMOB_INTEGRATION_ID');

      // Remove quotes if present
      if (_secretKey != null) _secretKey = _removeQuotes(_secretKey!);
      if (_publicKey != null) _publicKey = _removeQuotes(_publicKey!);
      if (_integrationId != null) _integrationId = _removeQuotes(_integrationId!);

      // Validate credentials
      if (_secretKey == null || _secretKey!.isEmpty) {
        debugPrint('❌ PAYMOB_SECRET_KEY is missing');
        throw Exception('Payment credentials not configured');
      }
    } catch (e) {
      debugPrint('Error loading payment environment variables: $e');
      rethrow;
    }
  }

  /// Helper method to remove quotes from values
  String _removeQuotes(String value) {
    if ((value.startsWith('"') && value.endsWith('"')) ||
        (value.startsWith("'") && value.endsWith("'"))) {
      return value.substring(1, value.length - 1);
    }
    return value;
  }

  /// Show native payment flow
  Future<bool> showNativePaymentFlow({
    required BuildContext context,
    required RequestModel request,
    required Map<String, dynamic> billingData,
    Map<String, dynamic>? serviceData,
  }) async {
    // Ensure service is initialized
    if (!_isInitialized) {
      await initialize();
    }

    try {
      // Verify prerequisites
      await _verifyPaymentPrerequisites(context, request, serviceData);

      // Show native payment flow
      final result = await showDialog<bool>(
        context: context,
        barrierDismissible: false,
        builder: (context) => Dialog.fullscreen(
          child: NativePaymentFlow(
            billingData: billingData,
            amount: request.amount,
            serviceName: request.serviceName,
            onPaymentConfirm: (cardData) async {
              // Process the payment with card data
              final success = await _processNativePayment(
                context,
                request,
                billingData,
                cardData,
                serviceData,
              );
              
              Navigator.of(context).pop(success);
            },
            onCancel: () => Navigator.of(context).pop(false),
          ),
        ),
      );

      return result ?? false;
    } catch (e) {
      debugPrint('Error showing native payment flow: $e');
      await _showErrorDialog(context, 'Payment Error', e.toString());
      return false;
    }
  }

  /// Verify payment prerequisites
  Future<void> _verifyPaymentPrerequisites(
    BuildContext context,
    RequestModel request,
    Map<String, dynamic>? serviceData,
  ) async {
    // Check if request exists (for existing requests)
    if (serviceData == null) {
      final requestExists = await _requestService.checkRequestExists(request.id);
      if (!requestExists) {
        throw Exception('Service request no longer exists. Please create a new request.');
      }
    }

    // Verify technicians are available
    final techniciansAvailable = await _technicianService.areTechniciansAvailable();
    if (!techniciansAvailable) {
      throw Exception('No technicians available at the moment. Please try again later.');
    }
  }

  /// Process native payment with card data
  Future<bool> _processNativePayment(
    BuildContext context,
    RequestModel request,
    Map<String, dynamic> billingData,
    Map<String, String> cardData,
    Map<String, dynamic>? serviceData,
  ) async {
    try {
      debugPrint('Processing native payment with card data');
      
      // Step 1: Create intention with Paymob
      final intentionId = await _createPaymentIntention(request, billingData);
      
      // Step 2: Create payment token with card data
      final paymentToken = await _createPaymentToken(intentionId, cardData);
      
      // Step 3: Process the payment
      final paymentSuccess = await _processPaymentWithToken(paymentToken);
      
      if (paymentSuccess) {
        // Step 4: Handle successful payment
        await _handleSuccessfulPayment(request, serviceData);
        return true;
      } else {
        throw Exception('Payment was declined or failed');
      }
    } catch (e) {
      debugPrint('Error processing native payment: $e');
      await _showErrorDialog(context, 'Payment Failed', e.toString());
      return false;
    }
  }

  /// Create payment intention with Paymob V2 API
  Future<String> _createPaymentIntention(
    RequestModel request,
    Map<String, dynamic> billingData,
  ) async {
    final formattedBillingData = _formatBillingData(billingData);
    
    final intentionPayload = {
      'amount': (request.amount * 100).toInt(),
      'currency': 'EGP',
      'payment_methods': [int.parse(_integrationId!)],
      'items': [
        {
          'name': request.serviceName,
          'amount': (request.amount * 100).toInt(),
          'description': request.serviceDescription ?? 'Technical support service',
          'quantity': 1,
        },
      ],
      'billing_data': formattedBillingData,
      'extras': {'request_id': request.id},
      'special_reference': 'mr_tech_${request.id}_${DateTime.now().millisecondsSinceEpoch}',
    };

    final response = await http.post(
      Uri.parse('$_baseUrl$_intentionEndpoint'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Token $_secretKey',
      },
      body: jsonEncode(intentionPayload),
    );

    if (response.statusCode != 201 && response.statusCode != 200) {
      debugPrint('Intention creation failed: ${response.statusCode} - ${response.body}');
      throw Exception('Failed to create payment intention');
    }

    final intentionData = jsonDecode(response.body);
    final intentionId = intentionData['id'];
    
    if (intentionId == null) {
      throw Exception('Invalid intention response from payment gateway');
    }

    debugPrint('Payment intention created: $intentionId');
    return intentionId.toString();
  }

  /// Create payment token with card data
  Future<String> _createPaymentToken(String intentionId, Map<String, String> cardData) async {
    // In a real implementation, this would create a payment token
    // using Paymob's tokenization API with the card data
    // For now, we'll simulate this process
    
    debugPrint('Creating payment token for intention: $intentionId');
    debugPrint('Card data: ${cardData.keys.join(', ')}');
    
    // Simulate token creation delay
    await Future.delayed(const Duration(milliseconds: 500));
    
    // Return a simulated token
    return 'token_${intentionId}_${DateTime.now().millisecondsSinceEpoch}';
  }

  /// Process payment with token
  Future<bool> _processPaymentWithToken(String paymentToken) async {
    debugPrint('Processing payment with token: $paymentToken');
    
    // Simulate payment processing
    await Future.delayed(const Duration(seconds: 1));
    
    // For demo purposes, randomly succeed or fail
    // In production, this would make the actual payment API call
    final success = DateTime.now().millisecondsSinceEpoch % 10 > 2; // 80% success rate
    
    debugPrint('Payment result: ${success ? 'SUCCESS' : 'FAILED'}');
    return success;
  }

  /// Handle successful payment
  Future<void> _handleSuccessfulPayment(
    RequestModel request,
    Map<String, dynamic>? serviceData,
  ) async {
    try {
      if (serviceData != null) {
        // Create new request after payment
        debugPrint('Creating request after successful payment');
        await _requestService.createRequest(
          serviceId: serviceData['serviceId'],
          serviceName: serviceData['serviceName'],
          serviceDescription: serviceData['serviceDescription'],
          customerIssue: serviceData['customerIssue'],
          amount: serviceData['amount'],
          isPaid: true,
          isVisible: true,
        );
      } else {
        // Update existing request
        await _requestService.confirmPaidRequest(request.id);
      }
      
      debugPrint('Payment completed successfully');
    } catch (e) {
      debugPrint('Error handling successful payment: $e');
      throw Exception('Payment processed but failed to update request: $e');
    }
  }

  /// Format billing data for Paymob API
  Map<String, String> _formatBillingData(Map<String, dynamic> userBillingData) {
    return {
      'apartment': (userBillingData['apartment'] ?? 'NA').toString(),
      'email': (userBillingData['email'] ?? '<EMAIL>').toString(),
      'floor': (userBillingData['floor'] ?? 'NA').toString(),
      'first_name': (userBillingData['first_name'] ?? 'Customer').toString(),
      'street': (userBillingData['street'] ?? 'NA').toString(),
      'building': (userBillingData['building'] ?? 'NA').toString(),
      'phone_number': (userBillingData['phone_number'] ?? '01000000000').toString(),
      'city': (userBillingData['city'] ?? 'Cairo').toString(),
      'country': (userBillingData['country'] ?? 'Egypt').toString(),
      'last_name': (userBillingData['last_name'] ?? 'User').toString(),
      'state': (userBillingData['state'] ?? 'Cairo').toString(),
    };
  }

  /// Show error dialog
  Future<void> _showErrorDialog(BuildContext context, String title, String message) async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}