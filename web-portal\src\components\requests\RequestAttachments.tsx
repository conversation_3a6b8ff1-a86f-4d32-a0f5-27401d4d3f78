import React, { useEffect, useState } from 'react';
import { Download, Eye, FileText, Image as ImageIcon, Paperclip, Plus, Trash2 } from 'lucide-react';
import { But<PERSON> } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import FileUpload from '../ui/FileUpload';
import { cn } from '../../lib/utils';
import fileUploadService from '../../services/fileUploadService.js';

export interface RequestAttachment {
  id: string;
  fileName: string;
  fileUrl: string;
  fileSize: number;
  fileType: string;
  uploadedAt: Date;
  uploadedBy: string;
  uploadedByName?: string;
}

interface RequestAttachmentsProps {
  requestId: string;
  attachments: RequestAttachment[];
  onAttachmentAdd?: (attachment: RequestAttachment) => void;
  onAttachmentRemove?: (attachmentId: string) => void;
  canUpload?: boolean;
  canDelete?: boolean;
  className?: string;
}

const RequestAttachments: React.FC<RequestAttachmentsProps> = ({
  requestId,
  attachments,
  onAttachmentAdd,
  onAttachmentRemove,
  canUpload = false,
  canDelete = false,
  className,
}) => {
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [previewAttachment, setPreviewAttachment] = useState<RequestAttachment | null>(null);

  const handleFileUpload = (url: string, file: File) => {
    const newAttachment: RequestAttachment = {
      id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      fileName: file.name,
      fileUrl: url,
      fileSize: file.size,
      fileType: file.type,
      uploadedAt: new Date(),
      uploadedBy: 'current-user', // This should come from auth context
      uploadedByName: 'Current User',
    };

    onAttachmentAdd?.(newAttachment);
    setShowUploadDialog(false);
  };

  const handleDownload = (attachment: RequestAttachment) => {
    const link = document.createElement('a');
    link.href = attachment.fileUrl;
    link.download = attachment.fileName;
    link.target = '_blank';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handlePreview = (attachment: RequestAttachment) => {
    if (fileUploadService.isImageUrl(attachment.fileUrl)) {
      setPreviewAttachment(attachment);
    } else {
      // For non-images, open in new tab
      window.open(attachment.fileUrl, '_blank');
    }
  };

  const handleDelete = (attachmentId: string) => {
    if (window.confirm('Are you sure you want to delete this attachment?')) {
      onAttachmentRemove?.(attachmentId);
    }
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return <ImageIcon className='h-5 w-5 text-blue-500' />;
    }
    return <FileText className='h-5 w-5 text-gray-500' />;
  };

  const getFileTypeCategory = (fileType: string) => {
    const category = fileUploadService.getFileTypeCategory(fileType);
    const colors = {
      image: 'bg-blue-100 text-blue-800',
      document: 'bg-green-100 text-green-800',
      other: 'bg-gray-100 text-gray-800',
    };
    return colors[category];
  };

  return (
    <Card className={cn('', className)}>
      <CardHeader>
        <div className='flex items-center justify-between'>
          <CardTitle className='flex items-center gap-2'>
            <Paperclip className='h-5 w-5' />
            Attachments ({attachments.length})
          </CardTitle>
          {canUpload && (
            <Dialog open={showUploadDialog} onOpenChange={setShowUploadDialog}>
              <DialogTrigger asChild>
                <Button variant='outline' size='sm'>
                  <Plus className='h-4 w-4 mr-2' />
                  Add Attachment
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Upload Attachment</DialogTitle>
                </DialogHeader>
                <FileUpload
                  requestId={requestId}
                  uploadOnSelect={true}
                  variant='dropzone'
                  onFileUpload={handleFileUpload}
                  showPreview={true}
                />
              </DialogContent>
            </Dialog>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {attachments.length === 0 ? (
          <div className='text-center py-8 text-gray-500'>
            <Paperclip className='h-12 w-12 mx-auto mb-4 text-gray-300' />
            <p>No attachments yet</p>
            {canUpload && <p className='text-sm mt-2'>Click "Add Attachment" to upload files</p>}
          </div>
        ) : (
          <div className='space-y-3'>
            {attachments.map((attachment) => (
              <div
                key={attachment.id}
                className='flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors'
              >
                <div className='flex items-center gap-3 flex-1 min-w-0'>
                  {getFileIcon(attachment.fileType)}
                  <div className='flex-1 min-w-0'>
                    <div className='flex items-center gap-2 mb-1'>
                      <p className='text-sm font-medium truncate'>{attachment.fileName}</p>
                      <Badge
                        variant='secondary'
                        className={cn('text-xs', getFileTypeCategory(attachment.fileType))}
                      >
                        {fileUploadService.getFileTypeCategory(attachment.fileType)}
                      </Badge>
                    </div>
                    <div className='flex items-center gap-4 text-xs text-gray-500'>
                      <span>{fileUploadService.formatFileSize(attachment.fileSize)}</span>
                      <span>
                        Uploaded by {attachment.uploadedByName || 'Unknown'} on{' '}
                        {attachment.uploadedAt.toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                </div>

                <div className='flex items-center gap-1'>
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={() => handlePreview(attachment)}
                    title='Preview/Open'
                  >
                    <Eye className='h-4 w-4' />
                  </Button>
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={() => handleDownload(attachment)}
                    title='Download'
                  >
                    <Download className='h-4 w-4' />
                  </Button>
                  {canDelete && (
                    <Button
                      variant='ghost'
                      size='sm'
                      onClick={() => handleDelete(attachment.id)}
                      title='Delete'
                      className='text-red-600 hover:text-red-700'
                    >
                      <Trash2 className='h-4 w-4' />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>

      {/* Image Preview Dialog */}
      {previewAttachment && fileUploadService.isImageUrl(previewAttachment.fileUrl) && (
        <Dialog open={!!previewAttachment} onOpenChange={() => setPreviewAttachment(null)}>
          <DialogContent className='max-w-4xl'>
            <DialogHeader>
              <DialogTitle>{previewAttachment.fileName}</DialogTitle>
            </DialogHeader>
            <div className='flex justify-center'>
              <img
                src={previewAttachment.fileUrl}
                alt={previewAttachment.fileName}
                className='max-w-full max-h-[70vh] object-contain rounded-lg'
              />
            </div>
            <div className='flex justify-center gap-2 mt-4'>
              <Button variant='outline' onClick={() => handleDownload(previewAttachment)}>
                <Download className='h-4 w-4 mr-2' />
                Download
              </Button>
              <Button
                variant='outline'
                onClick={() => window.open(previewAttachment.fileUrl, '_blank')}
              >
                <Eye className='h-4 w-4 mr-2' />
                Open in New Tab
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </Card>
  );
};

export default RequestAttachments;
