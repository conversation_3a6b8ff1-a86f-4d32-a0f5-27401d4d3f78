import { beforeEach, describe, expect, it, vi } from 'vitest';
import { FirebaseService } from '../firebaseService';
import { ErrorCode } from '../../types/enums';
import type { DocumentData } from 'firebase/firestore';
import * as firestore from 'firebase/firestore';

// Mock Firebase Firestore
vi.mock('firebase/firestore', async () => {
  const actual = await vi.importActual('firebase/firestore');
  return {
    ...actual,
    getDoc: vi.fn(),
    getDocs: vi.fn(),
    addDoc: vi.fn(),
    updateDoc: vi.fn(),
    deleteDoc: vi.fn(),
    doc: vi.fn(),
    collection: vi.fn(),
    query: vi.fn(),
    where: vi.fn(),
    orderBy: vi.fn(),
    limit: vi.fn(),
    startAfter: vi.fn(),
    serverTimestamp: vi.fn(() => ({ seconds: Date.now() / 1000, nanoseconds: 0 })),
  };
});

vi.mock('../config/firebase', () => ({
  db: {},
}));

interface TestDocument extends DocumentData {
  id: string;
  name: string;
  email: string;
  created_at?: any;
  updated_at?: any;
}

describe('FirebaseService', () => {
  let service: FirebaseService<TestDocument>;
  let mockGetDoc: any;
  let mockGetDocs: any;
  let mockAddDoc: any;
  let mockUpdateDoc: any;
  let mockDeleteDoc: any;
  let mockDoc: any;
  let mockCollection: any;
  let mockQuery: any;

  beforeEach(() => {
    service = new FirebaseService<TestDocument>('test-collection');

    // Get the mocked functions
    mockGetDoc = vi.mocked(firestore.getDoc);
    mockGetDocs = vi.mocked(firestore.getDocs);
    mockAddDoc = vi.mocked(firestore.addDoc);
    mockUpdateDoc = vi.mocked(firestore.updateDoc);
    mockDeleteDoc = vi.mocked(firestore.deleteDoc);
    mockDoc = vi.mocked(firestore.doc);
    mockCollection = vi.mocked(firestore.collection);
    mockQuery = vi.mocked(firestore.query);

    vi.clearAllMocks();
  });

  describe('constructor', () => {
    it('should initialize with default config', () => {
      const defaultService = new FirebaseService('test');
      expect(defaultService).toBeDefined();
    });

    it('should initialize with custom config', () => {
      const customService = new FirebaseService('test', {
        enableLogging: false,
        enableRetry: false,
        maxRetries: 1,
        retryDelay: 500,
      });
      expect(customService).toBeDefined();
    });
  });

  describe('getById', () => {
    it('should return document when it exists', async () => {
      const mockDocData = { name: 'Test User', email: '<EMAIL>' };
      const mockDocSnap = {
        exists: () => true,
        id: 'test-id',
        data: () => mockDocData,
      };
      
      mockDoc.mockReturnValue('mock-doc-ref');
      mockGetDoc.mockResolvedValue(mockDocSnap);

      const result = await service.getById('test-id');

      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        id: 'test-id',
        ...mockDocData,
      });
    });

    it('should return error when document does not exist', async () => {
      const mockDocSnap = {
        exists: () => false,
      };
      
      mockDoc.mockReturnValue('mock-doc-ref');
      mockGetDoc.mockResolvedValue(mockDocSnap);

      const result = await service.getById('test-id');

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe(ErrorCode.BUSINESS_RESOURCE_NOT_FOUND);
    });

    it('should return error for invalid ID', async () => {
      const result = await service.getById('');

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe(ErrorCode.VALIDATION_REQUIRED_FIELD);
    });

    it('should handle Firebase errors', async () => {
      mockDoc.mockReturnValue('mock-doc-ref');
      mockGetDoc.mockRejectedValue(new Error('Firebase error'));

      const result = await service.getById('test-id');

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe(ErrorCode.NETWORK_SERVER_ERROR);
    });
  });

  describe('getAll', () => {
    it('should return all documents', async () => {
      const mockDocs = [
        { id: 'doc1', data: () => ({ name: 'User 1' }) },
        { id: 'doc2', data: () => ({ name: 'User 2' }) },
      ];
      const mockQuerySnapshot = {
        docs: mockDocs,
      };

      mockCollection.mockReturnValue('mock-collection');
      mockQuery.mockReturnValue('mock-query');
      mockGetDocs.mockResolvedValue(mockQuerySnapshot);

      const result = await service.getAll();

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(2);
      expect(result.data?.[0]).toEqual({ id: 'doc1', name: 'User 1' });
    });

    it('should apply query constraints', async () => {
      const mockQuerySnapshot = { docs: [] };
      
      mockCollection.mockReturnValue('mock-collection');
      mockQuery.mockReturnValue('mock-query');
      mockGetDocs.mockResolvedValue(mockQuerySnapshot);

      const options = {
        where: [{ field: 'status', operator: '==', value: 'active' }],
        orderBy: { field: 'created_at', direction: 'desc' as const },
        limit: 10,
      };

      const result = await service.getAll(options);

      expect(result.success).toBe(true);
      expect(mockQuery).toHaveBeenCalled();
    });

    it('should handle Firebase errors', async () => {
      mockCollection.mockReturnValue('mock-collection');
      mockQuery.mockReturnValue('mock-query');
      mockGetDocs.mockRejectedValue(new Error('Firebase error'));

      const result = await service.getAll();

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe(ErrorCode.NETWORK_SERVER_ERROR);
    });
  });

  describe('create', () => {
    it('should create document successfully', async () => {
      const inputData = { name: 'New User', email: '<EMAIL>' };
      const mockDocRef = { id: 'new-doc-id' };
      const mockDocSnap = {
        exists: () => true,
        id: 'new-doc-id',
        data: () => ({ ...inputData, created_at: {}, updated_at: {} }),
      };

      mockCollection.mockReturnValue('mock-collection');
      mockAddDoc.mockResolvedValue(mockDocRef);
      mockDoc.mockReturnValue('mock-doc-ref');
      mockGetDoc.mockResolvedValue(mockDocSnap);

      const result = await service.create(inputData);

      expect(result.success).toBe(true);
      expect(result.data?.id).toBe('new-doc-id');
      expect(result.data?.name).toBe('New User');
    });

    it('should return error for invalid data', async () => {
      const result = await service.create(null as any);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe(ErrorCode.VALIDATION_REQUIRED_FIELD);
    });

    it('should handle Firebase errors', async () => {
      const inputData = { name: 'New User', email: '<EMAIL>' };
      
      mockCollection.mockReturnValue('mock-collection');
      mockAddDoc.mockRejectedValue(new Error('Firebase error'));

      const result = await service.create(inputData);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe(ErrorCode.NETWORK_SERVER_ERROR);
    });
  });

  describe('update', () => {
    it('should update document successfully', async () => {
      const updateData = { name: 'Updated User' };
      const mockDocSnap = {
        exists: () => true,
        id: 'test-id',
        data: () => ({ name: 'Updated User', email: '<EMAIL>', updated_at: {} }),
      };

      mockDoc.mockReturnValue('mock-doc-ref');
      mockUpdateDoc.mockResolvedValue(undefined);
      mockGetDoc.mockResolvedValue(mockDocSnap);

      const result = await service.update('test-id', updateData);

      expect(result.success).toBe(true);
      expect(result.data?.name).toBe('Updated User');
    });

    it('should return error for invalid ID', async () => {
      const result = await service.update('', { name: 'Updated' });

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe(ErrorCode.VALIDATION_REQUIRED_FIELD);
    });

    it('should return error for empty update data', async () => {
      const result = await service.update('test-id', {});

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe(ErrorCode.VALIDATION_REQUIRED_FIELD);
    });

    it('should handle Firebase errors', async () => {
      mockDoc.mockReturnValue('mock-doc-ref');
      mockUpdateDoc.mockRejectedValue(new Error('Firebase error'));

      const result = await service.update('test-id', { name: 'Updated' });

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe(ErrorCode.NETWORK_SERVER_ERROR);
    });
  });

  describe('delete', () => {
    it('should delete document successfully', async () => {
      mockDoc.mockReturnValue('mock-doc-ref');
      mockDeleteDoc.mockResolvedValue(undefined);

      const result = await service.delete('test-id');

      expect(result.success).toBe(true);
      expect(result.data).toBe(true);
    });

    it('should return error for invalid ID', async () => {
      const result = await service.delete('');

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe(ErrorCode.VALIDATION_REQUIRED_FIELD);
    });

    it('should handle Firebase errors', async () => {
      mockDoc.mockReturnValue('mock-doc-ref');
      mockDeleteDoc.mockRejectedValue(new Error('Firebase error'));

      const result = await service.delete('test-id');

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe(ErrorCode.NETWORK_SERVER_ERROR);
    });
  });

  describe('softDelete', () => {
    it('should perform soft delete by updating is_visible field', async () => {
      const mockDocSnap = {
        exists: () => true,
        id: 'test-id',
        data: () => ({ name: 'Test User', is_visible: false, updated_at: {} }),
      };

      mockDoc.mockReturnValue('mock-doc-ref');
      mockUpdateDoc.mockResolvedValue(undefined);
      mockGetDoc.mockResolvedValue(mockDocSnap);

      const result = await service.softDelete('test-id');

      expect(result.success).toBe(true);
      expect(mockUpdateDoc).toHaveBeenCalledWith(
        'mock-doc-ref',
        expect.objectContaining({ is_visible: false })
      );
    });
  });

  describe('query', () => {
    it('should execute custom query successfully', async () => {
      const mockDocs = [
        { id: 'doc1', data: () => ({ name: 'User 1' }) },
      ];
      const mockQuerySnapshot = { docs: mockDocs };

      mockQuery.mockReturnValue('mock-query');
      mockGetDocs.mockResolvedValue(mockQuerySnapshot);

      const constraints: any[] = [];
      const result = await service.query(constraints);

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(1);
    });

    it('should handle query errors', async () => {
      mockQuery.mockReturnValue('mock-query');
      mockGetDocs.mockRejectedValue(new Error('Query error'));

      const result = await service.query([]);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe(ErrorCode.NETWORK_SERVER_ERROR);
    });
  });

  describe('getPaginated', () => {
    it('should return paginated results', async () => {
      const mockDocs = [
        { id: 'doc1', data: () => ({ name: 'User 1' }) },
        { id: 'doc2', data: () => ({ name: 'User 2' }) },
      ];
      const mockQuerySnapshot = { docs: mockDocs };

      mockCollection.mockReturnValue('mock-collection');
      mockQuery.mockReturnValue('mock-query');
      mockGetDocs.mockResolvedValue(mockQuerySnapshot);

      const result = await service.getPaginated({ page: 1, limit: 10 });

      expect(result.success).toBe(true);
      expect(result.data?.data).toHaveLength(2);
      expect(result.data?.pagination.page).toBe(1);
      expect(result.data?.pagination.limit).toBe(10);
    });

    it('should validate pagination parameters', async () => {
      const result = await service.getPaginated({ page: 0, limit: 101 });

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe(ErrorCode.VALIDATION_OUT_OF_RANGE);
    });
  });
});
