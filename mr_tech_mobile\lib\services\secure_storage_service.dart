import 'package:flutter_secure_storage/flutter_secure_storage.dart';

/// Service for securely storing sensitive data like tokens and API keys
class SecureStorageService {
  // Singleton instance
  static final SecureStorageService _instance = SecureStorageService._internal();
  factory SecureStorageService() => _instance;
  SecureStorageService._internal();
  
  // Create storage instance with secure options
  final _storage = const FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      resetOnError: true,
      sharedPreferencesName: 'mr_tech_secure_prefs',
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock,
      synchronizable: false,
    ),
  );
  
  // Keys for stored data
  static const String _authTokenKey = 'auth_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _fcmTokenKey = 'fcm_token';
  static const String _userIdKey = 'user_id';
  static const String _lastLoginKey = 'last_login';
  
  // Store auth token securely
  Future<void> storeAuthToken(String token) async {
    await _storage.write(key: _authTokenKey, value: token);
  }
  
  // Get auth token
  Future<String?> getAuthToken() async {
    return await _storage.read(key: _authTokenKey);
  }
  
  // Store refresh token securely
  Future<void> storeRefreshToken(String token) async {
    await _storage.write(key: _refreshTokenKey, value: token);
  }
  
  // Get refresh token
  Future<String?> getRefreshToken() async {
    return await _storage.read(key: _refreshTokenKey);
  }
  
  // Store FCM token securely
  Future<void> storeFCMToken(String token) async {
    await _storage.write(key: _fcmTokenKey, value: token);
  }
  
  // Get FCM token
  Future<String?> getFCMToken() async {
    return await _storage.read(key: _fcmTokenKey);
  }
  
  // Store user ID securely
  Future<void> storeUserId(String userId) async {
    await _storage.write(key: _userIdKey, value: userId);
  }
  
  // Get user ID
  Future<String?> getUserId() async {
    return await _storage.read(key: _userIdKey);
  }
  
  // Store last login timestamp
  Future<void> storeLastLogin(String timestamp) async {
    await _storage.write(key: _lastLoginKey, value: timestamp);
  }
  
  // Get last login timestamp
  Future<String?> getLastLogin() async {
    return await _storage.read(key: _lastLoginKey);
  }
  
  // Delete all stored data (for logout)
  Future<void> deleteAllData() async {
    await _storage.deleteAll();
  }
  
  // Delete specific data
  Future<void> deleteData(String key) async {
    await _storage.delete(key: key);
  }
  
  // Store custom data securely
  Future<void> storeData(String key, String value) async {
    await _storage.write(key: key, value: value);
  }
  
  // Get custom data
  Future<String?> getData(String key) async {
    return await _storage.read(key: key);
  }
} 