import React, { useCallback, useRef, useState } from 'react';
import { AlertCircle, CheckCircle, File, Image, Loader2, Upload, X } from 'lucide-react';
import { Button } from './button';
import { Progress } from './progress';
import { cn } from '../../lib/utils';
import fileUploadService, { type FileUploadProgress } from '../../services/fileUploadService.js';

export interface FileUploadProps {
  onFileSelect?: (file: File) => void;
  onFileUpload?: (url: string, file: File) => void;
  onFileRemove?: () => void;
  accept?: string;
  maxSize?: number;
  multiple?: boolean;
  disabled?: boolean;
  className?: string;
  requestId?: string;
  uploadOnSelect?: boolean;
  showPreview?: boolean;
  variant?: 'default' | 'compact' | 'dropzone';
}

export interface FileUploadState {
  selectedFile: File | null;
  uploadProgress: FileUploadProgress | null;
  isUploading: boolean;
  uploadedUrl: string | null;
  error: string | null;
}

const FileUpload: React.FC<FileUploadProps> = ({
  onFileSelect,
  onFileUpload,
  onFileRemove,
  accept = fileUploadService.getAllowedFileTypes().join(','),
  maxSize = fileUploadService.getMaxFileSize(),
  multiple = false,
  disabled = false,
  className,
  requestId,
  uploadOnSelect = false,
  showPreview = true,
  variant = 'default',
}) => {
  const [state, setState] = useState<FileUploadState>({
    selectedFile: null,
    uploadProgress: null,
    isUploading: false,
    uploadedUrl: null,
    error: null,
  });

  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragOver, setIsDragOver] = useState(false);

  const handleFileSelect = useCallback(
    async (file: File) => {
      // Validate file
      if (file.size > maxSize) {
        setState((prev) => ({
          ...prev,
          error: `File size must be less than ${fileUploadService.formatFileSize(maxSize)}`,
        }));
        return;
      }

      setState((prev) => ({
        ...prev,
        selectedFile: file,
        error: null,
        uploadedUrl: null,
      }));

      onFileSelect?.(file);

      // Auto-upload if enabled and requestId is provided
      if (uploadOnSelect && requestId) {
        await handleUpload(file);
      }
    },
    [maxSize, onFileSelect, uploadOnSelect, requestId],
  );

  const handleUpload = async (file?: File) => {
    const fileToUpload = file || state.selectedFile;
    if (!fileToUpload || !requestId) return;

    setState((prev) => ({ ...prev, isUploading: true, error: null }));

    try {
      const result = await fileUploadService.uploadChatFile(fileToUpload, requestId, (progress) =>
        setState((prev) => ({ ...prev, uploadProgress: progress })),
      );

      if (result.success && result.url) {
        setState((prev) => ({
          ...prev,
          uploadedUrl: result.url!,
          isUploading: false,
          uploadProgress: null,
        }));
        onFileUpload?.(result.url, fileToUpload);
      } else {
        setState((prev) => ({
          ...prev,
          error: result.error || 'Upload failed',
          isUploading: false,
          uploadProgress: null,
        }));
      }
    } catch (error) {
      setState((prev) => ({
        ...prev,
        error: 'Upload failed',
        isUploading: false,
        uploadProgress: null,
      }));
    }
  };

  const handleRemove = () => {
    setState({
      selectedFile: null,
      uploadProgress: null,
      isUploading: false,
      uploadedUrl: null,
      error: null,
    });
    onFileRemove?.();
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    if (disabled) return;

    const files = Array.from(e.dataTransfer.files);
    const file = files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const openFileDialog = () => {
    if (!disabled) {
      fileInputRef.current?.click();
    }
  };

  const isImage = state.selectedFile?.type.startsWith('image/');

  if (variant === 'compact') {
    return (
      <div className={cn('flex items-center gap-2', className)}>
        <Button
          type='button'
          variant='outline'
          size='sm'
          onClick={openFileDialog}
          disabled={disabled || state.isUploading}
        >
          {state.isUploading ? (
            <Loader2 className='h-4 w-4 animate-spin' />
          ) : (
            <Upload className='h-4 w-4' />
          )}
        </Button>

        {state.selectedFile && (
          <div className='flex items-center gap-1 text-sm'>
            <span className='truncate max-w-32'>{state.selectedFile.name}</span>
            <Button
              type='button'
              variant='ghost'
              size='sm'
              onClick={handleRemove}
              className='h-6 w-6 p-0'
            >
              <X className='h-3 w-3' />
            </Button>
          </div>
        )}

        <input
          ref={fileInputRef}
          type='file'
          accept={accept}
          multiple={multiple}
          onChange={handleInputChange}
          className='hidden'
        />
      </div>
    );
  }

  if (variant === 'dropzone') {
    return (
      <div className={cn('space-y-4', className)}>
        <div
          className={cn(
            'border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors',
            isDragOver ? 'border-blue-500 bg-blue-50' : 'border-gray-300',
            disabled && 'opacity-50 cursor-not-allowed',
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={openFileDialog}
        >
          <Upload className='h-8 w-8 mx-auto mb-2 text-gray-400' />
          <p className='text-sm text-gray-600'>Drop files here or click to browse</p>
          <p className='text-xs text-gray-400 mt-1'>
            Max size: {fileUploadService.formatFileSize(maxSize)}
          </p>
        </div>

        {/* File Preview */}
        {state.selectedFile && showPreview && (
          <div className='border rounded-lg p-4'>
            <div className='flex items-center justify-between mb-2'>
              <div className='flex items-center gap-2'>
                {isImage ? (
                  <Image className='h-5 w-5 text-blue-500' />
                ) : (
                  <File className='h-5 w-5 text-gray-500' />
                )}
                <span className='text-sm font-medium'>{state.selectedFile.name}</span>
                <span className='text-xs text-gray-500'>
                  ({fileUploadService.formatFileSize(state.selectedFile.size)})
                </span>
              </div>
              <Button
                type='button'
                variant='ghost'
                size='sm'
                onClick={handleRemove}
                className='h-6 w-6 p-0'
              >
                <X className='h-4 w-4' />
              </Button>
            </div>

            {/* Upload Progress */}
            {state.uploadProgress && state.uploadProgress.status === 'uploading' && (
              <div className='space-y-2'>
                <Progress value={state.uploadProgress.progress} className='h-2' />
                <p className='text-xs text-gray-500'>
                  Uploading... {Math.round(state.uploadProgress.progress)}%
                </p>
              </div>
            )}

            {/* Upload Status */}
            {state.uploadedUrl && (
              <div className='flex items-center gap-2 text-green-600'>
                <CheckCircle className='h-4 w-4' />
                <span className='text-sm'>Upload complete</span>
              </div>
            )}

            {state.error && (
              <div className='flex items-center gap-2 text-red-600'>
                <AlertCircle className='h-4 w-4' />
                <span className='text-sm'>{state.error}</span>
              </div>
            )}

            {/* Manual Upload Button */}
            {!uploadOnSelect && !state.uploadedUrl && !state.isUploading && requestId && (
              <Button type='button' onClick={() => handleUpload()} size='sm' className='mt-2'>
                Upload File
              </Button>
            )}
          </div>
        )}

        <input
          ref={fileInputRef}
          type='file'
          accept={accept}
          multiple={multiple}
          onChange={handleInputChange}
          className='hidden'
        />
      </div>
    );
  }

  // Default variant
  return (
    <div className={cn('space-y-3', className)}>
      <Button
        type='button'
        variant='outline'
        onClick={openFileDialog}
        disabled={disabled || state.isUploading}
        className='w-full'
      >
        {state.isUploading ? (
          <>
            <Loader2 className='h-4 w-4 mr-2 animate-spin' />
            Uploading...
          </>
        ) : (
          <>
            <Upload className='h-4 w-4 mr-2' />
            Choose File
          </>
        )}
      </Button>

      {/* File Preview */}
      {state.selectedFile && showPreview && (
        <div className='border rounded-lg p-3'>
          <div className='flex items-center justify-between'>
            <div className='flex items-center gap-2'>
              {isImage ? (
                <Image className='h-5 w-5 text-blue-500' />
              ) : (
                <File className='h-5 w-5 text-gray-500' />
              )}
              <div>
                <p className='text-sm font-medium'>{state.selectedFile.name}</p>
                <p className='text-xs text-gray-500'>
                  {fileUploadService.formatFileSize(state.selectedFile.size)}
                </p>
              </div>
            </div>
            <Button
              type='button'
              variant='ghost'
              size='sm'
              onClick={handleRemove}
              className='h-6 w-6 p-0'
            >
              <X className='h-4 w-4' />
            </Button>
          </div>

          {/* Upload Progress */}
          {state.uploadProgress && state.uploadProgress.status === 'uploading' && (
            <div className='mt-3 space-y-2'>
              <Progress value={state.uploadProgress.progress} className='h-2' />
              <p className='text-xs text-gray-500'>
                Uploading... {Math.round(state.uploadProgress.progress)}%
              </p>
            </div>
          )}

          {/* Upload Status */}
          {state.uploadedUrl && (
            <div className='mt-2 flex items-center gap-2 text-green-600'>
              <CheckCircle className='h-4 w-4' />
              <span className='text-sm'>Upload complete</span>
            </div>
          )}

          {state.error && (
            <div className='mt-2 flex items-center gap-2 text-red-600'>
              <AlertCircle className='h-4 w-4' />
              <span className='text-sm'>{state.error}</span>
            </div>
          )}
        </div>
      )}

      <input
        ref={fileInputRef}
        type='file'
        accept={accept}
        multiple={multiple}
        onChange={handleInputChange}
        className='hidden'
      />
    </div>
  );
};

export default FileUpload;
