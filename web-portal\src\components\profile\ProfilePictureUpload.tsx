import React, { useCallback, useRef, useState } from 'react';
import { AlertCircle, Camera, CheckCircle, Loader2, Upload, X } from 'lucide-react';
import { But<PERSON> } from '../../components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '../../components/ui/avatar';
import { Alert, AlertDescription } from '../../components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../../components/ui/dialog';
import { profileService } from '../../services/profileService';

interface ProfilePictureUploadProps {
  currentImageUrl: string | null;
  onImageUpdate: (newUrl: string | null) => void;
  userId: string;
  userName?: string;
}

interface UploadState {
  isUploading: boolean;
  uploadProgress: number;
  error: string | null;
  success: boolean;
  previewUrl: string | null;
}

const ProfilePictureUpload: React.FC<ProfilePictureUploadProps> = ({
  currentImageUrl,
  onImageUpdate,
  userId,
  userName = 'User',
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploadState, setUploadState] = useState<UploadState>({
    isUploading: false,
    uploadProgress: 0,
    error: null,
    success: false,
    previewUrl: null,
  });
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const resetUploadState = () => {
    setUploadState({
      isUploading: false,
      uploadProgress: 0,
      error: null,
      success: false,
      previewUrl: null,
    });
  };

  const validateFile = (file: File): { valid: boolean; error?: string } => {
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: 'Please select a valid image file (JPEG, PNG, GIF, or WebP)',
      };
    }

    // Check file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return {
        valid: false,
        error: 'File size must be less than 5MB',
      };
    }

    // Check image dimensions (optional - can be implemented with FileReader)
    return { valid: true };
  };

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    resetUploadState();

    // Validate file
    const validation = validateFile(file);
    if (!validation.valid) {
      setUploadState((prev) => ({ ...prev, error: validation.error || 'Invalid file' }));
      return;
    }

    // Create preview URL
    const previewUrl = URL.createObjectURL(file);
    setUploadState((prev) => ({ ...prev, previewUrl }));
    setIsDialogOpen(true);
  }, []);

  const handleUpload = async () => {
    if (!uploadState.previewUrl || !fileInputRef.current?.files?.[0]) return;

    const file = fileInputRef.current.files[0];

    try {
      setUploadState((prev) => ({ ...prev, isUploading: true, error: null }));

      // Upload to Firebase Storage
      const downloadUrl = await profileService.uploadProfilePicture(file, userId, (progress) => {
        setUploadState((prev) => ({ ...prev, uploadProgress: progress }));
      });

      // Update profile picture URL in the appropriate collection
      await profileService.updateProfilePicture(userId, downloadUrl);

      // Update local state
      onImageUpdate(downloadUrl);

      setUploadState((prev) => ({
        ...prev,
        success: true,
        isUploading: false,
        uploadProgress: 100,
      }));

      // Close dialog after success
      setTimeout(() => {
        setIsDialogOpen(false);
        resetUploadState();
      }, 1500);
    } catch (error) {
      console.error('Error uploading profile picture:', error);
      setUploadState((prev) => ({
        ...prev,
        isUploading: false,
        error: 'Failed to upload image. Please try again.',
      }));
    }
  };

  const handleRemove = async () => {
    try {
      setUploadState((prev) => ({ ...prev, isUploading: true, error: null }));

      // Remove from Firebase Storage if there's a current image
      if (currentImageUrl) {
        await profileService.deleteProfilePicture(currentImageUrl);
      }

      // Update profile picture URL to null in the appropriate collection
      await profileService.updateProfilePicture(userId, null);

      // Update local state
      onImageUpdate(null);

      setUploadState((prev) => ({
        ...prev,
        success: true,
        isUploading: false,
      }));

      // Close dialog after success
      setTimeout(() => {
        setIsDialogOpen(false);
        resetUploadState();
      }, 1500);
    } catch (error) {
      console.error('Error removing profile picture:', error);
      setUploadState((prev) => ({
        ...prev,
        isUploading: false,
        error: 'Failed to remove image. Please try again.',
      }));
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((part) => part.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className='flex flex-col items-center space-y-4'>
      {/* Current Profile Picture */}
      <div className='relative'>
        <Avatar className='h-24 w-24'>
          <AvatarImage src={currentImageUrl || undefined} alt={userName} />
          <AvatarFallback className='text-lg'>{getInitials(userName)}</AvatarFallback>
        </Avatar>

        {/* Upload Button Overlay */}
        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button
              size='sm'
              className='absolute -bottom-2 -right-2 h-8 w-8 rounded-full p-0'
              onClick={() => fileInputRef.current?.click()}
            >
              <Camera className='h-4 w-4' />
            </Button>
          </DialogTrigger>

          <DialogContent className='sm:max-w-md'>
            <DialogHeader>
              <DialogTitle>Update Profile Picture</DialogTitle>
            </DialogHeader>

            <div className='space-y-4'>
              {/* Preview */}
              {uploadState.previewUrl && (
                <div className='flex justify-center'>
                  <Avatar className='h-32 w-32'>
                    <AvatarImage src={uploadState.previewUrl} alt='Preview' />
                    <AvatarFallback>{getInitials(userName)}</AvatarFallback>
                  </Avatar>
                </div>
              )}

              {/* Upload Progress */}
              {uploadState.isUploading && (
                <div className='space-y-2'>
                  <div className='flex items-center justify-between text-sm'>
                    <span>Uploading...</span>
                    <span>{Math.round(uploadState.uploadProgress)}%</span>
                  </div>
                  <div className='w-full bg-gray-200 rounded-full h-2'>
                    <div
                      className='bg-primary h-2 rounded-full transition-all duration-300'
                      style={{ width: `${uploadState.uploadProgress}%` }}
                    />
                  </div>
                </div>
              )}

              {/* Error Message */}
              {uploadState.error && (
                <Alert variant='destructive'>
                  <AlertCircle className='h-4 w-4' />
                  <AlertDescription>{uploadState.error}</AlertDescription>
                </Alert>
              )}

              {/* Success Message */}
              {uploadState.success && (
                <Alert className='border-green-200 bg-green-50'>
                  <CheckCircle className='h-4 w-4 text-green-600' />
                  <AlertDescription className='text-green-800'>
                    Profile picture updated successfully!
                  </AlertDescription>
                </Alert>
              )}

              {/* Action Buttons */}
              {!uploadState.success && (
                <div className='flex justify-between gap-2'>
                  <Button
                    variant='outline'
                    onClick={() => fileInputRef.current?.click()}
                    disabled={uploadState.isUploading}
                  >
                    <Upload className='h-4 w-4 mr-2' />
                    Choose File
                  </Button>

                  {uploadState.previewUrl && (
                    <Button onClick={handleUpload} disabled={uploadState.isUploading}>
                      {uploadState.isUploading ? (
                        <>
                          <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                          Uploading...
                        </>
                      ) : (
                        <>
                          <CheckCircle className='h-4 w-4 mr-2' />
                          Upload
                        </>
                      )}
                    </Button>
                  )}

                  {currentImageUrl && (
                    <Button
                      variant='destructive'
                      onClick={handleRemove}
                      disabled={uploadState.isUploading}
                    >
                      <X className='h-4 w-4 mr-2' />
                      Remove
                    </Button>
                  )}
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Upload Instructions */}
      <div className='text-center'>
        <p className='text-sm text-muted-foreground'>
          Click the camera icon to update your profile picture
        </p>
        <p className='text-xs text-muted-foreground mt-1'>
          Supported formats: JPEG, PNG, GIF, WebP (max 5MB)
        </p>
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type='file'
        accept='image/jpeg,image/png,image/gif,image/webp'
        onChange={handleFileSelect}
        className='hidden'
      />
    </div>
  );
};

export default ProfilePictureUpload;
