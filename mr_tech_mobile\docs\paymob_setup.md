# Paymob Integration Setup Guide

This guide outlines how to set up and configure the Paymob payment gateway in the Mr.Tech mobile app.

## Prerequisites

1. A Paymob merchant account
2. Paymob API credentials:
   - API Key
   - Integration ID
   - iFrame ID

## Installation

The Paymob Flutter SDK is already added to the project dependencies in `pubspec.yaml`:

```yaml
dependencies:
  paymob_flutter: ^0.1.0
```

## Configuration

### 1. Android Configuration

Open the `android/app/build.gradle` file and ensure you have added the following:

```gradle
android {
    defaultConfig {
        // Other configurations...
        minSdkVersion 19
    }
    
    buildTypes {
        release {
            // Other configurations...
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}
```

Create a file named `proguard-rules.pro` in the `android/app` directory with the following content:

```
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.** { *; }
-keep class io.flutter.util.** { *; }
-keep class io.flutter.view.** { *; }
-keep class io.flutter.** { *; }
-keep class io.flutter.plugins.** { *; }
-keep class androidx.** { *; }
-keep class com.google.android.** { *; }
```

### 2. Setup Environment Variables

Update the `PaymentService` class in `lib/services/payment_service.dart` with your actual Paymob credentials:

```dart
// Paymob API credentials - Replace with real values in production
static const String _apiKey = 'YOUR_PAYMOB_API_KEY';
static const String _integrationId = 'YOUR_PAYMOB_INTEGRATION_ID';
static const String _iframeId = 'YOUR_PAYMOB_IFRAME_ID';
```

For security, consider using environment variables or a secure configuration method in production.

## Usage

The payment flow is handled in the `PaymentScreen` which collects billing information and then calls the `processPaymentWithSDK` method from the `PaymentService`.

```dart
final success = await _paymentService.processPaymentWithSDK(
  context: context,
  request: widget.request,
  billingData: billingData,
);
```

On successful payment, the user is redirected to the `PaymentSuccessScreen`.

## Testing

1. Use Paymob test cards for development:
   - Card Number: ****************
   - Expiry Date: Any future date
   - CVV: 123

2. Test mode will be enabled automatically when using test credentials.

## Troubleshooting

1. If you encounter "No Implementation Found" errors:
   - Ensure the Paymob Flutter SDK dependency is correctly added
   - Run `flutter clean` and `flutter pub get`

2. If payment fails:
   - Check the console logs for detailed error messages
   - Verify your API credentials are correct
   - Ensure the billing data format is correct

## Webhook Integration (Server-side)

For production use, set up webhooks in your Paymob merchant dashboard to receive payment notifications on your backend:

1. Go to your Paymob dashboard
2. Navigate to Integration Settings
3. Add your backend webhook URL
4. Implement the webhook endpoint on your backend to validate and process payment notifications

## Resources

- [Paymob Flutter SDK Documentation](https://developers.paymob.com/egypt/mobile-sdks/mobile-sdks-v2/flutter-sdk-1)
- [Paymob API Documentation](https://developers.paymob.com/) 