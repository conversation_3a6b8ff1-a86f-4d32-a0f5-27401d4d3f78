import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// Specific migration script for existing services collection
/// This adds snake_case fields alongside existing camelCase fields
class ServicesMigrationScript {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Migrate all services to include snake_case fields
  /// This is safe - it only ADDS fields, never removes existing ones
  static Future<void> migrateServicesCollection() async {
    try {
      debugPrint('🔄 Starting services migration...');

      final servicesCollection = _firestore.collection('services');
      final querySnapshot = await servicesCollection.get();

      debugPrint('📊 Found ${querySnapshot.docs.length} services to migrate');

      int migrated = 0;
      int alreadyMigrated = 0;
      int errors = 0;

      // Process each service document
      for (final doc in querySnapshot.docs) {
        try {
          final data = doc.data();
          final updates = <String, dynamic>{};

          // Check if migration is needed and prepare updates
          bool needsUpdate = false;

          // Add snake_case version of basePrice
          if (data.containsKey('basePrice') &&
              !data.containsKey('base_price')) {
            updates['base_price'] = data['basePrice'];
            needsUpdate = true;
          }

          // Add snake_case version of isActive
          if (data.containsKey('isActive') && !data.containsKey('is_active')) {
            updates['is_active'] = data['isActive'];
            needsUpdate = true;
          }

          // Add snake_case version of estimatedDuration
          if (data.containsKey('estimatedDuration') &&
              !data.containsKey('estimated_duration')) {
            updates['estimated_duration'] = data['estimatedDuration'];
            needsUpdate = true;
          }

          // Add snake_case version of imageUrl if it exists
          if (data.containsKey('imageUrl') && !data.containsKey('image_url')) {
            updates['image_url'] = data['imageUrl'];
            needsUpdate = true;
          }

          // Add created_at if missing
          if (!data.containsKey('created_at') &&
              !data.containsKey('createdAt')) {
            updates['created_at'] = FieldValue.serverTimestamp();
            needsUpdate = true;
          } else if (data.containsKey('createdAt') &&
              !data.containsKey('created_at')) {
            updates['created_at'] = data['createdAt'];
            needsUpdate = true;
          }

          // Add updated_at
          if (!data.containsKey('updated_at')) {
            updates['updated_at'] = FieldValue.serverTimestamp();
            needsUpdate = true;
          }

          // Perform update if needed
          if (needsUpdate) {
            await doc.reference.update(updates);
            migrated++;
            debugPrint(
              '✅ Migrated service: ${doc.id} (${data['name'] ?? 'Unknown'})',
            );

            // Log what was migrated
            final fieldsMigrated = updates.keys.join(', ');
            debugPrint('   Added fields: $fieldsMigrated');
          } else {
            alreadyMigrated++;
            debugPrint('⏭️ Service ${doc.id} already has snake_case fields');
          }
        } catch (e) {
          errors++;
          debugPrint('❌ Error migrating service ${doc.id}: $e');
        }
      }

      // Print summary
      debugPrint('');
      debugPrint('🎉 Services migration completed!');
      debugPrint('📊 Summary:');
      debugPrint('   - Total services: ${querySnapshot.docs.length}');
      debugPrint('   - Migrated: $migrated');
      debugPrint('   - Already migrated: $alreadyMigrated');
      debugPrint('   - Errors: $errors');
      debugPrint('');

      if (errors > 0) {
        debugPrint('⚠️ Some services had errors. Check logs above.');
      } else {
        debugPrint('✅ All services migrated successfully!');
      }
    } catch (e) {
      debugPrint('❌ Fatal error during services migration: $e');
      rethrow;
    }
  }

  /// Validate that all services have the required snake_case fields
  static Future<bool> validateServicesMigration() async {
    try {
      debugPrint('🔍 Validating services migration...');

      final servicesCollection = _firestore.collection('services');
      final querySnapshot = await servicesCollection.get();

      int valid = 0;
      int invalid = 0;
      final List<String> missingFields = [];

      for (final doc in querySnapshot.docs) {
        final data = doc.data();
        bool isValid = true;

        // Check required snake_case fields
        final requiredFields = [
          'base_price',
          'is_active',
          'estimated_duration',
          'created_at',
          'updated_at',
        ];

        for (final field in requiredFields) {
          if (!data.containsKey(field)) {
            isValid = false;
            missingFields.add('${doc.id}.$field');
          }
        }

        if (isValid) {
          valid++;
        } else {
          invalid++;
          debugPrint('❌ Service ${doc.id} missing required fields');
        }
      }

      debugPrint('');
      debugPrint('📊 Validation Results:');
      debugPrint('   - Valid services: $valid');
      debugPrint('   - Invalid services: $invalid');

      if (missingFields.isNotEmpty) {
        debugPrint('   - Missing fields: ${missingFields.join(', ')}');
      }

      final isAllValid = invalid == 0;
      debugPrint(
        isAllValid
            ? '✅ All services are valid!'
            : '❌ Some services need attention',
      );

      return isAllValid;
    } catch (e) {
      debugPrint('❌ Error during validation: $e');
      return false;
    }
  }

  /// Get migration status for services
  static Future<Map<String, dynamic>> getServicesMigrationStatus() async {
    try {
      final servicesCollection = _firestore.collection('services');
      final querySnapshot = await servicesCollection.get();

      int totalServices = querySnapshot.docs.length;
      int migratedServices = 0;
      int camelCaseOnlyServices = 0;

      for (final doc in querySnapshot.docs) {
        final data = doc.data();

        // Check if has snake_case fields
        final hasSnakeCase =
            data.containsKey('base_price') ||
            data.containsKey('is_active') ||
            data.containsKey('estimated_duration');

        if (hasSnakeCase) {
          migratedServices++;
        } else {
          camelCaseOnlyServices++;
        }
      }

      return {
        'total_services': totalServices,
        'migrated_services': migratedServices,
        'camel_case_only': camelCaseOnlyServices,
        'migration_percentage':
            totalServices > 0
                ? (migratedServices / totalServices * 100).round()
                : 0,
        'is_complete': camelCaseOnlyServices == 0,
      };
    } catch (e) {
      debugPrint('❌ Error getting migration status: $e');
      return {
        'error': e.toString(),
        'total_services': 0,
        'migrated_services': 0,
        'camel_case_only': 0,
        'migration_percentage': 0,
        'is_complete': false,
      };
    }
  }

  /// Print a detailed migration report
  static Future<void> printMigrationReport() async {
    final status = await getServicesMigrationStatus();

    debugPrint('');
    debugPrint('📊 SERVICES MIGRATION REPORT');
    debugPrint('═' * 40);
    debugPrint('Total Services: ${status['total_services']}');
    debugPrint('Migrated: ${status['migrated_services']}');
    debugPrint('CamelCase Only: ${status['camel_case_only']}');
    debugPrint('Progress: ${status['migration_percentage']}%');
    debugPrint('Complete: ${status['is_complete'] ? 'YES' : 'NO'}');

    if (status['error'] != null) {
      debugPrint('Error: ${status['error']}');
    }

    debugPrint('═' * 40);
    debugPrint('');
  }
}
