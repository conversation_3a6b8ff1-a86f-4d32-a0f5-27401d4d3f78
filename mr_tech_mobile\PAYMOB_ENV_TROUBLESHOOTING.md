# Paymob V2 Integration Troubleshooting Guide

This guide helps you resolve issues with the Paymob V2 payment gateway integration, specifically focusing on environment configuration problems.

## ⚠️ IMPORTANT: V2 API Migration

This app now uses **Paymob V2 Intention API** instead of the legacy V1 API. The V2 API provides better security, simpler integration, and more flexible payment methods.

### Key Changes in V2:
- **Secret Key** instead of API Key
- **Public Key** for checkout
- **Intention-based** flow instead of authentication tokens
- **Unified checkout** experience

## Common Issues

### 1. "Paymob V2 Secret Key missing or empty" Error

This error occurs when the app cannot find or read the Paymob V2 API credentials in your environment configuration.

#### Possible causes:

1. **Missing `.env` file**: The app cannot find the file that should contain your credentials.
2. **Empty or incorrect credentials in `.env`**: The file exists but doesn't have the correct values.
3. **File permission issues**: The app doesn't have permission to read the file.
4. **Environment loading issues**: The code that loads the file isn't working correctly.

## Solutions

### Verify the .env File Exists

Run the provided check script:

```bash
./check_env.sh
```

This script will:
- Check if the `.env` file exists
- Verify it contains the required Paymob credentials
- Offer to create a template file if needed
- Run a test to verify the app can access the file

### Create or Update Your .env File

If your `.env` file is missing or incorrect, create it in the project root directory with this V2 configuration:

```
# Paymob V2 API Configuration
# Get these credentials from your Paymob dashboard (owner ID: 131480)

# V2 Secret Key (replaces the old API key)
# Get this from: Paymob Dashboard > Developers > API Keys > Secret Key
PAYMOB_SECRET_KEY="egy_sk_test_your_secret_key_here"

# V2 Public Key (required for checkout)
# Get this from: Paymob Dashboard > Developers > API Keys > Public Key
PAYMOB_PUBLIC_KEY="egy_pk_test_your_public_key_here"

# Integration ID (same as V1 but specific to your account)
# Get this from: Paymob Dashboard > Developers > Payment Integrations
# IMPORTANT: Use YOUR integration ID, not 3282480 which belongs to another account
PAYMOB_INTEGRATION_ID="your_correct_integration_id_here"

# Optional: Environment mode
PAYMENT_MODE="paymob"
```

### Important Notes for V2:
1. **Replace test keys with live keys** for production (prefix: `egy_sk_live_` and `egy_pk_live_`)
2. **Use your own Integration ID** - the one in the old configuration (3282480) belongs to another account
3. **Get all credentials** from your Paymob dashboard for owner ID 131480

### Update the pubspec.yaml

Ensure the `.env` file is included in your Flutter assets:

```yaml
flutter:
  assets:
    - .env
    # other assets...
```

### Clean and Rebuild

After making changes to your environment configuration, clean and rebuild:

```bash
flutter clean
flutter pub get
flutter run
```

## Technical Details

### How Environment Variables Are Loaded

1. **Initialization**: During app startup in `main.dart`, we use `flutter_dotenv` to load the `.env` file.
2. **EnvConfig**: Our custom `EnvConfig` class uses this to provide a clean API for accessing environment variables.
3. **PaymentService**: The payment service loads Paymob credentials from EnvConfig during initialization.

### Token Caching System

To avoid repeatedly authenticating with Paymob, we cache authentication tokens:

1. When a token is generated, it's stored in both memory and shared preferences
2. The token has a validity period of 58 minutes (just under the 1-hour Paymob limit)
3. Before making API requests, the app checks if there's a valid cached token

### Location of the .env File

The `.env` file should be placed in the root directory of your project. The expected location is:

```
/path/to/mr_tech_mobile/.env
```

### Debugging Tools

To debug environment variable issues:

1. Run the `check_env.sh` script for a quick diagnostic
2. Use the `env_test.dart` tool (created by the script) to test file loading
3. Check the app logs for error messages like "Paymob API key missing or empty"

## Still Having Issues?

If you've followed all the steps above and still encounter issues:

1. Check if your Paymob API credentials are correct in your Paymob dashboard
2. Verify you have the required Flutter packages installed (`flutter_dotenv`, `shared_preferences`)
3. Try running the app in debug mode and check the console for detailed error messages
4. Look for file path issues - make sure the app is looking in the right place for your `.env` file 