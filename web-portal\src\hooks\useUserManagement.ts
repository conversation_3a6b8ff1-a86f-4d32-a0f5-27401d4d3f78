import { useCallback, useState } from 'react';
import type {
  CreateUserInput,
  UpdateUserInput,
  User,
  UserActivity,
  UserAuditLog,
  UserQueryOptions,
  UserRole,
  UserStats,
} from '../types/user';
import userService from '../services/userService';
import permissionService from '../services/permissionService';
import { useAuth } from '../contexts/AuthContext';

interface UseUserManagementReturn {
  // State
  users: User[];
  loading: boolean;
  error: string | null;

  // User CRUD operations
  createUser: (userData: CreateUserInput, createAuthAccount?: boolean) => Promise<User>;
  updateUser: (userId: string, updateData: UpdateUserInput) => Promise<User>;
  deleteUser: (userId: string, hardDelete?: boolean) => Promise<void>;
  getUserById: (userId: string) => Promise<User | null>;

  // User queries
  queryUsers: (options?: UserQueryOptions) => Promise<User[]>;
  searchUsers: (searchTerm: string, role?: UserRole) => Promise<User[]>;
  getUsersByRole: (role: UserRole) => Promise<User[]>;

  // User management actions
  suspendUser: (userId: string, reason?: string) => Promise<User>;
  activateUser: (userId: string) => Promise<User>;
  changeUserRole: (userId: string, newRole: UserRole) => Promise<User>;

  // Statistics and analytics
  getUserStats: () => Promise<UserStats>;
  getUserActivity: (userId: string, limit?: number) => Promise<UserActivity[]>;
  getUserAuditLogs: (userId: string, limit?: number) => Promise<UserAuditLog[]>;

  // Bulk operations
  bulkUpdateUsers: (userIds: string[], updateData: UpdateUserInput) => Promise<void>;

  // Permission checks
  canManageUser: (targetUser: User) => boolean;
  canPerformAction: (action: string, targetUser?: User) => boolean;

  // Utility functions
  refreshUsers: () => Promise<void>;
  clearError: () => void;
}

export const useUserManagement = (): UseUserManagementReturn => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user: currentUser } = useAuth();

  // Helper function to handle async operations
  const handleAsyncOperation = useCallback(
    async <T>(operation: () => Promise<T>, successMessage?: string): Promise<T> => {
      try {
        setLoading(true);
        setError(null);
        const result = await operation();
        if (successMessage) {
          console.warn(successMessage);
        }
        return result;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
        setError(errorMessage);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  // Create a new user
  const createUser = useCallback(
    async (userData: CreateUserInput, createAuthAccount = true): Promise<User> => {
      return handleAsyncOperation(
        () => userService.createUser(userData, createAuthAccount),
        'User created successfully',
      );
    },
    [handleAsyncOperation],
  );

  // Update user
  const updateUser = useCallback(
    async (userId: string, updateData: UpdateUserInput): Promise<User> => {
      return handleAsyncOperation(
        () => userService.updateUser(userId, updateData, currentUser?.uid),
        'User updated successfully',
      );
    },
    [handleAsyncOperation, currentUser],
  );

  // Delete user
  const deleteUser = useCallback(
    async (userId: string, hardDelete = false): Promise<void> => {
      return handleAsyncOperation(
        () => userService.deleteUser(userId, currentUser?.uid || '', hardDelete),
        'User deleted successfully',
      );
    },
    [handleAsyncOperation, currentUser],
  );

  // Get user by ID
  const getUserById = useCallback(
    async (userId: string): Promise<User | null> => {
      return handleAsyncOperation(() => userService.getUserById(userId));
    },
    [handleAsyncOperation],
  );

  // Query users with filters
  const queryUsers = useCallback(
    async (options?: UserQueryOptions): Promise<User[]> => {
      const result = await handleAsyncOperation(() => userService.queryUsers(options));
      setUsers(result);
      return result;
    },
    [handleAsyncOperation],
  );

  // Search users
  const searchUsers = useCallback(
    async (searchTerm: string, role?: UserRole): Promise<User[]> => {
      const result = await handleAsyncOperation(() => userService.searchUsers(searchTerm, role));
      setUsers(result);
      return result;
    },
    [handleAsyncOperation],
  );

  // Get users by role
  const getUsersByRole = useCallback(
    async (role: UserRole): Promise<User[]> => {
      const result = await handleAsyncOperation(() => userService.getUsersByRole(role));
      setUsers(result);
      return result;
    },
    [handleAsyncOperation],
  );

  // Suspend user
  const suspendUser = useCallback(
    async (userId: string, reason?: string): Promise<User> => {
      return handleAsyncOperation(
        () => userService.suspendUser(userId, currentUser?.uid || '', reason),
        'User suspended successfully',
      );
    },
    [handleAsyncOperation, currentUser],
  );

  // Activate user
  const activateUser = useCallback(
    async (userId: string): Promise<User> => {
      return handleAsyncOperation(
        () => userService.activateUser(userId, currentUser?.uid || ''),
        'User activated successfully',
      );
    },
    [handleAsyncOperation, currentUser],
  );

  // Change user role
  const changeUserRole = useCallback(
    async (userId: string, newRole: UserRole): Promise<User> => {
      return handleAsyncOperation(
        () => userService.changeUserRole(userId, newRole, currentUser?.uid || ''),
        'User role changed successfully',
      );
    },
    [handleAsyncOperation, currentUser],
  );

  // Get user statistics
  const getUserStats = useCallback(async (): Promise<UserStats> => {
    return handleAsyncOperation(() => userService.getUserStats());
  }, [handleAsyncOperation]);

  // Get user activity
  const getUserActivity = useCallback(
    async (userId: string, limit = 50): Promise<UserActivity[]> => {
      return handleAsyncOperation(() => userService.getUserActivity(userId, limit));
    },
    [handleAsyncOperation],
  );

  // Get user audit logs
  const getUserAuditLogs = useCallback(
    async (userId: string, limit = 50): Promise<UserAuditLog[]> => {
      return handleAsyncOperation(() => userService.getUserAuditLogs(userId, limit));
    },
    [handleAsyncOperation],
  );

  // Bulk update users
  const bulkUpdateUsers = useCallback(
    async (userIds: string[], updateData: UpdateUserInput): Promise<void> => {
      return handleAsyncOperation(
        () => userService.bulkUpdateUsers(userIds, updateData, currentUser?.uid || ''),
        'Users updated successfully',
      );
    },
    [handleAsyncOperation, currentUser],
  );

  // Check if current user can manage target user
  const canManageUser = useCallback(
    (targetUser: User): boolean => {
      if (!currentUser) return false;
      return permissionService.canManageUser(currentUser, targetUser);
    },
    [currentUser],
  );

  // Check if current user can perform specific action
  const canPerformAction = useCallback(
    (action: string, targetUser?: User): boolean => {
      if (!currentUser) return false;

      // Map actions to permissions
      const actionPermissions: Record<string, string[]> = {
        create_user: ['CREATE_USER'],
        edit_user: ['UPDATE_USER'],
        delete_user: ['DELETE_USER'],
        suspend_user: ['UPDATE_USER'],
        change_role: ['CHANGE_USER_ROLE'],
        view_users: ['VIEW_USERS'],
        view_audit_logs: ['VIEW_REPORTS'],
      };

      const requiredPermissions = actionPermissions[action];
      if (!requiredPermissions) return false;

      // Check if user has any of the required permissions
      return requiredPermissions.some((permission) =>
        permissionService.hasPermission(currentUser, permission as any),
      );
    },
    [currentUser],
  );

  // Refresh users list
  const refreshUsers = useCallback(async (): Promise<void> => {
    await queryUsers();
  }, [queryUsers]);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    // State
    users,
    loading,
    error,

    // User CRUD operations
    createUser,
    updateUser,
    deleteUser,
    getUserById,

    // User queries
    queryUsers,
    searchUsers,
    getUsersByRole,

    // User management actions
    suspendUser,
    activateUser,
    changeUserRole,

    // Statistics and analytics
    getUserStats,
    getUserActivity,
    getUserAuditLogs,

    // Bulk operations
    bulkUpdateUsers,

    // Permission checks
    canManageUser,
    canPerformAction,

    // Utility functions
    refreshUsers,
    clearError,
  };
};
