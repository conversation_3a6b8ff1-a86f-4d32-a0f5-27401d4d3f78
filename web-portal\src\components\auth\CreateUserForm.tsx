import React, { useState } from 'react';
import { type CreateUserInput } from '../../types/user';

interface CreateUserFormProps {
  onSubmit: (userData: CreateUserInput) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

const CreateUserForm: React.FC<CreateUserFormProps> = ({ onSubmit, onCancel, loading = false }) => {
  const [formData, setFormData] = useState<CreateUserInput>({
    email: '',
    name: '',
    password: '',
    role: 'customer', // Fixed role for customers
    phone_number: '',
    preferred_language: 'en',
    address: '',
    city: '',
    country: '',
    anydesk_id: '',
  });

  const [createAccount, setCreateAccount] = useState(true);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Handle form field changes
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Validate form before submission
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Required fields validation
    if (!formData.email) newErrors.email = 'Email is required';
    else if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = 'Email is invalid';

    if (!formData.name) newErrors.name = 'Name is required';

    // Password validation if creating account
    if (createAccount && (!formData.password || formData.password.length < 6)) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    // Prepare submission data
    const submissionData: CreateUserInput = {
      ...formData,
      password: createAccount ? formData.password : undefined,
    };

    try {
      await onSubmit(submissionData);
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  return (
    <form onSubmit={handleSubmit} className='space-y-4'>
      {/* Basic Information */}
      <div>
        <h3 className='text-lg font-medium mb-2'>Basic Information</h3>
        <div className='grid grid-cols-1 gap-4'>
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>
              Email <span className='text-red-500'>*</span>
            </label>
            <input
              type='email'
              name='email'
              value={formData.email}
              onChange={handleChange}
              className={`w-full px-3 py-2 border rounded-md ${
                errors.email ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder='<EMAIL>'
            />
            {errors.email && <p className='text-red-500 text-xs mt-1'>{errors.email}</p>}
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>
              Name <span className='text-red-500'>*</span>
            </label>
            <input
              type='text'
              name='name'
              value={formData.name}
              onChange={handleChange}
              className={`w-full px-3 py-2 border rounded-md ${
                errors.name ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder='Full Name'
            />
            {errors.name && <p className='text-red-500 text-xs mt-1'>{errors.name}</p>}
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>Phone Number</label>
            <input
              type='tel'
              name='phone_number'
              value={formData.phone_number}
              onChange={handleChange}
              className='w-full px-3 py-2 border border-gray-300 rounded-md'
              placeholder='+****************'
            />
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>AnyDesk ID</label>
            <input
              type='text'
              name='anydesk_id'
              value={formData.anydesk_id || ''}
              onChange={handleChange}
              className='w-full px-3 py-2 border border-gray-300 rounded-md'
              placeholder='AnyDesk ID for remote support'
            />
            <p className='text-xs text-gray-500 mt-1'>
              The AnyDesk ID is used for remote support sessions
            </p>
          </div>
        </div>
      </div>

      {/* Authentication */}
      <div>
        <div className='flex items-center justify-between mb-2'>
          <h3 className='text-lg font-medium'>Authentication</h3>
          <div className='flex items-center'>
            <input
              type='checkbox'
              id='createAccount'
              checked={createAccount}
              onChange={() => setCreateAccount(!createAccount)}
              className='h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'
            />
            <label htmlFor='createAccount' className='ml-2 text-sm text-gray-700'>
              Create user account
            </label>
          </div>
        </div>

        {createAccount && (
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>
              Password <span className='text-red-500'>*</span>
            </label>
            <input
              type='password'
              name='password'
              value={formData.password}
              onChange={handleChange}
              className={`w-full px-3 py-2 border rounded-md ${
                errors.password ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder='Minimum 6 characters'
            />
            {errors.password && <p className='text-red-500 text-xs mt-1'>{errors.password}</p>}
          </div>
        )}
      </div>

      {/* Location Information */}
      <div>
        <h3 className='text-lg font-medium mb-2'>Location Information</h3>
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>City</label>
            <input
              type='text'
              name='city'
              value={formData.city || ''}
              onChange={handleChange}
              className='w-full px-3 py-2 border border-gray-300 rounded-md'
              placeholder='City'
            />
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>Country</label>
            <input
              type='text'
              name='country'
              value={formData.country || ''}
              onChange={handleChange}
              className='w-full px-3 py-2 border border-gray-300 rounded-md'
              placeholder='Country'
            />
          </div>

          <div className='md:col-span-2'>
            <label className='block text-sm font-medium text-gray-700 mb-1'>Address</label>
            <input
              type='text'
              name='address'
              value={formData.address || ''}
              onChange={handleChange}
              className='w-full px-3 py-2 border border-gray-300 rounded-md'
              placeholder='Street address'
            />
          </div>
        </div>
      </div>

      {/* Preferences */}
      <div>
        <h3 className='text-lg font-medium mb-2'>Preferences</h3>
        <div>
          <label className='block text-sm font-medium text-gray-700 mb-1'>Preferred Language</label>
          <select
            name='preferred_language'
            value={formData.preferred_language || 'en'}
            onChange={handleChange}
            className='w-full px-3 py-2 border border-gray-300 rounded-md'
          >
            <option value='en'>English</option>
            <option value='ar'>Arabic</option>
          </select>
        </div>
      </div>

      {/* Form Actions */}
      <div className='flex justify-end space-x-2 pt-4 border-t border-gray-200'>
        <button
          type='button'
          onClick={onCancel}
          className='px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50'
          disabled={loading}
        >
          Cancel
        </button>
        <button
          type='submit'
          className='px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:bg-blue-300'
          disabled={loading}
        >
          {loading ? 'Creating...' : 'Create Customer'}
        </button>
      </div>
    </form>
  );
};

export default CreateUserForm;
