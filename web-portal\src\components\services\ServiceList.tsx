import React, { useState } from 'react';
import type { ServiceModel } from '../../types/service';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Clock, DollarSign, Edit, Eye, EyeOff, Image as ImageIcon, Trash2 } from 'lucide-react';

interface ServiceListProps {
  services: ServiceModel[];
  onEdit: (service: ServiceModel) => void;
  onDelete: (serviceId: string) => void;
  onToggleActive: (serviceId: string) => void;
  loading?: boolean;
}

export const ServiceList: React.FC<ServiceListProps> = ({
  services,
  onEdit,
  onDelete,
  onToggleActive,
  loading = false,
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Get unique categories
  const categories = ['all', ...new Set(services.map((service) => service.category))];

  // Filter services by category
  const filteredServices =
    selectedCategory === 'all'
      ? services
      : services.filter((service) => service.category === selectedCategory);

  const getTranslatedText = (text: string | Record<string, string>): string => {
    if (typeof text === 'string') return text;
    return text.en || text.ar || Object.values(text)[0] || '';
  };

  const formatPrice = (price: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(price);
  };

  const formatDuration = (minutes: number): string => {
    if (minutes < 60) return `${minutes} min`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  };

  if (loading) {
    return (
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
        {[...Array(6)].map((_, i) => (
          <Card key={i} className='animate-pulse'>
            <CardHeader>
              <div className='h-4 bg-gray-200 rounded w-3/4'></div>
              <div className='h-3 bg-gray-200 rounded w-1/2'></div>
            </CardHeader>
            <CardContent>
              <div className='h-20 bg-gray-200 rounded mb-4'></div>
              <div className='h-3 bg-gray-200 rounded mb-2'></div>
              <div className='h-3 bg-gray-200 rounded w-2/3'></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Category Filter */}
      <div className='flex flex-wrap gap-2'>
        {categories.map((category) => (
          <Button
            key={category}
            variant={selectedCategory === category ? 'default' : 'outline'}
            size='sm'
            onClick={() => setSelectedCategory(category)}
          >
            {category === 'all' ? 'All Categories' : category}
          </Button>
        ))}
      </div>

      {/* Service Grid */}
      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
        {filteredServices.map((service) => (
          <Card key={service.id} className={`relative ${!service.is_active ? 'opacity-60' : ''}`}>
            <CardHeader className='pb-3'>
              <div className='flex items-start justify-between'>
                <div className='flex-1'>
                  <CardTitle className='text-lg font-semibold'>
                    {getTranslatedText(service.name)}
                  </CardTitle>
                  <Badge variant='secondary' className='mt-1'>
                    {service.category}
                  </Badge>
                </div>
                <Badge variant={service.is_active ? 'default' : 'destructive'}>
                  {service.is_active ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            </CardHeader>

            <CardContent className='space-y-4'>
              {/* Service Image */}
              <div className='h-32 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden'>
                {service.image_url ? (
                  <img
                    src={service.image_url}
                    alt={getTranslatedText(service.name)}
                    className='w-full h-full object-cover'
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      target.nextElementSibling?.classList.remove('hidden');
                    }}
                  />
                ) : null}
                <div
                  className={`flex items-center justify-center ${service.image_url ? 'hidden' : ''}`}
                >
                  <ImageIcon className='w-8 h-8 text-gray-400' />
                </div>
              </div>

              {/* Description */}
              <p className='text-sm text-gray-600 line-clamp-2'>
                {getTranslatedText(service.description)}
              </p>

              {/* Price and Duration */}
              <div className='flex items-center justify-between text-sm'>
                <div className='flex items-center gap-1 text-green-600'>
                  <DollarSign className='w-4 h-4' />
                  <span className='font-semibold'>{formatPrice(service.base_price)}</span>
                </div>
                <div className='flex items-center gap-1 text-gray-500'>
                  <Clock className='w-4 h-4' />
                  <span>{formatDuration(service.estimated_duration)}</span>
                </div>
              </div>

              {/* Actions */}
              <div className='flex gap-2 pt-2'>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={() => onEdit(service)}
                  className='flex-1'
                >
                  <Edit className='w-4 h-4 mr-1' />
                  Edit
                </Button>
                <Button variant='outline' size='sm' onClick={() => onToggleActive(service.id)}>
                  {service.is_active ? <EyeOff className='w-4 h-4' /> : <Eye className='w-4 h-4' />}
                </Button>
                <Button
                  variant='outline'
                  size='sm'
                  onClick={() => onDelete(service.id)}
                  className='text-red-600 hover:text-red-700'
                >
                  <Trash2 className='w-4 h-4' />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredServices.length === 0 && (
        <div className='text-center py-12'>
          <ImageIcon className='w-12 h-12 text-gray-400 mx-auto mb-4' />
          <h3 className='text-lg font-semibold text-gray-900 mb-2'>No services found</h3>
          <p className='text-gray-600'>
            {selectedCategory === 'all'
              ? 'No services have been added yet.'
              : `No services found in the "${selectedCategory}" category.`}
          </p>
        </div>
      )}
    </div>
  );
};
