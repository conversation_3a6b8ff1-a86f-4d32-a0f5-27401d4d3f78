import React from 'react';
import { AlertCircle, CheckCircle, Eye, EyeOff } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Input } from './input';
import { Label } from './label';
import { Textarea } from './textarea';

interface FormFieldEnhancedProps {
  label: string;
  name: string;
  type?: 'text' | 'email' | 'password' | 'tel' | 'url' | 'number' | 'textarea';
  placeholder?: string;
  value: string | number;
  onChange: (value: string | number) => void;
  error?: string;
  required?: boolean;
  disabled?: boolean;
  autoComplete?: string;
  description?: string;
  showPasswordToggle?: boolean;
  min?: number;
  max?: number;
  step?: number;
  rows?: number;
  maxLength?: number;
  className?: string;
}

export const FormFieldEnhanced: React.FC<FormFieldEnhancedProps> = ({
  label,
  name,
  type = 'text',
  placeholder,
  value,
  onChange,
  error,
  required = false,
  disabled = false,
  autoComplete,
  description,
  showPasswordToggle = false,
  min,
  max,
  step,
  rows = 3,
  maxLength,
  className,
}) => {
  const [showPassword, setShowPassword] = React.useState(false);
  const [isFocused, setIsFocused] = React.useState(false);
  const [hasBeenTouched, setHasBeenTouched] = React.useState(false);

  const fieldId = `field-${name}`;
  const errorId = `${fieldId}-error`;
  const descriptionId = `${fieldId}-description`;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const newValue = type === 'number' ? parseFloat(e.target.value) || 0 : e.target.value;
    onChange(newValue);
    if (!hasBeenTouched) {
      setHasBeenTouched(true);
    }
  };

  const handleBlur = () => {
    setIsFocused(false);
    setHasBeenTouched(true);
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const inputType =
    type === 'password' && showPasswordToggle ? (showPassword ? 'text' : 'password') : type;
  const hasError = error && hasBeenTouched;
  const isValid = hasBeenTouched && !error && value;

  const inputProps = {
    id: fieldId,
    name,
    type: inputType,
    placeholder,
    value: value || '',
    onChange: handleChange,
    onFocus: handleFocus,
    onBlur: handleBlur,
    disabled,
    autoComplete,
    min,
    max,
    step,
    maxLength,
    className: cn(
      'transition-colors duration-200',
      hasError && 'border-destructive focus-visible:ring-destructive',
      isValid && 'border-green-500 focus-visible:ring-green-500',
      isFocused && 'ring-2',
      showPasswordToggle && 'pr-10',
      className,
    ),
    'aria-invalid': hasError ? 'true' : 'false',
    'aria-describedby':
      cn(
        error && hasBeenTouched ? errorId : undefined,
        description ? descriptionId : undefined,
      ).trim() || undefined,
  };

  return (
    <div className='space-y-2'>
      <Label htmlFor={fieldId} className='flex items-center gap-1'>
        {label}
        {required && <span className='text-destructive'>*</span>}
        {isValid && <CheckCircle className='w-4 h-4 text-green-500' />}
      </Label>

      <div className='relative'>
        {type === 'textarea' ? <Textarea {...inputProps} rows={rows} /> : <Input {...inputProps} />}

        {/* Password toggle button */}
        {showPasswordToggle && type === 'password' && (
          <button
            type='button'
            className='absolute inset-y-0 right-0 pr-3 flex items-center disabled:opacity-50'
            onClick={() => setShowPassword(!showPassword)}
            disabled={disabled}
            aria-label={showPassword ? 'Hide password' : 'Show password'}
          >
            {showPassword ? (
              <EyeOff className='h-4 w-4 text-muted-foreground' />
            ) : (
              <Eye className='h-4 w-4 text-muted-foreground' />
            )}
          </button>
        )}

        {/* Validation icon */}
        {hasBeenTouched && !showPasswordToggle && (
          <div className='absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none'>
            {hasError ? (
              <AlertCircle className='h-4 w-4 text-destructive' />
            ) : isValid ? (
              <CheckCircle className='h-4 w-4 text-green-500' />
            ) : null}
          </div>
        )}
      </div>

      {/* Description */}
      {description && (
        <p id={descriptionId} className='text-sm text-muted-foreground'>
          {description}
        </p>
      )}

      {/* Error message */}
      {hasError && (
        <p id={errorId} className='text-sm text-destructive flex items-center gap-1'>
          <AlertCircle className='w-4 h-4' />
          {error}
        </p>
      )}

      {/* Character count for text inputs */}
      {maxLength && type !== 'number' && (
        <p className='text-xs text-muted-foreground text-right'>
          {String(value || '').length}/{maxLength}
        </p>
      )}
    </div>
  );
};

// Form validation status component
interface FormValidationStatusProps {
  isValid: boolean;
  isSubmitting: boolean;
  errors: Record<string, string | string[]>;
  className?: string;
}

export const FormValidationStatus: React.FC<FormValidationStatusProps> = ({
  isValid,
  isSubmitting,
  errors,
  className,
}) => {
  const errorCount = Object.keys(errors).length;

  if (isSubmitting) {
    return (
      <div className={cn('flex items-center gap-2 text-sm text-muted-foreground', className)}>
        <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-primary' />
        Validating form...
      </div>
    );
  }

  if (errorCount > 0) {
    return (
      <div className={cn('flex items-center gap-2 text-sm text-destructive', className)}>
        <AlertCircle className='w-4 h-4' />
        {errorCount} error{errorCount > 1 ? 's' : ''} found. Please fix them to continue.
      </div>
    );
  }

  if (isValid) {
    return (
      <div className={cn('flex items-center gap-2 text-sm text-green-600', className)}>
        <CheckCircle className='w-4 h-4' />
        Form is valid and ready to submit.
      </div>
    );
  }

  return null;
};

// Form section component for better organization
interface FormSectionProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
}

export const FormSection: React.FC<FormSectionProps> = ({
  title,
  description,
  children,
  className,
}) => {
  return (
    <div className={cn('space-y-4', className)}>
      <div className='space-y-1'>
        <h3 className='text-lg font-medium'>{title}</h3>
        {description && <p className='text-sm text-muted-foreground'>{description}</p>}
      </div>
      <div className='space-y-4'>{children}</div>
    </div>
  );
};
