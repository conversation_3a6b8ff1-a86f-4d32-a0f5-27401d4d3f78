import 'package:flutter/foundation.dart';
import '../services/remote_config_service.dart';
import 'env_config.dart';

/// Test utility for Remote Config integration
/// This helps verify that Remote Config is working properly with Paymob keys
class RemoteConfigTest {
  static final EnvConfig _envConfig = EnvConfig.instance;
  static final RemoteConfigService _remoteConfig = RemoteConfigService();

  /// Test the Remote Config integration for Paymob keys
  static Future<Map<String, dynamic>> testRemoteConfig() async {
    final results = <String, dynamic>{};
    
    try {
      debugPrint('\n======= REMOTE CONFIG TEST =======');
      
      // 1. Test EnvConfig initialization
      debugPrint('1. Testing EnvConfig initialization...');
      if (!_envConfig.isInitialized) {
        await _envConfig.initialize();
      }
      results['env_config_initialized'] = _envConfig.isInitialized;
      
      // 2. Test Remote Config initialization
      debugPrint('2. Testing Remote Config initialization...');
      await _remoteConfig.initialize();
      final configInfo = await _remoteConfig.getConfigInfo();
      results['remote_config_info'] = configInfo;
      
      // 3. Test Paymob config availability
      debugPrint('3. Testing Paymob configuration...');
      final paymobConfig = await _remoteConfig.getPaymobConfig();
      results['paymob_remote_keys'] = paymobConfig.keys.toList();
      results['has_valid_paymob_config'] = await _remoteConfig.hasValidPaymobConfig();
      
      // 4. Test EnvConfig fallback mechanism
      debugPrint('4. Testing EnvConfig Remote Config fallback...');
      final testKeys = ['PAYMOB_SECRET_KEY', 'PAYMOB_PUBLIC_KEY', 'PAYMOB_INTEGRATION_ID'];
      final keyResults = <String, String>{};
      
      for (final key in testKeys) {
        final value = await _envConfig.get(key);
        keyResults[key] = value?.isNotEmpty == true ? 'Available' : 'Missing';
      }
      results['key_availability'] = keyResults;
      
      // 5. Test Remote Config force refresh
      debugPrint('5. Testing Remote Config force refresh...');
      final refreshResult = await _envConfig.refreshRemoteConfig();
      results['refresh_success'] = refreshResult;
      
      debugPrint('✅ Remote Config test completed');
      debugPrint('===================================\n');
      
      return results;
      
    } catch (e) {
      debugPrint('❌ Error during Remote Config test: $e');
      results['error'] = e.toString();
      return results;
    }
  }

  /// Print a detailed report of the Remote Config test results
  static void printTestReport(Map<String, dynamic> results) {
    debugPrint('\n======= REMOTE CONFIG TEST REPORT =======');
    
    // EnvConfig status
    final envInitialized = results['env_config_initialized'] ?? false;
    debugPrint('EnvConfig Initialized: ${envInitialized ? "✅" : "❌"}');
    
    // Remote Config status
    final remoteConfigInfo = results['remote_config_info'] as Map<String, dynamic>?;
    if (remoteConfigInfo != null) {
      debugPrint('Remote Config Status:');
      debugPrint('  Initialized: ${remoteConfigInfo['initialized'] ? "✅" : "❌"}');
      debugPrint('  Last Fetch: ${remoteConfigInfo['last_fetch_time']}');
      debugPrint('  Fetch Status: ${remoteConfigInfo['last_fetch_status']}');
    }
    
    // Paymob configuration
    final hasValidPaymob = results['has_valid_paymob_config'] ?? false;
    debugPrint('Valid Paymob Config: ${hasValidPaymob ? "✅" : "❌"}');
    
    final paymobKeys = results['paymob_remote_keys'] as List<dynamic>?;
    if (paymobKeys != null) {
      debugPrint('Remote Config Keys: ${paymobKeys.join(", ")}');
    }
    
    // Key availability
    final keyResults = results['key_availability'] as Map<String, String>?;
    if (keyResults != null) {
      debugPrint('Key Availability:');
      keyResults.forEach((key, status) {
        debugPrint('  $key: ${status == "Available" ? "✅" : "❌"} $status');
      });
    }
    
    // Refresh test
    final refreshSuccess = results['refresh_success'] ?? false;
    debugPrint('Remote Config Refresh: ${refreshSuccess ? "✅" : "❌"}');
    
    // Overall status
    final hasError = results.containsKey('error');
    if (hasError) {
      debugPrint('❌ Test completed with errors: ${results['error']}');
    } else {
      debugPrint('✅ All tests completed successfully');
    }
    
    debugPrint('========================================\n');
  }

  /// Quick test that can be called from anywhere in the app
  static Future<void> quickTest() async {
    if (kDebugMode) {
      final results = await testRemoteConfig();
      printTestReport(results);
    }
  }
}