{"name": "web-portal", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "preview": "vite preview", "type-check": "tsc --noEmit", "style:fix": "node scripts/fix-code-style.cjs", "style:check": "npm run format:check && npm run lint", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@types/dompurify": "^3.0.5", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dompurify": "^3.2.6", "firebase": "^11.9.1", "firebase-admin": "^13.4.0", "lucide-react": "^0.517.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-day-picker": "^9.8.1", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "react-router-dom": "^7.6.2", "recharts": "^2.15.3", "sonner": "^2.0.5", "tailwind-merge": "^3.3.1", "zod": "^3.25.67", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/postcss": "^4.1.10", "@tailwindcss/vite": "^4.1.10", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^24.0.3", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "@vitejs/plugin-react": "^4.4.1", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "jsdom": "^26.1.0", "postcss": "^8.5.6", "prettier": "^3.5.3", "tailwindcss": "^4.1.10", "terser": "^5.43.1", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vitest": "^3.2.4"}}