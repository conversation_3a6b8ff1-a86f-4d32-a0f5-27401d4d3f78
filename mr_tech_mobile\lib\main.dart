import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:provider/provider.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_app_check/firebase_app_check.dart';
import 'dart:ui' as ui;
import 'services/translation_service.dart';
import 'services/notification_service.dart';
import 'services/theme_service.dart';
import 'services/auth_service.dart';
import 'services/database_service.dart';
import 'services/app_service.dart';
import 'views/welcome_screen.dart';
import 'views/main_navigation.dart';
import 'views/onboarding_screen.dart';
import 'main_app.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'firebase_options_minimal.dart';
import 'utils/env_config.dart';
import 'utils/migrations.dart';
import 'utils/performance_optimizer.dart';
import 'utils/startup_optimizer.dart';
import 'utils/animation_optimizer.dart';
import 'utils/accessibility_utils.dart';
import 'utils/navigation_manager.dart';
import 'utils/state_manager.dart';

// This is required to handle FCM messages when the app is in background or terminated
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Initialize Firebase first
  await Firebase.initializeApp();

  debugPrint("Handling a background message: ${message.messageId}");
  // Add background message handling logic here
}

// Helper function to get device language for Firebase Auth
String _getDeviceLanguageForFirebase() {
  try {
    final deviceLocale = ui.PlatformDispatcher.instance.locale;
    final deviceLanguageCode = deviceLocale.languageCode;

    // Supported languages for Firebase Auth (you can extend this list)
    const supportedLanguages = ['ar', 'en'];

    if (supportedLanguages.contains(deviceLanguageCode)) {
      debugPrint(
        'Setting Firebase Auth language to device language: $deviceLanguageCode',
      );
      return deviceLanguageCode;
    } else {
      debugPrint(
        'Device language $deviceLanguageCode not supported for Firebase Auth, using English',
      );
      return 'en'; // Fallback to English
    }
  } catch (e) {
    debugPrint(
      'Error detecting device language for Firebase Auth: $e, using English',
    );
    return 'en'; // Fallback to English on error
  }
}

void main() async {
  // Initialize Flutter binding
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize performance optimizations early
  StartupOptimizer.initialize();
  PerformanceOptimizer.initialize();
  AnimationOptimizer.initialize();

  // Initialize accessibility utilities
  AccessibilityUtils.initialize();

  // Initialize navigation and state management
  NavigationManager.initialize();
  await StateManager.initialize();

  // Configure system UI for proper iOS safe area handling
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.light,
      systemNavigationBarColor: Colors.transparent,
      systemNavigationBarDividerColor: Colors.transparent,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  // Enable edge-to-edge display
  SystemChrome.setEnabledSystemUIMode(
    SystemUiMode.edgeToEdge,
  );

  // Register background handler BEFORE Firebase initialization
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  try {
    // Execute critical startup tasks only
    await StartupOptimizer.executeCriticalStartup();

    // Initialize Firebase Core with minimal options for speed
    await Firebase.initializeApp(
      options: MinimalFirebaseOptions.fromFullOptions(),
    );

    // Set Firebase language based on device language
    final firebaseLanguage = _getDeviceLanguageForFirebase();
    FirebaseAuth.instance.setLanguageCode(firebaseLanguage);

    // Initialize Firebase App Check
    await FirebaseAppCheck.instance.activate(
      // Use debug provider for development
      androidProvider:
          kDebugMode ? AndroidProvider.debug : AndroidProvider.playIntegrity,
      // Use device check for iOS
      appleProvider: AppleProvider.deviceCheck,
    );

    // Create AppService and initialize it before creating the app
    final appService = AppService();

    // Add non-critical initialization as deferred tasks
    StartupOptimizer.addDeferredTask(() => appService.initializeApp());
    StartupOptimizer.addDeferredTask(() => Migrations().runAllMigrations());

    // Add background tasks
    StartupOptimizer.addBackgroundTask(() => _initializeInBackground(appService));

    // Show the main app UI with security features immediately
    runApp(const MainApp());
  } catch (e) {
    debugPrint('Error starting app: $e');
    debugPrint('Stack trace: ${StackTrace.current}');
    // Show error app if Firebase Core initialization failed
    runApp(
      ConfigErrorApp(
        errorMessage:
            'Error initializing app: $e\n\n'
            'Please check your Firebase configuration in firebase_options.dart',
      ),
    );
  }
}

Future<void> _initializeInBackground(AppService appService) async {
  // Delay to allow UI to render first
  await Future.delayed(const Duration(milliseconds: 500));

  try {
    // Load environment configuration
    if (!EnvConfig.instance.isInitialized) {
      await EnvConfig.instance.initialize();
    }

    // Initialize full Firebase with all features (slower)
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
      name: 'fullFirebase',
    );

    // Complete initialization of notification service
    await appService.completeAsyncInitialization();
  } catch (e) {
    debugPrint('Background initialization error: $e');
    // Non-critical error, app can still function
  }
}

// Error app for showing configuration issues
class ConfigErrorApp extends StatelessWidget {
  final String errorMessage;

  const ConfigErrorApp({super.key, required this.errorMessage});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Mr.Tech - Configuration Error',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.light,
        ),
      ),
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Configuration Error'),
          backgroundColor: Colors.redAccent,
        ),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.redAccent,
                  size: 80,
                ),
                const SizedBox(height: 24),
                const Text(
                  'Firebase Configuration Error',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Text(
                  errorMessage,
                  style: const TextStyle(fontSize: 16, color: Colors.black87),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                const Text(
                  'Steps to fix:',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 8),
                _buildInstructionStep(
                  '1',
                  'Create a Firebase project at console.firebase.google.com',
                ),
                _buildInstructionStep(
                  '2',
                  'Install FlutterFire CLI: "dart pub global activate flutterfire_cli"',
                ),
                _buildInstructionStep(
                  '3',
                  'Run: "flutterfire configure" and select your project',
                ),
                _buildInstructionStep(
                  '4',
                  'Restart the app after configuration is complete',
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInstructionStep(String number, String instruction) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 28,
            height: 28,
            decoration: const BoxDecoration(
              color: Colors.blue,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                number,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              instruction,
              style: const TextStyle(fontSize: 16, color: Colors.black87),
            ),
          ),
        ],
      ),
    );
  }
}

class MyApp extends StatefulWidget {
  final AppService appService;
  final NotificationService notificationService;
  final FirebaseAnalytics analytics;

  const MyApp({
    super.key,
    required this.appService,
    required this.notificationService,
    required this.analytics,
  });

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late AppService _appService;
  late TranslationService _translationService;
  late ThemeService _themeService;
  late AuthService _authService;
  late DatabaseService _databaseService;
  late NotificationService _notificationService;

  @override
  void initState() {
    super.initState();

    // Use services from AppService
    _appService = widget.appService;

    // Only access these services if app service is initialized
    if (_appService.isInitialized) {
      _translationService = _appService.translationService;
      _themeService = _appService.themeService;
      _authService = _appService.authService;
      _databaseService = _appService.databaseService;

      // Initialize notification service
      _notificationService = widget.notificationService;
    } else {
      // Fallback initialization if app service isn't ready
      _translationService = TranslationService();
      _themeService = ThemeService();
      _authService = AuthService();
      _databaseService = DatabaseService();

      // Try to initialize app service if it wasn't already
      _appService.initializeApp().then((_) {
        // Update services with the initialized ones
        setState(() {
          _translationService = _appService.translationService;
          _themeService = _appService.themeService;
          _authService = _appService.authService;
          _databaseService = _appService.databaseService;
          _notificationService = widget.notificationService;
        });
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        Provider<AppService>.value(value: _appService),
        Provider<AuthService>.value(value: _authService),
        ChangeNotifierProvider<ThemeService>.value(value: _themeService),
        ChangeNotifierProvider<TranslationService>.value(
          value: _translationService,
        ),
        Provider<DatabaseService>.value(value: _databaseService),
        Provider<NotificationService>.value(value: _notificationService),
        StreamProvider<User?>(
          create: (_) => _authService.authStateChanges,
          initialData: null,
        ),
      ],
      child: Builder(
        builder: (context) {
          final themeService = Provider.of<ThemeService>(context);
          final translationService = Provider.of<TranslationService>(context);

          return MaterialApp(
            navigatorKey: _appService.navigatorKey,
            title: 'Mr.Tech',
            debugShowCheckedModeBanner: false,
            theme: themeService.lightTheme,
            darkTheme: themeService.darkTheme,
            themeMode:
                themeService.isDarkMode ? ThemeMode.dark : ThemeMode.light,
            localizationsDelegates: const [
              GlobalMaterialLocalizations.delegate,
              GlobalWidgetsLocalizations.delegate,
              GlobalCupertinoLocalizations.delegate,
            ],
            supportedLocales: const [
              Locale('en', ''), // English
              Locale('ar', ''), // Arabic
            ],
            locale: translationService.currentLocale,
            home: _buildHomeScreen(),
            navigatorObservers: [
              FirebaseAnalyticsObserver(analytics: widget.analytics),
            ],
          );
        },
      ),
    );
  }

  Widget _buildHomeScreen() {
    // Check if auth service is initialized
    if (!_appService.isInitialized) {
      // Show loading screen while app is initializing
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              const Text('Initializing...'),
            ],
          ),
        ),
      );
    }

    final User? currentUser = _authService.currentUser;

    if (currentUser == null) {
      // Not logged in, show welcome screen
      return const WelcomeScreen();
    } else {
      // User is logged in
      // Check if they need to complete onboarding
      if (!_appService.hasCompletedOnboarding) {
        return const OnboardingScreen();
      } else {
        // User is logged in and has completed onboarding
        return const MainNavigation();
      }
    }
  }
}
