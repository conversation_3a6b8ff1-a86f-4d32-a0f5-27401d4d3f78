import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../../components/ui/dialog';
import { But<PERSON> } from '../../components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui/select';
import { Badge } from '../../components/ui/badge';
import { Textarea } from '../../components/ui/textarea';
import { Label } from '../../components/ui/label';
import { AlertCircle, Calendar, CheckCircle, Clock, DollarSign, User, XCircle } from 'lucide-react';

import { type RequestModel, RequestStatus } from '../../types/request';
import { type TechnicianModel } from '../../types/technician';
import requestService from '../../services/requestService';

interface StatusUpdateDialogProps {
  request: RequestModel | null;
  technicians: TechnicianModel[];
  isOpen: boolean;
  onClose: () => void;
  onUpdate: () => void;
}

const StatusUpdateDialog: React.FC<StatusUpdateDialogProps> = ({
  request,
  technicians,
  isOpen,
  onClose,
  onUpdate,
}) => {
  const [selectedStatus, setSelectedStatus] = useState<RequestStatus | ''>('');
  const [selectedTechnician, setSelectedTechnician] = useState<string>('');
  const [notes, setNotes] = useState('');
  const [isUpdating, setIsUpdating] = useState(false);

  // Reset form when dialog opens with new request
  React.useEffect(() => {
    if (request && isOpen) {
      setSelectedStatus(request.status);
      setSelectedTechnician(request.technician_id || 'unassigned');
      setNotes('');
    }
  }, [request, isOpen]);

  const getStatusConfig = (status: RequestStatus) => {
    const configs = {
      [RequestStatus.PAYMENT_PENDING]: {
        label: 'Payment Pending',
        icon: Clock,
        color: 'text-yellow-600',
      },
      [RequestStatus.PENDING]: {
        label: 'Pending',
        icon: Clock,
        color: 'text-blue-600',
      },
      [RequestStatus.APPROVED]: {
        label: 'Approved',
        icon: CheckCircle,
        color: 'text-green-600',
      },
      [RequestStatus.IN_PROGRESS]: {
        label: 'In Progress',
        icon: AlertCircle,
        color: 'text-orange-600',
      },
      [RequestStatus.COMPLETED]: {
        label: 'Completed',
        icon: CheckCircle,
        color: 'text-green-600',
      },
      [RequestStatus.CANCELLED]: {
        label: 'Cancelled',
        icon: XCircle,
        color: 'text-red-600',
      },
      [RequestStatus.REFUSED]: {
        label: 'Refused',
        icon: XCircle,
        color: 'text-red-600',
      },
    };
    return configs[status];
  };

  const getValidNextStatuses = (currentStatus: RequestStatus): RequestStatus[] => {
    const statusFlow = {
      [RequestStatus.PAYMENT_PENDING]: [RequestStatus.PENDING, RequestStatus.CANCELLED],
      [RequestStatus.PENDING]: [
        RequestStatus.APPROVED,
        RequestStatus.REFUSED,
        RequestStatus.CANCELLED,
      ],
      [RequestStatus.APPROVED]: [RequestStatus.IN_PROGRESS, RequestStatus.CANCELLED],
      [RequestStatus.IN_PROGRESS]: [RequestStatus.COMPLETED, RequestStatus.CANCELLED],
      [RequestStatus.COMPLETED]: [], // Final state
      [RequestStatus.CANCELLED]: [], // Final state
      [RequestStatus.REFUSED]: [], // Final state
    };

    return statusFlow[currentStatus] || [];
  };

  const handleUpdate = async () => {
    if (!request || !selectedStatus) return;

    setIsUpdating(true);
    try {
      // Update status
      if (selectedStatus !== request.status) {
        await requestService.updateStatus(request.id, selectedStatus);
      }

      // Update technician assignment if changed
      const currentTechnicianId = request.technician_id || 'unassigned';
      if (selectedTechnician !== currentTechnicianId) {
        const technicianName =
          selectedTechnician && selectedTechnician !== 'unassigned'
            ? getLocalizedText(technicians.find((t) => t.id === selectedTechnician)?.name, '')
            : '';

        await requestService.update(request.id, {
          technician_id: selectedTechnician === 'unassigned' ? undefined : selectedTechnician,
          technician_name: technicianName || undefined,
        });
      }

      // Add notes if provided (this would typically go to a notes/history collection)
      if (notes.trim()) {
        console.warn('Notes to be saved:', notes);
        // TODO: Implement notes/history functionality
      }

      onUpdate();
      onClose();
    } catch (error) {
      console.error('Error updating request:', error);
      // TODO: Show error toast
    } finally {
      setIsUpdating(false);
    }
  };

  const formatDate = (date: Date | any) => {
    const dateObj = date instanceof Date ? date : date.toDate();
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  // Helper function to handle multilingual fields
  const getLocalizedText = (text: any, fallback: string = ''): string => {
    // Handle null or undefined
    if (text == null) {
      return fallback;
    }

    // Handle string values
    if (typeof text === 'string') {
      return text;
    }

    // Handle multilingual objects
    if (typeof text === 'object' && text !== null) {
      // Check if it has the expected structure
      if (typeof text.en === 'string' || typeof text.ar === 'string') {
        return text.en || text.ar || fallback;
      }

      // If it's an object but not the expected structure, try to convert to string
      try {
        return String(text) || fallback;
      } catch {
        return fallback;
      }
    }

    // For any other type, try to convert to string
    try {
      return String(text) || fallback;
    } catch {
      return fallback;
    }
  };

  if (!request) return null;

  const currentStatusConfig = getStatusConfig(request.status);
  const CurrentStatusIcon = currentStatusConfig.icon;
  const validNextStatuses = getValidNextStatuses(request.status);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-2xl bg-white border border-gray-200 shadow-xl'>
        <DialogHeader>
          <DialogTitle>Update Request Status</DialogTitle>
          <DialogDescription>
            Update the status and assignment for request #{request.id.slice(-8)}
          </DialogDescription>
        </DialogHeader>

        <div className='space-y-6'>
          {/* Request Summary */}
          <div className='bg-gray-50 border border-gray-200 p-4 rounded-lg space-y-3'>
            <div className='flex items-center justify-between'>
              <h3 className='font-semibold'>{getLocalizedText(request.service_name)}</h3>
              <div className='flex items-center gap-2'>
                <CurrentStatusIcon className={`h-4 w-4 ${currentStatusConfig.color}`} />
                <Badge variant='outline'>{currentStatusConfig.label}</Badge>
              </div>
            </div>

            <div className='grid grid-cols-2 gap-4 text-sm'>
              <div className='flex items-center gap-2'>
                <DollarSign className='h-4 w-4 text-muted-foreground' />
                <span>{formatCurrency(request.amount)}</span>
                <Badge variant={request.is_paid ? 'default' : 'secondary'} className='text-xs'>
                  {request.is_paid ? 'Paid' : 'Unpaid'}
                </Badge>
              </div>
              <div className='flex items-center gap-2'>
                <Calendar className='h-4 w-4 text-muted-foreground' />
                <span>{formatDate(request.created_at)}</span>
              </div>
            </div>

            <div className='text-sm'>
              <strong>Issue:</strong> {request.customer_issue || 'No issue description'}
            </div>

            {(request.anydesk_id || request.anydesk_id) && (
              <div className='text-sm'>
                <strong>AnyDesk ID:</strong>
                <div className='flex items-center gap-2 mt-1'>
                  <code className='bg-background border px-2 py-1 rounded text-sm font-mono'>
                    {request.anydesk_id || request.anydesk_id}
                  </code>
                  <Button
                    size='sm'
                    variant='outline'
                    onClick={() => {
                      const anydesk_id = request.anydesk_id || request.anydesk_id;
                      if (anydesk_id) {
                        navigator.clipboard.writeText(anydesk_id);
                        // TODO: Show toast notification
                      }
                    }}
                    className='h-6 px-2 text-xs'
                  >
                    Copy
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* Status Update */}
          <div className='space-y-2'>
            <Label htmlFor='status'>New Status</Label>
            <Select
              value={selectedStatus}
              onValueChange={(value) => setSelectedStatus(value as RequestStatus)}
            >
              <SelectTrigger>
                <SelectValue placeholder='Select new status' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={request.status}>
                  {currentStatusConfig.label} (Current)
                </SelectItem>
                {validNextStatuses.map((status) => {
                  const config = getStatusConfig(status);
                  const StatusIcon = config.icon;
                  return (
                    <SelectItem key={status} value={status}>
                      <div className='flex items-center gap-2'>
                        <StatusIcon className={`h-4 w-4 ${config.color}`} />
                        {config.label}
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>

          {/* Technician Assignment */}
          <div className='space-y-2'>
            <Label htmlFor='technician'>Assign Technician</Label>
            <Select value={selectedTechnician} onValueChange={setSelectedTechnician}>
              <SelectTrigger>
                <SelectValue placeholder='Select technician' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='unassigned'>Unassigned</SelectItem>
                {technicians.map((technician) => (
                  <SelectItem key={technician.id} value={technician.id}>
                    <div className='flex items-center gap-2'>
                      <User className='h-4 w-4' />
                      {getLocalizedText(technician.name, 'Unknown Technician')}
                      {technician.rating > 0 && (
                        <span className='text-xs text-muted-foreground'>
                          ({technician.rating.toFixed(1)}★)
                        </span>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Notes */}
          <div className='space-y-2'>
            <Label htmlFor='notes'>Notes (Optional)</Label>
            <Textarea
              id='notes'
              placeholder='Add any notes about this status update...'
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant='outline' onClick={onClose} disabled={isUpdating}>
            Cancel
          </Button>
          <Button onClick={handleUpdate} disabled={isUpdating || !selectedStatus}>
            {isUpdating ? 'Updating...' : 'Update Request'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default StatusUpdateDialog;
