import { getFunctions, httpsCallable } from 'firebase/functions';
import app from '../config/firebase';

const functions = getFunctions(app);

export interface CreateTechnicianAccountData {
  email: string;
  password: string;
  name: string;
  technicianData: {
    phone_number?: string;
    photo_url?: string;
    specialties: string[];
    status: string;
    is_available: boolean;
    rating: number;
    completed_requests: number;
    active_requests: number;
  };
}

export interface CreateTechnicianAccountResult {
  success: boolean;
  uid: string;
  message: string;
}

class CloudFunctionService {
  // Create technician account without auto-login
  async createTechnicianAccount(
    data: CreateTechnicianAccountData,
  ): Promise<CreateTechnicianAccountResult> {
    try {
      const createTechnicianAccount = httpsCallable<
        CreateTechnicianAccountData,
        CreateTechnicianAccountResult
      >(functions, 'createTechnicianAccount');

      const result = await createTechnicianAccount(data);
      return result.data;
    } catch (error) {
      console.error('Error calling createTechnicianAccount function:', error);
      throw error;
    }
  }
}

export default new CloudFunctionService();
