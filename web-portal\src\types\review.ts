import type { Timestamp } from 'firebase/firestore';

/**
 * Review types based on the mobile app's ReviewModel
 * These must match exactly with the mobile app for compatibility
 */

export interface Review {
  id: string;
  request_id: string;
  customer_id: string;
  customer_name: string;
  technician_id: string;
  technician_name?: string;
  service_id: string;
  service_name?: string;
  rating: number;
  comment: string;
  created_at: Date | Timestamp;
  updated_at?: Date | Timestamp;

  // Additional fields that might be present
  is_verified?: boolean;
  is_public?: boolean;
  helpful_count?: number;
  reported_by?: string[];
  technician_response?: string;
  response_date?: Date | Timestamp;
  admin_notes?: string;
}

export interface EnhancedReview extends Review {
  overall_rating: number;
  category_ratings: Record<string, number>;
  tags: string[];
  sentiment?: 'positive' | 'neutral' | 'negative';
  sentiment_score?: number; // -1.0 to 1.0
}

export interface CreateReviewInput {
  request_id: string;
  customer_id: string;
  customer_name: string;
  technician_id: string;
  service_id: string;
  rating: number;
  comment: string;
  category_ratings?: Record<string, number>;
  tags?: string[];
}

export interface UpdateReviewInput {
  rating?: number;
  comment?: string;
  category_ratings?: Record<string, number>;
  tags?: string[];
  technician_response?: string;
  admin_notes?: string;
  is_public?: boolean;
}

export interface ReviewStats {
  total: number;
  averageRating: number;
  ratingDistribution: Record<number, number>; // 1-5 star counts
  totalByTechnician: Record<string, number>;
  averageByTechnician: Record<string, number>;
  recentReviews: Review[];
  topRatedTechnicians: Array<{
    technician_id: string;
    technician_name: string;
    average_rating: number;
    review_count: number;
  }>;
}

export interface ReviewFilters {
  rating?: number;
  technician_id?: string;
  customer_id?: string;
  service_id?: string;
  date_from?: Date;
  date_to?: Date;
  is_verified?: boolean;
  is_public?: boolean;
}

// Utility types for review operations
export type ReviewWithTechnician = Review & {
  technician_name: string;
  technician_avatar?: string;
};

export type ReviewWithCustomer = Review & {
  customer_avatar?: string;
};

export interface ReviewSummary {
  id: string;
  customer_name: string;
  technician_name: string;
  rating: number;
  comment: string;
  created_at: Date;
  service_name?: string;
}
