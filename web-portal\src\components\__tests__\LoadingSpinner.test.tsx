import { describe, expect, it } from 'vitest';
import { render, screen } from '@testing-library/react';
import LoadingSpinner from '../ui/loading-spinner';

describe('LoadingSpinner', () => {
  it('should render with default props', () => {
    render(<LoadingSpinner />);

    const spinner = document.querySelector('.animate-spin');
    expect(spinner).toBeInTheDocument();
  });

  it('should render with custom text', () => {
    const customText = 'Processing...';
    render(<LoadingSpinner text={customText} />);

    expect(screen.getByText(customText)).toBeInTheDocument();
  });

  it('should render with custom className', () => {
    const customClass = 'custom-spinner-class';
    render(<LoadingSpinner className={customClass} />);

    const container = document.querySelector(`.${customClass}`);
    expect(container).toBeInTheDocument();
  });

  it('should render fullScreen variant', () => {
    render(<LoadingSpinner fullScreen />);

    const overlay = document.querySelector('.fixed.inset-0');
    expect(overlay).toBeInTheDocument();
  });

  it('should apply correct size classes', () => {
    const { rerender } = render(<LoadingSpinner size='sm' />);
    let spinner = document.querySelector('.w-4.h-4');
    expect(spinner).toBeInTheDocument();

    rerender(<LoadingSpinner size='lg' />);
    spinner = document.querySelector('.w-8.h-8');
    expect(spinner).toBeInTheDocument();
  });

  it('should maintain animation classes', () => {
    render(<LoadingSpinner />);

    const spinner = document.querySelector('.animate-spin');
    expect(spinner).toBeInTheDocument();
  });

  it('should render text with appropriate size', () => {
    render(<LoadingSpinner text='Loading...' size='lg' />);

    const text = screen.getByText('Loading...');
    expect(text).toBeInTheDocument();
    expect(text).toHaveClass('text-lg');
  });
});
