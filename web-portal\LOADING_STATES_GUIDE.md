# Loading States Implementation Guide

## Overview

The web portal now includes a comprehensive loading states system designed to provide excellent user experience during data loading, form submissions, and async operations. This system includes skeleton screens, loading indicators, progress bars, and specialized loading components.

## Components

### 1. Skeleton Components (`components/ui/skeleton.tsx`)

Skeleton screens provide visual placeholders while content is loading, preventing layout shifts and giving users a sense of the content structure.

**Base Skeleton Component:**

```tsx
import { Skeleton } from '../components/ui/skeleton';

<Skeleton className='h-4 w-full' />;
```

**Specialized Skeleton Components:**

- **SkeletonText** - For text content with multiple lines
- **SkeletonCard** - For card layouts with header/footer options
- **SkeletonTable** - For table structures with configurable rows/columns
- **SkeletonList** - For list items with optional avatars
- **SkeletonStats** - For dashboard statistics cards
- **SkeletonChart** - For chart placeholders
- **SkeletonForm** - For form layouts
- **SkeletonProfile** - For user profile sections
- **SkeletonChat** - For chat message layouts

**Usage Examples:**

```tsx
// Dashboard loading
<SkeletonStats cards={4} />
<SkeletonCard showHeader={true} showFooter={false} />

// Table loading
<SkeletonTable rows={8} columns={6} />

// Chat loading
<SkeletonChat messages={6} />

// Form loading
<SkeletonForm fields={4} showSubmit={true} />
```

### 2. Loading States Hook (`hooks/useLoadingStates.ts`)

Advanced hook for managing multiple loading states with debouncing, minimum loading times, and progress tracking.

**Multiple Loading States:**

```tsx
import { useLoadingStates } from '../hooks/useLoadingStates';

const { startLoading, stopLoading, updateProgress, isLoading, getLoadingState } = useLoadingStates({
  minLoadingTime: 300, // Prevent flashing
  debounceTime: 100, // Debounce rapid changes
});

// Start loading with message
startLoading('dashboard', 'Loading dashboard data...', 'Fetching stats');

// Update progress
updateProgress('dashboard', 45, 'Loading activity data...', 'Processing');

// Stop loading
stopLoading('dashboard');

// Check loading state
const isDashboardLoading = isLoading('dashboard');
const loadingState = getLoadingState('dashboard');
```

**Simple Loading Hook:**

```tsx
import { useLoading } from '../hooks/useLoadingStates';

const { isLoading, startLoading, stopLoading, updateProgress, setStage } = useLoading();

// Usage in async operations
const handleSubmit = async () => {
  startLoading('Submitting form...');
  try {
    updateProgress(25, 'Validating data...');
    await validateData();

    updateProgress(50, 'Saving to database...');
    await saveData();

    updateProgress(100, 'Complete!');
  } finally {
    stopLoading();
  }
};
```

**Async Loading Hook:**

```tsx
import { useAsyncLoading } from '../hooks/useLoadingStates';

const { execute, data, error, isLoading } = useAsyncLoading();

const loadData = async () => {
  const result = await execute('fetchData', async () => await apiCall(), 'Loading data...');
  return result;
};
```

### 3. Loading UI Components (`components/ui/loading-states.tsx`)

Specialized components for different loading scenarios.

**LoadingSpinner:**

```tsx
<LoadingSpinner size='lg' text='Loading...' />
```

**ProgressBar:**

```tsx
<ProgressBar progress={75} variant='success' showPercentage={true} />
```

**LoadingOverlay:**

```tsx
<LoadingOverlay
  isLoading={isSubmitting}
  text='Saving changes...'
  progress={uploadProgress}
  stage='Uploading files...'
>
  <YourContent />
</LoadingOverlay>
```

**LoadingButton:**

```tsx
<LoadingButton isLoading={isSubmitting} loadingText='Saving...' onClick={handleSubmit}>
  Save Changes
</LoadingButton>
```

**StatusIndicator:**

```tsx
<StatusIndicator status='loading' text='Processing...' size='md' />
```

**ConnectionStatus:**

```tsx
<ConnectionStatus isConnected={isOnline} showText={true} />
```

**EmptyState:**

```tsx
<EmptyState
  title='No data found'
  description='Try adjusting your filters or refresh the page'
  action={{
    label: 'Refresh',
    onClick: handleRefresh,
    isLoading: isRefreshing,
  }}
  icon={<FileText className='w-12 h-12' />}
/>
```

## Implementation Examples

### 1. Dashboard Page Loading

```tsx
// DashboardPage.tsx
import { SkeletonStats, SkeletonCard, SkeletonList } from '../components/ui/skeleton';

if (dashboardDataOperation.isLoading) {
  return (
    <div className='space-y-6'>
      <div>
        <h1 className='text-3xl font-bold tracking-tight'>Dashboard</h1>
        <p className='text-muted-foreground mt-2'>Loading dashboard data...</p>
      </div>

      {/* Stats skeleton */}
      <SkeletonStats cards={4} />

      {/* Recent activity skeleton */}
      <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-7'>
        <SkeletonCard className='md:col-span-4' showHeader={true} />
        <SkeletonCard className='md:col-span-3' showHeader={true} />
      </div>

      {/* Recent reviews skeleton (admin only) */}
      {user?.role === 'admin' && <SkeletonList items={3} showAvatar={true} />}
    </div>
  );
}
```

### 2. Requests Page Loading

```tsx
// RequestsPage.tsx
import { SkeletonTable, SkeletonStats, SkeletonCard } from '../components/ui/skeleton';

if (loading) {
  return (
    <div className='space-y-6 p-6'>
      {/* Header skeleton */}
      <div className='flex justify-between items-center'>
        <div>
          <SkeletonCard className='h-8 w-48' />
          <SkeletonCard className='h-4 w-64 mt-2' />
        </div>
        <SkeletonCard className='h-10 w-32' />
      </div>

      {/* Stats skeleton */}
      <SkeletonStats cards={4} />

      {/* Filters skeleton */}
      <div className='flex gap-4'>
        <SkeletonCard className='h-10 w-64' />
        <SkeletonCard className='h-10 w-32' />
        <SkeletonCard className='h-10 w-32' />
      </div>

      {/* Table skeleton */}
      <SkeletonTable rows={8} columns={6} />
    </div>
  );
}
```

### 3. Chat Window Loading

```tsx
// ChatWindow.tsx
import { SkeletonChat } from '../components/ui/skeleton';

if (isLoading) {
  return (
    <div className='flex-1 p-4'>
      <div className='mb-4 text-center'>
        <div className='flex items-center justify-center gap-2 text-gray-500'>
          <Loader2 className='w-4 h-4 animate-spin' />
          <span className='text-sm'>Loading chat messages...</span>
        </div>
      </div>
      <SkeletonChat messages={6} />
    </div>
  );
}
```

### 4. Form Loading States

```tsx
// LoginForm.tsx
import { LoadingButton } from '../components/ui/loading-states';

<LoadingButton
  type='submit'
  isLoading={isLoading}
  loadingText='Signing in...'
  disabled={!watch('email') || !watch('password')}
  className='w-full'
>
  <LogIn className='h-4 w-4 mr-2' />
  Sign In
</LoadingButton>;
```

### 5. Async Operations with Progress

```tsx
// File upload example
const { execute, isLoading } = useAsyncLoading();

const handleFileUpload = async (file: File) => {
  await execute(
    'fileUpload',
    async () => {
      const formData = new FormData();
      formData.append('file', file);

      return await fetch('/api/upload', {
        method: 'POST',
        body: formData,
        onUploadProgress: (progressEvent) => {
          const progress = (progressEvent.loaded / progressEvent.total) * 100;
          updateProgress('fileUpload', progress, 'Uploading file...');
        },
      });
    },
    'Preparing upload...',
  );
};
```

## Best Practices

### 1. Skeleton Screen Guidelines

- Match the skeleton structure to the actual content layout
- Use appropriate skeleton components for different content types
- Show skeletons for at least 300ms to prevent flashing
- Animate skeletons with subtle pulse effects

### 2. Loading State Management

- Use minimum loading times to prevent jarring quick flashes
- Debounce rapid state changes to avoid UI flickering
- Provide meaningful loading messages and progress updates
- Show progress for operations that take more than 2 seconds

### 3. User Experience

- Always provide feedback for user actions
- Use appropriate loading indicators for different contexts
- Show progress for long-running operations
- Provide retry mechanisms for failed operations

### 4. Performance Considerations

- Lazy load skeleton components to reduce bundle size
- Use CSS animations instead of JavaScript for better performance
- Avoid excessive DOM updates during loading states
- Clean up timers and subscriptions properly

### 5. Accessibility

- Ensure loading states are announced to screen readers
- Use appropriate ARIA labels and roles
- Maintain keyboard navigation during loading states
- Provide alternative text for loading indicators

## Testing Loading States

### 1. Manual Testing

- Test with slow network connections
- Test rapid state changes
- Test loading state cancellation
- Test error scenarios during loading

### 2. Automated Testing

```tsx
// Example test for loading states
import { render, screen, waitFor } from '@testing-library/react';
import { useAsyncOperation } from '../hooks/useErrorHandler';

test('shows loading state during async operation', async () => {
  const TestComponent = () => {
    const operation = useAsyncOperation();

    return <div>{operation.isLoading ? <div>Loading...</div> : <div>Content loaded</div>}</div>;
  };

  render(<TestComponent />);

  // Should show loading initially
  expect(screen.getByText('Loading...')).toBeInTheDocument();

  // Should show content after loading
  await waitFor(() => {
    expect(screen.getByText('Content loaded')).toBeInTheDocument();
  });
});
```

## Migration Guide

### Existing Components

1. Replace manual loading spinners with `LoadingSpinner` component
2. Replace basic loading states with skeleton screens
3. Update form buttons to use `LoadingButton`
4. Add progress indicators for long operations
5. Implement proper loading state management with hooks

### Performance Optimization

1. Implement code splitting for skeleton components
2. Use CSS-based animations for better performance
3. Optimize loading state updates to prevent excessive re-renders
4. Add proper cleanup for loading state timers

This loading states system provides a comprehensive foundation for excellent user experience during all loading scenarios while maintaining performance and accessibility standards.
