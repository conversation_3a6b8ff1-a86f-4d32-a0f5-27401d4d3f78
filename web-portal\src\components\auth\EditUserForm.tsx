import React, { useEffect, useState } from 'react';
import { type UpdateUserInput, type User, UserStatus } from '../../types/user';

// Extend UpdateUserInput to include password for this form
interface EditFormData extends UpdateUserInput {
  password?: string;
}

interface EditUserFormProps {
  user: User;
  onSubmit: (userId: string, userData: UpdateUserInput) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

const EditUserForm: React.FC<EditUserFormProps> = ({
  user,
  onSubmit,
  onCancel,
  loading = false,
}) => {
  const [formData, setFormData] = useState<EditFormData>({
    name: user.name || '',
    phone_number: user.phone_number || '',
    address: user.address || '',
    city: user.city || '',
    country: user.country || '',
    status: user.status,
    preferred_language: user.preferred_language || 'en',
    anydesk_id: user.anydesk_id || '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [resetPassword, setResetPassword] = useState(false);
  const [newPassword, setNewPassword] = useState('');

  useEffect(() => {
    // Update form data when user prop changes
    setFormData({
      name: user.name || '',
      phone_number: user.phone_number || '',
      address: user.address || '',
      city: user.city || '',
      country: user.country || '',
      status: user.status,
      preferred_language: user.preferred_language || 'en',
      anydesk_id: user.anydesk_id || '',
    });
  }, [user]);

  // Handle form field changes
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Validate form before submission
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name) newErrors.name = 'Name is required';

    // Password validation if resetting
    if (resetPassword && newPassword.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    // Prepare submission data
    const submissionData: EditFormData = {
      ...formData,
    };

    if (resetPassword) {
      submissionData.password = newPassword;
    }

    try {
      await onSubmit(user.id, submissionData);
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  return (
    <form onSubmit={handleSubmit} className='space-y-4'>
      {/* Basic Information */}
      <div>
        <h3 className='text-lg font-medium mb-2'>Basic Information</h3>
        <div className='grid grid-cols-1 gap-4'>
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>Email</label>
            <input
              type='email'
              value={user.email}
              className='w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100'
              disabled
            />
            <p className='text-xs text-gray-500 mt-1'>Email cannot be changed</p>
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>
              Name <span className='text-red-500'>*</span>
            </label>
            <input
              type='text'
              name='name'
              value={formData.name}
              onChange={handleChange}
              className={`w-full px-3 py-2 border rounded-md ${
                errors.name ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder='Full Name'
            />
            {errors.name && <p className='text-red-500 text-xs mt-1'>{errors.name}</p>}
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>Phone Number</label>
            <input
              type='tel'
              name='phone_number'
              value={formData.phone_number || ''}
              onChange={handleChange}
              className='w-full px-3 py-2 border border-gray-300 rounded-md'
              placeholder='+****************'
            />
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>AnyDesk ID</label>
            <input
              type='text'
              name='anydesk_id'
              value={formData.anydesk_id || ''}
              onChange={handleChange}
              className='w-full px-3 py-2 border border-gray-300 rounded-md'
              placeholder='AnyDesk ID for remote support'
            />
            <p className='text-xs text-gray-500 mt-1'>
              The AnyDesk ID is used for remote support sessions
            </p>
          </div>
        </div>
      </div>

      {/* Authentication */}
      <div>
        <div className='flex items-center justify-between mb-2'>
          <h3 className='text-lg font-medium'>Authentication</h3>
          <div className='flex items-center'>
            <input
              type='checkbox'
              id='resetPassword'
              checked={resetPassword}
              onChange={() => setResetPassword(!resetPassword)}
              className='h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'
            />
            <label htmlFor='resetPassword' className='ml-2 text-sm text-gray-700'>
              Reset password
            </label>
          </div>
        </div>

        {resetPassword && (
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>
              New Password <span className='text-red-500'>*</span>
            </label>
            <input
              type='password'
              value={newPassword}
              onChange={(e) => setNewPassword(e.target.value)}
              className={`w-full px-3 py-2 border rounded-md ${
                errors.password ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder='Minimum 6 characters'
            />
            {errors.password && <p className='text-red-500 text-xs mt-1'>{errors.password}</p>}
          </div>
        )}

        <div className='mt-4'>
          <label className='block text-sm font-medium text-gray-700 mb-1'>Status</label>
          <select
            name='status'
            value={formData.status}
            onChange={handleChange}
            className='w-full px-3 py-2 border border-gray-300 rounded-md'
          >
            <option value={UserStatus.ACTIVE}>Active</option>
            <option value={UserStatus.SUSPENDED}>Suspended</option>
            <option value={UserStatus.INACTIVE}>Inactive</option>
          </select>
        </div>
      </div>

      {/* Location Information */}
      <div>
        <h3 className='text-lg font-medium mb-2'>Location Information</h3>
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>City</label>
            <input
              type='text'
              name='city'
              value={formData.city || ''}
              onChange={handleChange}
              className='w-full px-3 py-2 border border-gray-300 rounded-md'
              placeholder='City'
            />
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>Country</label>
            <input
              type='text'
              name='country'
              value={formData.country || ''}
              onChange={handleChange}
              className='w-full px-3 py-2 border border-gray-300 rounded-md'
              placeholder='Country'
            />
          </div>

          <div className='md:col-span-2'>
            <label className='block text-sm font-medium text-gray-700 mb-1'>Address</label>
            <input
              type='text'
              name='address'
              value={formData.address || ''}
              onChange={handleChange}
              className='w-full px-3 py-2 border border-gray-300 rounded-md'
              placeholder='Street address'
            />
          </div>
        </div>
      </div>

      {/* Preferences */}
      <div>
        <h3 className='text-lg font-medium mb-2'>Preferences</h3>
        <div>
          <label className='block text-sm font-medium text-gray-700 mb-1'>Preferred Language</label>
          <select
            name='preferred_language'
            value={formData.preferred_language || 'en'}
            onChange={handleChange}
            className='w-full px-3 py-2 border border-gray-300 rounded-md'
          >
            <option value='en'>English</option>
            <option value='ar'>Arabic</option>
          </select>
        </div>
      </div>

      {/* Form Actions */}
      <div className='flex justify-end space-x-2 pt-4 border-t border-gray-200'>
        <button
          type='button'
          onClick={onCancel}
          className='px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50'
          disabled={loading}
        >
          Cancel
        </button>
        <button
          type='submit'
          className='px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:bg-blue-300'
          disabled={loading}
        >
          {loading ? 'Saving...' : 'Save Changes'}
        </button>
      </div>
    </form>
  );
};

export default EditUserForm;
