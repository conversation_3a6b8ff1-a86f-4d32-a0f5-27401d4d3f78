# Error Handling System Guide

## Overview

The web portal now includes a comprehensive error handling system designed to provide better user experience, debugging capabilities, and system reliability. This system includes React Error Boundaries, custom hooks for async error handling, and specialized error display components.

## Components

### 1. Error Boundary (`components/error/ErrorBoundary.tsx`)

A React Error Boundary component that catches JavaScript errors anywhere in the component tree and displays fallback UI.

**Features:**

- Automatic error catching and logging
- Retry mechanism with configurable limits
- Different error levels (page, component, critical)
- Development-only error details
- Error reporting integration ready

**Usage:**

```tsx
<ErrorBoundary level='page' showDetails={false}>
  <YourComponent />
</ErrorBoundary>
```

**Props:**

- `level`: 'page' | 'component' | 'critical' - Determines UI and behavior
- `showDetails`: boolean - Show technical details (dev only)
- `onError`: Custom error handler function
- `fallback`: Custom fallback UI

### 2. Error Handler Hook (`hooks/useErrorHandler.ts`)

Custom hooks for handling async operations and form errors with retry mechanisms.

**Main Hook: `useErrorHand<PERSON>`**

```tsx
const errorHandler = useErrorHandler({
  onError: (error, context) => console.error(error),
  logErrors: true,
  defaultRetryOptions: {
    maxRetries: 3,
    retryDelay: 1000,
    exponentialBackoff: true,
  },
});

// Execute with error handling
const result = await errorHandler.executeWithErrorHandling(
  async () => await apiCall(),
  { component: 'MyComponent', action: 'fetchData' },
  { maxRetries: 2 },
);
```

**Form Error Hook: `useFormErrorHandler`**

```tsx
const { fieldErrors, generalError, setFieldError, clearAllErrors } = useFormErrorHandler();
```

**Async Operation Hook: `useAsyncOperation`**

```tsx
const operation = useAsyncOperation();

const handleSubmit = async () => {
  await operation.execute(async () => await submitData(), {
    onSuccess: (data) => console.log('Success:', data),
    onError: (error) => console.error('Error:', error),
  });
};
```

### 3. Error Display Components (`components/error/ErrorDisplay.tsx`)

Specialized components for displaying different types of errors with consistent UI.

**Main Component:**

```tsx
<ErrorDisplay
  error={error}
  type='network'
  severity='medium'
  title='Connection Problem'
  showRetry={true}
  onRetry={handleRetry}
/>
```

**Specialized Components:**

- `<NetworkError />` - For network-related errors
- `<ValidationError />` - For form validation errors
- `<PermissionError />` - For authorization errors
- `<NotFoundError />` - For 404-type errors
- `<ServerError />` - For server errors
- `<InlineError />` - For inline error messages

### 4. Error Service (`services/errorService.ts`)

Centralized error processing and categorization service.

**Features:**

- Automatic error categorization
- User-friendly message generation
- Error severity determination
- Retry logic recommendations
- Error tracking and statistics

**Usage:**

```tsx
import { errorService } from '../services/errorService';

const processedError = errorService.processError(error, {
  component: 'MyComponent',
  action: 'fetchData',
  userId: user.id,
});

console.log(processedError.userMessage); // User-friendly message
console.log(processedError.isRetryable); // Whether error is retryable
```

## Error Types and Categories

### Error Types

- `network` - Connection, fetch, or network-related errors
- `authentication` - Login, token, or auth errors
- `authorization` - Permission or access denied errors
- `validation` - Form validation or input errors
- `not-found` - Resource not found errors
- `server` - Server-side errors (5xx)
- `client` - Client-side errors (4xx)
- `timeout` - Request timeout errors
- `rate-limit` - Rate limiting errors
- `unknown` - Unclassified errors

### Error Severity Levels

- `low` - Minor issues, non-blocking
- `medium` - Moderate issues, may affect functionality
- `high` - Serious issues, significant impact
- `critical` - Critical failures, system-wide impact

## Implementation Examples

### 1. Page-Level Error Handling

```tsx
// In App.tsx - Page-level error boundaries
<Route
  path='/dashboard'
  element={
    <ErrorBoundary level='page'>
      <DashboardPage />
    </ErrorBoundary>
  }
/>
```

### 2. Component-Level Error Handling

```tsx
// In DashboardPage.tsx - Using async operation hook
const dashboardDataOperation = useAsyncOperation();

const loadData = useCallback(async () => {
  await dashboardDataOperation.execute(
    async () => {
      const stats = await fetchDashboardStats();
      const activity = await fetchRecentActivity();
      return { stats, activity };
    },
    {
      onSuccess: (data) => {
        setStats(data.stats);
        setActivity(data.activity);
      },
      retryOptions: {
        maxRetries: 2,
        retryDelay: 3000,
      },
    },
  );
}, [dashboardDataOperation]);

// In render
if (dashboardDataOperation.isError) {
  return (
    <ErrorDisplay
      error={dashboardDataOperation.error}
      title='Failed to Load Dashboard'
      showRetry={true}
      onRetry={loadData}
      type='server'
      severity='medium'
    />
  );
}
```

### 3. Service-Level Error Handling

```tsx
// In authService.ts - Enhanced error processing
import { errorService } from './errorService';

async login(credentials: LoginCredentials) {
  try {
    // ... login logic
  } catch (error: any) {
    const processedError = errorService.processError(error, {
      component: 'AuthService',
      action: 'login',
      userId: credentials.email,
    });

    logger.error('Login failed', {
      errorId: processedError.id,
      errorType: processedError.type,
    });

    throw new Error(processedError.userMessage);
  }
}
```

## Best Practices

### 1. Error Boundary Placement

- **Critical Level**: Root level (App.tsx) for catastrophic failures
- **Page Level**: Around each major page component
- **Component Level**: Around complex components that might fail

### 2. Error Messages

- Use user-friendly messages from `errorService.processError()`
- Provide actionable guidance when possible
- Include retry options for recoverable errors
- Show technical details only in development

### 3. Retry Logic

- Use exponential backoff for network errors
- Limit retry attempts (default: 3)
- Don't retry authentication/authorization errors
- Consider user experience in retry delays

### 4. Error Logging

- Log all errors with context information
- Include error IDs for tracking
- Use appropriate log levels based on severity
- Include user and session information when available

### 5. Testing Error Scenarios

- Test network failures
- Test authentication failures
- Test validation errors
- Test component crashes
- Test retry mechanisms

## Migration Guide

### Existing Components

1. Add error boundary imports
2. Replace manual error states with hooks
3. Update error display to use new components
4. Add retry mechanisms where appropriate

### Services

1. Import and use `errorService`
2. Replace generic error throwing with processed errors
3. Add context information to error processing
4. Update error logging to include error IDs

## Monitoring and Debugging

### Error Statistics

```tsx
import { errorService } from '../services/errorService';

// Get error statistics
const stats = errorService.getErrorStats();
console.log('Total errors:', stats.total);
console.log('By type:', stats.byType);
console.log('By severity:', stats.bySeverity);

// Get recent errors
const recentErrors = errorService.getRecentErrors(10);
```

### Development Tools

- Error boundaries show detailed stack traces in development
- Error service maintains error queue for debugging
- Console logging includes error IDs and context
- Performance monitoring tracks error rates

This error handling system provides a robust foundation for managing errors throughout the application while maintaining a good user experience and providing developers with the tools needed for debugging and monitoring.
