/**
 * Debug utilities for authentication and permissions
 */

import { getAuth, onAuthStateChanged } from 'firebase/auth';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '../config/firebase';

export const debugCurrentUser = () => {
  const auth = getAuth();

  onAuthStateChanged(auth, async (user) => {
    if (user) {
      console.warn('🔐 Current User:', {
        uid: user.uid,
        email: user.email,
        displayName: user.displayName,
        emailVerified: user.emailVerified,
        metadata: user.metadata,
      });

      // Check user role in database
      try {
        const userDoc = await getDoc(doc(db, 'users', user.uid));
        if (userDoc.exists()) {
          console.warn('👤 User Data:', userDoc.data());
        } else {
          console.warn('❌ User document not found in users collection');
        }

        // Check if user is admin
        const adminDoc = await getDoc(doc(db, 'admins', user.uid));
        if (adminDoc.exists()) {
          console.warn('👑 Admin Data:', adminDoc.data());
        } else {
          console.warn('ℹ️ User is not in admins collection');
        }

        // Check if user is technician
        const techDoc = await getDoc(doc(db, 'technicians', user.uid));
        if (techDoc.exists()) {
          console.warn('🔧 Technician Data:', techDoc.data());
        } else {
          console.warn('ℹ️ User is not in technicians collection');
        }
      } catch (error) {
        console.error('❌ Error fetching user data:', error);
      }
    } else {
      console.warn('❌ No user is currently signed in');
    }
  });
};

export const testPaymentPermissions = async () => {
  const auth = getAuth();
  const user = auth.currentUser;

  if (!user) {
    console.error('❌ No authenticated user');
    return false;
  }

  console.warn('🧪 Testing payment permissions for user:', user.email);

  try {
    // Try to read payment transactions
    const { getDocs, query, collection, limit } = await import('firebase/firestore');

    const paymentsQuery = query(collection(db, 'payment_transactions'), limit(1));
    const paymentsSnapshot = await getDocs(paymentsQuery);
    console.warn('✅ Can read payment_transactions:', !paymentsSnapshot.empty);

    // Try to read payment responses
    const responsesQuery = query(collection(db, 'payment_responses'), limit(1));
    const responsesSnapshot = await getDocs(responsesQuery);
    console.warn('✅ Can read payment_responses:', !responsesSnapshot.empty);

    return true;
  } catch (error) {
    console.error('❌ Payment permission test failed:', error);
    return false;
  }
};

// Add to window for easy debugging
if (typeof window !== 'undefined') {
  (window as any).debugAuth = {
    debugCurrentUser,
    testPaymentPermissions,
  };
}
