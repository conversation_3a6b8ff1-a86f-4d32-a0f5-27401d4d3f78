import React, { useCallback, useEffect, useRef, useState } from 'react';
import { CheckCircle, Info, Loader2, MessageCircle, Users, X, XCircle } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import {
  type ChatMessage as ChatMessageType,
  MessageType,
  SenderType,
  type TypingIndicator,
} from '../../types/chat.js';
import type { RequestModel, RequestStatus } from '../../types/request.js';
import ChatMessage from './ChatMessage';
import ChatInput from './ChatInput';
import chatService from '../../services/chatService';
import requestService from '../../services/requestService.js';
import { cn } from '../../lib/utils';
import { Button } from '../ui/button';
import { useErrorHandler } from '../../hooks/useErrorHandler';
import ErrorDisplay from '../error/ErrorDisplay';
import { SkeletonChat } from '../ui/skeleton';

interface ChatWindowProps {
  request: RequestModel;
  onClose?: () => void;
  className?: string;
}

const ChatWindow: React.FC<ChatWindowProps> = ({ request, onClose, className }) => {
  const { user } = useAuth();
  const [messages, setMessages] = useState<ChatMessageType[]>([]);
  const [typingUsers, setTypingUsers] = useState<TypingIndicator[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isChatActive, setIsChatActive] = useState(false);
  const [currentRequest, setCurrentRequest] = useState<RequestModel>(request);
  const [showCloseConfirm, setShowCloseConfirm] = useState(false);

  // Use error handler for better error management
  const errorHandler = useErrorHandler({
    onError: (error, context) => {
      console.error('Chat error:', error, context);
    },
  });
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom of messages
  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  // Load initial messages and set up listeners
  useEffect(() => {
    if (!request.id || !user) return;

    const loadChat = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Check chat status
        const statusResult = await chatService.getChatStatus(request.id);
        if (statusResult.success && statusResult.data) {
          setIsChatActive(statusResult.data.active);
        }

        // Load initial messages
        const messagesResult = await chatService.getMessages(request.id);
        if (messagesResult.success && messagesResult.data) {
          setMessages(messagesResult.data);
        }

        // Set up real-time listeners
        const unsubscribeMessages = chatService.listenToMessages(request.id, (newMessages) => {
          setMessages(newMessages);
          setTimeout(scrollToBottom, 100);
        });

        const unsubscribeTyping = chatService.listenToTypingIndicators(request.id, (typing) => {
          // Filter out current user from typing indicators
          const otherUsersTyping = typing.filter((t) => t.userId !== user.uid);
          setTypingUsers(otherUsersTyping);
        });

        // Mark messages as read
        await chatService.markMessagesAsRead(request.id, user.uid);

        return () => {
          unsubscribeMessages();
          unsubscribeTyping();
        };
      } catch (error) {
        errorHandler.setError(error instanceof Error ? error : new Error('Failed to load chat'), {
          component: 'ChatWindow',
          action: 'loadChat',
          requestId: request.id,
          userId: user.uid,
        });
      } finally {
        setIsLoading(false);
      }
    };

    const cleanup = loadChat();

    return () => {
      cleanup.then((cleanupFn) => cleanupFn?.());
    };
  }, [request.id, user, scrollToBottom]);

  // Auto-scroll when new messages arrive
  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  const handleSendMessage = async (content: string, fileUrl?: string) => {
    if (!user || (!content.trim() && !fileUrl)) return;

    const messageInput = {
      requestId: request.id,
      senderType: user.role === 'customer' ? SenderType.CUSTOMER : SenderType.TECHNICIAN,
      senderId: user.uid,
      messageType: fileUrl
        ? fileUrl.includes('image')
          ? MessageType.IMAGE
          : MessageType.FILE
        : MessageType.TEXT,
      content: content.trim(),
      fileUrl,
      isRead: false,
    };

    const result = await errorHandler.executeWithErrorHandling(
      async () => {
        const sendResult = await chatService.sendMessage(messageInput);
        if (!sendResult.success) {
          throw new Error(sendResult.error || 'Failed to send message');
        }
        return sendResult;
      },
      {
        component: 'ChatWindow',
        action: 'sendMessage',
        requestId: request.id,
        userId: user.uid,
      },
      {
        maxRetries: 2,
        retryDelay: 1000,
      },
    );
  };

  const handleTyping = async (isTyping: boolean) => {
    if (!user) return;

    await chatService.setTypingIndicator(request.id, user.uid, user.name, isTyping);
  };

  const handleInitializeChat = async () => {
    if (!user) return;

    setIsLoading(true);
    const result = await chatService.initializeChat(request.id, user.name);

    if (result.success) {
      setIsChatActive(true);
    } else {
      setError('Failed to initialize chat');
    }
    setIsLoading(false);
  };

  const handleCloseChat = async () => {
    if (!user) return;

    try {
      setIsLoading(true);

      // Send system message about chat closure
      await chatService.sendMessage({
        requestId: request.id,
        senderType: SenderType.SYSTEM,
        senderId: 'system',
        messageType: MessageType.SYSTEM,
        content: 'This chat has been closed by the technician.',
        isRead: true,
      });

      // Update chat status to inactive
      await chatService.updateChatStatus(request.id, false);

      // Update request status if it's still in progress
      if (currentRequest.status === 'in_progress' || currentRequest.status === 'approved') {
        await requestService.updateStatus(request.id, 'completed' as RequestStatus);
        setCurrentRequest((prev) => ({ ...prev, status: 'completed' as RequestStatus }));
      }

      setIsChatActive(false);
      setShowCloseConfirm(false);
    } catch (error) {
      console.error('Error closing chat:', error);
      setError('Failed to close chat');
    } finally {
      setIsLoading(false);
    }
  };

  const renderTypingIndicator = () => {
    if (typingUsers.length === 0) return null;

    const typingText =
      typingUsers.length === 1
        ? `${typingUsers[0].userName} is typing...`
        : `${typingUsers.length} people are typing...`;

    return (
      <div className='flex items-center gap-2 px-3 py-2 sm:px-4 sm:py-2 text-xs sm:text-sm text-gray-500 bg-gray-50 rounded-lg mx-2 sm:mx-4 mb-2'>
        <div className='flex gap-1'>
          <div
            className='w-1.5 h-1.5 sm:w-2 sm:h-2 bg-blue-500 rounded-full animate-bounce'
            style={{ animationDelay: '0ms' }}
          />
          <div
            className='w-1.5 h-1.5 sm:w-2 sm:h-2 bg-blue-500 rounded-full animate-bounce'
            style={{ animationDelay: '150ms' }}
          />
          <div
            className='w-1.5 h-1.5 sm:w-2 sm:h-2 bg-blue-500 rounded-full animate-bounce'
            style={{ animationDelay: '300ms' }}
          />
        </div>
        <span className='font-medium'>{typingText}</span>
      </div>
    );
  };

  const renderChatHeader = () => {
    const isCompleted =
      currentRequest.status === 'completed' || currentRequest.status === 'cancelled';
    const canClose =
      (user?.role === 'technician' || user?.role === 'admin') && isChatActive && !isCompleted;

    return (
      <div className='flex items-center justify-between p-3 sm:p-4 border-b border-gray-200 bg-white'>
        <div className='flex items-center gap-2 sm:gap-3 min-w-0 flex-1'>
          <MessageCircle className='w-4 h-4 sm:w-5 sm:h-5 text-blue-500 flex-shrink-0' />
          <div className='min-w-0 flex-1'>
            <h3 className='font-semibold text-gray-900 text-sm sm:text-base truncate'>
              {typeof request.service_name === 'string'
                ? request.service_name
                : request.service_name?.en || 'Chat Support'}
            </h3>
            <p className='text-xs sm:text-sm text-gray-500 truncate'>
              Request #{request.id.slice(-8)}
              {request.technician_name && ` • ${request.technician_name}`}
              {isCompleted && (
                <span className='ml-1 sm:ml-2 inline-flex items-center gap-1'>
                  {currentRequest.status === 'completed' ? (
                    <>
                      <CheckCircle className='w-3 h-3 text-green-500' />
                      <span className='text-green-600 font-medium hidden sm:inline'>Completed</span>
                    </>
                  ) : (
                    <>
                      <XCircle className='w-3 h-3 text-red-500' />
                      <span className='text-red-600 font-medium hidden sm:inline'>Cancelled</span>
                    </>
                  )}
                </span>
              )}
            </p>
          </div>
        </div>
        <div className='flex items-center gap-2 sm:gap-3 flex-shrink-0'>
          <div className='flex items-center gap-1 sm:gap-2'>
            <div
              className={cn(
                'w-2 h-2 rounded-full',
                isChatActive && !isCompleted ? 'bg-green-500' : 'bg-gray-400',
              )}
            />
            <span className='text-xs sm:text-sm text-gray-500 hidden sm:inline'>
              {isCompleted ? 'Closed' : isChatActive ? 'Active' : 'Inactive'}
            </span>
          </div>
          {canClose && (
            <Button
              variant='outline'
              size='sm'
              onClick={() => setShowCloseConfirm(true)}
              className='text-red-600 border-red-200 hover:bg-red-50 text-xs sm:text-sm px-2 sm:px-3'
            >
              <X className='w-3 h-3 sm:w-4 sm:h-4 sm:mr-1' />
              <span className='hidden sm:inline'>Close Chat</span>
            </Button>
          )}
        </div>
      </div>
    );
  };

  const renderChatContent = () => {
    if (isLoading) {
      return (
        <div className='flex-1 p-4'>
          <div className='mb-4 text-center'>
            <div className='flex items-center justify-center gap-2 text-gray-500'>
              <Loader2 className='w-4 h-4 animate-spin' />
              <span className='text-sm'>Loading chat messages...</span>
            </div>
          </div>
          <SkeletonChat messages={6} />
        </div>
      );
    }

    if (errorHandler.isError && errorHandler.error) {
      return (
        <div className='flex-1 flex items-center justify-center p-4'>
          <ErrorDisplay
            error={errorHandler.error}
            title='Chat Error'
            description='Failed to load chat messages. Please try again.'
            showRetry={true}
            onRetry={() => window.location.reload()}
            type='network'
            severity='medium'
            compact={true}
          />
        </div>
      );
    }

    const isCompleted =
      currentRequest.status === 'completed' || currentRequest.status === 'cancelled';

    if (!isChatActive || isCompleted) {
      return (
        <div className='flex-1 flex items-center justify-center bg-gray-50'>
          <div className='text-center max-w-md mx-auto p-6'>
            <MessageCircle className='w-12 h-12 text-gray-400 mx-auto mb-4' />
            <h3 className='text-lg font-semibold text-gray-900 mb-2'>
              {isCompleted ? 'Chat Closed' : 'Chat Not Active'}
            </h3>
            <p className='text-gray-500 mb-4'>
              {isCompleted
                ? `This request has been ${currentRequest.status} and the chat is now closed.`
                : "This chat session hasn't been started yet."}
              {!isCompleted && (user?.role === 'technician' || user?.role === 'admin')
                ? ' Click below to start the chat.'
                : !isCompleted
                  ? ' Please wait for a technician to start the chat.'
                  : ''}
            </p>
            {!isCompleted && (user?.role === 'technician' || user?.role === 'admin') && (
              <Button
                onClick={handleInitializeChat}
                disabled={isLoading}
                className='bg-blue-500 hover:bg-blue-600 text-white'
              >
                {isLoading ? 'Starting...' : 'Start Chat'}
              </Button>
            )}
          </div>
        </div>
      );
    }

    return (
      <>
        {/* Messages */}
        <div
          ref={messagesContainerRef}
          className='flex-1 overflow-y-auto p-2 sm:p-4 space-y-2 sm:space-y-4 bg-gray-50'
        >
          {messages.length === 0 ? (
            <div className='text-center text-gray-500 py-8'>
              <MessageCircle className='w-8 h-8 mx-auto mb-2 opacity-50' />
              <p className='text-sm sm:text-base'>No messages yet. Start the conversation!</p>
            </div>
          ) : (
            messages.map((message, index) => {
              const isConsecutive =
                index > 0 &&
                messages[index - 1].senderType === message.senderType &&
                messages[index - 1].senderId === message.senderId;

              return (
                <ChatMessage
                  key={message.id}
                  message={message}
                  currentUserId={user?.uid || ''}
                  senderName={
                    message.senderType === SenderType.CUSTOMER
                      ? request.customer_name || 'Customer'
                      : message.senderType === SenderType.TECHNICIAN
                        ? request.technician_name || 'Technician'
                        : 'System'
                  }
                  showAvatar={!isConsecutive}
                  showTimestamp={!isConsecutive}
                />
              );
            })
          )}
          {renderTypingIndicator()}
          <div ref={messagesEndRef} />
        </div>

        {/* Chat Input */}
        <ChatInput
          onSendMessage={handleSendMessage}
          onTyping={handleTyping}
          disabled={!isChatActive}
          requestId={request.id}
          placeholder={isChatActive ? 'Type a message...' : 'Chat is not active'}
        />

        {/* Close Confirmation Dialog */}
        {showCloseConfirm && (
          <div className='fixed inset-0 bg-black/80 flex items-center justify-center z-50'>
            <div className='bg-white rounded-lg p-6 max-w-md mx-4'>
              <h3 className='text-lg font-semibold text-gray-900 mb-2'>Close Chat</h3>
              <p className='text-gray-600 mb-4'>
                Are you sure you want to close this chat? This will mark the request as completed
                and end the conversation.
              </p>
              <div className='flex gap-3 justify-end'>
                <Button
                  variant='outline'
                  onClick={() => setShowCloseConfirm(false)}
                  disabled={isLoading}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleCloseChat}
                  disabled={isLoading}
                  className='bg-red-500 hover:bg-red-600 text-white'
                >
                  {isLoading ? 'Closing...' : 'Close Chat'}
                </Button>
              </div>
            </div>
          </div>
        )}
      </>
    );
  };

  return (
    <div className={cn('flex flex-col h-full bg-white', className)}>
      {renderChatHeader()}
      {renderChatContent()}
    </div>
  );
};

export default ChatWindow;
