# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Structure

This is a multi-platform Mr.Tech technical service application with four main components:

- **mr_tech_mobile/**: Flutter mobile app (main client application)
- **web-portal/**: React TypeScript web portal for admin/technician management  
- **firebase-config/**: Firebase backend configuration, rules, and Cloud Functions
- **landing-page/**: Static HTML/CSS/PHP landing page for the service

## Development Commands

### Flutter Mobile App (mr_tech_mobile/)
```bash
# Development
flutter pub get                    # Install dependencies
flutter run                       # Run on connected device/emulator
flutter run --debug               # Debug mode
flutter run --release             # Release mode

# Building
flutter build apk                 # Build Android APK
flutter build appbundle          # Build Android App Bundle
flutter build ios                # Build iOS (requires macOS)

# Testing & Analysis
flutter test                      # Run tests
flutter analyze                   # Static analysis
flutter pub deps                  # Check dependencies
```

### Web Portal (web-portal/)
```bash
# Development
npm install                       # Install dependencies
npm run dev                       # Start development server
npm run build                     # Build for production
npm run preview                   # Preview production build

# Code Quality
npm run lint                      # Run ESLint
npm run lint:fix                  # Fix ESLint issues
npm run format                    # Format with Prettier
npm run format:check              # Check formatting
npm run type-check                # TypeScript type checking
```

### Firebase Backend (firebase-config/)
```bash
# Setup
npm install                       # Install Firebase CLI dependencies

# Deployment
firebase deploy                   # Deploy all services
firebase deploy --only hosting   # Deploy hosting only
firebase deploy --only functions # Deploy functions only
firebase deploy --only firestore:rules  # Deploy Firestore rules
firebase deploy --only firestore:indexes # Deploy indexes

# Local Development
firebase emulators:start         # Start Firebase emulators
firebase use staging             # Switch to staging project
firebase use production          # Switch to production project
```

## Architecture Overview

### Mobile App Architecture
- **Provider Pattern**: Uses Provider for state management
- **Service Layer**: Centralized services (AuthService, DatabaseService, NotificationService, etc.)
- **Multilingual Support**: Arabic/English with RTL support
- **Firebase Integration**: Firestore, Auth, Messaging, Storage, Realtime Database
- **Security**: App Check, security checker, secure storage

### Web Portal Architecture  
- **React + TypeScript**: Modern web app with type safety
- **Vite**: Fast development build tool
- **ShadCN UI**: Modern component library with Tailwind CSS
- **Zustand**: Lightweight state management
- **Firebase Web SDK**: Authentication, Firestore, Storage integration
- **React Router**: Client-side routing

### Firebase Backend
- **Firestore**: Main database with security rules supporting both mobile and web
- **Realtime Database**: Chat messages and real-time features
- **Cloud Functions**: Notifications, user management, background tasks
- **Storage**: File uploads and media handling
- **Authentication**: User management with role-based access control

## Key Configuration Files

### Mobile App
- `pubspec.yaml`: Dependencies and app configuration
- `firebase_options.dart`: Firebase configuration for Flutter
- `lib/main.dart`: App entry point with Firebase initialization
- `android/app/build.gradle.kts`: Android build configuration

### Web Portal  
- `package.json`: Dependencies and build scripts
- `src/config/firebase.ts`: Firebase web configuration
- `vite.config.ts`: Vite build configuration
- `tsconfig.json`: TypeScript configuration

### Firebase
- `firebase.json`: Firebase project configuration
- `firestore.rules`: Database security rules
- `firestore.indexes.json`: Query optimization indexes
- `functions/src/`: Cloud Functions source code

## Data Models and Database

### Key Collections
- `users`: Customer profiles and settings
- `technicians`: Service provider profiles  
- `requests`: Service requests and status tracking
- `chats`: Chat conversations (references Realtime Database)
- `services`: Available service types
- `admins`: Administrative users

### Realtime Database Structure
- `chats/{chatId}/messages`: Chat messages
- `chats/{chatId}/participants`: Chat participants
- `users/{userId}/status`: Online/offline status

## Environment Variables

### Web Portal (.env)
```
VITE_FIREBASE_API_KEY=
VITE_FIREBASE_AUTH_DOMAIN=
VITE_FIREBASE_PROJECT_ID=
VITE_FIREBASE_STORAGE_BUCKET=
VITE_FIREBASE_MESSAGING_SENDER_ID=
VITE_FIREBASE_APP_ID=
VITE_FIREBASE_DATABASE_URL=
```

### Mobile App (.env)
```
PAYMOB_API_KEY=
PAYMOB_INTEGRATION_ID=
PAYMOB_IFRAME_ID=
PAYMOB_HMAC_SECRET=
```

## Security Considerations

- Firestore rules enforce role-based access control
- App Check enabled for mobile app security
- Secure storage for sensitive data in mobile app
- Environment variables for API keys and secrets
- CORS properly configured for web portal

## Testing

### Mobile App
- Widget tests in `test/` directory
- Run with `flutter test`

### Web Portal  
- Component tests with React Testing Library
- Run with `npm test` (when configured)

## Database Architecture and Patterns

### Key Collections
- `users`: Customer profiles and settings
- `technicians`: Service provider profiles  
- `service_requests`: Service requests and status tracking (updated from old `requests`)
- `chats`: Chat conversations (references Realtime Database)
- `services`: Available service types
- `admins`: Administrative users
- `notifications`: Push notifications
- `payment_transactions`: Payment records
- `reviews`: Customer feedback

### Realtime Database Structure
- `chats/{chatId}/messages`: Chat messages
- `chats/{chatId}/participants`: Chat participants
- `users/{userId}/status`: Online/offline status

### Migration and Field Names
The codebase supports both camelCase and snake_case field names for backward compatibility during migration phases. When working with database queries, be aware that some fields may exist in both formats.

### Security Rules Architecture
- **Role-based Access Control**: Admin, Technician, Customer roles
- **Multi-collection Permission System**: Cross-collection role verification
- **Environment Variable Security**: Sensitive data protection

## Architecture Patterns and Development Guidelines

### Mobile App (Flutter)
- **Service Architecture**: Centralized services with Provider pattern
  - `AppService` acts as the main service coordinator
  - Access services via `Provider.of<ServiceName>(context)`
  - Key services: AuthService, DatabaseService, ChatService, NotificationService
- **State Management**: Provider pattern with singleton services
- **Multilingual Support**: English/Arabic with RTL support via TranslationService
- **Security**: Firebase App Check, Security Checker, Secure Storage
- **Error Handling**: Comprehensive try-catch blocks with proper user feedback
- **UI Guidelines**: Material Design with custom theming via ThemeService

### Web Portal (React + TypeScript)
- **Component Architecture**: ShadCN UI components with Tailwind CSS
- **State Management**: Zustand for lightweight global state
- **Authentication**: AuthContext with protected routes
- **Type Safety**: Comprehensive TypeScript interfaces
- **Error Boundaries**: Proper error handling with React error boundaries
- **Routing**: React Router DOM v7 with role-based access control

### Firebase Backend
- **Database**: Hybrid Firestore (main) + Realtime Database (chat)
- **Security**: Comprehensive Firestore rules with role-based access
- **Functions**: Node.js Cloud Functions for background tasks
- **Storage**: Firebase Storage for file uploads and media

## Deployment

### Mobile App
- Android: Build APK/App Bundle and upload to Play Store
- iOS: Build IPA and upload to App Store Connect

### Web Portal
- Build with `npm run build`
- Deploy to Firebase Hosting with `firebase deploy --only hosting`

### Backend
- Deploy functions: `firebase deploy --only functions`
- Update rules: `firebase deploy --only firestore:rules`

## Key Features and Integrations

### Mobile App Features
- Real-time chat with file sharing capabilities
- Service request lifecycle management
- Payment integration via Paymob (API key required in .env)
- AnyDesk remote support integration
- Push notifications via Firebase Cloud Messaging
- Offline support and data synchronization
- Enhanced rating and review system

### Web Portal Features
- Admin dashboard with analytics
- Technician management and assignment
- Real-time chat interface
- User management with role-based permissions
- Service catalog management
- Payment tracking and transaction history

## Development Workflow with Taskmaster

This project uses Taskmaster for advanced task management. Key patterns from web-portal/.cursor/rules/:

### Basic Workflow
- Start with `task-master list` to see current tasks
- Use `task-master next` to identify next priority task
- Break down complex tasks with `task-master expand --id=<id> --research`
- Mark completed tasks with `task-master set-status --id=<id> --status=done`

### Advanced Patterns
- **Feature Branches**: Create corresponding task tags when creating git branches
- **Team Collaboration**: Use separate tags to prevent task conflicts
- **Large Features**: Use PRD-driven workflow with dedicated tags
- **Version-Based Development**: Tailor complexity based on project maturity (MVP vs production)

### Task Management
- Tasks support dependencies with status indicators
- Use `task-master move` to reorganize task hierarchy
- Update implementation progress with `task-master update-subtask`
- Leverage research-backed expansion with `--research` flag