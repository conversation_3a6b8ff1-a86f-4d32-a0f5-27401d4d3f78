/**
 * Comprehensive Firebase permission checker
 * Helps diagnose authentication and authorization issues
 */

import { getAuth } from 'firebase/auth';
import {
  addDoc,
  collection,
  deleteDoc,
  doc,
  getDoc,
  getDocs,
  limit,
  query,
  updateDoc,
} from 'firebase/firestore';
import { db } from '../config/firebase';

export const comprehensivePermissionTest = async () => {
  const auth = getAuth();
  const user = auth.currentUser;

  if (!user) {
    console.error('❌ No authenticated user found');
    return {
      success: false,
      error: 'Not authenticated',
    };
  }

  console.warn('🔄 Starting comprehensive permission test...');
  console.warn('👤 User:', { uid: user.uid, email: user.email });

  const results: Record<string, any> = {};

  // Test 1: Check user roles
  console.warn('\n1️⃣ Testing user roles...');
  try {
    // Check users collection
    const userDoc = await getDoc(doc(db, 'users', user.uid));
    results.userDoc = {
      exists: userDoc.exists(),
      data: userDoc.exists() ? userDoc.data() : null,
    };
    console.warn('👤 User doc:', results.userDoc);

    // Check admins collection
    const adminDoc = await getDoc(doc(db, 'admins', user.uid));
    results.adminDoc = {
      exists: adminDoc.exists(),
      data: adminDoc.exists() ? adminDoc.data() : null,
    };
    console.warn('👑 Admin doc:', results.adminDoc);

    // Check technicians collection
    const techDoc = await getDoc(doc(db, 'technicians', user.uid));
    results.techDoc = {
      exists: techDoc.exists(),
      data: techDoc.exists() ? techDoc.data() : null,
    };
    console.warn('🔧 Technician doc:', results.techDoc);
  } catch (error) {
    console.error('❌ Error checking user roles:', error);
    results.roleError = error;
  }

  // Test 2: Payment collections read access
  console.warn('\n2️⃣ Testing payment collections read access...');
  const collections = [
    'payment_transactions',
    'payment_responses',
    'payment_methods',
    'test_payments',
  ];

  for (const collectionName of collections) {
    try {
      const q = query(collection(db, collectionName), limit(1));
      const snapshot = await getDocs(q);
      results[`read_${collectionName}`] = {
        success: true,
        count: snapshot.size,
        hasData: !snapshot.empty,
      };
      console.warn(`✅ Can read ${collectionName}: ${snapshot.size} docs`);
    } catch (error) {
      results[`read_${collectionName}`] = {
        success: false,
        error,
      };
      console.error(`❌ Cannot read ${collectionName}:`, error);
    }
  }

  // Test 3: Payment transactions write access
  console.warn('\n3️⃣ Testing payment transactions write access...');
  try {
    // Try to create a test transaction
    const testTransaction = {
      id: `test_${Date.now()}`,
      paymobTransactionId: `test_paymob_${Date.now()}`,
      amount: 1.0,
      currency: 'EGP',
      status: 'test',
      paymentMethod: 'test',
      isLive: false,
      createdAt: new Date(),
      processedAt: new Date(),
      customerId: user.uid, // Set current user as customer
    };

    const docRef = await addDoc(collection(db, 'payment_transactions'), testTransaction);
    results.write_payment_transactions = {
      success: true,
      docId: docRef.id,
    };
    console.warn('✅ Can write to payment_transactions:', docRef.id);

    // Try to update the test transaction
    await updateDoc(docRef, { status: 'test_updated' });
    results.update_payment_transactions = { success: true };
    console.warn('✅ Can update payment_transactions');

    // Try to delete the test transaction
    await deleteDoc(docRef);
    results.delete_payment_transactions = { success: true };
    console.warn('✅ Can delete payment_transactions');
  } catch (error) {
    results.write_payment_transactions = {
      success: false,
      error,
    };
    console.error('❌ Cannot write to payment_transactions:', error);
  }

  // Test 4: Payment responses write access
  console.warn('\n4️⃣ Testing payment responses write access...');
  try {
    const testResponse = {
      transactionId: `test_${Date.now()}`,
      success: true,
      paymentStatus: 'test',
      responseParams: { test: true },
      createdAt: new Date(),
      customerId: user.uid,
    };

    const docRef = await addDoc(collection(db, 'payment_responses'), testResponse);
    results.write_payment_responses = {
      success: true,
      docId: docRef.id,
    };
    console.warn('✅ Can write to payment_responses:', docRef.id);

    // Cleanup
    await deleteDoc(docRef);
    console.warn('✅ Test cleanup completed');
  } catch (error) {
    results.write_payment_responses = {
      success: false,
      error,
    };
    console.error('❌ Cannot write to payment_responses:', error);
  }

  console.warn('\n📊 Test Results Summary:');
  console.table(results);

  return {
    success: true,
    results,
    recommendations: generateRecommendations(results),
  };
};

const generateRecommendations = (results: Record<string, any>) => {
  const recommendations: string[] = [];

  // Check if user has proper role setup
  if (!results.userDoc?.exists) {
    recommendations.push('User document missing in users collection - create user profile');
  }

  if (!results.adminDoc?.exists && !results.techDoc?.exists) {
    recommendations.push('User has no admin or technician role - assign appropriate role');
  }

  // Check payment collection access
  if (!results.read_payment_transactions?.success) {
    recommendations.push('Cannot read payment_transactions - check Firestore rules');
  }

  if (!results.read_payment_responses?.success) {
    recommendations.push('Cannot read payment_responses - check Firestore rules');
  }

  if (!results.write_payment_transactions?.success) {
    recommendations.push(
      'Cannot write to payment_transactions - ensure user has admin/technician role',
    );
  }

  if (recommendations.length === 0) {
    recommendations.push('All tests passed! Permissions are properly configured.');
  }

  return recommendations;
};

// Export for console access
if (typeof window !== 'undefined') {
  (window as any).firebasePermissionTest = comprehensivePermissionTest;
}
