import 'dart:async';
import 'dart:io';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:path/path.dart' as path;
import '../models/chat_message_model.dart';
import 'database_service.dart';
import 'notification_service.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/request_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/foundation.dart';

class ChatService {
  // Singleton instance
  static final ChatService _instance = ChatService._internal();

  factory ChatService() => _instance;

  ChatService._internal() {
    _databaseService = DatabaseService();
    _notificationService = NotificationService();
  }

  // Database service instance
  late DatabaseService _databaseService;
  late NotificationService _notificationService;
  String? _currentUserId;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseDatabase _realtimeDb = FirebaseDatabase.instance;
  final Map<String, StreamSubscription<List<ChatMessageModel>>>
  _messageSubscriptions = {};
  final Map<String, List<ChatMessageModel>> _messageCache = {};
  final Map<String, StreamController<List<ChatMessageModel>>>
  _streamControllers = {};
  final Map<String, StreamSubscription<List<ChatMessageModel>>> _listeners = {};
  final Map<String, StreamSubscription> _typingListeners = {};

  // Set current user ID
  void setCurrentUserId(String userId) {
    _currentUserId = userId;
    debugPrint('Chat service - current user ID set to: $userId');
  }

  // Get current user from Firebase Auth
  String? getCurrentUserId() {
    try {
      // Get current user from FirebaseAuth if not already set
      if (_currentUserId == null || _currentUserId!.isEmpty) {
        final user = FirebaseAuth.instance.currentUser;
        if (user != null) {
          _currentUserId = user.uid;
          debugPrint(
            'Chat service - retrieved user ID from FirebaseAuth: ${user.uid}',
          );
        } else {
          debugPrint(
            'Chat service - no authenticated user found in FirebaseAuth',
          );
          // Try to force a reload of the user
          FirebaseAuth.instance
              .authStateChanges()
              .first
              .then((user) {
                if (user != null) {
                  _currentUserId = user.uid;
                  debugPrint(
                    'Chat service - retrieved user ID from auth state changes: ${user.uid}',
                  );
                }
              })
              .catchError((e) {
                debugPrint('Chat service - error refreshing auth state: $e');
              });
        }
      }

      if (_currentUserId == null) {
        debugPrint('WARNING: No authenticated user found');
      }

      return _currentUserId;
    } catch (e) {
      debugPrint('Error in getCurrentUserId: $e');
      return null;
    }
  }

  // Get active chats for the current user
  Future<List<Map<String, dynamic>>> getActiveChats() async {
    try {
      // Get current user ID from Firebase Auth if not set
      final userId = getCurrentUserId();
      if (userId == null) {
        debugPrint('getActiveChats: User not authenticated');
        throw Exception('User not authenticated');
      }

      // Get all requests for current user where chat is active
      final activeRequests = await _databaseService.getActiveChatsForUser(
        userId,
      );

      // Process each request to create a chat item
      final List<Map<String, dynamic>> chats = [];

      for (final request in activeRequests) {
        // Get the last message for this request
        final lastMessageInfo = await _getLastMessageInfo(request.id);

        // Get unread count for this chat
        final unreadCount = await getUnreadMessageCount(request.id);

        // Get technician initials for avatar
        String technicianAvatar = 'T';
        if (request.technicianName != null &&
            request.technicianName!.isNotEmpty) {
          final nameParts = request.technicianName!.split(' ');
          if (nameParts.length > 1) {
            technicianAvatar = '${nameParts[0][0]}${nameParts[1][0]}';
          } else {
            technicianAvatar = nameParts[0][0];
          }
        }

        // Create a chat item
        chats.add({
          'id': 'chat-${request.id}',
          'requestId': request.id,
          'technician': request.technicianName ?? 'Technician',
          'technicianAvatar': technicianAvatar,
          'serviceName': request.serviceName,
          'lastMessage': lastMessageInfo['content'] ?? 'No messages yet',
          'timestamp':
              lastMessageInfo['timestamp'] ??
              request.updatedAt ??
              request.createdAt,
          'unreadCount': unreadCount,
          'isOnline':
              false, // This would require online status from the backend
        });
      }

      // Sort by most recent message first
      chats.sort((a, b) {
        final DateTime timeA = a['timestamp'] as DateTime;
        final DateTime timeB = b['timestamp'] as DateTime;
        return timeB.compareTo(timeA);
      });

      return chats;
    } catch (e) {
      debugPrint('Error getting active chats: $e');
      return [];
    }
  }

  // Helper method to get the last message info
  Future<Map<String, dynamic>> _getLastMessageInfo(String requestId) async {
    try {
      final messages = await _databaseService.getChatMessages(requestId);

      if (messages.isNotEmpty) {
        // Sort by most recent first
        messages.sort((a, b) => b.createdAt.compareTo(a.createdAt));

        return {
          'content': messages.first.content,
          'timestamp': messages.first.createdAt,
          'senderId': messages.first.senderId,
        };
      }

      return {'content': null, 'timestamp': null, 'senderId': null};
    } catch (e) {
      debugPrint('Error getting last message: $e');
      return {'content': null, 'timestamp': null, 'senderId': null};
    }
  }

  // Initialize chat for a request
  // This should ONLY be called after a technician accepts the request
  Future<void> initializeChat(
    String requestId, {
    String? technicianName,
  }) async {
    try {
      // Check if user is authenticated - use getCurrentUserId to ensure we try FirebaseAuth
      final userId = getCurrentUserId();
      if (userId == null) {
        debugPrint('initializeChat: User not authenticated');
        throw Exception('User not authenticated');
      }

      debugPrint(
        'Initializing chat for request: $requestId with technician: $technicianName',
      );

      // First check if the chat is already active
      final isActive = await isChatActive(requestId);
      if (isActive) {
        debugPrint('Chat is already active for request: $requestId, skipping initialization');
        return; // Chat is already active, no need to initialize again
      }

      // Double-check by querying the request document directly
      final requestDoc = await _firestore.collection('service_requests').doc(requestId).get();
      if (requestDoc.exists) {
        final data = requestDoc.data() as Map<String, dynamic>;
        final chatActive = data['chat_active'] == true || data['chatActive'] == true;

        if (chatActive) {
          debugPrint('Chat already active in Firestore for request: $requestId, skipping initialization');
          return;
        }
      }

      // Create a more personalized system message if technician name is provided
      final welcomeMessage =
          technicianName != null
              ? 'Chat is now active. $technicianName will assist you shortly.'
              : 'Chat is now active. The technician will assist you shortly.';

      final message = ChatMessageModel(
        requestId: requestId,
        senderType: SenderType.system,
        senderId: 'system',
        messageType: MessageType.system,
        content: welcomeMessage,
        createdAt: DateTime.now(),
        isRead: true, // System messages are always read
      );

      debugPrint('Sending welcome message to request: $requestId');

      // Send the message using database service
      await _databaseService.sendChatMessage(message, requestId);

      // Update the request chat status
      debugPrint('Updating request chat status to active for: $requestId');
      await _databaseService.updateRequestChatStatus(requestId, true);

      // Set up a listener for messages on this chat
      debugPrint('Setting up chat listener for request: $requestId');
      setupChatListener(requestId);

      debugPrint('Chat successfully initialized for request: $requestId');
    } catch (e) {
      debugPrint('Failed to initialize chat: $e');
      throw Exception('Failed to initialize chat: ${e.toString()}');
    }
  }

  // Setup chat listener for request
  Future<void> setupChatListener(String requestId) async {
    try {
      debugPrint('Setting up chat listener for request: $requestId');
      // Make sure we're not setting up duplicate listeners
      _listeners.remove(requestId);

      // Create a new listener for this request
      final listener = _databaseService
          .streamChatMessages(requestId)
          .listen(
            (messages) {
              // Cache the messages
              _messageCache[requestId] = messages;

              // Notify the stream controllers for this request
              if (_streamControllers[requestId] != null) {
                _streamControllers[requestId]!.add(messages);
              }

              // Mark all technician messages as read
              if (messages.isNotEmpty) {
                final unreadMessages =
                    messages
                        .where(
                          (msg) =>
                              msg.senderType == SenderType.technician &&
                              !msg.isRead,
                        )
                        .toList();

                if (unreadMessages.isNotEmpty) {
                  _markMessagesAsReadInternal(requestId, unreadMessages);
                }
              }
            },
            onError: (e) {
              debugPrint('Error in chat listener: $e');

              // Handle permission errors gracefully
              if (e.toString().contains('permission') ||
                  e.toString().contains('denied')) {
                debugPrint(
                  'Permission error in chat listener, user may not have access to this chat',
                );
              }
            },
          );

      // Store the listener
      _listeners[requestId] = listener;
    } catch (e) {
      debugPrint('Error setting up chat listener: $e');
    }
  }

  // Mark messages as read internally without triggering state updates
  Future<void> _markMessagesAsReadInternal(
    String requestId,
    List<ChatMessageModel> messages,
  ) async {
    try {
      final batch = _firestore.batch();
      final realtimeUpdatePromises = <Future>[];

      for (final message in messages) {
        if (message.senderType == SenderType.technician && !message.isRead) {
          // Update in Firestore batch
          final docRef = _firestore
              .collection('service_requests')
              .doc(requestId)
              .collection('messages')
              .doc(message.id);

          batch.update(docRef, {'read': true, 'isRead': true});

          // Also update in Realtime Database (don't wait for completion)
          final updatePromise = _realtimeDb
              .ref()
              .child('messages')
              .child(requestId)
              .child(message.id)
              .update({'read': true, 'isRead': true})
              .catchError((error) {
                debugPrint('Error updating RTDB message ${message.id}: $error');
                if (error.toString().contains('PERMISSION_DENIED')) {
                  debugPrint('Permission denied for RTDB message ${message.id}');
                }
                // Don't rethrow - continue with other updates
                return null;
              });

          realtimeUpdatePromises.add(updatePromise);
        }
      }

      // Commit Firestore batch
      await batch.commit();

      // Wait for all Realtime Database updates to complete
      await Future.wait(realtimeUpdatePromises);
    } catch (e) {
      debugPrint('Error marking messages as read internally: $e');
      // Don't rethrow as this is an internal optimization
    }
  }

  /// Set typing indicator for current user
  Future<void> setTypingIndicator(
    String requestId,
    String userId,
    String userName,
    bool isTyping,
  ) async {
    try {
      final typingData = {
        'is_typing': isTyping,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'user_name': userName,
      };

      if (isTyping) {
        await _realtimeDb
            .ref()
            .child('typing')
            .child(requestId)
            .child(userId)
            .set(typingData);
      } else {
        await _realtimeDb
            .ref()
            .child('typing')
            .child(requestId)
            .child(userId)
            .remove();
      }
    } catch (e) {
      debugPrint('Error setting typing indicator: $e');
    }
  }

  /// Listen for typing indicators
  StreamSubscription? listenToTypingIndicators(
    String requestId,
    Function(List<Map<String, dynamic>>) callback,
  ) {
    try {
      final typingRef = _realtimeDb.ref().child('typing').child(requestId);

      final subscription = typingRef.onValue.listen((event) {
        final List<Map<String, dynamic>> typingUsers = [];

        if (event.snapshot.exists) {
          final data = event.snapshot.value as Map<dynamic, dynamic>?;
          if (data != null) {
            data.forEach((userId, userData) {
              if (userData is Map && userData['is_typing'] == true) {
                typingUsers.add({
                  'userId': userId.toString(),
                  'userName': userData['user_name']?.toString() ?? 'Unknown',
                  'isTyping': userData['is_typing'] ?? false,
                  'timestamp': userData['timestamp'] ?? 0,
                });
              }
            });
          }
        }

        callback(typingUsers);
      });

      _typingListeners[requestId] = subscription;
      return subscription;
    } catch (e) {
      debugPrint('Error setting up typing indicator listener: $e');
      return null;
    }
  }

  /// Stop listening to typing indicators for a request
  void stopTypingListener(String requestId) {
    final subscription = _typingListeners[requestId];
    if (subscription != null) {
      subscription.cancel();
      _typingListeners.remove(requestId);
    }
  }

  // Helper to trigger notification for new messages
  Future<void> _notifyNewMessage(
    String requestId,
    ChatMessageModel message,
  ) async {
    try {
      // Don't notify for system messages
      if (message.senderType == SenderType.system) return;

      // Import the NotificationService only when needed to avoid circular dependencies
      // This is a workaround - in a production app, consider using a proper dependency injection solution
      final notificationService = NotificationService();

      // Format the sender name
      String senderName = 'Technician';
      if (message.senderType == SenderType.technician) {
        // Try to get the technician name from the request
        final request = await _databaseService.getRequestById(requestId);
        if (request?.technicianName != null &&
            request!.technicianName!.isNotEmpty) {
          senderName = request.technicianName!;
        }
      }

      // Create a notification
      await notificationService.createLocalNotification(
        title: 'New message from $senderName',
        body: message.content,
        type: 'new_message',
        requestId: requestId,
      );
    } catch (e) {
      debugPrint('Error creating notification for new message: $e');
    }
  }

  // Check if chat is active for a request with more detailed response
  Future<Map<String, dynamic>> getChatStatus(String requestId) async {
    try {
      final request = await _databaseService.getRequestById(requestId);

      if (request == null) {
        return {
          'active': false,
          'reason': 'request_not_found',
          'message': 'Request not found',
        };
      }

      // If request is completed, chat should be disabled
      if (request.status == RequestStatus.completed) {
        return {
          'active': false,
          'reason': 'request_completed',
          'message':
              'This request has been completed and the chat is now closed',
          'status': request.status.toString().split('.').last,
        };
      }

      if (!request.chatActive) {
        return {
          'active': false,
          'reason': 'chat_not_activated',
          'message': 'Chat has not been activated by the technician yet',
          'status': request.status.toString().split('.').last,
        };
      }

      return {
        'active': true,
        'technicianName': request.technicianName,
        'technicianId': request.technicianId,
      };
    } catch (e) {
      return {
        'active': false,
        'reason': 'error',
        'message': 'Error checking chat status: ${e.toString()}',
      };
    }
  }

  // Check if chat is active for a request
  Future<bool> isChatActive(String requestId) async {
    try {
      final request = await _databaseService.getRequestById(requestId);
      if (request == null) {
        debugPrint('isChatActive: Request not found: $requestId');
        return false;
      }

      // If request is completed, chat is not active regardless of the chatActive flag
      if (request.status == RequestStatus.completed) {
        debugPrint(
          'isChatActive: Request is completed, chat is disabled: $requestId',
        );
        return false;
      }

      debugPrint(
        'isChatActive: Chat active status for request $requestId: ${request.chatActive}',
      );
      return request.chatActive;
    } catch (e) {
      debugPrint('Error checking if chat is active: $e');
      return false;
    }
  }

  // Send a message to a chat
  Future<void> sendMessage({
    required String requestId,
    required String content,
    MessageType type = MessageType.text,
    String? fileUrl,
  }) async {
    try {
      final userId = getCurrentUserId();
      if (userId == null) {
        debugPrint('sendMessage: User not authenticated');
        throw Exception('User not authenticated');
      }

      debugPrint('Sending message from user $userId for request $requestId');

      // Check if chat is active
      final isActive = await isChatActive(requestId);
      if (!isActive && type != MessageType.system) {
        debugPrint('Chat is not active for request $requestId');
        throw Exception('Chat is not active for this request');
      }

      // Get the request to determine the user's role
      final request = await _databaseService.getRequestById(requestId);
      if (request == null) {
        debugPrint('Request $requestId not found');
        throw Exception('Request not found');
      }

      // Determine sender type
      SenderType senderType;
      bool isAdmin = false;

      // Check for admin role - try to get user data
      try {
        final userDoc = await _firestore.collection('users').doc(userId).get();
        if (userDoc.exists) {
          final userData = userDoc.data() as Map<String, dynamic>;
          isAdmin = userData['role'] == 'admin';
        }

        // If not found in users, check admins collection
        if (!isAdmin) {
          try {
            final adminDoc =
                await _firestore.collection('admins').doc(userId).get();
            isAdmin = adminDoc.exists;
          } catch (adminCheckError) {
            debugPrint('Error checking admin collection: $adminCheckError');
            // If we can't check admin status, assume not admin and continue
            isAdmin = false;
          }
        }
      } catch (e) {
        debugPrint('Error checking admin status: $e');
        // Continue with non-admin flow
        isAdmin = false;
      }

      // If user is admin, allow them to send messages with customer or technician type
      if (isAdmin) {
        // Default to technician type for admins
        senderType = SenderType.technician;
      } else if (userId == request.customerId) {
        senderType = SenderType.customer;
      } else if (userId == request.technicianId) {
        senderType = SenderType.technician;
      } else {
        // Unexpected user - let's check if they're a technician
        final technicianDoc =
            await _firestore.collection('technicians').doc(userId).get();
        if (technicianDoc.exists) {
          senderType = SenderType.technician;
        } else {
          debugPrint('User is not authorized to send messages in this chat');
          throw Exception(
            'User is not authorized to send messages in this chat',
          );
        }
      }

      // Create and send message
      final message = ChatMessageModel(
        requestId: requestId,
        senderId: userId,
        senderType: senderType,
        messageType: type,
        content: content,
        createdAt: DateTime.now(),
        isRead: false,
        fileUrl: fileUrl,
      );

      await _databaseService.sendChatMessage(message, requestId);

      // Update request's last activity timestamp
      await _firestore.collection('service_requests').doc(requestId).update({
        'updated_at': FieldValue.serverTimestamp(),
        'last_activity': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      debugPrint('Error sending chat message: $e');
      throw Exception('Failed to send message: ${e.toString()}');
    }
  }

  // Send file message
  Future<void> sendFileMessage({
    required String requestId,
    required File file,
    String? caption,
    Function(double)? onProgress,
  }) async {
    try {
      // Determine file type
      final String fileName = path.basename(file.path);
      final String extension = path.extension(fileName).toLowerCase();
      final MessageType messageType = _getMessageTypeFromExtension(extension);

      // Create storage reference
      final storageRef = FirebaseStorage.instance
          .ref()
          .child('chat_files')
          .child(requestId)
          .child('${DateTime.now().millisecondsSinceEpoch}_$fileName');

      // Upload file with progress tracking
      final uploadTask = storageRef.putFile(file);

      // Listen for progress updates
      if (onProgress != null) {
        uploadTask.snapshotEvents.listen((TaskSnapshot snapshot) {
          final progress = snapshot.bytesTransferred / snapshot.totalBytes;
          onProgress(progress);
        });
      }

      // Wait for completion
      final snapshot = await uploadTask;

      // Get download URL
      final String fileUrl = await snapshot.ref.getDownloadURL();

      // Send message with named parameters
      await sendMessage(
        requestId: requestId,
        content: caption ?? fileName,
        type: messageType,
        fileUrl: fileUrl,
      );

      return;
    } catch (e) {
      debugPrint('Error sending file message: $e');
      throw Exception('Failed to send file message: ${e.toString()}');
    }
  }

  // Helper method to determine message type from file extension
  MessageType _getMessageTypeFromExtension(String extension) {
    if (['.jpg', '.jpeg', '.png', '.gif', '.webp'].contains(extension)) {
      return MessageType.image;
    } else {
      return MessageType.file;
    }
  }

  // Listen to messages for a specific request
  Stream<List<ChatMessageModel>> listenToMessages(String requestId) {
    try {
      // Cancel any existing subscription first to prevent duplicates
      cancelMessageSubscription(requestId);

      // Get the stream of messages
      final messagesStream = _databaseService.streamChatMessages(requestId);

      // Create a subscription that marks messages as read
      final subscription = messagesStream.listen(
        (messages) {
          // Find messages from technician that are not read
          final unreadMessages =
              messages
                  .where(
                    (msg) =>
                        msg.senderType == SenderType.technician &&
                        !msg.isRead &&
                        msg.senderId != _currentUserId,
                  )
                  .toList();

          // Mark them as read
          if (unreadMessages.isNotEmpty) {
            _markMessagesAsReadAsync(requestId, unreadMessages);
          }
        },
        onError: (error) {
          debugPrint('Error in message subscription: $error');
        },
      );

      // Store the subscription to cancel later if needed
      _messageSubscriptions[requestId] = subscription;

      // Return the original stream
      return messagesStream;
    } catch (e) {
      debugPrint('Error listening to messages: $e');
      return Stream.value([]);
    }
  }

  // Helper method to mark messages as read asynchronously
  Future<void> _markMessagesAsReadAsync(
    String requestId,
    List<ChatMessageModel> messages,
  ) async {
    try {
      for (final message in messages) {
        // Update both Firestore and Realtime Database
        await _databaseService.updateChatMessage(requestId, message.id, {
          'is_read': true,
          'read': true,
        });
      }
    } catch (e) {
      debugPrint('Error marking messages as read: $e');
    }
  }

  // Mark all messages in a chat as read
  Future<void> markMessagesAsRead(String requestId) async {
    try {
      if (_currentUserId == null) {
        throw Exception('User not authenticated');
      }

      // Get all messages
      final messages = await _databaseService.getChatMessages(requestId);

      // Find messages that are not from current user and are unread
      final unreadMessages =
          messages
              .where((msg) => msg.senderId != _currentUserId && !msg.isRead)
              .toList();

      // Mark them as read
      for (final message in unreadMessages) {
        await _databaseService.updateChatMessage(requestId, message.id, {
          'is_read': true,
          'read': true,
        });
      }
    } catch (e) {
      debugPrint('Error marking messages as read: $e');
      throw Exception('Failed to mark messages as read: ${e.toString()}');
    }
  }

  // Get unread message count for a chat
  Future<int> getUnreadMessageCount(String requestId) async {
    try {
      if (_currentUserId == null) {
        return 0;
      }

      final messages = await _databaseService.getChatMessages(requestId);

      // Count messages that are not from current user and are unread
      return messages
          .where(
            (message) =>
                message.senderId != _currentUserId &&
                !message.isRead &&
                message.senderType != SenderType.system,
          )
          .length;
    } catch (e) {
      debugPrint('Error getting unread count: $e');
      return 0;
    }
  }

  // Cancel a specific message subscription
  void cancelMessageSubscription(String requestId) {
    final subscription = _messageSubscriptions[requestId];
    if (subscription != null) {
      subscription.cancel();
      _messageSubscriptions.remove(requestId);
    }
  }

  // Dispose resources
  void dispose() {
    // Cancel all active subscriptions
    for (final subscription in _messageSubscriptions.values) {
      subscription.cancel();
    }
    _messageSubscriptions.clear();
    _currentUserId = null;
  }

  // Get stream of messages for a request
  Stream<List<ChatMessageModel>> getMessagesStream(String requestId) {
    // Create a stream controller if it doesn't exist
    if (_streamControllers[requestId] == null) {
      _streamControllers[requestId] =
          StreamController<List<ChatMessageModel>>.broadcast();

      // If we have cached messages, emit them immediately
      if (_messageCache.containsKey(requestId)) {
        _streamControllers[requestId]!.add(_messageCache[requestId]!);
      }

      // Make sure we have an active listener
      if (_listeners[requestId] == null) {
        setupChatListener(requestId);
      }
    }

    return _streamControllers[requestId]!.stream;
  }

  // Disable chat when request is completed
  Future<void> disableChatForCompletedRequest(String requestId) async {
    try {
      // Check if chat is active
      final isActive = await isChatActive(requestId);

      // If chat is active, disable it
      if (isActive) {
        debugPrint(
          'ChatService: Disabling chat for completed request: $requestId',
        );

        // Create a completion message
        final message = ChatMessageModel(
          requestId: requestId,
          senderType: SenderType.system,
          senderId: 'system',
          messageType: MessageType.system,
          content:
              'This request has been completed and the chat is now closed.',
          createdAt: DateTime.now(),
          isRead: true,
        );

        // Send the message
        await _databaseService.sendChatMessage(message, requestId);

        // Update the request chat status to inactive
        await _databaseService.updateRequestChatStatus(requestId, false);
      }
    } catch (e) {
      debugPrint('ChatService: Error disabling chat for completed request: $e');
    }
  }

  // Dispose resources for a request
  void disposeResources(String requestId) {
    // Cancel listeners
    _listeners[requestId]?.cancel();
    _listeners.remove(requestId);

    // Close stream controllers
    _streamControllers[requestId]?.close();
    _streamControllers.remove(requestId);

    // Clear cache
    _messageCache.remove(requestId);
  }

  // Listen for chat activation notifications
  Future<void> setupChatActivationListener() async {
    try {
      final userId = getCurrentUserId();
      if (userId == null) {
        debugPrint('setupChatActivationListener: User not authenticated');
        return;
      }

      debugPrint(
        'Setting up chat activation notification listener for user $userId',
      );

      // Create NotificationService reference (needed for local notifications)
      final notificationService = NotificationService();

      // Track processed notification IDs to prevent duplicate processing
      final Set<String> processedIds = {};

      // Listen to the notifications collection for chat activation notifications with broader type matching
      _firestore
          .collection('notifications')
          .where('user_id', isEqualTo: userId)
          .where(
            'type',
            whereIn: [
              'CHAT_ACTIVATION',
              'chat_activation',
              'CHAT_STARTED',
              'chat_started',
            ],
          )
          .orderBy('timestamp', descending: true)
          .limit(10)
          .snapshots()
          .listen(
            (snapshot) async {
              if (snapshot.docs.isEmpty) {
                debugPrint(
                  'No chat activation notifications found in listener',
                );
                return;
              }

              debugPrint(
                'Chat activation listener found ${snapshot.docs.length} notifications',
              );

              for (final doc in snapshot.docs) {
                try {
                  // Skip if we've already processed this notification in memory
                  if (processedIds.contains(doc.id)) {
                    debugPrint(
                      'Skipping already processed notification (in memory): ${doc.id}',
                    );
                    continue;
                  }

                  final data = doc.data();
                  final String requestId = data['requestId'] ?? '';
                  final String technicianName =
                      data['technicianName'] ??
                      data['technician_name'] ??
                      'Technician';
                  final bool isDelivered = data['delivered'] == true;
                  final bool isProcessed =
                      data['processed'] == true ||
                      data['processed_by_app'] == true;

                  // Skip if already processed according to Firestore
                  if (isProcessed) {
                    processedIds.add(doc.id); // Remember we've seen this one
                    debugPrint(
                      'Skipping already processed notification ${doc.id}',
                    );
                    continue;
                  }

                  // Only process if requestId exists and notification hasn't been delivered/processed yet
                  if (requestId.isNotEmpty && (!isDelivered || !isProcessed)) {
                    debugPrint(
                      'Processing chat activation notification for request $requestId from ${doc.id}',
                    );

                    // Update request chat status locally - this is the important part
                    await _databaseService.updateRequestChatStatus(
                      requestId,
                      true,
                    );
                    debugPrint('Chat status updated successfully to true');

                    // Don't create additional notifications - the FCM system already handles this
                    debugPrint('Chat activation processed via Firestore listener - notification already handled by FCM');

                    // Add to our processed set even before we try to update Firestore
                    // This way, even if the update fails, we won't try to process it again
                    processedIds.add(doc.id);

                    // Try to mark notification as delivered and processed
                    try {
                      await doc.reference.update({
                        'delivered': true,
                        'delivered_at': FieldValue.serverTimestamp(),
                        'processed_by_app': true,
                        'processed': true,
                        'read': false, // Let user still see it as unread
                      });
                      debugPrint(
                        'Chat activation notification processed successfully',
                      );
                    } catch (updateError) {
                      // If we get a permission error, log it but don't rethrow
                      // We've already performed the important action (updating chat status)
                      debugPrint(
                        'Error updating notification in Firestore: $updateError',
                      );
                      debugPrint(
                        'Request chat status was already updated, continuing...',
                      );
                    }
                  }
                } catch (e) {
                  debugPrint(
                    'Error processing chat activation notification: $e',
                  );
                }
              }
            },
            onError: (e) {
              debugPrint('Error in chat activation listener: $e');

              // If we get a permission error, try a simpler approach
              if (e.toString().contains('permission') ||
                  e.toString().contains('denied') ||
                  e.toString().contains('index')) {
                debugPrint('Chat activation listener failed, using fallback approach');
                final userId = getCurrentUserId();
                if (userId != null) {
                  _setupSimpleChatActivationListener(userId);
                }
              }
            },
          );

      debugPrint('Chat activation listener setup complete');
    } catch (e) {
      debugPrint('Error setting up chat activation listener: $e');
      // Try fallback approach
      final userId = getCurrentUserId();
      if (userId != null) {
        _setupSimpleChatActivationListener(userId);
      }
    }
  }

  // Fallback chat activation listener with simpler queries
  Future<void> _setupSimpleChatActivationListener(String userId) async {
    try {
      debugPrint('Setting up simple chat activation listener for user $userId');

      // Listen to notifications collection with just user_id filter
      _firestore
          .collection('notifications')
          .where('user_id', isEqualTo: userId)
          .snapshots()
          .listen(
            (snapshot) async {
              for (final doc in snapshot.docs) {
                try {
                  final data = doc.data();
                  final type = data['type']?.toString().toLowerCase() ?? '';

                  // Check if it's a chat activation notification
                  if (type.contains('chat') &&
                      (type.contains('activation') || type.contains('started'))) {

                    final requestId = data['request_id']?.toString() ?? '';
                    final isDelivered = data['delivered'] == true;
                    final isProcessed = data['processed'] == true;

                    if (requestId.isNotEmpty && (!isDelivered || !isProcessed)) {
                      debugPrint('Processing simple chat activation for request $requestId');

                      // Update request chat status
                      await _databaseService.updateRequestChatStatus(requestId, true);

                      // Mark as processed
                      try {
                        await doc.reference.update({
                          'delivered': true,
                          'processed': true,
                          'processed_at': FieldValue.serverTimestamp(),
                        });
                      } catch (updateError) {
                        debugPrint('Error updating notification: $updateError');
                      }
                    }
                  }
                } catch (e) {
                  debugPrint('Error processing notification in simple listener: $e');
                }
              }
            },
            onError: (e) {
              debugPrint('Error in simple chat activation listener: $e');
            },
          );
    } catch (e) {
      debugPrint('Error setting up simple chat activation listener: $e');
    }
  }
}
