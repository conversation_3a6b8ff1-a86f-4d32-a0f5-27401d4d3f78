import { type FirebaseApp, initializeApp } from 'firebase/app';
import { type Auth, getAuth } from 'firebase/auth';
import { type Firestore, connectFirestoreEmulator, initializeFirestore } from 'firebase/firestore';
import { type FirebaseStorage, getStorage } from 'firebase/storage';
import { type Database, getDatabase } from 'firebase/database';
import { type Messaging, getMessaging } from 'firebase/messaging';

// Debug: Log raw env values
if (import.meta.env.DEV) {
  console.warn('Raw env vars:', {
    VITE_FIREBASE_API_KEY: import.meta.env.VITE_FIREBASE_API_KEY || 'not set',
    NODE_ENV: import.meta.env.NODE_ENV,
    DEV: import.meta.env.DEV,
    PROD: import.meta.env.PROD,
  });
}

// Firebase configuration - will be set from environment variables
const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY?.trim(),
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN?.trim(),
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID?.trim(),
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET?.trim(),
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID?.trim(),
  appId: import.meta.env.VITE_FIREBASE_APP_ID?.trim(),
  databaseURL: import.meta.env.VITE_FIREBASE_DATABASE_URL?.trim(),
};

// Debug: Log processed config
if (import.meta.env.DEV) {
  console.warn('Processed Firebase Config:', {
    apiKey: firebaseConfig.apiKey ? `${firebaseConfig.apiKey.slice(0, 8)}...` : 'missing',
    authDomain: firebaseConfig.authDomain || 'missing',
    projectId: firebaseConfig.projectId || 'missing',
    hasStorageBucket: !!firebaseConfig.storageBucket,
    hasMessagingSenderId: !!firebaseConfig.messagingSenderId,
    hasAppId: !!firebaseConfig.appId,
    hasDatabaseUrl: !!firebaseConfig.databaseURL,
    apiKeyLength: firebaseConfig.apiKey?.length || 0,
  });
}

// Validate Firebase configuration
const validateConfig = () => {
  const requiredKeys = ['apiKey', 'authDomain', 'projectId', 'appId'];
  const missingKeys = requiredKeys.filter(
    (key) => !firebaseConfig[key as keyof typeof firebaseConfig],
  );

  if (missingKeys.length > 0) {
    const message = `Missing Firebase configuration: ${missingKeys.join(', ')}`;
    if (import.meta.env.DEV) {
      console.warn(message);
      console.warn('Using Firebase emulators or disabled Firebase features in development mode.');
    } else {
      console.error('Missing Firebase configuration keys:', missingKeys);
      throw new Error(message);
    }
  }

  // Additional validation for API key format
  const apiKey = firebaseConfig.apiKey;
  if (apiKey && (!apiKey.startsWith('AIza') || apiKey.length < 30)) {
    throw new Error(
      'Firebase API key appears to be invalid. It should start with "AIza" and be at least 30 characters long.',
    );
  }
};

// Initialize Firebase with error handling
let app;
try {
  validateConfig();
  app = initializeApp(firebaseConfig);
  console.warn('Firebase app initialized successfully');
} catch (error) {
  console.error('Failed to initialize Firebase app:', error);
  throw error;
}

// Initialize Firebase services with error handling
let auth: Auth;
let db: Firestore;
let storage: FirebaseStorage;
let realtimeDb: Database;
let messaging: Messaging | null = null;

try {
  auth = getAuth(app);
  console.warn('Firebase Auth initialized');

  db = initializeFirestore(app, {
    ignoreUndefinedProperties: true,
    experimentalForceLongPolling: true,
  });
  console.warn('Firestore initialized');

  storage = getStorage(app);
  console.warn('Firebase Storage initialized');

  realtimeDb = getDatabase(app);
  console.warn('Realtime Database initialized');

  if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
    try {
      messaging = getMessaging(app);
      console.warn('Firebase Messaging initialized');
    } catch (error) {
      console.warn('Firebase Messaging not available:', error);
    }
  }
} catch (error) {
  console.error('Error initializing Firebase services:', error);
  throw error;
}

export { auth, db, storage, realtimeDb, messaging };
export default app;
