import { type MenuItem, Permission } from '../types/permissions';
import {
  Activity,
  <PERSON><PERSON>hart,
  CreditCard,
  FileText,
  FolderOpen,
  LayoutDashboard,
  MessageSquare,
  Settings,
  Shield,
  UserCheck,
  UserCog,
  Users,
  Wrench,
} from 'lucide-react';

export const navigationItems: MenuItem[] = [
  {
    id: 'dashboard',
    label: 'Dashboard',
    icon: 'LayoutDashboard',
    path: '/dashboard',
    permissions: [Permission.VIEW_DASHBOARD],
  },
  {
    id: 'requests',
    label: 'Requests',
    icon: 'FileText',
    path: '/requests',
    permissions: [Permission.VIEW_ALL_REQUESTS, Permission.VIEW_OWN_REQUESTS],
  },
  {
    id: 'technicians',
    label: 'Technicians',
    icon: 'UserCheck',
    path: '/technicians',
    permissions: [Permission.VIEW_TECHNICIANS],
  },
  {
    id: 'users',
    label: 'Users',
    icon: 'Users',
    path: '/users',
    permissions: [Permission.VIEW_USERS],
  },
  {
    id: 'admins',
    label: 'Admins',
    icon: 'Shield',
    path: '/admins',
    permissions: [Permission.VIEW_USERS, Permission.CHANGE_USER_ROLE],
  },
  {
    id: 'services',
    label: 'Services',
    icon: 'Wrench',
    path: '/services',
    permissions: [Permission.VIEW_SERVICES],
  },
  {
    id: 'chat',
    label: 'Chat',
    icon: 'MessageSquare',
    path: '/chat',
    permissions: [Permission.VIEW_ALL_CHATS, Permission.VIEW_OWN_CHATS],
  },
  {
    id: 'payments',
    label: 'Payments',
    icon: 'CreditCard',
    path: '/payments',
    permissions: [Permission.VIEW_PAYMENTS],
  },
  {
    id: 'reports',
    label: 'Reports',
    icon: 'BarChart',
    path: '/reports',
    permissions: [Permission.VIEW_REPORTS],
  },
  {
    id: 'files',
    label: 'File Management',
    icon: 'FolderOpen',
    path: '/files',
    permissions: [Permission.VIEW_FILES, Permission.MANAGE_FILES],
  },
  {
    id: 'settings',
    label: 'Settings',
    icon: 'Settings',
    path: '/settings',
    permissions: [Permission.VIEW_SETTINGS],
  },
  {
    id: 'profile',
    label: 'Profile',
    icon: 'UserCog',
    path: '/profile',
    permissions: [Permission.VIEW_DASHBOARD], // All authenticated users can access their profile
  },
];

// Icon mapping for dynamic rendering
export const iconMap = {
  LayoutDashboard,
  Users,
  FileText,
  Settings,
  MessageSquare,
  CreditCard,
  Wrench,
  BarChart,
  UserCheck,
  UserCog,
  Activity,
  Shield,
  FolderOpen,
};

// Get icon component by name
export const getIcon = (iconName?: string) => {
  if (!iconName) return null;
  return iconMap[iconName as keyof typeof iconMap] || null;
};
