/**
 * Utility to set up user roles for payment access
 */

import { getAuth } from 'firebase/auth';
import { doc, getDoc, setDoc } from 'firebase/firestore';
import { db } from '../config/firebase';

export const setupAdminRole = async () => {
  const auth = getAuth();
  const user = auth.currentUser;

  if (!user) {
    console.error('❌ No authenticated user');
    return false;
  }

  try {
    console.warn('🔄 Setting up admin role for user:', user.email);

    // Check if admin document already exists
    const adminDoc = await getDoc(doc(db, 'admins', user.uid));

    if (adminDoc.exists()) {
      console.warn('✅ Admin role already exists');
      return true;
    }

    // Create admin document
    const adminData = {
      uid: user.uid,
      email: user.email,
      name: user.displayName || user.email?.split('@')[0] || 'Admin User',
      role: 'admin',
      created_at: new Date(),
      permissions: [
        'VIEW_USERS',
        'CREATE_USER',
        'UPDATE_USER',
        'DELETE_USER',
        'VIEW_ALL_REQUESTS',
        'UPDATE_REQUEST',
        'DELETE_REQUEST',
        'VIEW_PAYMENTS',
        'MANAGE_PAYMENTS',
        'EXPORT_DATA',
      ],
    };

    await setDoc(doc(db, 'admins', user.uid), adminData);
    console.warn('✅ Admin role created successfully');

    // Also create/update user document to ensure consistency
    const userDocRef = doc(db, 'users', user.uid);
    const userDoc = await getDoc(userDocRef);

    const userData = {
      uid: user.uid,
      email: user.email,
      name: user.displayName || user.email?.split('@')[0] || 'Admin User',
      role: 'admin',
      status: 'active',
      created_at: userDoc.exists() ? userDoc.data()?.created_at : new Date(),
      updated_at: new Date(),
    };

    await setDoc(userDocRef, userData, { merge: true });
    console.warn('✅ User document updated with admin role');

    return true;
  } catch (error) {
    console.error('❌ Error setting up admin role:', error);
    return false;
  }
};

export const setupTechnicianRole = async () => {
  const auth = getAuth();
  const user = auth.currentUser;

  if (!user) {
    console.error('❌ No authenticated user');
    return false;
  }

  try {
    console.warn('🔄 Setting up technician role for user:', user.email);

    // Check if technician document already exists
    const techDoc = await getDoc(doc(db, 'technicians', user.uid));

    if (techDoc.exists()) {
      console.warn('✅ Technician role already exists');
      return true;
    }

    // Create technician document
    const techData = {
      uid: user.uid,
      email: user.email,
      name: user.displayName || user.email?.split('@')[0] || 'Technician',
      role: 'technician',
      status: 'active',
      is_available: true,
      rating: 5.0,
      completed_requests: 0,
      active_requests: 0,
      specialties: ['General Support'],
      created_at: new Date(),
      updated_at: new Date(),
    };

    await setDoc(doc(db, 'technicians', user.uid), techData);
    console.warn('✅ Technician role created successfully');

    // Also create/update user document
    const userDocRef = doc(db, 'users', user.uid);
    const userDoc = await getDoc(userDocRef);

    const userData = {
      uid: user.uid,
      email: user.email,
      name: user.displayName || user.email?.split('@')[0] || 'Technician',
      role: 'technician',
      status: 'active',
      created_at: userDoc.exists() ? userDoc.data()?.created_at : new Date(),
      updated_at: new Date(),
    };

    await setDoc(userDocRef, userData, { merge: true });
    console.warn('✅ User document updated with technician role');

    return true;
  } catch (error) {
    console.error('❌ Error setting up technician role:', error);
    return false;
  }
};

export const createUserDocument = async () => {
  const auth = getAuth();
  const user = auth.currentUser;

  if (!user) {
    console.error('❌ No authenticated user');
    return false;
  }

  try {
    console.warn('🔄 Creating user document for:', user.email);

    // Check if user document already exists
    const userDoc = await getDoc(doc(db, 'users', user.uid));

    if (userDoc.exists()) {
      console.warn('✅ User document already exists');
      return true;
    }

    // Determine role by checking admin/technician collections
    let role = 'customer'; // default role

    const adminDoc = await getDoc(doc(db, 'admins', user.uid));
    if (adminDoc.exists()) {
      role = 'admin';
    } else {
      const techDoc = await getDoc(doc(db, 'technicians', user.uid));
      if (techDoc.exists()) {
        role = 'technician';
      }
    }

    // Create user document
    const userData = {
      uid: user.uid,
      email: user.email,
      name: user.displayName || user.email?.split('@')[0] || 'User',
      role,
      status: 'active',
      is_verified: user.emailVerified,
      created_at: new Date(),
      updated_at: new Date(),
      last_login: new Date(),
    };

    await setDoc(doc(db, 'users', user.uid), userData);
    console.warn('✅ User document created successfully with role:', role);

    return true;
  } catch (error) {
    console.error('❌ Error creating user document:', error);
    return false;
  }
};

// Export for console access
declare global {
  interface Window {
    setupUserRole?: {
      setupAdminRole: typeof setupAdminRole;
      setupTechnicianRole: typeof setupTechnicianRole;
      createUserDocument: typeof createUserDocument;
    };
  }
}

if (typeof window !== 'undefined') {
  window.setupUserRole = {
    setupAdminRole,
    setupTechnicianRole,
    createUserDocument,
  };
}
