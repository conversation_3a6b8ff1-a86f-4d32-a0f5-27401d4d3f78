import { type User as AuthUser, type UserRole } from '../types/auth';
import { type AdminUser, type TechnicianUser, type User } from '../types/user';
import { Permission, rolePermissions } from '../types/permissions';
import logger from '../utils/logger';

class PermissionService {
  // Get all permissions for a specific role
  getRolePermissions(role: UserRole): Permission[] {
    return rolePermissions[role] || [];
  }

  // Check if a user has a specific permission (supports both auth user and full user types)
  hasPermission(user: AuthUser | User | null, permission: Permission): boolean {
    logger.debug('Checking permission', { permission, userId: user?.uid }, 'PERMISSIONS');

    if (!user) {
      logger.debug('No user provided, denying permission', undefined, 'PERMISSIONS');
      return false;
    }

    logger.debug('User role identified', { role: user.role }, 'PERMISSIONS');

    // Handle full User type with custom permissions
    if ('status' in user && user.role === 'admin') {
      logger.debug('User is admin with status property', undefined, 'PERMISSIONS');

      const adminUser = user as AdminUser;
      if (adminUser.permissions && adminUser.permissions.length > 0) {
        const hasPermission = adminUser.permissions.includes(permission.toString());
        logger.debug(
          'Admin custom permissions check',
          { permissions: adminUser.permissions, hasPermission },
          'PERMISSIONS',
        );
        return hasPermission;
      }

      // Check admin level permissions
      if (adminUser.admin_level) {
        const adminPermissions = this.getAdminLevelPermissions(adminUser.admin_level);
        const hasPermission = adminPermissions.includes(permission);
        logger.debug(
          'Admin level permissions check',
          { adminLevel: adminUser.admin_level, hasPermission },
          'PERMISSIONS',
        );
        return hasPermission;
      }
    }

    // Handle technician-specific permissions
    if ('status' in user && user.role === 'technician') {
      logger.debug('User is technician with status property', undefined, 'PERMISSIONS');

      const techUser = user as TechnicianUser;
      // Technicians can only work on requests if they're available
      if (permission === Permission.UPDATE_REQUEST && !techUser.is_available) {
        logger.debug(
          'Technician is not available, cannot update requests',
          undefined,
          'PERMISSIONS',
        );
        return false;
      }
    }

    // Default role-based permissions
    const userPermissions = this.getRolePermissions(user.role);
    const hasPermission = userPermissions.includes(permission);
    logger.debug('Using role-based permissions', { role: user.role, hasPermission }, 'PERMISSIONS');
    return hasPermission;
  }

  // Get admin level permissions
  private getAdminLevelPermissions(adminLevel: string): Permission[] {
    const allPermissions = Object.values(Permission);

    switch (adminLevel) {
      case 'super':
        return allPermissions; // Super admin has all permissions
      case 'standard':
        return allPermissions.filter(
          (p) => p !== Permission.DELETE_USER && p !== Permission.CHANGE_USER_ROLE,
        );
      case 'limited':
        return [
          Permission.VIEW_DASHBOARD,
          Permission.VIEW_ALL_REQUESTS,
          Permission.UPDATE_REQUEST,
          Permission.VIEW_TECHNICIANS,
          Permission.VIEW_SERVICES,
          Permission.VIEW_OWN_CHATS,
          Permission.SEND_MESSAGE,
        ];
      default:
        return this.getRolePermissions('admin');
    }
  }

  // Check if a user has any of the specified permissions
  hasAnyPermission(user: AuthUser | User | null, permissions: Permission[]): boolean {
    if (!user || permissions.length === 0) return false;
    return permissions.some((permission) => this.hasPermission(user, permission));
  }

  // Check if a user has all of the specified permissions
  hasAllPermissions(user: AuthUser | User | null, permissions: Permission[]): boolean {
    if (!user || permissions.length === 0) return false;
    return permissions.every((permission) => this.hasPermission(user, permission));
  }

  // Check if a user can perform an action based on permissions
  canPerformAction(
    user: AuthUser | User | null,
    permissions: Permission[],
    requireAll: boolean = false,
  ): boolean {
    if (!user) return false;
    return requireAll
      ? this.hasAllPermissions(user, permissions)
      : this.hasAnyPermission(user, permissions);
  }

  // Get filtered list of items based on user permissions
  filterByPermissions<T extends { permissions: Permission[] }>(
    user: AuthUser | User | null,
    items: T[],
    requireAll: boolean = false,
  ): T[] {
    logger.debug(
      'Filtering items by permissions',
      { userId: user?.uid, role: user?.role, itemsCount: items.length },
      'PERMISSIONS',
    );

    if (!user) {
      logger.debug('No user provided, returning empty array', undefined, 'PERMISSIONS');
      return [];
    }

    const filtered = items.filter((item) => {
      const hasPermission = this.canPerformAction(user, item.permissions, requireAll);
      logger.debug(
        'Item permission check',
        { itemId: item.id || 'unknown', requiredPermissions: item.permissions, hasPermission },
        'PERMISSIONS',
      );
      return hasPermission;
    });

    logger.debug(
      'Items filtered by permissions',
      { filteredCount: filtered.length },
      'PERMISSIONS',
    );
    return filtered;
  }

  // Check if a user can access a specific resource
  canAccessResource(
    user: AuthUser | User | null,
    resourceOwnerId?: string,
    requiredPermissions?: Permission[],
  ): boolean {
    if (!user) return false;

    // Admin can access everything (but check admin level for full User type)
    if (user.role === 'admin') {
      if ('status' in user) {
        const adminUser = user as AdminUser;
        // Limited admins might not have access to everything
        if (adminUser.admin_level === 'limited' && requiredPermissions) {
          return this.hasAnyPermission(user, requiredPermissions);
        }
      }
      return true;
    }

    // Check ownership (use id for full User type, uid for auth user)
    const userId = 'status' in user ? user.id : user.uid;
    if (resourceOwnerId && userId === resourceOwnerId) {
      return true;
    }

    // Check permissions
    if (requiredPermissions && requiredPermissions.length > 0) {
      return this.hasAnyPermission(user, requiredPermissions);
    }

    return false;
  }

  // Generate permission-based UI hints
  getPermissionHints(user: AuthUser | User | null): {
    canCreateUsers: boolean;
    canManageRequests: boolean;
    canViewReports: boolean;
    canManageServices: boolean;
    canProcessPayments: boolean;
    canViewAllChats: boolean;
    isAdmin: boolean;
    isTechnician: boolean;
    isSuperAdmin: boolean;
    isAvailable?: boolean; // for technicians
    adminLevel?: string; // for admins
  } {
    const hints = {
      canCreateUsers: this.hasPermission(user, Permission.CREATE_USER),
      canManageRequests: this.hasPermission(user, Permission.ASSIGN_REQUEST),
      canViewReports: this.hasPermission(user, Permission.VIEW_REPORTS),
      canManageServices: this.hasPermission(user, Permission.UPDATE_SERVICE),
      canProcessPayments: this.hasPermission(user, Permission.PROCESS_REFUND),
      canViewAllChats: this.hasPermission(user, Permission.VIEW_ALL_CHATS),
      isAdmin: user?.role === 'admin',
      isTechnician: user?.role === 'technician',
      isSuperAdmin: false,
      isAvailable: undefined,
      adminLevel: undefined,
    };

    // Add additional info for full User types
    if (user && 'status' in user) {
      if (user.role === 'admin') {
        const adminUser = user as AdminUser;
        hints.isSuperAdmin = adminUser.admin_level === 'super';
        hints.adminLevel = adminUser.admin_level;
      } else if (user.role === 'technician') {
        const techUser = user as TechnicianUser;
        hints.isAvailable = techUser.is_available;
      }
    }

    return hints;
  }

  // Check if user can manage another user
  canManageUser(currentUser: AuthUser | User | null, targetUser: User): boolean {
    if (!currentUser) return false;

    // Super admin can manage anyone
    if ('status' in currentUser && currentUser.role === 'admin') {
      const adminUser = currentUser as AdminUser;
      if (adminUser.admin_level === 'super') {
        return true;
      }

      // Standard/limited admins cannot manage other admins
      if (targetUser.role === 'admin') {
        return false;
      }
    }

    // Check if user has permission to manage users
    return this.hasPermission(currentUser, Permission.UPDATE_USER);
  }

  // Get all permissions for a user (including custom admin permissions)
  getUserPermissions(user: AuthUser | User | null): Permission[] {
    if (!user) return [];

    if ('status' in user && user.role === 'admin') {
      const adminUser = user as AdminUser;
      if (adminUser.permissions && adminUser.permissions.length > 0) {
        return adminUser.permissions.map((p) => p as Permission);
      }

      if (adminUser.admin_level) {
        return this.getAdminLevelPermissions(adminUser.admin_level);
      }
    }

    return this.getRolePermissions(user.role);
  }
}

export default new PermissionService();
