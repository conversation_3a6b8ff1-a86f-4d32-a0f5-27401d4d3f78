import 'package:cloud_firestore/cloud_firestore.dart';

enum TechnicianStatus { active, offline, busy, onLeave }

class TechnicianModel {
  final String id;
  final String email;
  final String name;
  final String? photoUrl;
  final String? phoneNumber;
  final List<String> specialties;
  final TechnicianStatus status;
  final double rating;
  final int completedRequests;
  final int activeRequests;
  final bool isAvailable;
  final DateTime createdAt;
  final DateTime? updatedAt;

  TechnicianModel({
    required this.id,
    required this.email,
    required this.name,
    this.photoUrl,
    this.phoneNumber,
    required this.specialties,
    required this.status,
    this.rating = 0.0,
    this.completedRequests = 0,
    this.activeRequests = 0,
    this.isAvailable = true,
    required this.createdAt,
    this.updatedAt,
  });

  // Factory constructor to create a TechnicianModel from a Firestore document
  factory TechnicianModel.fromFirestore(
    DocumentSnapshot<Map<String, dynamic>> doc,
  ) {
    final data = doc.data() ?? {};

    return TechnicianModel(
      id: doc.id,
      email: data['email'] ?? '',
      name: data['name'] ?? '',
      photoUrl: data['photo_url'],
      phoneNumber: data['phone_number'],
      specialties: List<String>.from(data['specialties'] ?? []),
      status: _parseStatus(data['status']),
      rating: (data['rating'] ?? 0.0).toDouble(),
      completedRequests: data['completed_requests'] ?? 0,
      activeRequests: data['active_requests'] ?? 0,
      isAvailable: data['is_available'] ?? true,
      createdAt: (data['created_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updated_at'] as Timestamp?)?.toDate(),
    );
  }

  // Factory method to create TechnicianModel from a document ID and Map
  // PHASE 1: Prefer snake_case but maintain full backward compatibility
  factory TechnicianModel.fromMap(String id, Map<String, dynamic> data) {
    try {
      // Phase 1 Migration: Prefer snake_case, fallback to camelCase for safety

      // Log field access patterns for monitoring
      if (data['photo_url'] == null && data['photoUrl'] != null) {
        print(
          'TechnicianModel.fromMap: Using camelCase fallback for photo_url in technician $id',
        );
      }

      return TechnicianModel(
        id: id,
        email: data['email'] ?? '',
        name: data['name'] ?? '',
        // Phase 1: Prefer snake_case with safe fallbacks
        photoUrl: data['photo_url'] ?? data['photoUrl'],
        phoneNumber: data['phone_number'] ?? data['phoneNumber'],
        specialties: List<String>.from(data['specialties'] ?? []),
        status: _parseStatus(data['status']),
        rating: (data['rating'] ?? 0.0).toDouble(),
        completedRequests:
            data['completed_requests'] ?? data['completedRequests'] ?? 0,
        activeRequests: data['active_requests'] ?? data['activeRequests'] ?? 0,
        isAvailable: data['is_available'] ?? data['isAvailable'] ?? true,
        createdAt:
            _parseTimestamp(data['created_at'] ?? data['createdAt']) ??
            DateTime.now(),
        updatedAt: _parseTimestamp(data['updated_at'] ?? data['updatedAt']),
      );
    } catch (e) {
      print('Error parsing TechnicianModel from map: $e for ID: $id');
      // Return safe empty technician instead of crashing
      return TechnicianModel(
        id: id,
        email: '',
        name: 'Error Loading Technician',
        specialties: [],
        status: TechnicianStatus.offline,
        createdAt: DateTime.now(),
      );
    }
  }

  // Helper method to safely parse timestamps
  static DateTime? _parseTimestamp(dynamic timestamp) {
    if (timestamp == null) return null;
    if (timestamp is Timestamp) return timestamp.toDate();
    if (timestamp is DateTime) return timestamp;
    if (timestamp is String) {
      try {
        return DateTime.parse(timestamp);
      } catch (e) {
        print('Error parsing timestamp string: $e');
        return null;
      }
    }
    return null;
  }

  // Convert TechnicianModel to a Map for Firestore
  // PHASE 2: Write ONLY snake_case (no more dual-write)
  Map<String, dynamic> toFirestore() {
    final Map<String, dynamic> data = {
      // Use ONLY snake_case fields - standardized format
      'email': email,
      'name': name,
      'photo_url': photoUrl,
      'phone_number': phoneNumber,
      'specialties': specialties,
      'status': status.toString().split('.').last,
      'rating': rating,
      'completed_requests': completedRequests,
      'active_requests': activeRequests,
      'is_available': isAvailable,
      'created_at': createdAt,
      'updated_at': updatedAt ?? FieldValue.serverTimestamp(),
    };

    return data;
  }

  // Create a copy of the technician with updated fields
  TechnicianModel copyWith({
    String? id,
    String? email,
    String? name,
    String? photoUrl,
    String? phoneNumber,
    List<String>? specialties,
    TechnicianStatus? status,
    double? rating,
    int? completedRequests,
    int? activeRequests,
    bool? isAvailable,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TechnicianModel(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      photoUrl: photoUrl ?? this.photoUrl,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      specialties: specialties ?? this.specialties,
      status: status ?? this.status,
      rating: rating ?? this.rating,
      completedRequests: completedRequests ?? this.completedRequests,
      activeRequests: activeRequests ?? this.activeRequests,
      isAvailable: isAvailable ?? this.isAvailable,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper to parse status string to enum
  static TechnicianStatus _parseStatus(String? status) {
    switch (status) {
      case 'offline':
        return TechnicianStatus.offline;
      case 'busy':
        return TechnicianStatus.busy;
      case 'onLeave':
        return TechnicianStatus.onLeave;
      case 'active':
      default:
        return TechnicianStatus.active;
    }
  }
}
