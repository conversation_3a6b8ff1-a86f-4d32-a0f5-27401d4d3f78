import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io' show Platform;
import 'package:flutter/foundation.dart';

class Migrations {
  // Singleton instance
  static final Migrations _instance = Migrations._internal();
  factory Migrations() => _instance;
  Migrations._internal();
  
  // Firestore instance
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  
  // Migration for FCM tokens in service requests
  Future<void> migrateServiceRequestsWithFCMToken() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        debugPrint('Cannot migrate service requests: No authenticated user');
        return;
      }
      
      // Get the current FCM token
      final fcmToken = await FirebaseMessaging.instance.getToken();
      if (fcmToken == null) {
        debugPrint('Cannot migrate service requests: No FCM token available');
        return;
      }
      
      debugPrint('Running FCM token migration for service requests...');
      
      // Get all service requests for this user (not just active ones)
      final snapshot = await _firestore
          .collection('service_requests')
          .where('customer_id', isEqualTo: user.uid)
          .get();
      
      if (snapshot.docs.isEmpty) {
        debugPrint('No service requests found to migrate');
        return;
      }
      
      debugPrint('Found ${snapshot.docs.length} service requests to update with FCM token');
      int updatedCount = 0;
      
      // Update all service requests with current token
      for (final doc in snapshot.docs) {
        final data = doc.data();
        // Check if token is missing or different
        if (data['customer_fcm_token'] != fcmToken || data['customerFcmToken'] != fcmToken) {
          await doc.reference.update({
            'customer_fcm_token': fcmToken,
            'customerFcmToken': fcmToken,
            'updated_at': FieldValue.serverTimestamp(),
            'updatedAt': FieldValue.serverTimestamp(),
          });
          updatedCount++;
        }
      }
      
      debugPrint('Migration complete. Updated $updatedCount service requests with FCM token');
      
      // Record completion for analytics
      try {
        await FirebaseFirestore.instance.collection('migrations').doc('fcm_tokens').set({
          'completed_at': FieldValue.serverTimestamp(),
          'user_id': user.uid,
          'device': Platform.isAndroid ? 'Android' : 'iOS',
          'requests_updated': updatedCount,
          'total_requests': snapshot.docs.length,
          'token_length': fcmToken.length
        }, SetOptions(merge: true));
      } catch (e) {
        // Non-critical error, just log
        debugPrint('Error recording migration completion: $e');
      }
      
      // Mark migration as done in shared preferences for this device
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('migration_fcm_tokens_done', true);
    } catch (e) {
      debugPrint('Error during FCM token migration: $e');
      // Don't throw so app can continue loading
    }
  }
  
  // Run all migrations
  Future<void> runAllMigrations() async {
    try {
      await migrateServiceRequestsWithFCMToken();
    } catch (e) {
      debugPrint('Error running migrations: $e');
    }
  }
} 