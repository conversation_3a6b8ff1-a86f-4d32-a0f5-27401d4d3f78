                        -HD:\<PERSON>\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DANDROID_PLATFORM=android-21
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\26.1.10909125
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\26.1.10909125
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\sdk\ndk\26.1.10909125\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=D:\Ibrahim\mr.tech.v2\mr_tech_mobile\build\app\intermediates\cxx\Debug\1gw1z6o4\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=D:\Ibrahim\mr.tech.v2\mr_tech_mobile\build\app\intermediates\cxx\Debug\1gw1z6o4\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=Debug
-BD:\Ibrahim\mr.tech.v2\mr_tech_mobile\android\app\.cxx\Debug\1gw1z6o4\armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2