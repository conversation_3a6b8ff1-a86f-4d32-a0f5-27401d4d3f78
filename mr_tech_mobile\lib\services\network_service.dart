import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'dart:async';

class NetworkService {
  static final NetworkService _instance = NetworkService._internal();
  factory NetworkService() => _instance;
  NetworkService._internal();

  final Connectivity _connectivity = Connectivity();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;
  
  bool _isOnline = true;
  bool get isOnline => _isOnline;

  final StreamController<bool> _connectivityController = StreamController<bool>.broadcast();
  Stream<bool> get connectivityStream => _connectivityController.stream;

  // Initialize network monitoring
  Future<void> initialize() async {
    try {
      // Check initial connectivity
      final connectivityResults = await _connectivity.checkConnectivity();
      _isOnline = !connectivityResults.contains(ConnectivityResult.none);
      
      // Listen to connectivity changes
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
        (List<ConnectivityResult> results) {
          final wasOnline = _isOnline;
          _isOnline = !results.contains(ConnectivityResult.none);
          
          if (wasOnline != _isOnline) {
            _connectivityController.add(_isOnline);
            debugPrint('Network connectivity changed: ${_isOnline ? 'Online' : 'Offline'}');
          }
        },
      );
      
      debugPrint('NetworkService initialized. Current status: ${_isOnline ? 'Online' : 'Offline'}');
    } catch (e) {
      debugPrint('Error initializing NetworkService: $e');
    }
  }

  // Check if error is network-related
  static bool isNetworkError(dynamic error) {
    if (error == null) return false;
    
    final errorString = error.toString().toLowerCase();
    return errorString.contains('unavailable') ||
           errorString.contains('timeout') ||
           errorString.contains('network') ||
           errorString.contains('connection') ||
           errorString.contains('deadline-exceeded') ||
           errorString.contains('failed to connect') ||
           errorString.contains('no internet');
  }

  // Get user-friendly error message for network issues
  static String getNetworkErrorMessage(dynamic error) {
    if (!isNetworkError(error)) {
      return error.toString();
    }
    
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('unavailable')) {
      return 'Service is temporarily unavailable. Please check your internet connection and try again.';
    } else if (errorString.contains('timeout') || errorString.contains('deadline-exceeded')) {
      return 'Request timed out. Please check your internet connection and try again.';
    } else if (errorString.contains('network') || errorString.contains('connection')) {
      return 'Network connection error. Please check your internet connection and try again.';
    } else {
      return 'Connection error. Please check your internet connection and try again.';
    }
  }

  // Retry operation with exponential backoff
  static Future<T?> retryOperation<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration baseDelay = const Duration(seconds: 1),
    bool Function(dynamic)? isRetryableError,
  }) async {
    for (int attempt = 0; attempt < maxRetries; attempt++) {
      try {
        return await operation();
      } catch (e) {
        final isLastAttempt = attempt == maxRetries - 1;
        final shouldRetry = isRetryableError?.call(e) ?? isNetworkError(e);
        
        debugPrint('Operation failed (attempt ${attempt + 1}/$maxRetries): $e');
        
        if (isLastAttempt || !shouldRetry) {
          debugPrint('Max retries reached or non-retryable error');
          rethrow;
        }
        
        // Exponential backoff
        final delay = Duration(
          milliseconds: baseDelay.inMilliseconds * (1 << attempt),
        );
        debugPrint('Retrying in ${delay.inMilliseconds}ms...');
        await Future.delayed(delay);
      }
    }
    return null;
  }

  // Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _connectivityController.close();
  }
}
