import 'package:cloud_firestore/cloud_firestore.dart';

class NotificationModel {
  final String id;
  final String userId;
  final String title;
  final String body;
  final String type;
  final String? requestId;
  final Timestamp createdAt;
  final bool read;
  final bool deleted;

  NotificationModel({
    required this.id,
    required this.userId,
    required this.title,
    required this.body,
    required this.type,
    this.requestId,
    required this.createdAt,
    required this.read,
    this.deleted = false,
  });

  factory NotificationModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return NotificationModel(
      id: doc.id,
      userId: data['user_id'] ?? data['userId'] ?? '',
      title: data['title'] ?? '',
      body: data['body'] ?? '',
      type: data['type'] ?? '',
      requestId: data['request_id'] ?? data['requestId'],
      createdAt:
          data['created_at'] as Timestamp? ??
          data['createdAt'] as Timestamp? ??
          Timestamp.now(),
      read: data['read'] ?? false,
      deleted: data['deleted'] ?? false,
    );
  }

  // Factory method to create NotificationModel from a document ID and Map
  // PHASE 1: Prefer snake_case but maintain full backward compatibility
  factory NotificationModel.fromMap(String id, Map<String, dynamic> data) {
    try {
      // Phase 1 Migration: Prefer snake_case, fallback to camelCase for safety

      // Log field access patterns for monitoring
      if (data['user_id'] == null && data['userId'] != null) {
        print(
          'NotificationModel.fromMap: Using camelCase fallback for user_id in notification $id',
        );
      }

      return NotificationModel(
        id: id,
        // Phase 1: Prefer snake_case with safe fallbacks
        userId: data['user_id'] ?? data['userId'] ?? '',
        title: data['title'] ?? '',
        body: data['body'] ?? '',
        type: data['type'] ?? '',
        requestId: data['request_id'] ?? data['requestId'],
        createdAt:
            data['created_at'] as Timestamp? ??
            data['createdAt'] as Timestamp? ??
            Timestamp.now(),
        read: data['read'] ?? false,
        deleted: data['deleted'] ?? false,
      );
    } catch (e) {
      print('Error parsing NotificationModel from map: $e for ID: $id');
      // Return safe empty notification instead of crashing
      return NotificationModel(
        id: id,
        userId: '',
        title: 'Error',
        body: 'Error loading notification',
        type: 'error',
        createdAt: Timestamp.now(),
        read: false,
        deleted: false,
      );
    }
  }

  // Convert NotificationModel to a Map for Firestore
  // PHASE 2: Write ONLY snake_case (no more dual-write)
  Map<String, dynamic> toMap() {
    return {
      // Use ONLY snake_case fields - standardized format
      'user_id': userId,
      'title': title,
      'body': body,
      'type': type,
      'request_id': requestId,
      'created_at': createdAt,
      'read': read,
      'deleted': deleted,
    };
  }
}
