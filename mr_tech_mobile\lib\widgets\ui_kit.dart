/// Modern UI Components
///
/// This file provides a comprehensive UI kit with consistent styling and spacing.
/// Uses the 8px grid system for all spacing and follows Material 3 design principles.
library;

import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../utils/spacing.dart';
import 'text_styles.dart';

// Export text styles and typography utilities
export 'text_styles.dart';
export '../utils/typography.dart';

// Export card system and form system
export 'card_system.dart';
export 'form_system.dart';

// Export theme, spacing, and utilities for direct access
export '../theme/app_theme.dart';
export '../utils/spacing.dart';
export '../utils/visual_hierarchy.dart';

// Export performance utilities
export '../utils/performance_optimizer.dart';
export '../utils/startup_optimizer.dart';
export '../utils/animation_optimizer.dart';

// Export code quality utilities
export '../utils/code_quality_analyzer.dart';
export '../utils/architecture_patterns.dart';
export '../utils/refactoring_utils.dart';

// Export accessibility utilities
export '../utils/accessibility_utils.dart';
export '../utils/wcag_compliance.dart';

// Export navigation and state management utilities
export '../utils/navigation_manager.dart';
export '../utils/state_manager.dart';

/// A collection of pre-styled UI components for consistent UI across the app
/// All components follow the 8px grid system and Material 3 design principles
class UIKit {
  // Private constructor to prevent instantiation
  UIKit._();
  
  // Standard padding values - using 8px grid system
  static const double paddingXS = Spacing.xs;   // 4px
  static const double paddingS = Spacing.s;     // 8px
  static const double paddingM = Spacing.m;     // 16px
  static const double paddingL = Spacing.l;     // 24px
  static const double paddingXL = Spacing.xl;   // 32px
  
  // Standard border radius values
  static const double radiusXS = 4.0;
  static const double radiusS = 8.0;
  static const double radiusM = 12.0;
  static const double radiusL = 16.0;
  static const double radiusXL = 24.0;
  
  // Standard elevation values
  static const double elevationNone = 0.0;
  static const double elevationXS = 1.0;
  static const double elevationS = 2.0;
  static const double elevationM = 4.0;
  static const double elevationL = 8.0;
  static const double elevationXL = 16.0;
  
  // Create a styled text widget with adaptive color support
  static Widget text(
    BuildContext context,
    String text, {
    TextStyle Function(BuildContext, {Color? color})? styleFunction,
    Color? color,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
    bool adaptiveColor = true,
  }) {
    final theme = Theme.of(context);

    // Use adaptive color if not explicitly provided
    final effectiveColor = color ??
        (adaptiveColor ? theme.colorScheme.onSurface : null);

    final TextStyle style = styleFunction != null
        ? styleFunction(context, color: effectiveColor)
        : AppTextStyles.bodyMedium(context, color: effectiveColor);

    return Text(
      text,
      style: style,
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,
    );
  }

  // Create adaptive heading text
  static Widget heading(
    BuildContext context,
    String text, {
    TextStyle Function(BuildContext, {Color? color})? styleFunction,
    Color? color,
    TextAlign? textAlign,
    int? maxLines,
  }) {
    return UIKit.text(
      context,
      text,
      styleFunction: styleFunction ?? AppTextStyles.headingMedium,
      color: color,
      textAlign: textAlign,
      maxLines: maxLines,
    );
  }

  // Create adaptive subtitle text
  static Widget subtitle(
    BuildContext context,
    String text, {
    Color? color,
    TextAlign? textAlign,
    int? maxLines,
  }) {
    final theme = Theme.of(context);
    return UIKit.text(
      context,
      text,
      styleFunction: AppTextStyles.bodyMedium,
      color: color ?? theme.colorScheme.onSurfaceVariant,
      textAlign: textAlign,
      maxLines: maxLines,
    );
  }

  // Create adaptive caption text
  static Widget caption(
    BuildContext context,
    String text, {
    Color? color,
    TextAlign? textAlign,
    int? maxLines,
  }) {
    final theme = Theme.of(context);
    return UIKit.text(
      context,
      text,
      styleFunction: AppTextStyles.bodySmall,
      color: color ?? theme.colorScheme.onSurfaceVariant.withOpacity(0.8),
      textAlign: textAlign,
      maxLines: maxLines,
    );
  }
  
  // Create a standard app bar with consistent styling
  static AppBar appBar(
    BuildContext context, {
    required String title,
    List<Widget>? actions,
    bool centerTitle = true,
    Widget? leading,
    PreferredSizeWidget? bottom,
    Color? backgroundColor,
    double? elevation,
    bool automaticallyImplyLeading = true,
  }) {
    final theme = Theme.of(context);
    
    return AppBar(
      title: text(
        context,
        title,
        styleFunction: AppTextStyles.headingSmall,
      ),
      centerTitle: centerTitle,
      actions: actions,
      leading: leading,
      bottom: bottom,
      backgroundColor: backgroundColor ?? theme.colorScheme.surface,
      elevation: elevation ?? 0,
      automaticallyImplyLeading: automaticallyImplyLeading,
    );
  }
  
  // Create a card with consistent styling that adapts to theme
  static Widget card(
    BuildContext context, {
    required Widget child,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    Color? backgroundColor,
    double? elevation,
    BorderRadius? borderRadius,
    VoidCallback? onTap,
    bool useAdaptiveDecoration = true,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    if (useAdaptiveDecoration) {
      // Use adaptive decoration for better dark mode support
      return Container(
        margin: margin ?? Spacing.cardMargin,
        decoration: AppTheme.adaptiveCardDecoration(context),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onTap,
            borderRadius: borderRadius ?? AppTheme.mediumRadius,
            child: Padding(
              padding: padding ?? Spacing.cardPadding,
              child: child,
            ),
          ),
        ),
      );
    }

    final cardWidget = Card(
      margin: margin ?? Spacing.cardMargin,
      elevation: elevation ?? (isDark ? 0 : elevationXS),
      color: backgroundColor ?? theme.colorScheme.surface,
      shape: RoundedRectangleBorder(
        borderRadius: borderRadius ?? BorderRadius.circular(radiusM),
        side: isDark
          ? BorderSide(
              color: theme.colorScheme.outline.withOpacity(0.2),
              width: 1,
            )
          : BorderSide.none,
      ),
      child: Padding(
        padding: padding ?? Spacing.cardPadding,
        child: child,
      ),
    );

    return onTap != null
        ? InkWell(
            onTap: onTap,
            borderRadius: borderRadius ?? BorderRadius.circular(radiusM),
            child: cardWidget,
          )
        : cardWidget;
  }
  
  // Create a standard divider with adaptive styling
  static Widget divider(BuildContext context, {double height = 1.0, double indent = 0.0}) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Divider(
      height: height + 16.0, // Add some padding
      thickness: height,
      indent: indent,
      endIndent: indent,
      color: isDark
        ? theme.colorScheme.outline.withOpacity(0.3)
        : theme.colorScheme.outline.withOpacity(0.2),
    );
  }

  // Create an elevated card with adaptive styling
  static Widget elevatedCard(
    BuildContext context, {
    required Widget child,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    Color? backgroundColor,
    BorderRadius? borderRadius,
    VoidCallback? onTap,
  }) {
    return Container(
      margin: margin ?? Spacing.cardMargin,
      decoration: AppTheme.adaptiveElevatedCardDecoration(context),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: borderRadius ?? AppTheme.mediumRadius,
          child: Padding(
            padding: padding ?? Spacing.cardPadding,
            child: child,
          ),
        ),
      ),
    );
  }

  // Create a frosted glass container with adaptive styling
  static Widget frostedContainer(
    BuildContext context, {
    required Widget child,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    BorderRadius? borderRadius,
    VoidCallback? onTap,
  }) {
    return Container(
      margin: margin ?? Spacing.cardMargin,
      decoration: AppTheme.adaptiveFrostedGlass(context),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: borderRadius ?? AppTheme.mediumRadius,
          child: Padding(
            padding: padding ?? Spacing.cardPadding,
            child: child,
          ),
        ),
      ),
    );
  }

  // Create an adaptive surface container
  static Widget surfaceContainer(
    BuildContext context, {
    required Widget child,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    BorderRadius? borderRadius,
    VoidCallback? onTap,
    int elevation = 1,
  }) {
    final theme = Theme.of(context);
    final surfaceColor = ElevationOverlay.applySurfaceTint(
      theme.colorScheme.surface,
      theme.colorScheme.surfaceTint,
      elevation.toDouble(),
    );

    return Container(
      margin: margin ?? Spacing.cardMargin,
      decoration: BoxDecoration(
        color: surfaceColor,
        borderRadius: borderRadius ?? AppTheme.mediumRadius,
        boxShadow: AppTheme.adaptiveSubtleShadow(context),
        border: theme.brightness == Brightness.dark
          ? Border.all(
              color: theme.colorScheme.outline.withOpacity(0.2),
              width: 1,
            )
          : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: borderRadius ?? AppTheme.mediumRadius,
          child: Padding(
            padding: padding ?? Spacing.cardPadding,
            child: child,
          ),
        ),
      ),
    );
  }
  
  // Create a spacer with variable height
  static Widget spacer({double height = 16.0}) {
    return SizedBox(height: height);
  }
  
  // Create a styled button
  static Widget button(
    BuildContext context, {
    required String text,
    required VoidCallback onPressed,
    bool isPrimary = true,
    bool isLoading = false,
    IconData? icon,
    Color? backgroundColor,
    Color? textColor,
    double? width,
    double height = 48.0,
    BorderRadius? borderRadius,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    // Determine colors based on button type
    final bgColor = backgroundColor ?? (isPrimary ? colorScheme.primary : Colors.transparent);
    final fgColor = textColor ?? (isPrimary ? colorScheme.onPrimary : colorScheme.primary);
    
    // Create the button content
    Widget buttonContent = isLoading
        ? SizedBox(
            width: 24,
            height: 24,
            child: CircularProgressIndicator(
              strokeWidth: 2.0,
              valueColor: AlwaysStoppedAnimation<Color>(fgColor),
            ),
          )
        : Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (icon != null) ...[
                Icon(icon, color: fgColor, size: 18),
                SizedBox(width: paddingS),
              ],
              UIKit.text(
                context,
                text,
                styleFunction: AppTextStyles.buttonMedium,
                color: fgColor,
              ),
            ],
          );
    
    // Create the button
    return SizedBox(
      width: width,
      height: height,
      child: isPrimary
          ? ElevatedButton(
              onPressed: isLoading ? null : onPressed,
              style: ElevatedButton.styleFrom(
                backgroundColor: bgColor,
                foregroundColor: fgColor,
                shape: RoundedRectangleBorder(
                  borderRadius: borderRadius ?? BorderRadius.circular(radiusS),
                ),
                padding: EdgeInsets.symmetric(horizontal: Spacing.m),
              ),
              child: buttonContent,
            )
          : OutlinedButton(
              onPressed: isLoading ? null : onPressed,
              style: OutlinedButton.styleFrom(
                foregroundColor: fgColor,
                side: BorderSide(color: fgColor),
                shape: RoundedRectangleBorder(
                  borderRadius: borderRadius ?? BorderRadius.circular(radiusS),
                ),
                padding: EdgeInsets.symmetric(horizontal: Spacing.m),
              ),
              child: buttonContent,
            ),
    );
  }
  
  // Create a styled chip
  static Widget chip(
    BuildContext context, {
    required String label,
    Color? backgroundColor,
    Color? textColor,
    IconData? icon,
    VoidCallback? onTap,
    VoidCallback? onDeleted,
  }) {
    final theme = Theme.of(context);
    final bgColor = backgroundColor ?? theme.colorScheme.surfaceContainerLow;
    final fgColor = textColor ?? theme.colorScheme.onSurface;
    
    return RawChip(
      label: UIKit.text(
        context, 
        label, 
        styleFunction: AppTextStyles.labelMedium,
        color: fgColor,
      ),
      backgroundColor: bgColor,
      avatar: icon != null ? Icon(icon, size: 16, color: fgColor) : null,
      onPressed: onTap,
      onDeleted: onDeleted,
      deleteIcon: const Icon(Icons.close, size: 16),
      padding: const EdgeInsets.symmetric(horizontal: paddingXS, vertical: 0),
      visualDensity: VisualDensity.compact,
    );
  }
  
  // Create a styled error message
  static Widget errorMessage(
    BuildContext context, {
    required String message,
    VoidCallback? onRetry,
  }) {
    return Container(
      padding: Spacing.cardPadding,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.errorContainer,
        borderRadius: BorderRadius.circular(radiusS),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          UIKit.text(
            context,
            message,
            styleFunction: AppTextStyles.bodyMedium,
            color: Theme.of(context).colorScheme.onErrorContainer,
            textAlign: TextAlign.center,
          ),
          if (onRetry != null) ...[
            Spacing.verticalM,
            TextButton(
              onPressed: onRetry,
              child: UIKit.text(
                context,
                'Retry',
                styleFunction: AppTextStyles.buttonMedium,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ],
        ],
      ),
    );
  }

  // Create a loading indicator with adaptive styling
  static Widget loadingIndicator(BuildContext context, {double size = 24.0}) {
    final theme = Theme.of(context);

    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        strokeWidth: 2.0,
        valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
      ),
    );
  }

  // Create an adaptive icon with proper color
  static Widget adaptiveIcon(
    BuildContext context,
    IconData icon, {
    double? size,
    Color? color,
    bool useOnSurfaceColor = true,
  }) {
    final theme = Theme.of(context);
    final effectiveColor = color ??
        (useOnSurfaceColor ? theme.colorScheme.onSurface : theme.colorScheme.primary);

    return Icon(
      icon,
      size: size,
      color: effectiveColor,
    );
  }

  // Create an adaptive list tile
  static Widget listTile(
    BuildContext context, {
    Widget? leading,
    required Widget title,
    Widget? subtitle,
    Widget? trailing,
    VoidCallback? onTap,
    EdgeInsetsGeometry? contentPadding,
    bool dense = false,
  }) {
    final theme = Theme.of(context);

    return Material(
      color: Colors.transparent,
      child: ListTile(
        leading: leading,
        title: title,
        subtitle: subtitle,
        trailing: trailing,
        onTap: onTap,
        contentPadding: contentPadding ?? EdgeInsets.symmetric(horizontal: Spacing.m, vertical: Spacing.xs),
        dense: dense,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(radiusS)),
        tileColor: Colors.transparent,
        selectedTileColor: theme.colorScheme.primary.withOpacity(0.1),
      ),
    );
  }

  // Create an adaptive input field
  static Widget inputField(
    BuildContext context, {
    required String label,
    String? hint,
    TextEditingController? controller,
    bool obscureText = false,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    void Function(String)? onChanged,
    Widget? suffixIcon,
    Widget? prefixIcon,
    int? maxLines = 1,
    bool enabled = true,
  }) {
    final theme = Theme.of(context);

    return TextFormField(
      controller: controller,
      obscureText: obscureText,
      keyboardType: keyboardType,
      validator: validator,
      onChanged: onChanged,
      maxLines: maxLines,
      enabled: enabled,
      style: TextStyle(
        color: theme.colorScheme.onSurface,
        fontSize: 16,
      ),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        suffixIcon: suffixIcon,
        prefixIcon: prefixIcon,
        filled: true,
        fillColor: theme.brightness == Brightness.dark
          ? theme.colorScheme.surfaceContainer
          : theme.colorScheme.surface,
        contentPadding: Spacing.inputPadding,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusS),
          borderSide: BorderSide(
            color: theme.colorScheme.outline.withOpacity(0.4),
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusS),
          borderSide: BorderSide(
            color: theme.colorScheme.outline.withOpacity(0.4),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusS),
          borderSide: BorderSide(
            color: theme.colorScheme.primary,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(radiusS),
          borderSide: BorderSide(
            color: theme.colorScheme.error,
          ),
        ),
      ),
    );
  }
}