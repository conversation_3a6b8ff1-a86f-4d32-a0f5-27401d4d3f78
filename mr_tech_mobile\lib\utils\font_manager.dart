import 'package:flutter/material.dart';

/// FontManager - Single source of truth for all font-related functionality
/// 
/// This class manages font families, styles, and weights throughout the app.
/// It handles RTL/LTR font selection and provides consistent text styles.
class FontManager {
  // Private constructor to prevent instantiation
  FontManager._();
  
  // Font family names
  static const String playfairDisplay = 'Playfair Display';
  static const String montserrat = 'Montserrat';
  static const String tajawal = 'Tajawal';
  static const String roboto = 'Roboto';
  
  /// Get primary font family based on locale
  static String getPrimaryFontFamily(BuildContext context) {
    // Try to get text direction safely, fall back to LTR if not available
    final TextDirection textDirection = _getTextDirection(context);
    return textDirection == TextDirection.rtl ? tajawal : playfairDisplay;
  }
  
  /// Get secondary font family based on locale
  static String getSecondaryFontFamily(BuildContext context) {
    // Try to get text direction safely, fall back to LTR if not available
    final TextDirection textDirection = _getTextDirection(context);
    return textDirection == TextDirection.rtl ? tajawal : montserrat;
  }
  
  /// Helper method to safely get text direction
  static TextDirection _getTextDirection(BuildContext context) {
    // First try to get from Directionality if available
    try {
      return Directionality.of(context);
    } catch (e) {
      // If not available, try to get from Locale if TranslationService is available
      try {
        final locale = Localizations.localeOf(context);
        return locale.languageCode == 'ar' ? TextDirection.rtl : TextDirection.ltr;
      } catch (e) {
        // Default to LTR if all else fails
        return TextDirection.ltr;
      }
    }
  }
  
  /// Get default font family (used when context is not available)
  static String get defaultFontFamily => montserrat;
  
  /// Get default RTL font family (used when context is not available)
  static String get defaultRtlFontFamily => tajawal;

  /// Create a fallback TextStyle when no context is available
  static TextStyle fallbackTextStyle({
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? letterSpacing,
    double? height,
    bool isRtl = false,
    bool isPrimary = false,
  }) {
    final fontFamily = isRtl
        ? tajawal
        : (isPrimary ? playfairDisplay : montserrat);

    return TextStyle(
      fontFamily: fontFamily,
      fontSize: fontSize ?? 14.0,
      fontWeight: fontWeight ?? FontWeight.normal,
      color: color ?? Colors.black,
      letterSpacing: letterSpacing,
      height: height,
    );
  }

  /// Get a TextStyle with the correct font for a given text direction
  static TextStyle getStyleForDirection({
    required TextDirection textDirection,
    bool isPrimary = false,
    double? fontSize,
    FontWeight? fontWeight,
    Color? color,
    double? letterSpacing,
    double? height,
  }) {
    final fontFamily = textDirection == TextDirection.rtl
        ? tajawal
        : (isPrimary ? playfairDisplay : montserrat);

    return TextStyle(
      fontFamily: fontFamily,
      fontSize: fontSize ?? 14.0,
      fontWeight: fontWeight ?? FontWeight.normal,
      color: color ?? Colors.black,
      letterSpacing: letterSpacing,
      height: height,
    );
  }
  
  /// Create a TextTheme with the appropriate font families
  static TextTheme createTextTheme(BuildContext context, ColorScheme colorScheme) {
    // Try to get text direction safely
    final TextDirection textDirection = _getTextDirection(context);
    final primaryFont = textDirection == TextDirection.rtl ? tajawal : playfairDisplay;
    final secondaryFont = textDirection == TextDirection.rtl ? tajawal : montserrat;
    final onSurface = colorScheme.onSurface;
    final onSurfaceVariant = colorScheme.onSurfaceVariant;
    
    return TextTheme(
      // Display styles - Elegant serif for headlines
      displayLarge: TextStyle(
        fontFamily: primaryFont,
        fontSize: 57,
        fontWeight: FontWeight.w700,
        color: onSurface,
        letterSpacing: -0.5,
        height: 1.2,
      ),
      displayMedium: TextStyle(
        fontFamily: primaryFont,
        fontSize: 45,
        fontWeight: FontWeight.w700,
        color: onSurface,
        letterSpacing: -0.25,
        height: 1.2,
      ),
      displaySmall: TextStyle(
        fontFamily: primaryFont,
        fontSize: 36,
        fontWeight: FontWeight.w700,
        color: onSurface,
        letterSpacing: -0.15,
        height: 1.2,
      ),
      
      // Headline styles - Refined with better kerning
      headlineLarge: TextStyle(
        fontFamily: primaryFont,
        fontSize: 32,
        fontWeight: FontWeight.w600,
        color: onSurface,
        letterSpacing: -0.25,
        height: 1.25,
      ),
      headlineMedium: TextStyle(
        fontFamily: primaryFont,
        fontSize: 28,
        fontWeight: FontWeight.w600,
        color: onSurface,
        letterSpacing: -0.15,
        height: 1.3,
      ),
      headlineSmall: TextStyle(
        fontFamily: primaryFont,
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: onSurface,
        letterSpacing: -0.05,
        height: 1.3,
      ),
      
      // Title styles - More elegant with refined spacing
      titleLarge: TextStyle(
        fontFamily: secondaryFont,
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: onSurface,
        letterSpacing: 0,
        height: 1.4,
      ),
      titleMedium: TextStyle(
        fontFamily: secondaryFont,
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: onSurface,
        letterSpacing: 0.15,
        height: 1.4,
      ),
      titleSmall: TextStyle(
        fontFamily: secondaryFont,
        fontSize: 14,
        fontWeight: FontWeight.w600,
        color: onSurface,
        letterSpacing: 0.1,
        height: 1.4,
      ),
      
      // Body styles - Clean, modern sans-serif for readability
      bodyLarge: TextStyle(
        fontFamily: secondaryFont,
        fontSize: 16,
        fontWeight: FontWeight.w400,
        color: onSurface,
        letterSpacing: 0.15,
        height: 1.6,
      ),
      bodyMedium: TextStyle(
        fontFamily: secondaryFont,
        fontSize: 14,
        fontWeight: FontWeight.w400,
        color: onSurface,
        letterSpacing: 0.25,
        height: 1.6,
      ),
      bodySmall: TextStyle(
        fontFamily: secondaryFont,
        fontSize: 12,
        fontWeight: FontWeight.w400,
        color: onSurfaceVariant,
        letterSpacing: 0.4,
        height: 1.5,
      ),
      
      // Label styles - Refined for UI elements
      labelLarge: TextStyle(
        fontFamily: secondaryFont,
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: onSurface,
        letterSpacing: 0.1,
        height: 1.4,
      ),
      labelMedium: TextStyle(
        fontFamily: secondaryFont,
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: onSurface,
        letterSpacing: 0.5,
        height: 1.4,
      ),
      labelSmall: TextStyle(
        fontFamily: secondaryFont,
        fontSize: 11,
        fontWeight: FontWeight.w500,
        color: onSurfaceVariant,
        letterSpacing: 0.5,
        height: 1.4,
      ),
    );
  }
  
  /// Get a text style for app bar titles
  static TextStyle appBarTitleStyle(BuildContext context, Color color) {
    // Try to get text direction safely
    final TextDirection textDirection = _getTextDirection(context);
    final fontFamily = textDirection == TextDirection.rtl ? tajawal : playfairDisplay;
    
    return TextStyle(
      fontFamily: fontFamily,
      fontSize: 18,
      fontWeight: FontWeight.w600,
      color: color,
      letterSpacing: 0.15,
    );
  }
  
  /// Get a text style for buttons
  static TextStyle buttonTextStyle(BuildContext context, Color color) {
    // Try to get text direction safely
    final TextDirection textDirection = _getTextDirection(context);
    final fontFamily = textDirection == TextDirection.rtl ? tajawal : montserrat;
    
    return TextStyle(
      fontFamily: fontFamily,
      fontSize: 15,
      fontWeight: FontWeight.w600,
      color: color,
      letterSpacing: 0.3,
    );
  }
  
  /// Get a text style for input fields
  static TextStyle inputTextStyle(BuildContext context, Color color) {
    // Try to get text direction safely
    final TextDirection textDirection = _getTextDirection(context);
    final fontFamily = textDirection == TextDirection.rtl ? tajawal : montserrat;
    
    return TextStyle(
      fontFamily: fontFamily,
      fontSize: 16,
      fontWeight: FontWeight.w400,
      color: color,
      letterSpacing: 0.15,
    );
  }
  
  /// Get a text style for navigation items
  static TextStyle navigationTextStyle(BuildContext context, Color color, {bool selected = false}) {
    // Try to get text direction safely
    final TextDirection textDirection = _getTextDirection(context);
    final fontFamily = textDirection == TextDirection.rtl ? tajawal : montserrat;
    
    return TextStyle(
      fontFamily: fontFamily,
      fontSize: selected ? 12 : 11,
      fontWeight: selected ? FontWeight.w600 : FontWeight.w500,
      color: color,
      letterSpacing: selected ? 0.2 : 0.1,
    );
  }
} 