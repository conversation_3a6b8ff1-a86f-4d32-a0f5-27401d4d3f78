import React, { useEffect, useState } from 'react';
import {
  Download,
  FileText,
  HardDrive,
  Image as ImageIcon,
  Plus,
  RefreshCw,
  Trash2,
  Upload,
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { usePermissions } from '../hooks/usePermissions';
import PermissionGate from '../components/auth/PermissionGate';
import { Permission } from '../types/permissions';
import { Button } from '../components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../components/ui/dialog';
import { Badge } from '../components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import FileUpload from '../components/ui/FileUpload';
import FileGallery, { type FileItem } from '../components/ui/FileGallery';
import { SkeletonCard } from '../components/ui/skeleton';
import { useErrorHandler } from '../hooks/useErrorHandler';
import ErrorDisplay from '../components/error/ErrorDisplay';
import fileUploadService from '../services/fileUploadService.js';

const FileManagementPage: React.FC = () => {
  const { user } = useAuth();
  const { hasPermission } = usePermissions();
  const [files, setFiles] = useState<FileItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showUploadDialog, setShowUploadDialog] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [stats, setStats] = useState({
    totalFiles: 0,
    totalSize: 0,
    imageCount: 0,
    documentCount: 0,
    otherCount: 0,
  });

  const errorHandler = useErrorHandler({
    onError: (error, context) => {
      console.error('File management error:', error, context);
    },
  });

  // Mock data - In real implementation, this would come from Firebase/API
  useEffect(() => {
    const loadFiles = async () => {
      try {
        setIsLoading(true);

        // Mock file data - replace with actual API call
        const mockFiles: FileItem[] = [
          {
            id: '1',
            fileName: 'screenshot_issue.png',
            fileUrl: 'https://via.placeholder.com/400x300/0066cc/ffffff?text=Screenshot',
            fileSize: 245760,
            fileType: 'image/png',
            uploadedAt: new Date('2024-01-15'),
            uploadedBy: 'user1',
            uploadedByName: 'John Doe',
            category: 'chat_attachment',
          },
          {
            id: '2',
            fileName: 'service_manual.pdf',
            fileUrl: 'https://example.com/manual.pdf',
            fileSize: 1048576,
            fileType: 'application/pdf',
            uploadedAt: new Date('2024-01-14'),
            uploadedBy: 'user2',
            uploadedByName: 'Jane Smith',
            category: 'request_attachment',
          },
          {
            id: '3',
            fileName: 'error_log.txt',
            fileUrl: 'https://example.com/error.txt',
            fileSize: 2048,
            fileType: 'text/plain',
            uploadedAt: new Date('2024-01-13'),
            uploadedBy: 'user1',
            uploadedByName: 'John Doe',
            category: 'system_log',
          },
        ];

        setFiles(mockFiles);

        // Calculate stats
        const totalSize = mockFiles.reduce((sum, file) => sum + file.fileSize, 0);
        const imageCount = mockFiles.filter((f) => f.fileType.startsWith('image/')).length;
        const documentCount = mockFiles.filter(
          (f) =>
            f.fileType.includes('pdf') ||
            f.fileType.includes('document') ||
            f.fileType.includes('text'),
        ).length;
        const otherCount = mockFiles.length - imageCount - documentCount;

        setStats({
          totalFiles: mockFiles.length,
          totalSize,
          imageCount,
          documentCount,
          otherCount,
        });
      } catch (error) {
        errorHandler.handleError(error, 'Failed to load files');
      } finally {
        setIsLoading(false);
      }
    };

    loadFiles();
  }, [errorHandler]);

  const handleFileUpload = (url: string, file: File) => {
    const newFile: FileItem = {
      id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      fileName: file.name,
      fileUrl: url,
      fileSize: file.size,
      fileType: file.type,
      uploadedAt: new Date(),
      uploadedBy: user?.uid || 'unknown',
      uploadedByName: user?.displayName || 'Unknown User',
      category: 'manual_upload',
    };

    setFiles((prev) => [newFile, ...prev]);
    setStats((prev) => ({
      ...prev,
      totalFiles: prev.totalFiles + 1,
      totalSize: prev.totalSize + file.size,
      imageCount: file.type.startsWith('image/') ? prev.imageCount + 1 : prev.imageCount,
      documentCount:
        file.type.includes('pdf') || file.type.includes('document') || file.type.includes('text')
          ? prev.documentCount + 1
          : prev.documentCount,
      otherCount:
        !file.type.startsWith('image/') &&
        !file.type.includes('pdf') &&
        !file.type.includes('document') &&
        !file.type.includes('text')
          ? prev.otherCount + 1
          : prev.otherCount,
    }));
    setShowUploadDialog(false);
  };

  const handleFileDelete = (fileId: string) => {
    const fileToDelete = files.find((f) => f.id === fileId);
    if (!fileToDelete) return;

    setFiles((prev) => prev.filter((f) => f.id !== fileId));
    setStats((prev) => ({
      ...prev,
      totalFiles: prev.totalFiles - 1,
      totalSize: prev.totalSize - fileToDelete.fileSize,
      imageCount: fileToDelete.fileType.startsWith('image/')
        ? prev.imageCount - 1
        : prev.imageCount,
      documentCount:
        fileToDelete.fileType.includes('pdf') ||
        fileToDelete.fileType.includes('document') ||
        fileToDelete.fileType.includes('text')
          ? prev.documentCount - 1
          : prev.documentCount,
      otherCount:
        !fileToDelete.fileType.startsWith('image/') &&
        !fileToDelete.fileType.includes('pdf') &&
        !fileToDelete.fileType.includes('document') &&
        !fileToDelete.fileType.includes('text')
          ? prev.otherCount - 1
          : prev.otherCount,
    }));
  };

  const handleBulkDelete = () => {
    if (selectedFiles.length === 0) return;

    if (window.confirm(`Are you sure you want to delete ${selectedFiles.length} files?`)) {
      selectedFiles.forEach((fileId) => handleFileDelete(fileId));
      setSelectedFiles([]);
    }
  };

  const filterFilesByCategory = (category: string) => {
    switch (category) {
      case 'images':
        return files.filter((f) => f.fileType.startsWith('image/'));
      case 'documents':
        return files.filter(
          (f) =>
            f.fileType.includes('pdf') ||
            f.fileType.includes('document') ||
            f.fileType.includes('text'),
        );
      case 'chat':
        return files.filter((f) => f.category === 'chat_attachment');
      case 'requests':
        return files.filter((f) => f.category === 'request_attachment');
      default:
        return files;
    }
  };

  if (errorHandler.error) {
    return <ErrorDisplay error={errorHandler.error} onRetry={errorHandler.clearError} />;
  }

  return (
    <div className='container mx-auto p-6 space-y-6'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4'>
        <div>
          <h1 className='text-3xl font-bold'>File Management</h1>
          <p className='text-muted-foreground'>Manage uploaded files and attachments</p>
        </div>

        <div className='flex items-center gap-2'>
          <Button variant='outline' onClick={() => window.location.reload()}>
            <RefreshCw className='h-4 w-4 mr-2' />
            Refresh
          </Button>

          <PermissionGate permission={Permission.MANAGE_FILES}>
            <Dialog open={showUploadDialog} onOpenChange={setShowUploadDialog}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className='h-4 w-4 mr-2' />
                  Upload Files
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Upload Files</DialogTitle>
                </DialogHeader>
                <FileUpload
                  requestId='general'
                  uploadOnSelect={true}
                  variant='dropzone'
                  onFileUpload={handleFileUpload}
                  showPreview={true}
                />
              </DialogContent>
            </Dialog>
          </PermissionGate>
        </div>
      </div>

      {/* Stats Cards */}
      {isLoading ? (
        <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
          {Array.from({ length: 4 }).map((_, i) => (
            <SkeletonCard key={i} />
          ))}
        </div>
      ) : (
        <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Total Files</CardTitle>
              <HardDrive className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{stats.totalFiles}</div>
              <p className='text-xs text-muted-foreground'>
                {fileUploadService.formatFileSize(stats.totalSize)} total
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Images</CardTitle>
              <ImageIcon className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{stats.imageCount}</div>
              <p className='text-xs text-muted-foreground'>Image files</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Documents</CardTitle>
              <FileText className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{stats.documentCount}</div>
              <p className='text-xs text-muted-foreground'>PDF, Word, Text files</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Other Files</CardTitle>
              <Upload className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{stats.otherCount}</div>
              <p className='text-xs text-muted-foreground'>Other file types</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Bulk Actions */}
      {selectedFiles.length > 0 && (
        <Card>
          <CardContent className='p-4'>
            <div className='flex items-center justify-between'>
              <div className='flex items-center gap-2'>
                <Badge variant='secondary'>{selectedFiles.length} files selected</Badge>
              </div>
              <div className='flex items-center gap-2'>
                <Button variant='outline' onClick={() => setSelectedFiles([])}>
                  Clear Selection
                </Button>
                <PermissionGate permission={Permission.MANAGE_FILES}>
                  <Button variant='destructive' onClick={handleBulkDelete}>
                    <Trash2 className='h-4 w-4 mr-2' />
                    Delete Selected
                  </Button>
                </PermissionGate>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* File Tabs */}
      <Tabs defaultValue='all' className='space-y-4'>
        <TabsList>
          <TabsTrigger value='all'>All Files</TabsTrigger>
          <TabsTrigger value='images'>Images</TabsTrigger>
          <TabsTrigger value='documents'>Documents</TabsTrigger>
          <TabsTrigger value='chat'>Chat Attachments</TabsTrigger>
          <TabsTrigger value='requests'>Request Attachments</TabsTrigger>
        </TabsList>

        <TabsContent value='all'>
          <FileGallery
            files={files}
            onFileDelete={hasPermission(Permission.MANAGE_FILES) ? handleFileDelete : undefined}
            canDelete={hasPermission(Permission.MANAGE_FILES)}
            showSearch={true}
            showFilter={true}
          />
        </TabsContent>

        <TabsContent value='images'>
          <FileGallery
            files={filterFilesByCategory('images')}
            onFileDelete={hasPermission(Permission.MANAGE_FILES) ? handleFileDelete : undefined}
            canDelete={hasPermission(Permission.MANAGE_FILES)}
            showSearch={true}
            showFilter={false}
          />
        </TabsContent>

        <TabsContent value='documents'>
          <FileGallery
            files={filterFilesByCategory('documents')}
            onFileDelete={hasPermission(Permission.MANAGE_FILES) ? handleFileDelete : undefined}
            canDelete={hasPermission(Permission.MANAGE_FILES)}
            showSearch={true}
            showFilter={false}
          />
        </TabsContent>

        <TabsContent value='chat'>
          <FileGallery
            files={filterFilesByCategory('chat')}
            onFileDelete={hasPermission(Permission.MANAGE_FILES) ? handleFileDelete : undefined}
            canDelete={hasPermission(Permission.MANAGE_FILES)}
            showSearch={true}
            showFilter={false}
          />
        </TabsContent>

        <TabsContent value='requests'>
          <FileGallery
            files={filterFilesByCategory('requests')}
            onFileDelete={hasPermission(Permission.MANAGE_FILES) ? handleFileDelete : undefined}
            canDelete={hasPermission(Permission.MANAGE_FILES)}
            showSearch={true}
            showFilter={false}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default FileManagementPage;
