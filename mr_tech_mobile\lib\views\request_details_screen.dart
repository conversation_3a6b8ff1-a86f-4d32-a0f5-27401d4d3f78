import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/translation_service.dart';
import '../models/service_model.dart';
import 'request_review_screen.dart';
import '../utils/navigation_utils.dart';
import '../widgets/text_styles.dart';

class RequestDetailsScreen extends StatefulWidget {
  final ServiceModel service;

  const RequestDetailsScreen({super.key, required this.service});

  @override
  State<RequestDetailsScreen> createState() => _RequestDetailsScreenState();
}

class _RequestDetailsScreenState extends State<RequestDetailsScreen>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _descriptionController = TextEditingController();
  late AnimationController _animationController;
  final bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _submitRequest() async {
    if (_formKey.currentState!.validate()) {
      // Navigate to review screen with real data
      NavigationUtils.navigateKeepingStack(
        context,
        RequestReviewScreen(
          service: widget.service,
          issueDescription: _descriptionController.text,
        ),
      );
    }
  }

  String _translate(String key) {
    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );
    return translationService.translate(key);
  }

  Widget _buildSelectedServiceCard() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );
    final languageCode = translationService.currentLocale.languageCode;

    // Get translated name and description from the service model
    final translatedName = widget.service.getTranslatedName(languageCode);
    final translatedDescription = widget.service.getTranslatedDescription(
      languageCode,
    );

    // Get icon for service
    IconData getServiceIcon() {
      if (widget.service.metadata != null &&
          widget.service.metadata!.containsKey('icon')) {
        final dynamic iconData = widget.service.metadata!['icon'];
        if (iconData is IconData) {
          return iconData;
        }

        // If it's a string, map it to an IconData
        if (iconData is String) {
          // Map of common icon names to IconData
          final Map<String, IconData> iconMap = {
            'computer': Icons.computer_rounded,
            'article': Icons.article_rounded,
            'print': Icons.print_rounded,
            'wifi': Icons.wifi_rounded,
            'security': Icons.security_rounded,
            'backup': Icons.backup_rounded,
            'storage': Icons.storage_rounded,
            'devices': Icons.devices_rounded,
          };

          return iconMap[iconData] ?? Icons.computer_rounded;
        }
      }

      // Default icon based on category
      final Map<String, IconData> categoryIcons = {
        'os': Icons.computer_rounded,
        'productivity': Icons.article_rounded,
        'hardware': Icons.devices_other_rounded,
        'network': Icons.wifi_rounded,
        'security': Icons.security_rounded,
        'data': Icons.storage_rounded,
      };

      return categoryIcons[widget.service.category] ?? Icons.computer_rounded;
    }

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade300),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _translate('Selected Service'),
              style: AppTextStyles.labelMedium(
                context,
                color: colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    getServiceIcon(),
                    color: colorScheme.primary,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        translatedName,
                        style: AppTextStyles.headingSmall(
                          context,
                          color: colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${_translate('L.E')} ${widget.service.basePrice.toStringAsFixed(0)}',
                        style: AppTextStyles.headingSmall(
                          context,
                          color: colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Divider(height: 1, color: Colors.grey.shade300),
            const SizedBox(height: 16),
            Text(
              translatedDescription,
              style: AppTextStyles.bodyMedium(
                context,
                color: colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: colorScheme.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.schedule, size: 16, color: colorScheme.primary),
                  const SizedBox(width: 8),
                  Text(
                    _translate('Est. Time: {duration} min').replaceAll(
                      '{duration}',
                      widget.service.estimatedDuration.toString(),
                    ),
                    style: AppTextStyles.bodyMedium(
                      context,
                      color: colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTipCard() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade300),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.lightbulb_outline_rounded,
                    color: Colors.orange.shade800,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _translate('Pro Tip'),
                    style: AppTextStyles.headingSmall(
                      context,
                      color: Colors.orange.shade800,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              _translate(
                'Provide as much detail as possible for a faster diagnosis. Mention any error messages, recent changes, or steps you have already tried.',
              ),
              style: AppTextStyles.bodyMedium(
                context,
                color: colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final translationService = Provider.of<TranslationService>(context);
    final isRtl = translationService.isRtl;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: CustomScrollView(
        slivers: [
          // Floating header
          SliverAppBar(
            expandedHeight: 80,
            floating: true,
            pinned: false,
            snap: true,
            elevation: 0,
            backgroundColor: colorScheme.surface,
            leading: BackButton(color: colorScheme.onSurface),
            flexibleSpace: FlexibleSpaceBar(
              background: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 10,
                  ),
                  child: Row(
                    children: [
                      const SizedBox(width: 56), // Space for back button
                      Expanded(
                        child: Text(
                          _translate('Request Details'),
                          style: AppTextStyles.headingMedium(
                            context,
                            color: colorScheme.onSurface,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(width: 56), // Balance the layout
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Main content
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Selected service summary card
                  _buildSelectedServiceCard(),

                  const SizedBox(height: 24),

                  // Issue description form
                  Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _translate('Describe the issue'),
                          style: AppTextStyles.headingSmall(context),
                        ),
                        const SizedBox(height: 16),
                        TextFormField(
                          controller: _descriptionController,
                          minLines: 4,
                          maxLines: 8,
                          style: AppTextStyles.bodyMedium(context),
                          decoration: InputDecoration(
                            hintText: _translate(
                              'e.g., "My computer is running slow..."',
                            ),
                            hintStyle: AppTextStyles.bodyMedium(
                              context,
                              color: colorScheme.onSurface.withOpacity(0.6),
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(
                                color: Colors.grey.shade300,
                              ),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(
                                color: Colors.grey.shade300,
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(
                                color: colorScheme.primary,
                                width: 2,
                              ),
                            ),
                          ),
                          validator: (value) {
                            if (value == null || value.trim().length < 10) {
                              return _translate(
                                'Please enter at least 10 characters',
                              );
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Pro Tip card
                  _buildTipCard(),
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(16),
        child: FilledButton(
          onPressed: _isSubmitting ? null : _submitRequest,
          style: FilledButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 16),
            backgroundColor: colorScheme.primary,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child:
              _isSubmitting
                  ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                  : Text(
                    _translate('Proceed to Review'),
                    style: AppTextStyles.buttonMedium(
                      context,
                      color: Colors.white,
                    ),
                  ),
        ),
      ),
    );
  }
}
