import { useCallback, useEffect, useRef, useState } from 'react';

export interface LoadingState {
  isLoading: boolean;
  progress?: number;
  message?: string;
  stage?: string;
}

export interface LoadingStatesConfig {
  [key: string]: LoadingState;
}

export interface UseLoadingStatesOptions {
  defaultStates?: LoadingStatesConfig;
  minLoadingTime?: number; // Minimum time to show loading (prevents flashing)
  debounceTime?: number; // Debounce rapid state changes
}

export function useLoadingStates(options: UseLoadingStatesOptions = {}) {
  const { defaultStates = {}, minLoadingTime = 300, debounceTime = 100 } = options;

  const [states, setStates] = useState<LoadingStatesConfig>(defaultStates);
  const timersRef = useRef<Map<string, NodeJS.Timeout>>(new Map());
  const startTimesRef = useRef<Map<string, number>>(new Map());

  // Cleanup timers on unmount
  useEffect(() => {
    return () => {
      timersRef.current.forEach((timer) => clearTimeout(timer));
      timersRef.current.clear();
    };
  }, []);

  const setLoadingState = useCallback(
    (key: string, state: Partial<LoadingState> | boolean) => {
      const normalizedState: Partial<LoadingState> =
        typeof state === 'boolean' ? { isLoading: state } : state;

      // Clear existing timer for this key
      const existingTimer = timersRef.current.get(key);
      if (existingTimer) {
        clearTimeout(existingTimer);
        timersRef.current.delete(key);
      }

      // If starting loading, record start time
      if (normalizedState.isLoading && !states[key]?.isLoading) {
        startTimesRef.current.set(key, Date.now());
      }

      // If stopping loading, ensure minimum loading time
      if (normalizedState.isLoading === false && states[key]?.isLoading) {
        const startTime = startTimesRef.current.get(key);
        if (startTime) {
          const elapsed = Date.now() - startTime;
          const remaining = Math.max(0, minLoadingTime - elapsed);

          if (remaining > 0) {
            // Delay the state update to meet minimum loading time
            const timer = setTimeout(() => {
              setStates((prev) => ({
                ...prev,
                [key]: { ...prev[key], ...normalizedState },
              }));
              startTimesRef.current.delete(key);
              timersRef.current.delete(key);
            }, remaining);

            timersRef.current.set(key, timer);
            return;
          } else {
            startTimesRef.current.delete(key);
          }
        }
      }

      // Apply debouncing for rapid changes
      if (debounceTime > 0) {
        const timer = setTimeout(() => {
          setStates((prev) => ({
            ...prev,
            [key]: { ...prev[key], ...normalizedState },
          }));
          timersRef.current.delete(key);
        }, debounceTime);

        timersRef.current.set(key, timer);
      } else {
        setStates((prev) => ({
          ...prev,
          [key]: { ...prev[key], ...normalizedState },
        }));
      }
    },
    [states, minLoadingTime, debounceTime],
  );

  const startLoading = useCallback(
    (key: string, message?: string, stage?: string) => {
      setLoadingState(key, {
        isLoading: true,
        message,
        stage,
        progress: undefined,
      });
    },
    [setLoadingState],
  );

  const stopLoading = useCallback(
    (key: string) => {
      setLoadingState(key, {
        isLoading: false,
        message: undefined,
        stage: undefined,
        progress: undefined,
      });
    },
    [setLoadingState],
  );

  const updateProgress = useCallback(
    (key: string, progress: number, message?: string, stage?: string) => {
      setLoadingState(key, {
        isLoading: true,
        progress: Math.max(0, Math.min(100, progress)),
        message,
        stage,
      });
    },
    [setLoadingState],
  );

  const setStage = useCallback(
    (key: string, stage: string, message?: string) => {
      setLoadingState(key, {
        isLoading: true,
        stage,
        message,
      });
    },
    [setLoadingState],
  );

  const isLoading = useCallback(
    (key: string) => {
      return states[key]?.isLoading ?? false;
    },
    [states],
  );

  const isAnyLoading = useCallback(() => {
    return Object.values(states).some((state) => state.isLoading);
  }, [states]);

  const getLoadingState = useCallback(
    (key: string): LoadingState => {
      return states[key] ?? { isLoading: false };
    },
    [states],
  );

  const clearAll = useCallback(() => {
    // Clear all timers
    timersRef.current.forEach((timer) => clearTimeout(timer));
    timersRef.current.clear();
    startTimesRef.current.clear();

    // Reset all states
    setStates({});
  }, []);

  const clearState = useCallback((key: string) => {
    const timer = timersRef.current.get(key);
    if (timer) {
      clearTimeout(timer);
      timersRef.current.delete(key);
    }
    startTimesRef.current.delete(key);

    setStates((prev) => {
      const newStates = { ...prev };
      delete newStates[key];
      return newStates;
    });
  }, []);

  return {
    states,
    setLoadingState,
    startLoading,
    stopLoading,
    updateProgress,
    setStage,
    isLoading,
    isAnyLoading,
    getLoadingState,
    clearAll,
    clearState,
  };
}

// Hook for simple single loading state
export function useLoading(initialLoading = false, options: UseLoadingStatesOptions = {}) {
  const { startLoading, stopLoading, isLoading, getLoadingState, updateProgress, setStage } =
    useLoadingStates(options);

  const key = 'default';

  // Initialize with initial loading state
  useEffect(() => {
    if (initialLoading) {
      startLoading(key);
    }
  }, [initialLoading, startLoading]);

  return {
    isLoading: isLoading(key),
    startLoading: (message?: string, stage?: string) => startLoading(key, message, stage),
    stopLoading: () => stopLoading(key),
    updateProgress: (progress: number, message?: string, stage?: string) =>
      updateProgress(key, progress, message, stage),
    setStage: (stage: string, message?: string) => setStage(key, stage, message),
    loadingState: getLoadingState(key),
  };
}

// Hook for managing loading states with async operations
export function useAsyncLoading<T = any>(options: UseLoadingStatesOptions = {}) {
  const { startLoading, stopLoading, isLoading, getLoadingState } = useLoadingStates(options);
  const [data, setData] = useState<T | null>(null);
  const [error, setError] = useState<Error | null>(null);

  const execute = useCallback(
    async (
      key: string,
      operation: () => Promise<T>,
      loadingMessage?: string,
    ): Promise<T | null> => {
      try {
        setError(null);
        startLoading(key, loadingMessage);

        const result = await operation();
        setData(result);
        return result;
      } catch (err) {
        const error = err instanceof Error ? err : new Error(String(err));
        setError(error);
        return null;
      } finally {
        stopLoading(key);
      }
    },
    [startLoading, stopLoading],
  );

  return {
    execute,
    data,
    error,
    isLoading,
    getLoadingState,
    clearError: () => setError(null),
    clearData: () => setData(null),
  };
}
