import React, { useEffect, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { ArrowLeft, MessageCircle } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import type { RequestModel } from '../types/request.js';
import requestService from '../services/requestService.js';
import ChatList from '../components/chat/ChatList';
import ChatWindow from '../components/chat/ChatWindow';
import { Button } from '../components/ui/button';
import { cn } from '../lib/utils';

const ChatPage: React.FC = () => {
  const { user } = useAuth();
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedRequest, setSelectedRequest] = useState<RequestModel | null>(null);
  const [isMobileView, setIsMobileView] = useState(false);

  // Get request ID from URL params
  const requestIdFromUrl = searchParams.get('requestId') || searchParams.get('request');

  // Handle responsive design
  useEffect(() => {
    const checkMobileView = () => {
      setIsMobileView(window.innerWidth < 768);
    };

    checkMobileView();
    window.addEventListener('resize', checkMobileView);

    return () => window.removeEventListener('resize', checkMobileView);
  }, []);

  // Handle chat selection
  const handleChatSelect = (request: RequestModel) => {
    setSelectedRequest(request);

    // Update URL params
    setSearchParams({ requestId: request.id });
  };

  // Handle back to chat list (mobile)
  const handleBackToList = () => {
    setSelectedRequest(null);
    setSearchParams({});
  };

  // Auto-select chat if request ID is in URL
  useEffect(() => {
    const loadRequestFromUrl = async () => {
      if (requestIdFromUrl && !selectedRequest) {
        try {
          const request = await requestService.getById(requestIdFromUrl);
          if (request) {
            setSelectedRequest(request);
          }
        } catch (error) {
          console.error('Error loading request from URL:', error);
        }
      }
    };

    loadRequestFromUrl();
  }, [requestIdFromUrl, selectedRequest]);

  if (!user) {
    return (
      <div className='flex items-center justify-center h-full bg-white'>
        <div className='text-center'>
          <MessageCircle className='w-12 h-12 text-gray-400 mx-auto mb-4' />
          <h2 className='text-xl font-semibold text-gray-900 mb-2'>Authentication Required</h2>
          <p className='text-gray-500'>Please log in to access the chat system.</p>
        </div>
      </div>
    );
  }

  // Mobile view - show either list or chat
  if (isMobileView) {
    return (
      <div className='h-full flex flex-col'>
        {selectedRequest ? (
          <>
            {/* Mobile Chat Header */}
            <div className='flex items-center gap-3 p-4 border-b border-gray-200 bg-white'>
              <Button variant='ghost' size='sm' onClick={handleBackToList} className='p-2'>
                <ArrowLeft className='w-5 h-5' />
              </Button>
              <div className='flex-1'>
                <h1 className='text-lg font-semibold text-gray-900'>Chat</h1>
                <p className='text-sm text-gray-500'>Request #{selectedRequest.id.slice(-8)}</p>
              </div>
            </div>

            {/* Chat Window */}
            <div className='flex-1'>
              <ChatWindow request={selectedRequest} onClose={handleBackToList} />
            </div>
          </>
        ) : (
          <>
            {/* Mobile Chat List Header */}
            <div className='p-4 border-b border-gray-200 bg-white'>
              <h1 className='text-lg font-semibold text-gray-900'>Chats</h1>
              <p className='text-sm text-gray-500'>
                {user.role === 'customer' ? 'Your support conversations' : 'Active support chats'}
              </p>
            </div>

            {/* Chat List */}
            <div className='flex-1'>
              <ChatList onChatSelect={handleChatSelect} selectedChatId={selectedRequest?.id} />
            </div>
          </>
        )}
      </div>
    );
  }

  // Desktop view - show both list and chat side by side
  return (
    <div className='h-full flex bg-white'>
      {/* Chat List Sidebar */}
      <div className='w-80 border-r border-gray-200 flex-shrink-0'>
        <ChatList onChatSelect={handleChatSelect} selectedChatId={selectedRequest?.id} />
      </div>

      {/* Chat Window */}
      <div className='flex-1'>
        {selectedRequest ? (
          <ChatWindow request={selectedRequest} onClose={() => setSelectedRequest(null)} />
        ) : (
          <div className='h-full flex items-center justify-center bg-gray-50'>
            <div className='text-center max-w-md mx-auto'>
              <MessageCircle className='w-16 h-16 text-gray-400 mx-auto mb-6' />
              <h2 className='text-2xl font-semibold text-gray-900 mb-4'>Welcome to Chat Support</h2>
              <p className='text-gray-500 mb-6'>
                {user.role === 'customer'
                  ? 'Select a conversation from the sidebar to start chatting with our support team.'
                  : 'Select a chat from the sidebar to start helping customers.'}
              </p>
              <div className='space-y-2 text-sm text-gray-400'>
                <p>💬 Real-time messaging</p>
                <p>📎 File sharing support</p>
                <p>🔔 Instant notifications</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatPage;
