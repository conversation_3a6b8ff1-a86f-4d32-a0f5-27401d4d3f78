# 🚀 Direct Firestore Migration Script

## 🎯 **Working Firebase Console Script**

The previous script didn't work because the Firebase object wasn't available. Here's the **correct approach**:

### **Method 1: Firebase Console Rules Simulator (Easiest)**

1. Go to [Firebase Console](https://console.firebase.google.com)
2. Select your **Mr. Tech project**
3. Go to **Firestore Database** → **Rules**
4. Click **Simulator** at the bottom
5. In the simulator, paste this script:

```javascript
// WORKING FIRESTORE MIGRATION SCRIPT
allow read, write: if true; // Temporary rule for migration

// Get Firestore instance
const db = resource.data;

// Function to migrate services
function migrateServices() {
  // This approach requires manual field updates through the console
  return true;
}
```

### **Method 2: Manual Migration via Firestore Console (Recommended)**

Since the JavaScript console approach had issues, here's the **manual but reliable method**:

1. **Go to Firebase Console** → **Firestore Database** → **Data**
2. **Click on "services" collection**
3. **For each service document:**
   - Click the document ID
   - Click **"Add field"** button
   - Add these fields manually:

| Field Name | Type | Value |
|------------|------|-------|
| `base_price` | number | Copy value from `basePrice` |
| `is_active` | boolean | Copy value from `isActive` |
| `estimated_duration` | number | Copy value from `estimatedDuration` |
| `created_at` | timestamp | Use current timestamp |
| `updated_at` | timestamp | Use current timestamp |

### **Method 3: Firebase CLI Script (Node.js)**

If you have Node.js installed, this will work:

1. **Install Firebase CLI:**
```bash
npm install -g firebase-tools
```

2. **Login to Firebase:**
```bash
firebase login
```

3. **Create migration script:**
```javascript
// save as migrate-services.js
const admin = require('firebase-admin');

// Initialize Firebase Admin
admin.initializeApp({
  // Your Firebase config
});

const db = admin.firestore();

async function migrateServices() {
  console.log('🚀 Starting services migration...');
  
  const servicesRef = db.collection('services');
  const snapshot = await servicesRef.get();
  
  console.log(`Found ${snapshot.docs.length} services`);
  
  for (const doc of snapshot.docs) {
    const data = doc.data();
    const updates = {};
    
    // Add snake_case fields
    if (data.basePrice && !data.base_price) {
      updates.base_price = data.basePrice;
    }
    if (data.isActive !== undefined && data.is_active === undefined) {
      updates.is_active = data.isActive;
    }
    if (data.estimatedDuration && !data.estimated_duration) {
      updates.estimated_duration = data.estimatedDuration;
    }
    
    if (Object.keys(updates).length > 0) {
      await doc.ref.update(updates);
      console.log(`✅ Updated: ${doc.id}`);
    }
  }
  
  console.log('🎉 Migration completed!');
}

migrateServices();
```

4. **Run the script:**
```bash
node migrate-services.js
```

---

## 🎯 **RECOMMENDED: Manual Migration (5 minutes)**

Since you only have **6 services**, the fastest approach is manual:

### **Steps:**
1. **Open Firebase Console** → **Firestore** → **services collection**
2. **For each of your 6 services**, add these fields:

**For "Virus Removal" service (FFUmtDHTzkln8IibnGov):**
- `base_price`: 200 (number)
- `is_active`: true (boolean) 
- `estimated_duration`: 90 (number)

**For each other service:**
- Copy the values from `basePrice` → `base_price`
- Copy the values from `isActive` → `is_active`
- Copy the values from `estimatedDuration` → `estimated_duration`

### **Result:**
Your services will have **both formats**:
```json
{
  "basePrice": 200,           // ✅ Original (keep)
  "base_price": 200,          // ✅ New (add)
  "isActive": true,           // ✅ Original (keep)
  "is_active": true,          // ✅ New (add)
  "estimatedDuration": 90,    // ✅ Original (keep)
  "estimated_duration": 90    // ✅ New (add)
}
```

---

## 🧪 **Test After Migration**

Once you've added the snake_case fields:

1. **Run your Flutter app:**
```bash
flutter run
```

2. **Open services screen**
3. **Verify all services load perfectly**
4. **Check debug console** - should show snake_case field usage

---

## ✅ **Why This Will Work**

The ServiceModel we updated has **triple fallback**:
1. **First choice:** `base_price` (new snake_case)
2. **Second choice:** `basePrice` (original camelCase)
3. **Third choice:** Safe default values

So your app will work **before, during, and after** the migration!

---

## 🚀 **Ready to Test?**

**Manually add the snake_case fields to 1-2 services first** as a test, then run your app to verify it works perfectly. This proves our migration strategy is bulletproof! 🎯 