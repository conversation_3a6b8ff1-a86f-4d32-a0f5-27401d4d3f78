const functions = require("firebase-functions");
const admin = require("firebase-admin");

/**
 * Cloud function to send FCM notifications when a new document
 * is added to the notifications collection. This is used for
 * sending chat activation notifications to mobile app users.
 */
exports.sendFcmNotification = functions.firestore
  .document("notifications/{notificationId}")
  .onCreate(async (snapshot, context) => {
    const notificationData = snapshot.data();
    
    console.log("Processing notification:", JSON.stringify({
      notificationId: context.params.notificationId,
      recipientId: notificationData.recipient_id || notificationData.userId || notificationData.user_id || "MISSING",
      title: notificationData.title,
      type: notificationData.type || "DEFAULT",
      hasToken: !!notificationData.fcm_token || !!notificationData.fcmToken ||
        (notificationData.fcmTokens && notificationData.fcmTokens.length > 0) ||
        (notificationData.device_tokens && notificationData.device_tokens.length > 0)
    }));
    
    // Extract the token - handle all possible token storage formats
    let fcmToken = null;
    let userId = notificationData.recipient_id || notificationData.userId || notificationData.user_id;
    
    try {
      // Option 1: Direct FCM token in the notification - snake_case
      if (notificationData.fcm_token) {
        console.log("Using direct fcm_token from notification");
        fcmToken = notificationData.fcm_token;
      } 
      // Option 2: Direct FCM token in the notification - camelCase
      else if (notificationData.fcmToken) {
        console.log("Using direct fcmToken from notification");
        fcmToken = notificationData.fcmToken;
      } 
      // Option 3: Array of FCM tokens in the notification
      else if (notificationData.fcmTokens && notificationData.fcmTokens.length > 0) {
        console.log("Using first token from fcmTokens array");
        fcmToken = notificationData.fcmTokens[0];
      }
      // Option 4: device_tokens array in the notification
      else if (notificationData.device_tokens && notificationData.device_tokens.length > 0) {
        console.log("Using first token from device_tokens array");
        fcmToken = notificationData.device_tokens[0];
      }
      // Option 5: Look up the user in Firestore if we have userId
      else if (userId) {
        console.log(`No token in notification. Fetching from user document: ${userId}`);
        
        try {
          const userDoc = await admin.firestore().collection("users").doc(userId).get();
          
          if (userDoc.exists) {
            const userData = userDoc.data();
            console.log(`Found user document for ${userId}, checking for tokens`);
            console.log(`User document data keys: ${Object.keys(userData)}`);
            
            // Check for FCM token in different possible fields (priority ordered)
            let tokenSource = null;
            
            // Prioritize standardized device_tokens field (snake_case)
            if (Array.isArray(userData.device_tokens) && userData.device_tokens.length > 0) {
              fcmToken = userData.device_tokens[0];
              tokenSource = 'device_tokens';
              console.log(`Using token from device_tokens array: ${fcmToken.substring(0, 10)}...`);
            } 
            // Second priority: deviceTokens (camelCase)
            else if (Array.isArray(userData.deviceTokens) && userData.deviceTokens.length > 0) {
              fcmToken = userData.deviceTokens[0];
              tokenSource = 'deviceTokens';
              console.log(`Using token from deviceTokens array: ${fcmToken.substring(0, 10)}...`);
            }
            // Fallbacks for legacy fields
            else if (userData.fcm_token) {
              fcmToken = userData.fcm_token;
              tokenSource = 'fcm_token';
              console.log(`Using token from fcm_token field: ${fcmToken.substring(0, 10)}...`);
            } else if (userData.fcmToken) {
              fcmToken = userData.fcmToken;
              tokenSource = 'fcmToken';
              console.log(`Using token from fcmToken field: ${fcmToken.substring(0, 10)}...`);
            } else if (Array.isArray(userData.fcmTokens) && userData.fcmTokens.length > 0) {
              fcmToken = userData.fcmTokens[0];
              tokenSource = 'fcmTokens';
              console.log(`Using token from fcmTokens array: ${fcmToken.substring(0, 10)}...`);
            }
            
            // Check the service request document first (primary source according to documentation)
            if (!fcmToken && notificationData.request_id) {
              console.log(`Checking service request for FCM token: ${notificationData.request_id}`);

              try {
                const requestDoc = await admin.firestore()
                  .collection('service_requests')
                  .doc(notificationData.request_id)
                  .get();

                if (requestDoc.exists) {
                  const requestData = requestDoc.data();
                  console.log(`Service request data keys: ${Object.keys(requestData)}`);

                  if (requestData.customer_fcm_token) {
                    fcmToken = requestData.customer_fcm_token;
                    tokenSource = 'service_request_customer_fcm_token';
                    console.log(`Found token in service request (snake_case): ${fcmToken.substring(0, 10)}...`);
                  } else if (requestData.customerFcmToken) {
                    fcmToken = requestData.customerFcmToken;
                    tokenSource = 'service_request_customerFcmToken';
                    console.log(`Found token in service request (camelCase): ${fcmToken.substring(0, 10)}...`);
                  } else {
                    console.log(`No FCM token found in service request document`);
                  }
                } else {
                  console.log(`Service request document not found: ${notificationData.request_id}`);
                }
              } catch (requestFetchError) {
                console.error(`Error fetching service request: ${requestFetchError}`);
              }
            }

            // If we still don't have a token, check the dedicated tokens collection
            if (!fcmToken) {
              console.log(`No token found in user document or service request. Checking device_tokens collection...`);

              // Query the tokens collection for devices belonging to this user
              const tokensSnapshot = await admin.firestore()
                .collection('device_tokens')
                .where('userId', '==', userId)
                .where('active', '==', true)
                .orderBy('lastUpdated', 'desc')
                .limit(1)
                .get();

              if (!tokensSnapshot.empty) {
                const tokenDoc = tokensSnapshot.docs[0];
                fcmToken = tokenDoc.data().token;
                tokenSource = 'device_tokens_collection';
                console.log(`Found token in device_tokens collection: ${fcmToken.substring(0, 10)}...`);
              }
            }
          } else {
            console.error(`User document not found for ID: ${userId}`);
          }
        } catch (userFetchError) {
          console.error(`Error fetching user document: ${userFetchError}`);
        }
      }
      
      // Exit if no token found
      if (!fcmToken) {
        console.error("No FCM token found for recipient. Notification cannot be sent.");
        console.error(`Recipient ID: ${userId || "unknown"}`);
        return null;
      }
      
      console.log(`Sending FCM notification to token: ${fcmToken.substring(0, 10)}...`);

      // Check for duplicate notifications (chat_activation and request_approved)
      const duplicateCheckTypes = ['chat_activation', 'CHAT_ACTIVATION', 'request_approved', 'REQUEST_APPROVED'];
      if (duplicateCheckTypes.includes(notificationData.type)) {
        const requestId = notificationData.request_id || notificationData.requestId;
        if (requestId) {
          try {
            const recentNotifications = await admin.firestore()
              .collection('notifications')
              .where('request_id', '==', requestId)
              .where('type', '==', notificationData.type)
              .where('delivered', '==', true)
              .orderBy('timestamp', 'desc')
              .limit(1)
              .get();

            if (!recentNotifications.empty) {
              const recentNotification = recentNotifications.docs[0];
              const recentTimestamp = recentNotification.data().timestamp;
              const currentTimestamp = notificationData.timestamp;

              // If there's a delivered notification within the last 30 seconds, skip this one
              if (currentTimestamp && recentTimestamp && (currentTimestamp - recentTimestamp < 30000)) {
                console.log(`Recent ${notificationData.type} notification already delivered for request ${requestId}, skipping duplicate`);
                await snapshot.ref.update({
                  delivered: true,
                  processed: true,
                  skipped_duplicate: true,
                  processed_at: admin.firestore.FieldValue.serverTimestamp(),
                });
                return null;
              }
            }
          } catch (duplicateCheckError) {
            console.error("Error checking for duplicate notifications:", duplicateCheckError);
            // Continue with sending the notification if duplicate check fails
          }
        }
      }

      // Prepare message data
      const messageData = {
        notification: {
          title: notificationData.title || "New Notification",
          body: notificationData.body || "You have a new notification",
        },
        data: {
          // Convert all values to strings since FCM requires string values
          requestId: notificationData.requestId || "",
          type: notificationData.type || "DEFAULT",
          notificationType: notificationData.notificationType || notificationData.type?.toLowerCase() || "default",
          clickAction: notificationData.clickAction || "FLUTTER_NOTIFICATION_CLICK",
          timestamp: String(notificationData.timestamp || Date.now()),
        },
        token: fcmToken,
        android: {
          priority: "high",
          notification: {
            sound: "default",
            channelId: "mr_tech_notifications"
          }
        },
        apns: {
          payload: {
            aps: {
              sound: "default",
              badge: 1
            }
          }
        }
      };
      
      // Add additional data fields if present
      if (notificationData.technicianName) {
        messageData.data.technicianName = notificationData.technicianName;
      }
      
      // Include any data field from the notification
      if (notificationData.data && typeof notificationData.data === 'object') {
        Object.keys(notificationData.data).forEach(key => {
          // Convert any non-string values to strings
          const value = notificationData.data[key];
          messageData.data[key] = typeof value === 'string' ? value : String(value);
        });
      }
      
      // Log the full message data (excluding token for security)
      const logData = { ...messageData };
      if (logData.token) {
        logData.token = `${logData.token.substring(0, 10)}...`;
      }
      console.log(`FCM message data: ${JSON.stringify(logData)}`);
      
      // Send the notification
      const response = await admin.messaging().send(messageData);
      
      // Log success and return
      console.log(`Successfully sent FCM notification: ${response}`);
      
      // Update the notification document with success status
      await snapshot.ref.update({
        delivered: true,
        delivered_at: admin.firestore.FieldValue.serverTimestamp(),
      });
      
      return {success: true, messageId: response};
    } catch (error) {
      // Log error details
      console.error("Error sending FCM notification:", error);
      console.error(`Error code: ${error.code}, Error message: ${error.message}`);
      console.error(`Notification data: ${JSON.stringify({
        title: notificationData.title,
        type: notificationData.type,
        recipientId: userId
      })}`);
      
      // Update the notification document with failure status
      await snapshot.ref.update({
        delivered: false,
        error: error.message,
        error_code: error.code || "unknown_error",
        updated_at: admin.firestore.FieldValue.serverTimestamp(),
      });
      
      return {success: false, error: error.message};
    }
  });

exports.sendNotificationToUser = async (userId, title, body, data = {}) => {
  console.log(`Sending notification to user: ${userId}`);
  console.log(`Notification title: ${title}`);
  console.log(`Notification body: ${body}`);
  console.log(`Notification data: ${JSON.stringify(data)}`);
  
  try {
    let fcmToken = null;
    
    // First, get user document to find their token
    const userDoc = await admin.firestore().collection('users').doc(userId).get();
    
    if (userDoc.exists) {
      const userData = userDoc.data();
      console.log(`User document data keys: ${Object.keys(userData)}`);
      
      // Try all possible token storage patterns
      if (userData.fcm_token) {
        console.log("Using fcm_token from user document");
        fcmToken = userData.fcm_token;
      } else if (userData.fcmToken) {
        console.log("Using fcmToken from user document");
        fcmToken = userData.fcmToken;
      } else if (userData.fcmTokens && userData.fcmTokens.length > 0) {
        console.log("Using first token from fcmTokens array in user document");
        fcmToken = userData.fcmTokens[0];
      } else if (userData.deviceTokens && userData.deviceTokens.length > 0) {
        console.log("Using first token from deviceTokens array in user document");
        fcmToken = userData.deviceTokens[0];
      } else if (userData.device_tokens && userData.device_tokens.length > 0) {
        console.log("Using first token from device_tokens array in user document");
        fcmToken = userData.device_tokens[0];
      } else {
        console.error(`No FCM token found for user ${userId} in user document`);
        console.log(`Available fields in user document: ${JSON.stringify(Object.keys(userData))}`);
      }
    } else {
      console.error(`User document not found for ID: ${userId}`);
    }
    
    // If no token found in user document, check the tokens collection
    if (!fcmToken) {
      console.log(`No token found in user document. Checking device_tokens collection...`);
      
      // Query the tokens collection for devices belonging to this user
      const tokensSnapshot = await admin.firestore()
        .collection('device_tokens')
        .where('userId', '==', userId)
        .where('active', '==', true)
        .orderBy('lastUpdated', 'desc')
        .limit(1)
        .get();
      
      if (!tokensSnapshot.empty) {
        const tokenDoc = tokensSnapshot.docs[0];
        fcmToken = tokenDoc.data().token;
        console.log(`Found token in device_tokens collection: ${fcmToken.substring(0, 10)}...`);
      }
    }
    
    // Debug log full FCM token (only in development)
    if (process.env.NODE_ENV !== 'production' && fcmToken) {
      console.log(`DEBUG - Full FCM token used: ${fcmToken}`);
    }
    
    if (!fcmToken) {
      console.error(`No valid FCM token found for user ${userId}`);
      return { success: false, error: 'No FCM token found', userId };
    }
    
    // Send the notification
    const message = {
      token: fcmToken,
      notification: {
        title: title,
        body: body,
      },
      data: {
        ...data,
        // Convert all data values to strings as FCM requires
        ...Object.keys(data).reduce((acc, key) => {
          acc[key] = String(data[key]);
          return acc;
        }, {})
      },
      android: {
        priority: 'high',
        notification: {
          channelId: 'mr_tech_notifications',
          priority: 'high',
          sound: 'default',
          defaultSound: true,
        },
      },
      apns: {
        headers: {
          'apns-priority': '10',
        },
        payload: {
          aps: {
            sound: 'default',
            badge: 1,
          },
        },
      },
    };
    
    // Send the message and get the response
    const response = await admin.messaging().send(message);
    console.log(`Successfully sent message: ${response}`);
    return { success: true, messageId: response };
    
  } catch (error) {
    console.error("Error sending notification to user:", error);
    console.error(`Error code: ${error.code}, Error message: ${error.message}`);
    return { success: false, error: error.message };
  }
}; 