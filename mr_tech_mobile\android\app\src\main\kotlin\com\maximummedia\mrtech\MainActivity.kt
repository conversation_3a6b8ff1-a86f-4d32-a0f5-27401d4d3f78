package com.maximummedia.mrtech

import android.os.Build
import android.os.Bundle
import android.content.pm.PackageManager
import android.util.Log
import android.view.View
import android.view.WindowManager
import androidx.core.view.WindowCompat
import androidx.core.view.WindowInsetsControllerCompat
import io.flutter.embedding.android.FlutterActivity

class MainActivity: FlutterActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Configure edge-to-edge display for Android
        configureEdgeToEdgeDisplay()

        // Print SHA-1 for debugging Google Sign-In
        try {
            val info = packageManager.getPackageInfo(packageName, PackageManager.GET_SIGNATURES)
            info.signatures?.forEach { signature ->
                val md = java.security.MessageDigest.getInstance("SHA-1")
                md.update(signature.toByteArray())
                val sha1 = java.math.BigInteger(1, md.digest()).toString(16).padStart(40, '0')
                Log.d("KeyHash", "SHA1: $sha1")
            }
        } catch (e: Exception) {
            Log.e("KeyHash", "Error getting SHA1: ${e.message}")
        }
    }

    private fun configureEdgeToEdgeDisplay() {
        // Enable edge-to-edge display
        WindowCompat.setDecorFitsSystemWindows(window, false)

        // Configure system bars for transparency
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            window.statusBarColor = android.graphics.Color.TRANSPARENT
            window.navigationBarColor = android.graphics.Color.TRANSPARENT

            // Set system bar appearance
            val windowInsetsController = WindowCompat.getInsetsController(window, window.decorView)
            windowInsetsController.isAppearanceLightStatusBars = true
            windowInsetsController.isAppearanceLightNavigationBars = true
            
            // Hide the navigation bar divider
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
                window.navigationBarDividerColor = android.graphics.Color.TRANSPARENT
            }
            
            // Add FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS flag
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            
            // For Android 10+ (API 29+), set proper flags for gesture navigation
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                window.isNavigationBarContrastEnforced = false
            }
        }

        // For Android 12+ (API 31+), disable navigation bar contrast enforcement
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            window.isNavigationBarContrastEnforced = false
            window.isStatusBarContrastEnforced = false
        }
        
        // Set the root view to handle insets properly
        window.decorView.setOnApplyWindowInsetsListener { view, insets ->
            view.setPadding(0, 0, 0, 0) // Remove any padding that might be applied
            insets
        }
    }
}