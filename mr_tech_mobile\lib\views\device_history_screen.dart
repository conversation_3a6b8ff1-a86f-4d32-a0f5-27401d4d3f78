import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/translation_service.dart';
import '../widgets/text_styles.dart';

class DeviceHistoryScreen extends StatefulWidget {
  const DeviceHistoryScreen({super.key});

  @override
  State<DeviceHistoryScreen> createState() => _DeviceHistoryScreenState();
}

class _DeviceHistoryScreenState extends State<DeviceHistoryScreen> {
  final List<DeviceInfo> _devices = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadDevices();
  }

  String _translate(String key) {
    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );
    return translationService.translate(key);
  }

  Future<void> _loadDevices() async {
    // Simulate API call with delay
    await Future.delayed(const Duration(seconds: 1));

    // Mock data - in a real app, this would come from a backend service
    final mockDevices = [
      DeviceInfo(
        name: 'Windows Desktop',
        deviceType: DeviceType.desktop,
        lastLogin: DateTime.now().subtract(const Duration(hours: 2)),
        location: 'Cairo, Egypt',
        ipAddress: '*************',
        isCurrentDevice: true,
      ),
      DeviceInfo(
        name: 'Samsung Galaxy S22',
        deviceType: DeviceType.mobile,
        lastLogin: DateTime.now().subtract(const Duration(days: 1, hours: 5)),
        location: 'Cairo, Egypt',
        ipAddress: '*************',
        isCurrentDevice: false,
      ),
      DeviceInfo(
        name: 'iPad Pro',
        deviceType: DeviceType.tablet,
        lastLogin: DateTime.now().subtract(const Duration(days: 5, hours: 3)),
        location: 'Alexandria, Egypt',
        ipAddress: '*************',
        isCurrentDevice: false,
      ),
      DeviceInfo(
        name: 'MacBook Pro',
        deviceType: DeviceType.desktop,
        lastLogin: DateTime.now().subtract(const Duration(days: 12, hours: 7)),
        location: 'Cairo, Egypt',
        ipAddress: '*************',
        isCurrentDevice: false,
      ),
    ];

    setState(() {
      _devices.addAll(mockDevices);
      _isLoading = false;
    });
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        return _translate('Just now');
      } else {
        return _translate(
          'Today at ${date.hour}:${date.minute.toString().padLeft(2, '0')}',
        );
      }
    } else if (difference.inDays == 1) {
      return _translate(
        'Yesterday at ${date.hour}:${date.minute.toString().padLeft(2, '0')}',
      );
    } else if (difference.inDays < 7) {
      return _translate('${difference.inDays} days ago');
    } else {
      final months = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec',
      ];
      return '${date.day} ${_translate(months[date.month - 1])} ${date.year}';
    }
  }

  IconData _getDeviceIcon(DeviceType type) {
    switch (type) {
      case DeviceType.desktop:
        return Icons.computer_rounded;
      case DeviceType.mobile:
        return Icons.smartphone_rounded;
      case DeviceType.tablet:
        return Icons.tablet_mac_rounded;
    }
  }

  void _showDeviceDetails(DeviceInfo device) {
    final colorScheme = Theme.of(context).colorScheme;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: BorderSide(color: Colors.grey.shade300),
            ),
            title: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withOpacity(0.1),
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: Icon(
                    _getDeviceIcon(device.deviceType),
                    size: 20,
                    color: colorScheme.primary,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    device.name,
                    style: AppTextStyles.headingSmall(context),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDeviceInfoItem(
                  label: _translate('Last Login'),
                  value: _formatDate(device.lastLogin),
                  icon: Icons.access_time_rounded,
                  colorScheme: colorScheme,
                ),
                const SizedBox(height: 16),
                _buildDeviceInfoItem(
                  label: _translate('Location'),
                  value: device.location,
                  icon: Icons.location_on_rounded,
                  colorScheme: colorScheme,
                ),
                const SizedBox(height: 16),
                _buildDeviceInfoItem(
                  label: _translate('IP Address'),
                  value: device.ipAddress,
                  icon: Icons.wifi_rounded,
                  colorScheme: colorScheme,
                ),
                const SizedBox(height: 16),
                _buildDeviceInfoItem(
                  label: _translate('Status'),
                  value:
                      device.isCurrentDevice
                          ? _translate('Current Device')
                          : _translate('Previously Used'),
                  icon:
                      device.isCurrentDevice
                          ? Icons.check_circle_rounded
                          : Icons.history_rounded,
                  colorScheme: colorScheme,
                  valueColor:
                      device.isCurrentDevice ? Colors.green.shade700 : null,
                ),
                const SizedBox(height: 24),
                if (!device.isCurrentDevice)
                  SizedBox(
                    width: double.infinity,
                    child: OutlinedButton.icon(
                      onPressed: () {
                        Navigator.pop(context);
                        _showRemoveConfirmation(device);
                      },
                      icon: Icon(Icons.delete_outline_rounded, size: 18),
                      label: Text(_translate('Remove Device')),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.red,
                        side: BorderSide(color: Colors.grey.shade300),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(_translate('Close')),
              ),
            ],
          ),
    );
  }

  Widget _buildDeviceInfoItem({
    required String label,
    required String value,
    required IconData icon,
    required ColorScheme colorScheme,
    Color? valueColor,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: colorScheme.primary.withOpacity(0.1),
            shape: BoxShape.circle,
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: Icon(icon, size: 14, color: colorScheme.primary),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTextStyles.bodySmall(
                  context,
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: AppTextStyles.bodyMedium(
                  context,
                  color: valueColor ?? colorScheme.onSurface,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showRemoveConfirmation(DeviceInfo device) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: BorderSide(color: Colors.grey.shade300),
            ),
            title: Text(_translate('Remove Device')),
            content: Text(
              _translate(
                'Are you sure you want to remove this device from your account? You will need to sign in again on this device.',
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(_translate('Cancel')),
              ),
              FilledButton(
                onPressed: () {
                  // Remove device logic would go here in a real app
                  Navigator.pop(context);
                  setState(() {
                    _devices.remove(device);
                  });
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(_translate('Device removed successfully')),
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                },
                style: FilledButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: Text(_translate('Remove')),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final translationService = Provider.of<TranslationService>(context);
    final isRtl = translationService.isRtl;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : CustomScrollView(
                slivers: [
                  // Floating header
                  SliverAppBar(
                    expandedHeight: 80,
                    floating: true,
                    pinned: false,
                    snap: true,
                    elevation: 0,
                    backgroundColor: colorScheme.surface,
                    leading: BackButton(color: colorScheme.onSurface),
                    flexibleSpace: FlexibleSpaceBar(
                      background: SafeArea(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 10,
                          ),
                          child: Row(
                            children: [
                              const SizedBox(
                                width: 56,
                              ), // Space for back button
                              Expanded(
                                child: Text(
                                  _translate('Device History'),
                                  style: AppTextStyles.headingMedium(
                                    context,
                                    color: colorScheme.onSurface,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              const SizedBox(width: 56), // Balance the layout
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),

                  // Main content
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        children: [
                          // Info card
                          Card(
                            elevation: 0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                              side: BorderSide(color: Colors.grey.shade300),
                            ),
                            child: Container(
                              decoration: BoxDecoration(
                                color: colorScheme.primaryContainer.withOpacity(
                                  0.3,
                                ),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.grey.shade300),
                              ),
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        padding: const EdgeInsets.all(8),
                                        decoration: BoxDecoration(
                                          color: colorScheme.primary
                                              .withOpacity(0.1),
                                          shape: BoxShape.circle,
                                          border: Border.all(
                                            color: Colors.grey.shade300,
                                          ),
                                        ),
                                        child: Icon(
                                          Icons.security_rounded,
                                          color: colorScheme.primary,
                                          size: 20,
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      Text(
                                        _translate('Security Info'),
                                        style: AppTextStyles.headingSmall(
                                          context,
                                          color: colorScheme.onSurface,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 12),
                                  Text(
                                    _translate(
                                      'This is a list of devices that have logged into your account. If you don\'t recognize a device, remove it and change your password.',
                                    ),
                                    style: AppTextStyles.bodyMedium(
                                      context,
                                      color: colorScheme.onSurface,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),

                          const SizedBox(height: 20),

                          // Devices list
                          ...List.generate(
                            _devices.length,
                            (index) => _buildDeviceCard(_devices[index], index),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
    );
  }

  Widget _buildDeviceCard(DeviceInfo device, int index) {
    final colorScheme = Theme.of(context).colorScheme;
    final translationService = Provider.of<TranslationService>(context);
    final isRtl = translationService.isRtl;

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Card(
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(color: Colors.grey.shade300),
        ),
        child: InkWell(
          onTap: () => _showDeviceDetails(device),
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color:
                        device.isCurrentDevice
                            ? colorScheme.primary.withOpacity(0.1)
                            : colorScheme.surfaceContainerHighest.withOpacity(
                              0.5,
                            ),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: Icon(
                    _getDeviceIcon(device.deviceType),
                    color:
                        device.isCurrentDevice
                            ? colorScheme.primary
                            : colorScheme.onSurfaceVariant,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        device.name,
                        style: AppTextStyles.bodyLarge(
                          context,
                          color: colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        '${_translate('Last used')}: ${_formatDate(device.lastLogin)}',
                        style: AppTextStyles.bodySmall(
                          context,
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                Icon(
                  isRtl
                      ? Icons.chevron_left_rounded
                      : Icons.chevron_right_rounded,
                  color: colorScheme.onSurfaceVariant.withOpacity(0.6),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class DeviceInfo {
  final String name;
  final DeviceType deviceType;
  final DateTime lastLogin;
  final String location;
  final String ipAddress;
  final bool isCurrentDevice;

  DeviceInfo({
    required this.name,
    required this.deviceType,
    required this.lastLogin,
    required this.location,
    required this.ipAddress,
    required this.isCurrentDevice,
  });
}

enum DeviceType { desktop, mobile, tablet }
