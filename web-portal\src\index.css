@import 'tailwindcss';

@layer base {
  :root {
    /* Modern light theme with better contrast */
    --background: 0 0% 98%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem;
    /* Enhanced colors */
    --success: 142.1 76.2% 36.3%;
    --success-foreground: 355.7 100% 97.3%;
    --warning: 38 92% 50%;
    --warning-foreground: 48 96% 89%;
    --info: 204 94% 94%;
    --info-foreground: 213 31% 91%;
  }

  .dark {
    /* Modern dark theme */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 217.2 91.2% 59.8%;
    /* Enhanced colors */
    --success: 142.1 70.6% 45.3%;
    --success-foreground: 144.9 80.4% 10%;
    --warning: 48 96% 53%;
    --warning-foreground: 20 14.3% 4.1%;
    --info: 204 94% 94%;
    --info-foreground: 213 31% 91%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }

  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-family:
      'Inter',
      system-ui,
      -apple-system,
      BlinkMacSystemFont,
      'Segoe UI',
      Roboto,
      'Helvetica Neue',
      Arial,
      sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    font-variation-settings: normal;
    line-height: 1.6;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Enhanced focus styles */
  *:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }

  /* Modern card shadows */
  .card-shadow {
    box-shadow:
      0 1px 3px 0 rgb(0 0 0 / 0.1),
      0 1px 2px -1px rgb(0 0 0 / 0.1);
  }

  .card-shadow-lg {
    box-shadow:
      0 10px 15px -3px rgb(0 0 0 / 0.1),
      0 4px 6px -4px rgb(0 0 0 / 0.1);
  }

  /* Gradient backgrounds */
  .gradient-bg {
    background: linear-gradient(135deg, hsl(var(--primary) / 0.05) 0%, hsl(var(--accent)) 100%);
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Modern button hover effects */
  .btn-hover {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .btn-hover:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgb(0 0 0 / 0.15);
  }

  /* Remove ALL glass effects from components */
  [data-radix-popper-content-wrapper],
  [data-state="open"][data-side],
  .radix-popover-content,
  .radix-dropdown-menu-content,
  .radix-select-content,
  /* Services module specific */
  .bg-card,
  .bg-blue-50,
  .bg-green-50,
  .bg-orange-50,
  .bg-purple-50,
  .bg-gray-100,
  .bg-gray-50,
  /* Additional glass effect prevention */
  .backdrop-blur,
  .backdrop-blur-sm,
  .backdrop-blur-md,
  .backdrop-blur-lg,
  .backdrop-blur-xl,
  .backdrop-blur-2xl,
  .backdrop-blur-3xl {
    background-color: white !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    background-image: none !important;
    filter: none !important;
    -webkit-filter: none !important;
  }

  /* Dark mode adjustments */
  .dark [data-radix-popper-content-wrapper],
  .dark [data-state='open'][data-side],
  .dark .radix-popover-content,
  .dark .radix-dropdown-menu-content,
  .dark .radix-select-content,
  .dark .bg-card,
  .dark .bg-blue-50,
  .dark .bg-green-50,
  .dark .bg-orange-50,
  .dark .bg-purple-50,
  .dark .bg-gray-100,
  .dark .bg-gray-50 {
    background-color: hsl(var(--card)) !important;
  }

  /* Ensure solid backgrounds for service cards and all components */
  .card,
  .Card,
  [data-slot='card'],
  .service-card,
  .service-list,
  .service-form {
    background-color: white !important;
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
    filter: none !important;
    -webkit-filter: none !important;
  }

  .dark .card,
  .dark .Card,
  .dark [data-slot='card'],
  .dark .service-card,
  .dark .service-list,
  .dark .service-form {
    background-color: hsl(var(--card)) !important;
  }

  /* Override any Tailwind backdrop utilities */
  .backdrop-blur-none,
  .backdrop-blur-sm,
  .backdrop-blur,
  .backdrop-blur-md,
  .backdrop-blur-lg,
  .backdrop-blur-xl,
  .backdrop-blur-2xl,
  .backdrop-blur-3xl {
    backdrop-filter: none !important;
    -webkit-backdrop-filter: none !important;
  }

  /* Remove any transparency from background colors */
  .bg-white\/10,
  .bg-white\/20,
  .bg-white\/30,
  .bg-white\/40,
  .bg-white\/50,
  .bg-white\/60,
  .bg-white\/70,
  .bg-white\/80,
  .bg-white\/90,
  .bg-black\/10,
  .bg-black\/20,
  .bg-black\/30,
  .bg-black\/40,
  .bg-black\/50,
  .bg-black\/60,
  .bg-black\/70,
  .bg-black\/80,
  .bg-black\/90 {
    background-color: white !important;
  }

  .dark .bg-white\/10,
  .dark .bg-white\/20,
  .dark .bg-white\/30,
  .dark .bg-white\/40,
  .dark .bg-white\/50,
  .dark .bg-white\/60,
  .dark .bg-white\/70,
  .dark .bg-white\/80,
  .dark .bg-white\/90,
  .dark .bg-black\/10,
  .dark .bg-black\/20,
  .dark .bg-black\/30,
  .dark .bg-black\/40,
  .dark .bg-black\/50,
  .dark .bg-black\/60,
  .dark .bg-black\/70,
  .dark .bg-black\/80,
  .dark .bg-black\/90 {
    background-color: hsl(var(--card)) !important;
  }
}
