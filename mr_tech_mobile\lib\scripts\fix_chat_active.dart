import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_database/firebase_database.dart';
import '../firebase_options.dart';
import 'package:flutter/foundation.dart';

/// Utility script to fix chat_active fields for all service requests
/// Run with: flutter run -d chrome lib/scripts/fix_chat_active.dart
void main() async {
  // Initialize Firebase
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );
  
  await fixChatActiveFields();
}

Future<void> fixChatActiveFields() async {
  try {
    // Get Firestore instance
    final FirebaseFirestore firestore = FirebaseFirestore.instance;
    final FirebaseDatabase rtdb = FirebaseDatabase.instance;
    
    // Get all service requests
    debugPrint('Fetching service requests...');
    final requestsSnapshot = await firestore.collection('service_requests').get();
    
    debugPrint('Found ${requestsSnapshot.docs.length} service requests.');
    
    // Track stats for reporting
    int fixed = 0;
    int alreadyConsistent = 0;
    int errors = 0;
    
    // Process each request
    for (final requestDoc in requestsSnapshot.docs) {
      try {
        final requestData = requestDoc.data();
        final requestId = requestDoc.id;
        
        // Get current chat active status values
        final bool chatActive = requestData['chatActive'] == true;
        final bool hasActiveChat = requestData['has_active_chat'] == true;
        
        // If any of the fields are true, all should be true
        // If none are true, all should be false
        final bool shouldBeActive = chatActive || chatActive || hasActiveChat;
        
        // Check if fields are consistent
        final bool isConsistent = (chatActive == shouldBeActive) && 
                            (chatActive == shouldBeActive) && 
                            (hasActiveChat == shouldBeActive);
        
        if (!isConsistent) {
          debugPrint('Fixing inconsistent chat active status for request $requestId...');
          debugPrint('  Before: chatActive=$chatActive, chat_active=$chatActive, has_active_chat=$hasActiveChat');
          
          // Update document with consistent values
          await firestore.collection('service_requests').doc(requestId).update({
            'chatActive': shouldBeActive,
            'chat_active': shouldBeActive,
            'has_active_chat': shouldBeActive
          });
          
          // Update in Realtime Database too
          try {
            await rtdb.ref().child('requests_meta/$requestId').update({
              'chatActive': shouldBeActive,
              'chat_active': shouldBeActive,
              'has_active_chat': shouldBeActive
            });
          } catch (rtdbError) {
            debugPrint('Error updating RTDB (continuing anyway): $rtdbError');
          }
          
          debugPrint('  After: All values set to $shouldBeActive');
          fixed++;
        } else {
          alreadyConsistent++;
        }
      } catch (requestError) {
        debugPrint('Error processing request ${requestDoc.id}: $requestError');
        errors++;
      }
    }
    
    // Print summary
    debugPrint('\nSummary:');
    debugPrint('- Total requests processed: ${requestsSnapshot.docs.length}');
    debugPrint('- Requests with consistent chat active status: $alreadyConsistent');
    debugPrint('- Requests fixed: $fixed');
    debugPrint('- Errors: $errors');
    
  } catch (error) {
    debugPrint('Error fixing chat active fields: $error');
  }
} 