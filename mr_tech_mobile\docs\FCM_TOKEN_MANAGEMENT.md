# FCM Token Management

This document explains how FCM (Firebase Cloud Messaging) tokens are managed in the Mr. Tech app to ensure reliable notifications.

## Overview

We've implemented a streamlined approach to FCM token management to ensure that:

1. Only one source of truth exists for FCM tokens: the service requests themselves
2. Tokens are automatically updated when they change
3. Unnecessary duplicative token registration is eliminated
4. The web portal reads FCM tokens directly from service requests

## Implementation Details

### FCM Token Source of Truth

FCM tokens are stored directly in the service request documents in Firestore with these fields:
- `customer_fcm_token` (snake_case version)
- `customerFcmToken` (camelCase version)

This approach ensures that when the web portal needs to send a notification, it can pull the token directly from the request without additional lookups or complex chains of references.

### Key Components

The token management system consists of these key parts:

1. **RequestService.updateRequestFCMToken()**: Primary method to update FCM tokens in service requests
2. **RequestService.updateFCMTokenWhenViewing()**: Ensures tokens are refreshed when viewing a request
3. **Migrations.migrateServiceRequestsWithFCMToken()**: Migrates existing service requests with current token
4. **AppService**: Simplified to call RequestService instead of duplicating token storage logic
5. **NotificationService**: Streamlined to focus on core notification handling

### Update Triggers

FCM tokens are updated in service requests:

1. When creating a new service request
2. When viewing an existing service request
3. When the FCM token is refreshed by Firebase
4. When signing in to the app
5. During app initialization (via migrations)

### Web Portal Integration

The web portal has been updated to:

1. Read FCM tokens directly from service requests
2. Fall back to user tokens only if not found in the request
3. Use the stored FCM token for all notification types

## Migration

An automatic migration runs on app startup to ensure all existing service requests have the current FCM token:

1. Gets the current FCM token
2. Finds all service requests for the current user
3. Updates any service requests with missing or different tokens
4. Records migration completion in shared preferences

## Eliminated Redundancies

We've removed:
- Device token collection records
- User document token arrays
- Redundant token fields in user documents
- Multiple token update methods across different services

## Testing

To verify FCM token management:
1. Create a new service request and check Firestore to confirm it has the FCM token
2. Refresh the FCM token via NotificationService.refreshToken() and verify service requests are updated
3. View a service request and check Firestore to confirm the token is refreshed 