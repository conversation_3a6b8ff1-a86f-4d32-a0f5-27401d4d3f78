import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/translation_service.dart';
import '../theme/app_theme.dart';
import 'text_styles.dart';

/// A luxury-styled section container with customizable header and content.
///
/// This widget provides a consistent, premium look for content sections throughout the app
/// with customizable header styling, content layout, and visual effects.
class LuxurySection extends StatelessWidget {
  final String? title;
  final String? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final List<Widget> children;
  final EdgeInsetsGeometry padding;
  final EdgeInsetsGeometry margin;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize mainAxisSize;
  final bool hasDivider;
  final bool hasBackground;
  final Color? backgroundColor;
  final Color? borderColor;
  final BorderRadius? borderRadius;
  final bool hasShadow;
  final double? minHeight;
  final VoidCallback? onTap;
  final TextStyle? titleStyle;
  final TextStyle? subtitleStyle;
  final double spacing;

  const LuxurySection({
    super.key,
    this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    required this.children,
    this.padding = const EdgeInsets.all(16),
    this.margin = const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
    this.crossAxisAlignment = CrossAxisAlignment.start,
    this.mainAxisSize = MainAxisSize.min,
    this.hasDivider = true,
    this.hasBackground = true,
    this.backgroundColor,
    this.borderColor,
    this.borderRadius,
    this.hasShadow = true,
    this.minHeight,
    this.onTap,
    this.titleStyle,
    this.subtitleStyle,
    this.spacing = 16,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bgColor = backgroundColor ?? theme.cardTheme.color;
    final radius = borderRadius ?? AppTheme.mediumRadius;

    Widget content = Column(
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: [
        // Header section
        if (title != null || leading != null || trailing != null)
          Padding(
            padding: EdgeInsets.only(bottom: subtitle != null ? 4 : spacing),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                if (leading != null) ...[leading!, SizedBox(width: 12)],
                if (title != null)
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title!,
                          style:
                              titleStyle ?? AppTextStyles.labelLarge(context),
                        ),
                        if (subtitle != null) ...[
                          SizedBox(height: 2),
                          Text(
                            subtitle!,
                            style:
                                subtitleStyle ??
                                AppTextStyles.bodySmall(context),
                          ),
                        ],
                      ],
                    ),
                  ),
                if (trailing != null) trailing!,
              ],
            ),
          ),

        // Divider if enabled and we have a header
        if (hasDivider &&
            (title != null || leading != null || trailing != null)) ...[
          Divider(
            color: theme.dividerTheme.color?.withOpacity(0.5),
            height: spacing,
          ),
        ],

        // Content section with spacing between items
        ...List.generate(children.length * 2 - 1, (index) {
          if (index.isEven) {
            return children[index ~/ 2];
          } else {
            return SizedBox(height: spacing);
          }
        }),
      ],
    );

    // Apply container styling if background is enabled
    if (hasBackground) {
      content = Container(
        padding: padding,
        margin: margin,
        constraints:
            minHeight != null ? BoxConstraints(minHeight: minHeight!) : null,
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: radius,
          border:
              borderColor != null
                  ? Border.all(color: borderColor!, width: 1)
                  : null,
          boxShadow: hasShadow ? AppTheme.subtleShadow : null,
        ),
        child: content,
      );

      // Add tap functionality if provided
      if (onTap != null) {
        content = InkWell(onTap: onTap, borderRadius: radius, child: content);
      }
    } else {
      // Just add padding and margin without background styling
      content = Padding(padding: padding, child: content);

      if (margin != EdgeInsets.zero) {
        content = Padding(padding: margin, child: content);
      }
    }

    return content;
  }
}

/// A luxury-styled list item with various configurations
class LuxuryListItem extends StatelessWidget {
  final String title;
  final String? subtitle;
  final String? trailing;
  final IconData? icon;
  final Widget? leading;
  final Widget? trailingWidget;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final Color? backgroundColor;
  final Color? iconColor;
  final bool hasDivider;
  final EdgeInsetsGeometry padding;
  final bool hasChevron;
  final BorderRadius? borderRadius;
  final bool hasBorder;
  final double? leadingSize;
  final TextStyle? titleStyle;
  final TextStyle? subtitleStyle;
  final bool dense;

  const LuxuryListItem({
    super.key,
    required this.title,
    this.subtitle,
    this.trailing,
    this.icon,
    this.leading,
    this.trailingWidget,
    this.onTap,
    this.onLongPress,
    this.backgroundColor,
    this.iconColor,
    this.hasDivider = true,
    this.padding = const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
    this.hasChevron = false,
    this.borderRadius,
    this.hasBorder = false,
    this.leadingSize = 40,
    this.titleStyle,
    this.subtitleStyle,
    this.dense = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final radius = borderRadius ?? AppTheme.smallRadius;

    // Base list item
    Widget listItem = Padding(
      padding: padding,
      child: Consumer<TranslationService>(
        builder: (context, translationService, child) {
          final isRtl = translationService.isRtl;
          return Row(
            children: [
              // Leading element (icon or widget)
              if (leading != null) ...[
                SizedBox(
                  width: leadingSize,
                  height: leadingSize,
                  child: leading,
                ),
                SizedBox(width: 16),
              ] else if (icon != null) ...[
                Container(
                  width: leadingSize,
                  height: leadingSize,
                  decoration: BoxDecoration(
                    color: (iconColor ?? theme.colorScheme.primary).withOpacity(
                      0.1,
                    ),
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Icon(
                      icon,
                      color: iconColor ?? theme.colorScheme.primary,
                      size: leadingSize! * 0.5,
                    ),
                  ),
                ),
                SizedBox(width: 16),
              ],

              // Title and subtitle
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      title,
                      style: titleStyle ?? AppTextStyles.labelLarge(context),
                    ),
                    if (subtitle != null) ...[
                      SizedBox(height: dense ? 2 : 4),
                      Text(
                        subtitle!,
                        style:
                            subtitleStyle ?? AppTextStyles.bodySmall(context),
                      ),
                    ],
                  ],
                ),
              ),

              // Trailing element (text, widget, or chevron)
              if (trailingWidget != null) ...[
                SizedBox(width: 16),
                trailingWidget!,
              ] else if (trailing != null) ...[
                SizedBox(width: 16),
                Text(
                  trailing!,
                  style: AppTextStyles.bodyMedium(
                    context,
                    color: AppTextStyles.bodyMedium(context).color,
                  ),
                ),
              ],

              // Chevron if enabled
              if (hasChevron) ...[
                SizedBox(width: trailing != null ? 8 : 16),
                Icon(
                  isRtl ? Icons.chevron_left : Icons.chevron_right,
                  color: theme.iconTheme.color?.withOpacity(0.5),
                  size: 22,
                ),
              ],
            ],
          );
        },
      ),
    );

    // Apply background color if provided
    if (backgroundColor != null || hasBorder || (onTap != null)) {
      listItem = Container(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        decoration: BoxDecoration(
          color:
              backgroundColor ?? (onTap != null ? theme.cardTheme.color : null),
          borderRadius: radius,
          border:
              hasBorder
                  ? Border.all(
                    color:
                        theme.dividerTheme.color?.withOpacity(0.3) ??
                        Colors.grey.withOpacity(0.3),
                    width: 1,
                  )
                  : null,
        ),
        child: listItem,
      );
    }

    // Add tap functionality if provided
    if (onTap != null || onLongPress != null) {
      listItem = InkWell(
        onTap: onTap,
        onLongPress: onLongPress,
        borderRadius: radius,
        child: listItem,
      );
    }

    // Add divider if enabled
    if (hasDivider) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          listItem,
          if (backgroundColor == null && !hasBorder)
            Divider(
              height: 1,
              indent: icon != null || leading != null ? 56 + 16 : 16,
              endIndent: 16,
            ),
        ],
      );
    }

    return listItem;
  }
}

/// A luxury section specifically for settings or preferences
class LuxurySettingsSection extends StatelessWidget {
  final String title;
  final List<Widget> children;
  final EdgeInsetsGeometry margin;
  final bool hasShadow;
  final Color? backgroundColor;
  final TextStyle? titleStyle;

  const LuxurySettingsSection({
    super.key,
    required this.title,
    required this.children,
    this.margin = const EdgeInsets.only(bottom: 24),
    this.hasShadow = false,
    this.backgroundColor,
    this.titleStyle,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Padding(
      padding: margin,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Padding(
            padding: const EdgeInsets.only(left: 16, right: 16, bottom: 8),
            child: Text(
              title.toUpperCase(),
              style:
                  titleStyle ??
                  AppTextStyles.buttonSmall(
                    context,
                    color: theme.colorScheme.primary,
                  ),
            ),
          ),

          // Section content
          Container(
            decoration: BoxDecoration(
              color: backgroundColor ?? theme.cardTheme.color,
              borderRadius: AppTheme.mediumRadius,
              boxShadow: hasShadow ? AppTheme.subtleShadow : null,
            ),
            child: Column(children: children),
          ),
        ],
      ),
    );
  }
}

/// A luxury setting item with a toggle switch
class LuxurySettingToggle extends StatelessWidget {
  final String title;
  final String? subtitle;
  final bool value;
  final ValueChanged<bool> onChanged;
  final IconData? icon;
  final Color? iconColor;
  final bool hasDivider;

  const LuxurySettingToggle({
    super.key,
    required this.title,
    this.subtitle,
    required this.value,
    required this.onChanged,
    this.icon,
    this.iconColor,
    this.hasDivider = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    Widget listItem = Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // Leading icon if provided
          if (icon != null) ...[
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: (iconColor ?? theme.colorScheme.primary).withOpacity(
                  0.1,
                ),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Icon(
                  icon,
                  color: iconColor ?? theme.colorScheme.primary,
                  size: 20,
                ),
              ),
            ),
            SizedBox(width: 16),
          ],

          // Title and subtitle
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: AppTextStyles.labelLarge(context)),
                if (subtitle != null) ...[
                  SizedBox(height: 4),
                  Text(subtitle!, style: AppTextStyles.bodySmall(context)),
                ],
              ],
            ),
          ),

          // Toggle switch
          Switch.adaptive(
            value: value,
            onChanged: onChanged,
            activeColor: theme.colorScheme.primary,
          ),
        ],
      ),
    );

    // Add tap functionality
    listItem = InkWell(onTap: () => onChanged(!value), child: listItem);

    // Add divider if enabled
    if (hasDivider) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          listItem,
          Divider(
            height: 1,
            indent: icon != null ? 56 + 16 : 16,
            endIndent: 16,
          ),
        ],
      );
    }

    return listItem;
  }
}

/// A luxury styled header for section dividers
class LuxurySectionHeader extends StatelessWidget {
  final String title;
  final Widget? action;
  final EdgeInsetsGeometry padding;
  final TextStyle? style;
  final bool hasBackground;
  final Color? backgroundColor;
  final bool hasDivider;
  final VoidCallback? onTap;

  const LuxurySectionHeader({
    super.key,
    required this.title,
    this.action,
    this.padding = const EdgeInsets.fromLTRB(16, 24, 16, 12),
    this.style,
    this.hasBackground = false,
    this.backgroundColor,
    this.hasDivider = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    Widget header = Padding(
      padding: padding,
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: style ?? AppTextStyles.labelLarge(context),
            ),
          ),
          if (action != null) action!,
        ],
      ),
    );

    if (hasBackground) {
      header = Container(
        color: backgroundColor ?? theme.scaffoldBackgroundColor,
        child: header,
      );
    }

    if (hasDivider) {
      header = Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [header, Divider(height: 1, indent: 16, endIndent: 16)],
      );
    }

    if (onTap != null) {
      header = InkWell(onTap: onTap, child: header);
    }

    return header;
  }
}
