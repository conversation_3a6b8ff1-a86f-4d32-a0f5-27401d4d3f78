import 'dart:io';
import 'package:flutter/foundation.dart';

void main() async {
  // Directory to scan (start from lib)
  final directory = Directory('lib');

  // Count of files updated
  int filesUpdated = 0;

  // Process all dart files
  final files = directory
      .listSync(recursive: true)
      .whereType<File>()
      .where((file) => file.path.endsWith('.dart'));

  for (final file in files) {
    try {
      String content = await file.readAsString();
      bool modified = false;

      // Replace const AppTextStyles with AppTextStyles
      if (content.contains('const AppTextStyles')) {
        content = content.replaceAll('const AppTextStyles', 'AppTextStyles');
        modified = true;
      }

      // Fix the typo 'context)l?.color' that might have been introduced
      content = content.replaceAll('context)l?.color', 'context).color');

      if (modified) {
        await file.writeAsString(content);
        filesUpdated++;
        debugPrint('Fixed const AppTextStyles in ${file.path}');
      }
    } catch (e) {
      // Skip files that can't be read
    }
  }

  debugPrint('\nSummary:');
  debugPrint('Files updated: $filesUpdated');
  debugPrint('Removed "const" from AppTextStyles usage');
  debugPrint('Run flutter analyze to check for any remaining issues');
}
