import {
  deleteObject,
  getDownloadURL,
  getStorage,
  ref,
  uploadBytesResumable,
} from 'firebase/storage';
import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  serverTimestamp,
  setDoc,
  updateDoc,
  where,
} from 'firebase/firestore';
import { updateProfile } from 'firebase/auth';
import { auth, db } from '../config/firebase';
import { type UserRole } from '../types/auth';
import logger from '../utils/logger';

export interface ProfileUpdateData {
  name?: string;
  email?: string;
  phone?: string;
  photo_url?: string | null;
  bio?: string;
  specialties?: string[];
  is_available?: boolean;
  status?: 'active' | 'offline' | 'busy' | 'onLeave';
  updated_at?: Date;
}

class ProfileService {
  private storage = getStorage();

  /**
   * Upload profile picture to Firebase Storage
   */
  async uploadProfilePicture(
    file: File,
    userId: string,
    onProgress?: (progress: number) => void,
  ): Promise<string> {
    try {
      // Create storage reference with user-specific path
      const storageRef = ref(this.storage, `profile_images/${userId}/${file.name}`);

      // Create upload task
      const uploadTask = uploadBytesResumable(storageRef, file);

      return new Promise((resolve, reject) => {
        uploadTask.on(
          'state_changed',
          (snapshot) => {
            // Calculate progress
            const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
            onProgress?.(progress);
          },
          (error) => {
            console.error('Upload error:', error);
            reject(new Error('Failed to upload image'));
          },
          async () => {
            try {
              // Get download URL
              const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
              resolve(downloadURL);
            } catch (error) {
              reject(new Error('Failed to get download URL'));
            }
          },
        );
      });
    } catch (error) {
      console.error('Error uploading profile picture:', error);
      throw new Error('Failed to upload profile picture');
    }
  }

  /**
   * Delete profile picture from Firebase Storage
   */
  async deleteProfilePicture(imageUrl: string): Promise<void> {
    try {
      // Extract storage path from URL
      const url = new URL(imageUrl);
      const pathMatch = url.pathname.match(/\/o\/(.+)\?/);

      if (!pathMatch) {
        throw new Error('Invalid image URL format');
      }

      const storagePath = decodeURIComponent(pathMatch[1]);
      const storageRef = ref(this.storage, storagePath);

      await deleteObject(storageRef);
    } catch (error) {
      console.error('Error deleting profile picture:', error);
      throw new Error('Failed to delete profile picture');
    }
  }

  /**
   * Update user profile in Firestore
   */
  async updateUserProfile(userId: string, data: ProfileUpdateData): Promise<void> {
    try {
      // First, check if the user exists in any collection and get their role
      let userRole = 'customer';
      let userExists = false;

      // Check admins collection first
      const adminRef = doc(db, 'admins', userId);
      const adminSnap = await getDoc(adminRef);

      if (adminSnap.exists()) {
        userRole = 'admin';
        userExists = true;

        // Update admin document
        const updateData: any = {
          updated_at: new Date(),
        };

        if (data.name !== undefined) updateData.name = data.name;
        if (data.email !== undefined) updateData.email = data.email;
        if (data.phone !== undefined) updateData.phone = data.phone;
        if (data.photo_url !== undefined) updateData.photo_url = data.photo_url;

        await updateDoc(adminRef, updateData);
        logger.debug('Updated admin document', undefined, 'PROFILE');
        return; // Exit early as we've updated the admin document
      }

      // If not an admin, check technicians collection
      const techRef = doc(db, 'technicians', userId);
      const techSnap = await getDoc(techRef);

      if (techSnap.exists()) {
        userRole = 'technician';
        userExists = true;

        // For technicians, we'll still update the users collection for backwards compatibility
        // but we won't exit early
      }

      // Finally, check users collection
      const userRef = doc(db, 'users', userId);
      const userSnap = await getDoc(userRef);

      // Prepare update data with snake_case fields
      const updateData: any = {
        updated_at: new Date(),
      };

      if (data.name !== undefined) updateData.name = data.name;
      if (data.email !== undefined) updateData.email = data.email;
      if (data.phone !== undefined) updateData.phone = data.phone;
      if (data.photo_url !== undefined) updateData.photo_url = data.photo_url;

      if (userSnap.exists()) {
        // Update existing document
        await updateDoc(userRef, updateData);
        logger.debug('Updated user document', undefined, 'PROFILE');
      } else if (userRole === 'technician') {
        // Create new document with all required fields for technician (for backwards compatibility)
        await setDoc(userRef, {
          name: data.name || '',
          email: data.email || '',
          phone: data.phone || '',
          photo_url: data.photo_url || null,
          role: 'technician', // Preserve technician role
          created_at: new Date(),
          updated_at: new Date(),
        });
        logger.debug('Created new user document for technician', undefined, 'PROFILE');
      }
      // We don't create user documents for admins as they should only exist in the admins collection
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw new Error('Failed to update user profile');
    }
  }

  /**
   * Update technician profile in Firestore
   */
  async updateTechnicianProfile(technicianId: string, data: ProfileUpdateData): Promise<void> {
    try {
      const technicianRef = doc(db, 'technicians', technicianId);

      // Prepare update data with snake_case fields
      const updateData: any = {
        updated_at: new Date(),
      };

      if (data.name !== undefined) updateData.name = data.name;
      if (data.email !== undefined) updateData.email = data.email;
      if (data.phone !== undefined) updateData.phone_number = data.phone;
      if (data.photo_url !== undefined) updateData.photo_url = data.photo_url;
      if (data.bio !== undefined) updateData.bio = data.bio;
      if (data.specialties !== undefined) updateData.specialties = data.specialties;
      if (data.is_available !== undefined) updateData.is_available = data.is_available;
      if (data.status !== undefined) updateData.status = data.status;

      await updateDoc(technicianRef, updateData);
    } catch (error) {
      console.error('Error updating technician profile:', error);
      throw new Error('Failed to update technician profile');
    }
  }

  /**
   * Get user profile from Firestore
   */
  async getUserProfile(userId: string): Promise<any> {
    try {
      const userRef = doc(db, 'users', userId);
      const userSnap = await getDoc(userRef);

      if (userSnap.exists()) {
        return { id: userSnap.id, ...userSnap.data() };
      } else {
        throw new Error('User not found');
      }
    } catch (error) {
      console.error('Error getting user profile:', error);
      throw new Error('Failed to get user profile');
    }
  }

  /**
   * Get technician profile from Firestore
   */
  async getTechnicianProfile(technicianId: string): Promise<any> {
    try {
      const technicianRef = doc(db, 'technicians', technicianId);
      const technicianSnap = await getDoc(technicianRef);

      if (technicianSnap.exists()) {
        return { id: technicianSnap.id, ...technicianSnap.data() };
      } else {
        throw new Error('Technician not found');
      }
    } catch (error) {
      console.error('Error getting technician profile:', error);
      throw new Error('Failed to get technician profile');
    }
  }

  /**
   * Validate image file
   */
  validateImageFile(file: File): { valid: boolean; error?: string } {
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: 'Please select a valid image file (JPEG, PNG, GIF, or WebP)',
      };
    }

    // Check file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return {
        valid: false,
        error: 'File size must be less than 5MB',
      };
    }

    return { valid: true };
  }

  /**
   * Generate optimized filename for profile picture
   */
  generateProfilePictureFilename(userId: string, originalFilename: string): string {
    const timestamp = Date.now();
    const extension = originalFilename.split('.').pop()?.toLowerCase() || 'jpg';
    return `profile_${userId}_${timestamp}.${extension}`;
  }

  /**
   * Resize image before upload (client-side)
   */
  async resizeImage(
    file: File,
    maxWidth: number = 400,
    maxHeight: number = 400,
    quality: number = 0.8,
  ): Promise<File> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;

        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height;
            height = maxHeight;
          }
        }

        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height);

        canvas.toBlob(
          (blob) => {
            if (blob) {
              const resizedFile = new File([blob], file.name, {
                type: file.type,
                lastModified: Date.now(),
              });
              resolve(resizedFile);
            } else {
              resolve(file); // Fallback to original file
            }
          },
          file.type,
          quality,
        );
      };

      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Update profile picture in the appropriate collection based on user role
   */
  async updateProfilePicture(userId: string, photoUrl: string | null): Promise<void> {
    try {
      logger.debug('Updating profile picture', { userId }, 'PROFILE');

      // First check if the user exists in admins collection
      const adminRef = doc(db, 'admins', userId);
      const adminSnap = await getDoc(adminRef);

      if (adminSnap.exists()) {
        logger.debug('User is an admin, updating admin document', undefined, 'PROFILE');
        await updateDoc(adminRef, {
          photo_url: photoUrl,
          photoUrl, // For backward compatibility
          updated_at: serverTimestamp(),
          updatedAt: serverTimestamp(),
        });

        // Update Firebase Auth profile picture
        await this.updateAuthProfilePicture(photoUrl);

        return; // Exit early as we've updated the admin document
      }

      // If not an admin, check technicians collection
      const techRef = doc(db, 'technicians', userId);
      const techSnap = await getDoc(techRef);

      if (techSnap.exists()) {
        logger.debug('User is a technician, updating technician document', undefined, 'PROFILE');
        await updateDoc(techRef, {
          photo_url: photoUrl,
          photoUrl, // For backward compatibility
          updated_at: serverTimestamp(),
          updatedAt: serverTimestamp(),
        });

        // Also update users collection for technicians (backward compatibility)
        const userRef = doc(db, 'users', userId);
        const userSnap = await getDoc(userRef);

        if (userSnap.exists()) {
          logger.debug('Also updating user document for technician', undefined, 'PROFILE');
          await updateDoc(userRef, {
            photo_url: photoUrl,
            photoUrl, // For backward compatibility
            updated_at: serverTimestamp(),
            updatedAt: serverTimestamp(),
          });
        }

        // Update Firebase Auth profile picture
        await this.updateAuthProfilePicture(photoUrl);

        return;
      }

      // If not found in admin or technician collections, update users collection
      const userRef = doc(db, 'users', userId);
      await updateDoc(userRef, {
        photo_url: photoUrl,
        photoUrl, // For backward compatibility
        updated_at: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });
      logger.debug('Updated regular user document', undefined, 'PROFILE');

      // Update Firebase Auth profile picture
      await this.updateAuthProfilePicture(photoUrl);
    } catch (error) {
      console.error('Error updating profile picture:', error);
      throw new Error('Failed to update profile picture in database');
    }
  }

  /**
   * Update Firebase Auth profile picture
   */
  private async updateAuthProfilePicture(photoUrl: string | null): Promise<void> {
    try {
      if (auth.currentUser) {
        await updateProfile(auth.currentUser, {
          photoURL: photoUrl || undefined,
        });
        logger.debug('Updated Firebase Auth profile picture', undefined, 'PROFILE');
      }
    } catch (error) {
      console.error('Error updating Firebase Auth profile picture:', error);
      // Don't throw error here, just log it
    }
  }
}

export const profileService = new ProfileService();
