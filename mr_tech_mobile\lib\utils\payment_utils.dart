import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import '../services/translation_service.dart';

/// A utility class for payment-related functionality
class PaymentUtils {
  
  /// Formats user data into billing data required by Paymob
  static Map<String, dynamic> formatBillingData({
    required String fullName,
    required String email,
    required String phone,
    required String address,
    required String city,
    required String country,
    String postalCode = '00000',
  }) {
    // Split full name into first and last name
    final nameParts = fullName.split(' ');
    final firstName = nameParts.first;
    final lastName = nameParts.length > 1 
        ? nameParts.skip(1).join(' ')
        : '';
    
    return {
      'first_name': firstName,
      'last_name': lastName,
      'email': email,
      'phone_number': phone,
      'street': address,
      'city': city,
      'country': country,
      'state': city, // Using city as state if not available
      'postal_code': postalCode,
      'apartment': 'NA',
      'floor': 'NA',
      'building': 'NA',
      'shipping_method': 'NA',
    };
  }
  
  /// Retrieves user billing info from Firebase Auth if available
  static Future<Map<String, String>> getUserBillingInfo() async {
    final user = FirebaseAuth.instance.currentUser;
    final result = <String, String>{};
    
    if (user != null) {
      result['full_name'] = user.displayName ?? '';
      result['email'] = user.email ?? '';
      result['phone'] = user.phoneNumber ?? '';
    }
    
    return result;
  }
  
  /// Shows a payment error dialog to the user
  static Future<void> showPaymentErrorDialog(
    BuildContext context,
    String errorMessage,
  ) async {
    final translationService = TranslationService();
    final translate = translationService.translate;

    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(translate('Payment error')),
        content: Text(errorMessage),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(translate('OK')),
          ),
        ],
      ),
    );
  }
  
  /// Formats a price value to a display string
  static String formatPrice(double price, {String currency = 'L.E'}) {
    return '$currency ${price.toStringAsFixed(2)}';
  }
} 