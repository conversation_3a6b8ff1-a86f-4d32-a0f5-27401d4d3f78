import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/service_model.dart';
import '../models/request_model.dart';

/// Advanced search service for Mr.<PERSON> app
/// Provides filtering, sorting, search history, and smart suggestions
class SearchService {
  static final SearchService _instance = SearchService._internal();
  factory SearchService() => _instance;
  SearchService._internal();

  late SharedPreferences _prefs;
  bool _isInitialized = false;

  // Search history keys
  static const String _searchHistoryKey = 'search_history';
  static const String _popularSearchesKey = 'popular_searches';
  static const int _maxHistoryItems = 20;

  /// Initialize the search service
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();
      _isInitialized = true;
      debugPrint('SearchService initialized');
    } catch (e) {
      debugPrint('Error initializing SearchService: $e');
      rethrow;
    }
  }

  /// Advanced service search with multiple filters
  List<ServiceModel> searchServices({
    required List<ServiceModel> services,
    required String query,
    required String languageCode,
    List<String>? categories,
    double? minPrice,
    double? maxPrice,
    int? maxDuration,
    ServiceSortOption sortBy = ServiceSortOption.relevance,
    bool ascending = true,
  }) {
    // Start with all services
    List<ServiceModel> results = List.from(services);

    // Apply text search filter
    if (query.isNotEmpty) {
      results = _filterByQuery(results, query, languageCode);
    }

    // Apply category filter
    if (categories != null && categories.isNotEmpty) {
      results =
          results
              .where((service) => categories.contains(service.category))
              .toList();
    }

    // Apply price range filter
    if (minPrice != null) {
      results =
          results.where((service) => service.basePrice >= minPrice).toList();
    }

    if (maxPrice != null) {
      results =
          results.where((service) => service.basePrice <= maxPrice).toList();
    }

    // Apply duration filter
    if (maxDuration != null) {
      results =
          results
              .where((service) => service.estimatedDuration <= maxDuration)
              .toList();
    }

    // Apply sorting
    results = _sortServices(results, sortBy, ascending, query, languageCode);

    // Save search query to history if it's not empty
    if (query.isNotEmpty) {
      _saveSearchToHistory(query);
    }

    return results;
  }

  /// Filter services by search query with fuzzy matching
  List<ServiceModel> _filterByQuery(
    List<ServiceModel> services,
    String query,
    String languageCode,
  ) {
    final searchTerms =
        query
            .toLowerCase()
            .split(' ')
            .where((term) => term.isNotEmpty)
            .toList();

    return services.where((service) {
      final name = service.getTranslatedName(languageCode).toLowerCase();
      final description =
          service.getTranslatedDescription(languageCode).toLowerCase();
      final category = service.category.toLowerCase();

      // Check if all search terms are found in name, description, or category
      return searchTerms.every(
        (term) =>
            name.contains(term) ||
            description.contains(term) ||
            category.contains(term) ||
            _fuzzyMatch(name, term) ||
            _fuzzyMatch(description, term),
      );
    }).toList();
  }

  /// Simple fuzzy matching for typo tolerance
  bool _fuzzyMatch(String text, String term) {
    if (term.length < 3) return false;

    // Allow one character difference for terms longer than 3 characters
    for (int i = 0; i <= text.length - term.length; i++) {
      final substring = text.substring(i, i + term.length);
      int differences = 0;

      for (int j = 0; j < term.length; j++) {
        if (substring[j] != term[j]) {
          differences++;
          if (differences > 1) break;
        }
      }

      if (differences <= 1) return true;
    }

    return false;
  }

  /// Sort services based on different criteria
  List<ServiceModel> _sortServices(
    List<ServiceModel> services,
    ServiceSortOption sortBy,
    bool ascending,
    String query,
    String languageCode,
  ) {
    switch (sortBy) {
      case ServiceSortOption.relevance:
        return _sortByRelevance(services, query, languageCode);

      case ServiceSortOption.price:
        services.sort(
          (a, b) =>
              ascending
                  ? a.basePrice.compareTo(b.basePrice)
                  : b.basePrice.compareTo(a.basePrice),
        );
        break;

      case ServiceSortOption.duration:
        services.sort(
          (a, b) =>
              ascending
                  ? a.estimatedDuration.compareTo(b.estimatedDuration)
                  : b.estimatedDuration.compareTo(a.estimatedDuration),
        );
        break;

      case ServiceSortOption.popularity:
        services.sort((a, b) {
          final aPopularity = a.metadata?['popularityIndex'] ?? 0;
          final bPopularity = b.metadata?['popularityIndex'] ?? 0;
          return ascending
              ? aPopularity.compareTo(bPopularity)
              : bPopularity.compareTo(aPopularity);
        });
        break;

      case ServiceSortOption.alphabetical:
        services.sort((a, b) {
          final aName = a.getTranslatedName(languageCode);
          final bName = b.getTranslatedName(languageCode);
          return ascending ? aName.compareTo(bName) : bName.compareTo(aName);
        });
        break;
    }

    return services;
  }

  /// Sort by relevance based on search query
  List<ServiceModel> _sortByRelevance(
    List<ServiceModel> services,
    String query,
    String languageCode,
  ) {
    if (query.isEmpty) return services;

    final searchTerms = query.toLowerCase().split(' ');

    // Calculate relevance score for each service
    final scoredServices =
        services.map((service) {
          final name = service.getTranslatedName(languageCode).toLowerCase();
          final description =
              service.getTranslatedDescription(languageCode).toLowerCase();
          final category = service.category.toLowerCase();

          int score = 0;

          for (final term in searchTerms) {
            // Exact name match gets highest score
            if (name.contains(term)) {
              score += name == term ? 100 : 50;
            }

            // Category match gets medium score
            if (category.contains(term)) {
              score += 30;
            }

            // Description match gets lower score
            if (description.contains(term)) {
              score += 10;
            }

            // Bonus for starting with search term
            if (name.startsWith(term)) {
              score += 25;
            }
          }

          return MapEntry(service, score);
        }).toList();

    // Sort by score (highest first)
    scoredServices.sort((a, b) => b.value.compareTo(a.value));

    return scoredServices.map((entry) => entry.key).toList();
  }

  /// Search requests with filters
  List<RequestModel> searchRequests({
    required List<RequestModel> requests,
    required String query,
    List<String>? statuses,
    DateTime? fromDate,
    DateTime? toDate,
    RequestSortOption sortBy = RequestSortOption.dateDesc,
  }) {
    List<RequestModel> results = List.from(requests);

    // Apply text search filter
    if (query.isNotEmpty) {
      results =
          results.where((request) {
            final serviceName = request.serviceName.toLowerCase();
            final customerIssue = request.customerIssue.toLowerCase();
            final queryLower = query.toLowerCase();

            return serviceName.contains(queryLower) ||
                customerIssue.contains(queryLower);
          }).toList();
    }

    // Apply status filter
    if (statuses != null && statuses.isNotEmpty) {
      results =
          results
              .where((request) => statuses.contains(request.status))
              .toList();
    }

    // Apply date range filter
    if (fromDate != null) {
      results =
          results
              .where(
                (request) =>
                    request.createdAt.isAfter(fromDate) ||
                    request.createdAt.isAtSameMomentAs(fromDate),
              )
              .toList();
    }

    if (toDate != null) {
      results =
          results
              .where(
                (request) =>
                    request.createdAt.isBefore(toDate.add(Duration(days: 1))),
              )
              .toList();
    }

    // Apply sorting
    switch (sortBy) {
      case RequestSortOption.dateDesc:
        results.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case RequestSortOption.dateAsc:
        results.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case RequestSortOption.status:
        results.sort(
          (a, b) => a.status.toString().compareTo(b.status.toString()),
        );
        break;
      case RequestSortOption.price:
        results.sort((a, b) => b.amount.compareTo(a.amount));
        break;
    }

    return results;
  }

  /// Save search query to history
  Future<void> _saveSearchToHistory(String query) async {
    try {
      final history = await getSearchHistory();

      // Remove if already exists to avoid duplicates
      history.removeWhere((item) => item['query'] == query);

      // Add to beginning
      history.insert(0, {
        'query': query,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });

      // Keep only the most recent items
      if (history.length > _maxHistoryItems) {
        history.removeRange(_maxHistoryItems, history.length);
      }

      await _prefs.setString(_searchHistoryKey, jsonEncode(history));
    } catch (e) {
      debugPrint('Error saving search to history: $e');
    }
  }

  /// Get search history
  Future<List<Map<String, dynamic>>> getSearchHistory() async {
    try {
      final historyData = _prefs.getString(_searchHistoryKey);
      if (historyData == null) return [];

      final decoded = jsonDecode(historyData) as List;
      return decoded.cast<Map<String, dynamic>>();
    } catch (e) {
      debugPrint('Error getting search history: $e');
      return [];
    }
  }

  /// Clear search history
  Future<void> clearSearchHistory() async {
    try {
      await _prefs.remove(_searchHistoryKey);
    } catch (e) {
      debugPrint('Error clearing search history: $e');
    }
  }

  /// Get search suggestions based on history and popular searches
  Future<List<String>> getSearchSuggestions(String query) async {
    final suggestions = <String>[];

    try {
      // Get from history
      final history = await getSearchHistory();
      final historyQueries =
          history
              .map((item) => item['query'] as String)
              .where((q) => q.toLowerCase().contains(query.toLowerCase()))
              .take(5)
              .toList();

      suggestions.addAll(historyQueries);

      // Add popular searches if we need more suggestions
      if (suggestions.length < 5) {
        final popularSearches = await getPopularSearches();
        final popularQueries =
            popularSearches
                .where((q) => q.toLowerCase().contains(query.toLowerCase()))
                .where((q) => !suggestions.contains(q))
                .take(5 - suggestions.length)
                .toList();

        suggestions.addAll(popularQueries);
      }
    } catch (e) {
      debugPrint('Error getting search suggestions: $e');
    }

    return suggestions;
  }

  /// Get popular searches (could be updated from server)
  Future<List<String>> getPopularSearches() async {
    try {
      final popularData = _prefs.getString(_popularSearchesKey);
      if (popularData != null) {
        final decoded = jsonDecode(popularData) as List;
        return decoded.cast<String>();
      }
    } catch (e) {
      debugPrint('Error getting popular searches: $e');
    }

    // Default popular searches
    return [
      'Windows troubleshooting',
      'Network setup',
      'Virus removal',
      'Software installation',
      'Hardware repair',
      'Data recovery',
      'Printer setup',
      'Email configuration',
    ];
  }

  /// Update popular searches (could be called from server data)
  Future<void> updatePopularSearches(List<String> searches) async {
    try {
      await _prefs.setString(_popularSearchesKey, jsonEncode(searches));
    } catch (e) {
      debugPrint('Error updating popular searches: $e');
    }
  }

  /// Get filter options for services
  Map<String, dynamic> getServiceFilterOptions(List<ServiceModel> services) {
    final categories = services.map((s) => s.category).toSet().toList();
    final prices = services.map((s) => s.basePrice).toList();
    final durations = services.map((s) => s.estimatedDuration).toList();

    return {
      'categories': categories,
      'minPrice': prices.isEmpty ? 0.0 : prices.reduce((a, b) => a < b ? a : b),
      'maxPrice':
          prices.isEmpty ? 100.0 : prices.reduce((a, b) => a > b ? a : b),
      'minDuration':
          durations.isEmpty ? 0 : durations.reduce((a, b) => a < b ? a : b),
      'maxDuration':
          durations.isEmpty ? 120 : durations.reduce((a, b) => a > b ? a : b),
    };
  }
}

/// Service sorting options
enum ServiceSortOption { relevance, price, duration, popularity, alphabetical }

/// Request sorting options
enum RequestSortOption { dateDesc, dateAsc, status, price }
