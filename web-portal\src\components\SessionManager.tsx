import React, { useCallback, useEffect, useState } from 'react';
import { LogOut, Monitor, Smartphone } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import sessionService from '../services/sessionService';
import { type Session } from '../types/session';
import { Button } from '../components/ui/button';
import { Badge } from '../components/ui/badge';

const SessionManager: React.FC = () => {
  const { user, logoutAllDevices } = useAuth();
  const [sessions, setSessions] = useState<Session[]>([]);
  const [loading, setLoading] = useState(true);
  const [isLoading, setIsLoading] = useState(false);

  const loadSessions = useCallback(async () => {
    if (!user?.uid) return;

    try {
      setLoading(true);
      const userSessions = await sessionService.getUserSessions(user.uid);
      setSessions(userSessions);
    } catch (error) {
      console.error('Failed to load sessions:', error);
    } finally {
      setLoading(false);
    }
  }, [user?.uid]);

  useEffect(() => {
    loadSessions();
  }, [loadSessions]);

  const handleLogoutAllDevices = async () => {
    try {
      setIsLoading(true);
      await logoutAllDevices();
    } catch (error) {
      console.error('Failed to logout from all devices:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (loading) {
    return <div className='text-sm text-muted-foreground'>Loading sessions...</div>;
  }

  return (
    <div className='space-y-4'>
      {sessions.length === 0 ? (
        <p className='text-sm text-muted-foreground'>No active sessions found</p>
      ) : (
        <div className='space-y-3'>
          {sessions.map((session) => (
            <div
              key={`${session.uid}_${session.deviceInfo?.deviceId}`}
              className='flex items-center justify-between p-4 rounded-lg border bg-card'
            >
              <div className='flex items-center space-x-3'>
                {session.deviceInfo?.device?.includes('mobile') ? (
                  <Smartphone className='h-5 w-5 text-muted-foreground' />
                ) : (
                  <Monitor className='h-5 w-5 text-muted-foreground' />
                )}
                <div>
                  <p className='text-sm font-medium'>
                    {session.deviceInfo?.browser || 'Unknown Browser'} on{' '}
                    {session.deviceInfo?.platform || 'Unknown Platform'}
                  </p>
                  <p className='text-xs text-muted-foreground'>
                    Last active: {new Date(session.lastActivity).toLocaleString()}
                  </p>
                </div>
              </div>
              {sessionService.getCurrentSessionId() === session.uid && (
                <Badge variant='outline'>Current</Badge>
              )}
            </div>
          ))}
        </div>
      )}

      {sessions.length > 1 && (
        <div className='pt-4 border-t'>
          <Button
            onClick={handleLogoutAllDevices}
            disabled={isLoading}
            variant='destructive'
            className='w-full'
          >
            <LogOut className='h-4 w-4 mr-2' />
            {isLoading ? 'Logging out...' : 'Log out all devices'}
          </Button>
        </div>
      )}
    </div>
  );
};

export default SessionManager;
