/* ============================================================
   Mr.Tech Landing Page - Luxury Design
   A world-class, award-winning design with clean layout and luxurious effects
============================================================ */

/* Reset & Base Styles
============================================================ */
*, *::before, *::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Variables */
    --color-primary: #3a6df0;
    --color-primary-dark: #2a5cd9;
    --color-primary-light: #6690f4;
    --color-secondary: #6c5ce7;
    --color-accent: #00d2d3;
    --color-dark: #0a2540;
    --color-darker: #051b30;
    --color-light: #ffffff;
    --color-light-gray: #f8fafc;
    --color-gray: #94a3b8;
    --color-text: #334155;
    --color-text-light: #64748b;
    --color-success: #10b981;
    --color-warning: #f59e0b;
    --color-error: #ef4444;
    
    /* Typography */
    --font-heading: 'Playfair Display', serif;
    --font-body: 'Montserrat', sans-serif;
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.15);
    --shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.2);
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 16px;
    --radius-xl: 24px;
    --radius-full: 9999px;
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-2xl: 4rem;
    --spacing-3xl: 6rem;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-body);
    color: var(--color-text);
    line-height: 1.6;
    overflow-x: hidden;
    background-color: var(--color-light);
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

a {
    text-decoration: none;
    color: inherit;
    transition: color var(--transition-fast);
}

ul, ol {
    list-style: none;
}

button, input, textarea, select {
    font-family: inherit;
    font-size: inherit;
    color: inherit;
    border: none;
    background: none;
    outline: none;
}

button {
    cursor: pointer;
}

/* Container & Layout
============================================================ */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

section {
    padding: var(--spacing-3xl) 0;
    position: relative;
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.section-tagline {
    display: inline-block;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 2px;
    color: var(--color-primary);
    margin-bottom: var(--spacing-xs);
    position: relative;
}

.section-tagline::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 2px;
    background-color: var(--color-primary);
}

.section-title {
    font-family: var(--font-heading);
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--color-dark);
    line-height: 1.2;
}

.section-title span {
    color: var(--color-primary);
    font-style: italic;
}

@media (min-width: 768px) {
    .section-title {
        font-size: 3rem;
    }
}

/* Typography
============================================================ */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 600;
    line-height: 1.3;
    color: var(--color-dark);
    margin-bottom: var(--spacing-sm);
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.5rem;
}

h4 {
    font-size: 1.25rem;
}

p {
    margin-bottom: var(--spacing-md);
}

@media (min-width: 768px) {
    h1 {
        font-size: 3.5rem;
    }
    
    h2 {
        font-size: 2.5rem;
    }
    
    h3 {
        font-size: 1.75rem;
    }
}

/* Buttons & Links
============================================================ */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    text-align: center;
    border-radius: var(--radius-full);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    transition: all 0.4s ease;
    z-index: -1;
}

.btn:hover::after {
    left: 0;
}

.btn-primary {
    background-color: var(--color-primary);
    color: var(--color-light);
}

.btn-primary:hover {
    background-color: var(--color-primary-dark);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background-color: var(--color-secondary);
    color: var(--color-light);
}

.btn-secondary:hover {
    background-color: #5a4ed1;
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.btn-outline {
    background-color: transparent;
    border: 2px solid var(--color-primary);
    color: var(--color-primary);
}

.btn-outline:hover {
    background-color: var(--color-primary);
    color: var(--color-light);
}

.btn-white {
    background-color: var(--color-light);
    color: var(--color-primary);
}

.btn-white:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

.btn-outline-white {
    background-color: transparent;
    border: 2px solid var(--color-light);
    color: var(--color-light);
}

.btn-outline-white:hover {
    background-color: var(--color-light);
    color: var(--color-primary);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* Navigation
============================================================ */
.header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 100;
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(5px);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
}

.header.scrolled {
    background-color: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-md);
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 80px;
}

.logo {
    display: flex;
    align-items: center;
}

.logo img {
    height: 40px;
}

.nav-links {
    display: none;
}

.nav-link {
    margin-left: var(--spacing-lg);
    font-weight: 500;
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--color-primary);
    transition: width var(--transition-normal);
}

.nav-link:hover, .nav-link.active {
    color: var(--color-primary);
}

.nav-link:hover::after, .nav-link.active::after {
    width: 100%;
}

.nav-actions {
    display: none;
}

.menu-toggle {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 30px;
    height: 20px;
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 0;
    z-index: 10;
}

.menu-toggle span {
    display: block;
    width: 100%;
    height: 2px;
    background-color: var(--color-dark);
    transition: all var(--transition-fast);
}

@media (min-width: 992px) {
    .nav-links {
        display: flex;
    }
    
    .nav-actions {
        display: block;
    }
    
    .menu-toggle {
        display: none;
    }
}

/* Hero Section
============================================================ */
.hero {
    padding: 140px 0 var(--spacing-3xl);
    background: linear-gradient(135deg, #f6f9fc 0%, #eef2f7 100%);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: -10%;
    right: -10%;
    width: 600px;
    height: 600px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(108, 92, 231, 0.1) 0%, rgba(58, 109, 240, 0.1) 100%);
    z-index: 0;
}

.hero::after {
    content: '';
    position: absolute;
    bottom: -5%;
    left: -5%;
    width: 400px;
    height: 400px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(0, 210, 211, 0.1) 0%, rgba(58, 109, 240, 0.05) 100%);
    z-index: 0;
}

.hero .container {
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 1;
}

.hero-content {
    max-width: 600px;
    margin-bottom: var(--spacing-xl);
}

.hero-title {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-md);
    line-height: 1.2;
}

.hero-title span {
    color: var(--color-primary);
    font-style: italic;
    display: block;
}

.hero-subtitle {
    font-size: 1.1rem;
    color: var(--color-text-light);
    margin-bottom: var(--spacing-lg);
}

.hero-cta {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.hero-badges {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

.app-badge img {
    height: 40px;
    transition: transform var(--transition-normal);
}

.app-badge:hover img {
    transform: translateY(-5px);
}

.hero-image {
    position: relative;
    margin: 0 auto;
    max-width: 80%;
}

.device-mockup {
    position: relative;
    z-index: 2;
    filter: drop-shadow(0 20px 40px rgba(0, 0, 0, 0.2));
    transition: transform var(--transition-slow);
}

.hero-image:hover .device-mockup {
    transform: translateY(-10px);
}

.hero-shape-1, .hero-shape-2 {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-secondary) 100%);
    filter: blur(30px);
    opacity: 0.4;
    z-index: 1;
}

.hero-shape-1 {
    width: 200px;
    height: 200px;
    top: -30px;
    right: 10%;
}

.hero-shape-2 {
    width: 150px;
    height: 150px;
    bottom: 10%;
    left: 5%;
    background: linear-gradient(135deg, var(--color-accent) 0%, var(--color-primary-light) 100%);
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    color: var(--color-text-light);
    opacity: 0.8;
    transition: opacity var(--transition-normal);
    z-index: 2;
}

.scroll-indicator span {
    font-size: 0.8rem;
    margin-bottom: 5px;
}

.scroll-indicator i {
    font-size: 1rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.scroll-indicator:hover {
    opacity: 1;
}

@media (min-width: 992px) {
    .hero .container {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        gap: var(--spacing-xl);
    }
    
    .hero-content {
        margin-bottom: 0;
        flex: 1;
    }
    
    .hero-image {
        flex: 1;
        max-width: 100%;
    }
    
    .hero-title {
        font-size: 3.5rem;
    }
}

/* Features Section
============================================================ */
.features {
    background-color: var(--color-light);
    position: relative;
    overflow: hidden;
}

.features::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233a6df0' fill-opacity='0.03'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    opacity: 0.5;
}

.features-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
    position: relative;
    z-index: 1;
}

.feature-card {
    background-color: var(--color-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 100%);
    transition: height var(--transition-normal);
    z-index: -1;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.feature-card:hover::before {
    height: 100%;
    opacity: 0.05;
}

.feature-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-full);
    background: linear-gradient(135deg, var(--color-primary-light) 0%, var(--color-primary) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-md);
    color: var(--color-light);
    font-size: 1.5rem;
    box-shadow: 0 10px 20px rgba(58, 109, 240, 0.2);
}

.feature-card h3 {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-sm);
}

.feature-card p {
    color: var(--color-text-light);
    margin-bottom: 0;
}

@media (min-width: 768px) {
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 992px) {
    .features-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* How It Works Section
============================================================ */
.how-it-works {
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
    color: var(--color-light);
    position: relative;
    overflow: hidden;
}

.how-it-works::before, .how-it-works::after {
    content: '';
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.05);
}

.how-it-works::before {
    width: 400px;
    height: 400px;
    top: -200px;
    right: -200px;
}

.how-it-works::after {
    width: 300px;
    height: 300px;
    bottom: -150px;
    left: -150px;
}

.how-it-works .section-header {
    color: var(--color-light);
}

.how-it-works .section-tagline {
    color: rgba(255, 255, 255, 0.8);
}

.how-it-works .section-tagline::after {
    background-color: rgba(255, 255, 255, 0.3);
}

.how-it-works .section-title {
    color: var(--color-light);
}

.how-it-works .section-title span {
    color: var(--color-light);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.steps {
    position: relative;
    z-index: 1;
}

.step {
    display: flex;
    flex-direction: column;
    margin-bottom: var(--spacing-xl);
    position: relative;
}

.step:last-child {
    margin-bottom: 0;
}

.step-number {
    width: 50px;
    height: 50px;
    border-radius: var(--radius-full);
    background-color: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: var(--font-heading);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
}

.step-content {
    padding-left: var(--spacing-lg);
}

.step h3 {
    color: var(--color-light);
    margin-bottom: var(--spacing-xs);
}

.step p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: var(--spacing-lg);
}

.step-image {
    width: 100%;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    transition: transform var(--transition-normal);
}

.step-image:hover {
    transform: translateY(-5px) scale(1.02);
}

@media (min-width: 768px) {
    .step {
        flex-direction: row;
        align-items: center;
    }
    
    .step-number {
        margin-bottom: 0;
    }
    
    .step-content {
        flex: 1;
    }
    
    .step-image {
        width: 40%;
        margin-left: var(--spacing-lg);
    }
}

/* Services Section
============================================================ */
.services {
    background-color: var(--color-light-gray);
    position: relative;
}

.services-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-lg);
}

.service-card {
    background-color: var(--color-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-hover);
}

.service-icon {
    width: 70px;
    height: 70px;
    border-radius: var(--radius-full);
    background: var(--color-light);
    border: 2px solid var(--color-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
    color: var(--color-primary);
    font-size: 1.75rem;
    position: relative;
    z-index: 1;
    transition: all var(--transition-normal);
}

.service-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: var(--radius-full);
    background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-secondary) 100%);
    z-index: -1;
    opacity: 0;
    transform: scale(0.8);
    transition: all var(--transition-normal);
}

.service-card:hover .service-icon {
    color: var(--color-light);
    border-color: transparent;
}

.service-card:hover .service-icon::before {
    opacity: 1;
    transform: scale(1);
}

.service-card h3 {
    margin-bottom: var(--spacing-sm);
}

.service-card p {
    color: var(--color-text-light);
    margin-bottom: 0;
}

@media (min-width: 768px) {
    .services-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 992px) {
    .services-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* CTA Section
============================================================ */
.cta {
    background: linear-gradient(135deg, var(--color-secondary) 0%, var(--color-primary) 100%);
    color: var(--color-light);
    padding: var(--spacing-2xl) 0;
    position: relative;
    overflow: hidden;
}

.cta::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'%3E%3Cpath d='M0 40L40 0H20L0 20M40 40V20L20 40'/%3E%3C/g%3E%3C/svg%3E");
}

.cta-content {
    position: relative;
    z-index: 1;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.cta-content h2 {
    color: var(--color-light);
    margin-bottom: var(--spacing-md);
    font-size: 2.5rem;
}

.cta-content p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--spacing-lg);
    font-size: 1.125rem;
}

.cta-buttons {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--spacing-md);
}

/* Testimonials Section
============================================================ */
.testimonials {
    background-color: var(--color-light);
    position: relative;
}

.testimonials-slider {
    max-width: 800px;
    margin: 0 auto;
    overflow: hidden;
    position: relative;
}

.testimonial {
    background-color: var(--color-light);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin: 0 var(--spacing-sm);
    box-shadow: var(--shadow-lg);
    position: relative;
}

.testimonial::before {
    content: '\201C';
    font-family: var(--font-heading);
    position: absolute;
    top: -30px;
    left: 20px;
    font-size: 6rem;
    color: var(--color-primary);
    opacity: 0.1;
}

.testimonial-content {
    margin-bottom: var(--spacing-md);
}

.testimonial-content p {
    font-style: italic;
    color: var(--color-text);
    line-height: 1.8;
    margin-bottom: 0;
}

.testimonial-info {
    display: flex;
    align-items: center;
}

.testimonial-avatar {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-full);
    overflow: hidden;
    margin-right: var(--spacing-md);
    border: 3px solid var(--color-light);
    box-shadow: var(--shadow-md);
}

.testimonial-meta h4 {
    margin-bottom: 5px;
    font-size: 1.1rem;
}

.testimonial-rating {
    color: #ffc107;
    font-size: 0.9rem;
}

.testimonials-nav {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.testimonial-prev,
.testimonial-next {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    background-color: var(--color-light);
    color: var(--color-primary);
    box-shadow: var(--shadow-md);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-normal);
}

.testimonial-prev:hover,
.testimonial-next:hover {
    background-color: var(--color-primary);
    color: var(--color-light);
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
}

/* FAQ Section
============================================================ */
.faq {
    background-color: var(--color-light-gray);
    position: relative;
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background-color: var(--color-light);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-md);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

.faq-question {
    padding: var(--spacing-md);
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    transition: all var(--transition-normal);
}

.faq-question h3 {
    margin-bottom: 0;
    font-size: 1.125rem;
    transition: color var(--transition-normal);
}

.faq-toggle {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-normal);
}

.faq-question:hover h3 {
    color: var(--color-primary);
}

.faq-answer {
    padding: 0 var(--spacing-md) var(--spacing-md);
    display: none;
}

.faq-answer p {
    color: var(--color-text-light);
    margin-bottom: 0;
}

.faq-item.active .faq-question {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.faq-item.active .faq-question h3 {
    color: var(--color-primary);
}

.faq-item.active .faq-toggle {
    transform: rotate(45deg);
}

.faq-item.active .faq-answer {
    display: block;
}

/* Download Section
============================================================ */
.download {
    background-color: var(--color-light);
    position: relative;
    overflow: hidden;
}

.download-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-2xl);
}

.download-text {
    text-align: center;
    max-width: 600px;
}

.download-badges {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: var(--spacing-md);
    margin-top: var(--spacing-md);
}

.download-image {
    position: relative;
    max-width: 100%;
}

.download-shape {
    position: absolute;
    width: 400px;
    height: 400px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(108, 92, 231, 0.1) 0%, rgba(58, 109, 240, 0.1) 100%);
    top: -10%;
    right: -10%;
    z-index: -1;
    filter: blur(50px);
}

@media (min-width: 992px) {
    .download-content {
        flex-direction: row;
        justify-content: space-between;
    }
    
    .download-text {
        text-align: left;
        flex: 1;
    }
    
    .download-badges {
        justify-content: flex-start;
    }
    
    .download-image {
        flex: 1;
    }
}

/* Footer
============================================================ */
.footer {
    background-color: var(--color-darker);
    color: var(--color-light);
    padding-top: var(--spacing-2xl);
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--color-primary) 0%, var(--color-secondary) 100%);
}

.footer-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-xl);
    padding-bottom: var(--spacing-xl);
}

.footer-info p {
    color: rgba(255, 255, 255, 0.7);
    margin: var(--spacing-md) 0;
}

.footer-logo img {
    height: 40px;
}

.social-links {
    display: flex;
    gap: var(--spacing-sm);
}

.social-links a {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-full);
    background-color: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--color-light);
    transition: all var(--transition-normal);
}

.social-links a:hover {
    background-color: var(--color-primary);
    transform: translateY(-5px);
}

.footer-links h4 {
    color: var(--color-light);
    margin-bottom: var(--spacing-md);
    position: relative;
    display: inline-block;
}

.footer-links h4::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 2px;
    background-color: var(--color-primary);
}

.footer-links ul li {
    margin-bottom: var(--spacing-xs);
}

.footer-links ul li a {
    color: rgba(255, 255, 255, 0.7);
    transition: all var(--transition-normal);
}

.footer-links ul li a:hover {
    color: var(--color-light);
    padding-left: 5px;
}

.contact-info li {
    display: flex;
    align-items: center;
}

.contact-info li i {
    margin-right: 10px;
    color: var(--color-primary);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: var(--spacing-md) 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 0;
}

.footer-bottom-links {
    display: flex;
    gap: var(--spacing-md);
}

.footer-bottom-links a {
    color: rgba(255, 255, 255, 0.7);
}

.footer-bottom-links a:hover {
    color: var(--color-light);
}

@media (min-width: 768px) {
    .footer-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .footer-bottom {
        flex-direction: row;
        justify-content: space-between;
    }
}

@media (min-width: 992px) {
    .footer-grid {
        grid-template-columns: 2fr 1fr 1fr 1fr;
    }
}

/* Back to Top Button
============================================================ */
#back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    border-radius: var(--radius-full);
    background-color: var(--color-primary);
    color: var(--color-light);
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all var(--transition-normal);
    z-index: 99;
}

#back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

#back-to-top:hover {
    background-color: var(--color-primary-dark);
    transform: translateY(-5px);
}

/* Cookie Consent
============================================================ */
.cookie-consent {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 40px);
    max-width: 500px;
    background-color: var(--color-dark);
    color: var(--color-light);
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-xl);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transform: translateX(-50%) translateY(20px);
    transition: all var(--transition-normal);
}

.cookie-consent.show {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(0);
}

.cookie-consent p {
    margin-bottom: 0;
    font-size: 0.9rem;
}

.cookie-buttons {
    display: flex;
    gap: var(--spacing-sm);
    justify-content: flex-end;
}

/* Animations
============================================================ */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-in {
    animation: fadeIn 0.8s ease forwards;
}

.animate-delay-1 {
    animation-delay: 0.2s;
}

.animate-delay-2 {
    animation-delay: 0.4s;
}

.animate-delay-3 {
    animation-delay: 0.6s;
} 