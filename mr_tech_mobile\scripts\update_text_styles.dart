import 'dart:io';
import 'package:flutter/foundation.dart';

void main() async {
  // Directory to scan (start from lib)
  final directory = Directory('lib');

  // Pattern to look for TextStyle instances
  final textStylePatterns = [
    RegExp(
      r'style:\s*TextStyle\(\s*fontSize:\s*(\d+),\s*fontWeight:\s*FontWeight\.([a-zA-Z0-9]+),\s*color:',
    ),
    RegExp(
      r'style:\s*TextStyle\(\s*fontWeight:\s*FontWeight\.([a-zA-Z0-9]+),\s*fontSize:\s*(\d+),\s*color:',
    ),
    RegExp(r'style:\s*theme\.textTheme\.([a-zA-Z0-9]+)\?\.copyWith\('),
  ];

  // Count of files updated
  int filesUpdated = 0;
  int textStylesReplaced = 0;

  // Process all dart files
  await for (final entity in directory.list(recursive: true)) {
    if (entity is File && entity.path.endsWith('.dart')) {
      final file = File(entity.path);
      String content = await file.readAsString();
      final originalContent = content;

      // Skip files that already fully use AppTextStyles
      if (!content.contains('TextStyle(') &&
          !content.contains('.copyWith(') &&
          !content.contains('TextStyle.')) {
        continue;
      }

      // Check if we need to add import for AppTextStyles
      bool needsImport =
          !content.contains("import '../widgets/text_styles.dart'") &&
          !content.contains("import 'text_styles.dart'");

      // Replace theme.textTheme references
      content = _replaceThemeTextThemeReferences(content);

      // Replace direct TextStyle instances
      content = _replaceDirectTextStyles(content);

      // Add import if needed and content was modified
      if (needsImport && content != originalContent) {
        // Find the last import statement
        final importPattern = RegExp(r'(import [^;]+;)\n');
        final matches = importPattern.allMatches(content).toList();
        if (matches.isNotEmpty) {
          final lastImport = matches.last;
          final insertPosition = lastImport.end;
          content =
              "${content.substring(0, insertPosition)}\nimport '../widgets/text_styles.dart';${content.substring(insertPosition)}";
        }
      }

      // If content was modified, write it back
      if (content != originalContent) {
        await file.writeAsString(content);
        filesUpdated++;

        // Count replacements
        final originalMatches = _countMatches(originalContent);
        final newMatches = _countMatches(content);
        textStylesReplaced += (originalMatches - newMatches);

        debugPrint('Updated ${entity.path}');
      }
    }
  }

  debugPrint('\nSummary:');
  debugPrint('Files updated: $filesUpdated');
  debugPrint('TextStyle instances replaced: $textStylesReplaced');
  debugPrint(
    '\nNote: This script performs automated replacements, but manual review is recommended.',
  );
  debugPrint(
    'Some TextStyle instances may need manual adjustment for proper styling.',
  );
}

// Helper to count TextStyle instances
int _countMatches(String content) {
  int count = 0;
  count += 'TextStyle('.allMatches(content).length;
  count += 'textTheme.'.allMatches(content).length;
  return count;
}

// Replace theme.textTheme references with AppTextStyles
String _replaceThemeTextThemeReferences(String content) {
  // Map theme.textTheme properties to AppTextStyles methods
  final Map<String, String> textThemeToAppTextStyles = {
    'displayLarge': 'displayLarge',
    'displayMedium': 'displayMedium',
    'displaySmall': 'displaySmall',
    'headlineLarge': 'headingLarge',
    'headlineMedium': 'headingMedium',
    'headlineSmall': 'headingSmall',
    'titleLarge': 'headingSmall',
    'titleMedium': 'labelLarge',
    'titleSmall': 'labelMedium',
    'bodyLarge': 'bodyLarge',
    'bodyMedium': 'bodyMedium',
    'bodySmall': 'bodySmall',
    'labelLarge': 'labelLarge',
    'labelMedium': 'labelMedium',
    'labelSmall': 'labelSmall',
  };

  // Pattern for theme.textTheme.X?.copyWith(...)
  final themeTextThemePattern = RegExp(
    r'(theme|Theme\.of\(context\))\.textTheme\.([a-zA-Z0-9]+)\?\.(copyWith\([^)]*\))',
  );

  content = content.replaceAllMapped(themeTextThemePattern, (match) {
    final themeRef = match.group(1)!;
    final textThemeProperty = match.group(2)!;
    final copyWithArgs = match.group(3)!;

    // Get the corresponding AppTextStyles method
    final appTextStyleMethod =
        textThemeToAppTextStyles[textThemeProperty] ?? 'bodyMedium';

    // Extract color from copyWith if present
    String? colorParam;
    final colorMatch = RegExp(r'color:\s*([^,)]+)').firstMatch(copyWithArgs);
    if (colorMatch != null) {
      colorParam = colorMatch.group(1);
    }

    // Build the replacement
    if (colorParam != null) {
      return 'AppTextStyles.$appTextStyleMethod(context, color: $colorParam)';
    } else {
      return 'AppTextStyles.$appTextStyleMethod(context)';
    }
  });

  // Also handle theme.textTheme.X without copyWith
  final simpleThemeTextThemePattern = RegExp(
    r'(theme|Theme\.of\(context\))\.textTheme\.([a-zA-Z0-9]+)(?!\?)',
  );

  content = content.replaceAllMapped(simpleThemeTextThemePattern, (match) {
    final themeRef = match.group(1)!;
    final textThemeProperty = match.group(2)!;

    // Get the corresponding AppTextStyles method
    final appTextStyleMethod =
        textThemeToAppTextStyles[textThemeProperty] ?? 'bodyMedium';

    return 'AppTextStyles.$appTextStyleMethod(context)';
  });

  return content;
}

// Replace direct TextStyle instances with AppTextStyles
String _replaceDirectTextStyles(String content) {
  // Multiple patterns for different TextStyle formats

  // Pattern 1: TextStyle with fontSize and fontWeight
  final textStylePattern1 = RegExp(
    r'TextStyle\(\s*fontSize:\s*(\d+),\s*fontWeight:\s*FontWeight\.([a-zA-Z0-9]+)([^)]*)\)',
  );
  content = content.replaceAllMapped(textStylePattern1, (match) {
    return _processTextStyleMatch(
      match.group(1)!,
      match.group(2)!,
      match.group(3) ?? '',
    );
  });

  // Pattern 2: TextStyle with fontWeight and fontSize
  final textStylePattern2 = RegExp(
    r'TextStyle\(\s*fontWeight:\s*FontWeight\.([a-zA-Z0-9]+),\s*fontSize:\s*(\d+)([^)]*)\)',
  );
  content = content.replaceAllMapped(textStylePattern2, (match) {
    return _processTextStyleMatch(
      match.group(2)!,
      match.group(1)!,
      match.group(3) ?? '',
    );
  });

  // Pattern 3: TextStyle with just fontSize
  final textStylePattern3 = RegExp(r'TextStyle\(\s*fontSize:\s*(\d+)([^)]*)\)');
  content = content.replaceAllMapped(textStylePattern3, (match) {
    final fontSize = int.parse(match.group(1)!);
    final otherProps = match.group(2) ?? '';

    // Default to normal weight
    return _processTextStyleMatch(match.group(1)!, 'normal', otherProps);
  });

  // Pattern 4: TextStyle with color first
  final textStylePattern4 = RegExp(
    r'TextStyle\(\s*color:\s*([^,)]+),\s*fontSize:\s*(\d+)([^)]*)\)',
  );
  content = content.replaceAllMapped(textStylePattern4, (match) {
    final colorParam = match.group(1);
    final fontSize = match.group(2)!;
    final otherProps = match.group(3) ?? '';

    // Extract fontWeight if present
    String fontWeight = 'normal';
    final weightMatch = RegExp(
      r'fontWeight:\s*FontWeight\.([a-zA-Z0-9]+)',
    ).firstMatch(otherProps);
    if (weightMatch != null) {
      fontWeight = weightMatch.group(1)!;
    }

    final appTextStyleMethod = _mapToAppTextStyleMethod(
      int.parse(fontSize),
      fontWeight,
    );

    if (colorParam != null) {
      return 'AppTextStyles.$appTextStyleMethod(context, color: $colorParam)';
    } else {
      return 'AppTextStyles.$appTextStyleMethod(context)';
    }
  });

  return content;
}

// Process a TextStyle match with fontSize and fontWeight
String _processTextStyleMatch(
  String fontSizeStr,
  String fontWeight,
  String otherProps,
) {
  final fontSize = int.parse(fontSizeStr);

  // Map fontSize and fontWeight to AppTextStyles method
  String appTextStyleMethod = _mapToAppTextStyleMethod(fontSize, fontWeight);

  // Extract color from other properties if present
  String? colorParam;
  final colorMatch = RegExp(r'color:\s*([^,)]+)').firstMatch(otherProps);
  if (colorMatch != null) {
    colorParam = colorMatch.group(1);
  }

  // Build the replacement
  if (colorParam != null) {
    return 'AppTextStyles.$appTextStyleMethod(context, color: $colorParam)';
  } else {
    return 'AppTextStyles.$appTextStyleMethod(context)';
  }
}

// Map fontSize and fontWeight to appropriate AppTextStyles method
String _mapToAppTextStyleMethod(int fontSize, String fontWeight) {
  final bool isBold =
      fontWeight == 'bold' ||
      fontWeight == 'w600' ||
      fontWeight == 'w700' ||
      fontWeight == 'w800' ||
      fontWeight == 'w900';

  // Map based on fontSize and weight
  if (fontSize >= 32) {
    return 'displayLarge';
  } else if (fontSize >= 28) {
    return 'displayMedium';
  } else if (fontSize >= 24) {
    return 'displaySmall';
  } else if (fontSize >= 22) {
    return 'headingLarge';
  } else if (fontSize >= 20) {
    return 'headingMedium';
  } else if (fontSize >= 16) {
    return isBold ? 'headingSmall' : 'bodyLarge';
  } else if (fontSize >= 14) {
    return isBold ? 'buttonMedium' : 'bodyMedium';
  } else if (fontSize >= 12) {
    return isBold ? 'buttonSmall' : 'bodySmall';
  } else {
    return 'labelSmall';
  }
}
