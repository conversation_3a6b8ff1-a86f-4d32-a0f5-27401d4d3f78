import 'package:flutter/foundation.dart';
import 'dart:async';

/// Architecture pattern utilities for improved code organization
/// Provides standardized patterns for state management, dependency injection, and service organization
class ArchitecturePatterns {
  // Private constructor to prevent instantiation
  ArchitecturePatterns._();

  /// Repository pattern implementation for data access
  static abstract class Repository<T> {
    /// Get item by ID
    Future<T?> getById(String id);
    
    /// Get all items
    Future<List<T>> getAll();
    
    /// Create new item
    Future<T> create(T item);
    
    /// Update existing item
    Future<T> update(String id, T item);
    
    /// Delete item
    Future<void> delete(String id);
    
    /// Search items with criteria
    Future<List<T>> search(Map<String, dynamic> criteria);
  }

  /// Use case pattern for business logic
  static abstract class UseCase<TInput, TOutput> {
    /// Execute the use case
    Future<TOutput> execute(TInput input);
    
    /// Validate input before execution
    bool validateInput(TInput input) => true;
    
    /// Handle errors during execution
    TOutput handleError(dynamic error) {
      debugPrint('UseCase error: $error');
      rethrow;
    }
  }

  /// Result pattern for operation outcomes
  static class Result<T> {
    final T? data;
    final String? error;
    final bool isSuccess;
    final Map<String, dynamic>? metadata;

    const Result._({
      this.data,
      this.error,
      required this.isSuccess,
      this.metadata,
    });

    /// Create successful result
    factory Result.success(T data, {Map<String, dynamic>? metadata}) {
      return Result._(
        data: data,
        isSuccess: true,
        metadata: metadata,
      );
    }

    /// Create error result
    factory Result.error(String error, {Map<String, dynamic>? metadata}) {
      return Result._(
        error: error,
        isSuccess: false,
        metadata: metadata,
      );
    }

    /// Check if result is failure
    bool get isFailure => !isSuccess;

    /// Get data or throw if error
    T get dataOrThrow {
      if (isSuccess && data != null) {
        return data!;
      }
      throw Exception(error ?? 'Unknown error');
    }

    /// Get data or return fallback
    T getDataOr(T fallback) {
      return isSuccess && data != null ? data! : fallback;
    }

    /// Transform successful result
    Result<U> map<U>(U Function(T data) transform) {
      if (isSuccess && data != null) {
        try {
          return Result.success(transform(data!), metadata: metadata);
        } catch (e) {
          return Result.error('Transform error: $e', metadata: metadata);
        }
      }
      return Result.error(error ?? 'No data to transform', metadata: metadata);
    }

    /// Handle result with callbacks
    void when({
      required void Function(T data) onSuccess,
      required void Function(String error) onError,
    }) {
      if (isSuccess && data != null) {
        onSuccess(data!);
      } else {
        onError(error ?? 'Unknown error');
      }
    }
  }

  /// Service locator pattern for dependency injection
  static class ServiceLocator {
    static final Map<Type, dynamic> _services = {};
    static final Map<Type, dynamic Function()> _factories = {};

    /// Register a singleton service
    static void registerSingleton<T>(T service) {
      _services[T] = service;
    }

    /// Register a factory for creating services
    static void registerFactory<T>(T Function() factory) {
      _factories[T] = factory;
    }

    /// Get service instance
    static T get<T>() {
      // Check for singleton first
      if (_services.containsKey(T)) {
        return _services[T] as T;
      }

      // Check for factory
      if (_factories.containsKey(T)) {
        final factory = _factories[T] as T Function();
        return factory();
      }

      throw Exception('Service of type $T not registered');
    }

    /// Check if service is registered
    static bool isRegistered<T>() {
      return _services.containsKey(T) || _factories.containsKey(T);
    }

    /// Clear all registrations (useful for testing)
    static void clear() {
      _services.clear();
      _factories.clear();
    }

    /// Get all registered service types
    static List<Type> getRegisteredTypes() {
      return [..._services.keys, ..._factories.keys];
    }
  }

  /// Event bus pattern for decoupled communication
  static class EventBus {
    static final Map<Type, List<Function>> _listeners = {};

    /// Subscribe to events of type T
    static void subscribe<T>(void Function(T event) listener) {
      _listeners.putIfAbsent(T, () => []).add(listener);
    }

    /// Unsubscribe from events
    static void unsubscribe<T>(void Function(T event) listener) {
      _listeners[T]?.remove(listener);
      if (_listeners[T]?.isEmpty == true) {
        _listeners.remove(T);
      }
    }

    /// Publish an event
    static void publish<T>(T event) {
      final listeners = _listeners[T];
      if (listeners != null) {
        for (final listener in List.from(listeners)) {
          try {
            (listener as void Function(T))(event);
          } catch (e) {
            debugPrint('EventBus error in listener: $e');
          }
        }
      }
    }

    /// Clear all listeners
    static void clear() {
      _listeners.clear();
    }

    /// Get listener count for event type
    static int getListenerCount<T>() {
      return _listeners[T]?.length ?? 0;
    }
  }

  /// Command pattern for encapsulating operations
  static abstract class Command<T> {
    /// Execute the command
    Future<T> execute();
    
    /// Undo the command (if supported)
    Future<void> undo() async {
      throw UnimplementedError('Undo not implemented for this command');
    }
    
    /// Check if command can be undone
    bool get canUndo => false;
    
    /// Get command description
    String get description;
  }

  /// Command invoker for managing command execution
  static class CommandInvoker {
    final List<Command> _history = [];
    final int _maxHistorySize;

    CommandInvoker({int maxHistorySize = 50}) : _maxHistorySize = maxHistorySize;

    /// Execute a command and add to history
    Future<T> execute<T>(Command<T> command) async {
      try {
        final result = await command.execute();
        
        // Add to history if it supports undo
        if (command.canUndo) {
          _history.add(command);
          
          // Maintain history size
          if (_history.length > _maxHistorySize) {
            _history.removeAt(0);
          }
        }
        
        return result;
      } catch (e) {
        debugPrint('Command execution failed: $e');
        rethrow;
      }
    }

    /// Undo the last command
    Future<void> undo() async {
      if (_history.isEmpty) {
        throw Exception('No commands to undo');
      }

      final command = _history.removeLast();
      try {
        await command.undo();
      } catch (e) {
        debugPrint('Command undo failed: $e');
        // Re-add to history if undo failed
        _history.add(command);
        rethrow;
      }
    }

    /// Check if undo is available
    bool get canUndo => _history.isNotEmpty;

    /// Get command history
    List<String> get commandHistory {
      return _history.map((cmd) => cmd.description).toList();
    }

    /// Clear command history
    void clearHistory() {
      _history.clear();
    }
  }

  /// Observer pattern for reactive programming
  static class Observable<T> {
    T _value;
    final List<void Function(T)> _observers = [];

    Observable(this._value);

    /// Get current value
    T get value => _value;

    /// Set new value and notify observers
    set value(T newValue) {
      if (_value != newValue) {
        _value = newValue;
        _notifyObservers();
      }
    }

    /// Subscribe to value changes
    void subscribe(void Function(T) observer) {
      _observers.add(observer);
    }

    /// Unsubscribe from value changes
    void unsubscribe(void Function(T) observer) {
      _observers.remove(observer);
    }

    /// Notify all observers
    void _notifyObservers() {
      for (final observer in List.from(_observers)) {
        try {
          observer(_value);
        } catch (e) {
          debugPrint('Observer error: $e');
        }
      }
    }

    /// Transform observable value
    Observable<U> map<U>(U Function(T) transform) {
      final mapped = Observable<U>(transform(_value));
      subscribe((value) {
        try {
          mapped.value = transform(value);
        } catch (e) {
          debugPrint('Observable transform error: $e');
        }
      });
      return mapped;
    }

    /// Filter observable values
    Observable<T> where(bool Function(T) predicate) {
      final filtered = Observable<T>(_value);
      subscribe((value) {
        if (predicate(value)) {
          filtered.value = value;
        }
      });
      return filtered;
    }

    /// Dispose observable
    void dispose() {
      _observers.clear();
    }

    /// Get observer count
    int get observerCount => _observers.length;
  }

  /// State machine pattern for complex state management
  static class StateMachine<TState, TEvent> {
    TState _currentState;
    final Map<TState, Map<TEvent, TState>> _transitions = {};
    final Map<TState, void Function()> _onEnterCallbacks = {};
    final Map<TState, void Function()> _onExitCallbacks = {};

    StateMachine(this._currentState);

    /// Get current state
    TState get currentState => _currentState;

    /// Add state transition
    void addTransition(TState from, TEvent event, TState to) {
      _transitions.putIfAbsent(from, () => {})[event] = to;
    }

    /// Add state enter callback
    void onEnter(TState state, void Function() callback) {
      _onEnterCallbacks[state] = callback;
    }

    /// Add state exit callback
    void onExit(TState state, void Function() callback) {
      _onExitCallbacks[state] = callback;
    }

    /// Trigger an event
    bool trigger(TEvent event) {
      final transitions = _transitions[_currentState];
      if (transitions == null || !transitions.containsKey(event)) {
        debugPrint('Invalid transition: $_currentState -> $event');
        return false;
      }

      final newState = transitions[event]!;
      
      // Call exit callback for current state
      _onExitCallbacks[_currentState]?.call();
      
      // Change state
      _currentState = newState;
      
      // Call enter callback for new state
      _onEnterCallbacks[_currentState]?.call();
      
      return true;
    }

    /// Check if transition is valid
    bool canTransition(TEvent event) {
      return _transitions[_currentState]?.containsKey(event) == true;
    }

    /// Get available events for current state
    List<TEvent> getAvailableEvents() {
      return _transitions[_currentState]?.keys.toList() ?? [];
    }
  }
}
