import 'dart:io';
import 'package:flutter/foundation.dart';

void main() async {
  final directory = Directory('lib');
  final files = directory
      .listSync(recursive: true)
      .whereType<File>()
      .where((file) => file.path.endsWith('.dart'));

  int filesUpdated = 0;

  for (final file in files) {
    try {
      String content = await file.readAsString();
      final originalContent = content;
      bool modified = false;

      // Check if file uses AppTextStyles but doesn't import it
      if (content.contains('AppTextStyles') &&
          !content.contains("import '../theme/app_theme.dart'") &&
          !content.contains(
            "import 'package:mr_tech_mobile/theme/app_theme.dart'",
          )) {
        // Add the import at the top
        final lines = content.split('\n');
        int importIndex = 0;

        // Find the last import statement
        for (int i = 0; i < lines.length; i++) {
          if (lines[i].trim().startsWith('import ')) {
            importIndex = i + 1;
          } else if (lines[i].trim().isNotEmpty &&
              !lines[i].trim().startsWith('//')) {
            break;
          }
        }

        // Insert the import
        lines.insert(importIndex, "import '../theme/app_theme.dart';");
        content = lines.join('\n');
        modified = true;
      }

      if (modified) {
        await file.writeAsString(content);
        filesUpdated++;
        debugPrint('Added AppTextStyles import to ${file.path}');
      }
    } catch (e) {
      debugPrint('Error processing ${file.path}: $e');
    }
  }

  debugPrint('\nSummary:');
  debugPrint('Files updated with missing imports: $filesUpdated');
}
