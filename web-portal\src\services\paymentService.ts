import type { QueryDocumentSnapshot } from 'firebase/firestore';
import {
  Timestamp,
  addDoc,
  collection,
  doc,
  limit as firestoreLimit,
  getDoc,
  getDocs,
  orderBy,
  query,
  startAfter,
  updateDoc,
  where,
} from 'firebase/firestore';
import { db } from '../config/firebase';
import type {
  PaymentFilters,
  PaymentStatus,
  PaymentSummary,
  PaymentTransaction,
  PaymentTransactionResponse,
  RefundRequest,
  RefundResponse,
} from '../types/payment';
import type { PaymentDetailsResponse } from '../types/paymentDetails';

class PaymentService {
  private readonly COLLECTION_NAME = 'payment_transactions';
  private readonly RESPONSES_COLLECTION = 'payment_responses';

  /**
   * Get paginated payment transactions with filters
   */
  async getTransactions(
    filters: PaymentFilters = {},
    page: number = 1,
    pageSize: number = 20,
    lastDoc?: QueryDocumentSnapshot<any>,
  ): Promise<PaymentTransactionResponse> {
    try {
      console.warn('🔍 PaymentService.getTransactions called with:', { filters, page, pageSize });
      let q = query(collection(db, this.COLLECTION_NAME));

      // Apply filters
      if (filters.status && filters.status.length > 0) {
        q = query(q, where('status', 'in', filters.status));
      }

      if (filters.paymentMethod && filters.paymentMethod.length > 0) {
        q = query(q, where('paymentMethod', 'in', filters.paymentMethod));
      }

      if (filters.requestId) {
        q = query(q, where('requestId', '==', filters.requestId));
      }

      if (filters.customerId) {
        q = query(q, where('customerId', '==', filters.customerId));
      }

      if (filters.isLive !== undefined) {
        q = query(q, where('isLive', '==', filters.isLive));
      }

      if (filters.dateRange) {
        q = query(q, where('createdAt', '>=', Timestamp.fromDate(filters.dateRange.start)));
        q = query(q, where('createdAt', '<=', Timestamp.fromDate(filters.dateRange.end)));
      }

      // Order by creation date (newest first)
      q = query(q, orderBy('createdAt', 'desc'));

      // Pagination
      if (lastDoc) {
        q = query(q, startAfter(lastDoc));
      }

      q = query(q, firestoreLimit(pageSize + 1)); // Get one extra to check if there are more

      const snapshot = await getDocs(q);
      console.warn('📋 Firestore query executed, found', snapshot.size, 'documents');
      const transactions: PaymentTransaction[] = [];
      const docs = snapshot.docs;

      // Process documents
      for (let i = 0; i < Math.min(docs.length, pageSize); i++) {
        const doc = docs[i];
        const data = doc.data();

        transactions.push({
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate() || new Date(),
          processedAt: data.processedAt?.toDate() || new Date(),
          completedAt: data.completedAt?.toDate(),
        } as PaymentTransaction);
      }

      const hasMore = docs.length > pageSize;

      return {
        transactions,
        total: transactions.length, // Note: This is not the total count, just current page
        page,
        limit: pageSize,
        hasMore,
      };
    } catch (error) {
      console.error('Error fetching payment transactions:', error);
      throw new Error('Failed to fetch payment transactions');
    }
  }

  /**
   * Get a single payment transaction by ID
   */
  async getTransactionById(transactionId: string): Promise<PaymentDetailsResponse | null> {
    try {
      const docRef = doc(db, this.COLLECTION_NAME, transactionId);
      const docSnap = await getDoc(docRef);

      if (!docSnap.exists()) {
        return null;
      }

      const data = docSnap.data();
      const transaction: PaymentTransaction = {
        id: docSnap.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        processedAt: data.processedAt?.toDate() || new Date(),
        completedAt: data.completedAt?.toDate(),
      } as PaymentTransaction;

      // Get related request if available
      let relatedRequest = null;
      if (transaction.requestId) {
        try {
          const requestRef = doc(db, 'requests', transaction.requestId);
          const requestSnap = await getDoc(requestRef);
          if (requestSnap.exists()) {
            relatedRequest = {
              id: requestSnap.id,
              ...requestSnap.data(),
            };
          }
        } catch (error) {
          console.warn('Could not fetch related request:', error);
        }
      }

      return {
        transaction,
        relatedRequest,
      };
    } catch (error) {
      console.error('Error fetching payment transaction:', error);
      throw new Error('Failed to fetch payment transaction');
    }
  }

  /**
   * Get payment summary statistics
   */
  async getPaymentSummary(filters: PaymentFilters = {}): Promise<PaymentSummary> {
    try {
      console.warn('📊 PaymentService.getPaymentSummary called with filters:', filters);
      let q = query(collection(db, this.COLLECTION_NAME));

      // Apply filters
      if (filters.dateRange) {
        q = query(q, where('createdAt', '>=', Timestamp.fromDate(filters.dateRange.start)));
        q = query(q, where('createdAt', '<=', Timestamp.fromDate(filters.dateRange.end)));
      }

      if (filters.isLive !== undefined) {
        q = query(q, where('isLive', '==', filters.isLive));
      }

      const snapshot = await getDocs(q);
      console.warn('📊 Summary query executed, found', snapshot.size, 'documents for summary');

      let totalTransactions = 0;
      let totalAmount = 0;
      let completedTransactions = 0;
      let completedAmount = 0;
      let failedTransactions = 0;
      let failedAmount = 0;
      let pendingTransactions = 0;
      let pendingAmount = 0;
      let refundedTransactions = 0;
      let refundedAmount = 0;

      snapshot.docs.forEach((doc) => {
        const data = doc.data();
        const amount = data.amount || 0;
        const status = data.status as PaymentStatus;

        totalTransactions++;
        totalAmount += amount;

        switch (status) {
          case 'completed':
            completedTransactions++;
            completedAmount += amount;
            break;
          case 'failed':
            failedTransactions++;
            failedAmount += amount;
            break;
          case 'pending':
            pendingTransactions++;
            pendingAmount += amount;
            break;
          case 'refunded':
            refundedTransactions++;
            refundedAmount += amount;
            break;
        }
      });

      return {
        totalTransactions,
        totalAmount,
        completedTransactions,
        completedAmount,
        failedTransactions,
        failedAmount,
        pendingTransactions,
        pendingAmount,
        refundedTransactions,
        refundedAmount,
      };
    } catch (error) {
      console.error('Error fetching payment summary:', error);
      throw new Error('Failed to fetch payment summary');
    }
  }

  /**
   * Process a refund request
   */
  async processRefund(refundRequest: RefundRequest): Promise<RefundResponse> {
    try {
      // Get the original transaction
      const transactionDetails = await this.getTransactionById(refundRequest.transactionId);

      if (!transactionDetails) {
        return {
          success: false,
          message: 'Transaction not found',
        };
      }

      const { transaction } = transactionDetails;

      // Validate refund request
      if (transaction.status !== 'completed') {
        return {
          success: false,
          message: 'Can only refund completed transactions',
        };
      }

      if (transaction.isRefunded) {
        return {
          success: false,
          message: 'Transaction has already been refunded',
        };
      }

      const refundAmount = refundRequest.amount || transaction.amount;
      if (refundAmount > transaction.amount) {
        return {
          success: false,
          message: 'Refund amount cannot exceed transaction amount',
        };
      }

      // Create refund record
      const refundRecord = {
        ...refundRequest,
        refundAmount,
        status: 'pending' as const,
        createdAt: Timestamp.now(),
        originalTransactionId: transaction.id,
        originalAmount: transaction.amount,
      };

      const refundRef = await addDoc(collection(db, 'refund_requests'), refundRecord);

      // Update transaction status
      const transactionRef = doc(db, this.COLLECTION_NAME, transaction.id);
      await updateDoc(transactionRef, {
        status: 'refunded',
        isRefunded: true,
        refundedAmountCents: refundAmount * 100,
        refundId: refundRef.id,
        refundedAt: Timestamp.now(),
        updatedAt: Timestamp.now(),
      });

      // Note: In a real implementation, you would also call Paymob's refund API here
      // For now, we'll mark it as completed immediately
      await updateDoc(refundRef, {
        status: 'completed',
        processedAt: Timestamp.now(),
      });

      return {
        success: true,
        refundId: refundRef.id,
        message: 'Refund processed successfully',
        refundAmount,
        refundStatus: 'completed',
      };
    } catch (error) {
      console.error('Error processing refund:', error);
      return {
        success: false,
        message: `Failed to process refund: ${error instanceof Error ? error.message : String(error)}`,
      };
    }
  }

  /**
   * Search transactions by text query
   */
  async searchTransactions(
    searchQuery: string,
    filters: PaymentFilters = {},
    limit: number = 20,
  ): Promise<PaymentTransaction[]> {
    try {
      // For now, we'll do a simple search by transaction ID or request ID
      // In a production app, you might want to use Algolia or similar for full-text search

      const queries = [];

      // Search by transaction ID
      if (searchQuery.startsWith('paymob_')) {
        queries.push(
          query(
            collection(db, this.COLLECTION_NAME),
            where('id', '==', searchQuery),
            orderBy('createdAt', 'desc'),
            firestoreLimit(limit),
          ),
        );
      }

      // Search by Paymob transaction ID
      if (!isNaN(Number(searchQuery))) {
        queries.push(
          query(
            collection(db, this.COLLECTION_NAME),
            where('paymobTransactionId', '==', searchQuery),
            orderBy('createdAt', 'desc'),
            firestoreLimit(limit),
          ),
        );
      }

      // Search by request ID
      queries.push(
        query(
          collection(db, this.COLLECTION_NAME),
          where('requestId', '==', searchQuery),
          orderBy('createdAt', 'desc'),
          firestoreLimit(limit),
        ),
      );

      const results: PaymentTransaction[] = [];
      const seenIds = new Set<string>();

      for (const q of queries) {
        const snapshot = await getDocs(q);
        snapshot.docs.forEach((doc) => {
          if (!seenIds.has(doc.id)) {
            seenIds.add(doc.id);
            const data = doc.data();
            results.push({
              id: doc.id,
              ...data,
              createdAt: data.createdAt?.toDate() || new Date(),
              processedAt: data.processedAt?.toDate() || new Date(),
              completedAt: data.completedAt?.toDate(),
            } as PaymentTransaction);
          }
        });
      }

      return results.slice(0, limit);
    } catch (error) {
      console.error('Error searching transactions:', error);
      throw new Error('Failed to search transactions');
    }
  }
}

export default new PaymentService();
