#!/bin/bash

# Deploy Firebase Cloud Functions
echo "🚀 Deploying Firebase Cloud Functions..."

cd firebase-config/functions

# Install dependencies if needed
echo "📦 Installing dependencies..."
npm install

# Deploy the functions
echo "🔥 Deploying to Firebase..."
firebase deploy --only functions

echo "✅ Cloud Functions deployed successfully!"
echo ""
echo "📋 Functions deployed:"
echo "  - sendFcmNotification (triggers on notifications collection)"
echo "  - createTechnicianAccount (callable function)"
echo ""
echo "🔍 To check logs:"
echo "  firebase functions:log --only sendFcmNotification"
echo ""
echo "🧪 To test notifications:"
echo "  1. Create a service request from mobile app"
echo "  2. Approve it from web portal"
echo "  3. Check mobile app for notification"
echo "  4. Check logs: firebase functions:log"
