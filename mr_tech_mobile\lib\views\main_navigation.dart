import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/translation_service.dart';
import '../widgets/text_styles.dart';
import 'home_screen.dart';
import 'chats_list_screen.dart';
import 'services_screen.dart';
import 'settings_screen.dart';
import 'requests_screen.dart';
import 'dart:ui';

// Add AppBarTheme class to maintain unified app bar style across the app
class UnifiedAppBar {
  static AppBar build(BuildContext context, String title) {
    final colorScheme = Theme.of(context).colorScheme;
    return AppBar(
      title: Text(title, style: AppTextStyles.headingSmall(context)),
      centerTitle: true,
      elevation: 0,
      scrolledUnderElevation: 2.0,
      shadowColor: colorScheme.shadow.withOpacity(0.1),
      shape: Border(
        bottom: BorderSide(
          color: colorScheme.surfaceContainerHighest.withOpacity(0.2),
          width: 0.5,
        ),
      ),
    );
  }
}

class MainNavigation extends StatefulWidget {
  final int initialIndex;
  final Widget? replacementScreen;

  const MainNavigation({
    super.key,
    this.initialIndex = 0,
    this.replacementScreen,
  });

  @override
  State<MainNavigation> createState() => _MainNavigationState();
}

class _MainNavigationState extends State<MainNavigation>
    with SingleTickerProviderStateMixin {
  late int _selectedIndex;
  late AnimationController _animationController;

  // List of screens for the bottom navigation
  late final List<Widget> _screens;

  @override
  void initState() {
    super.initState();

    // Initialize selected index from widget
    _selectedIndex = widget.initialIndex;

    // Initialize screens
    _screens = [
      const HomeScreen(),
      const RequestsScreen(),
      const ServicesScreen(),
      const ChatsListScreen(),
      const SettingsScreen(),
    ];

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onItemTapped(int index) {
    if (_selectedIndex != index) {
      setState(() {
        _selectedIndex = index;
      });
      _animationController.reset();
      _animationController.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );
    final colorScheme = Theme.of(context).colorScheme;
    final isRtl = translationService.isRtl;

    // Wrap everything with Directionality to ensure proper RTL/LTR layout
    return Directionality(
      // Use the text direction from the translation service
      textDirection: isRtl ? TextDirection.rtl : TextDirection.ltr,
      child: Scaffold(
        extendBody: true,
        extendBodyBehindAppBar: true,
        body: SafeArea(
          bottom:
              false, // Don't apply bottom padding to allow content to extend behind nav bar
          child: FadeTransition(
            opacity: Tween(begin: 0.8, end: 1.0).animate(
              CurvedAnimation(
                parent: _animationController,
                curve: Curves.easeInOut,
              ),
            ),
            child: SlideTransition(
              position: Tween<Offset>(
                begin: const Offset(0.05, 0),
                end: Offset.zero,
              ).animate(
                CurvedAnimation(
                  parent: _animationController,
                  curve: Curves.easeOutCubic,
                ),
              ),
              // Use replacement screen if provided, otherwise use the selected screen
              child: widget.replacementScreen ?? _screens[_selectedIndex],
            ),
          ),
        ),
        bottomNavigationBar: ClipRRect(
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: Container(
              decoration: BoxDecoration(
                color:
                    Theme.of(
                      context,
                    ).bottomNavigationBarTheme.backgroundColor ??
                    colorScheme.surface.withOpacity(0.9),
                boxShadow: [
                  BoxShadow(
                    color: colorScheme.shadow.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(0, -5),
                  ),
                ],
                border: Border(
                  top: BorderSide(
                    color: colorScheme.surfaceContainerHighest.withOpacity(0.2),
                    width: 0.5,
                  ),
                ),
              ),
              child: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8.0,
                    vertical: 8.0,
                  ),
                  child: BottomNavigationBar(
                    type: BottomNavigationBarType.fixed,
                    currentIndex: _selectedIndex,
                    selectedItemColor: colorScheme.primary,
                    unselectedItemColor: colorScheme.onSurface.withOpacity(0.6),
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                    selectedLabelStyle: AppTextStyles.labelMedium(
                      context,
                    ).copyWith(fontWeight: FontWeight.w600),
                    unselectedLabelStyle: AppTextStyles.labelSmall(
                      context,
                    ).copyWith(fontWeight: FontWeight.w500),
                    onTap: _onItemTapped,
                    items: [
                      _buildNavItem(
                        Icons.home_outlined,
                        Icons.home_rounded,
                        translationService.translate('Home'),
                        0,
                        isRtl,
                      ),
                      _buildNavItem(
                        Icons.assignment_outlined,
                        Icons.assignment_rounded,
                        translationService.translate('Requests'),
                        1,
                        isRtl,
                      ),
                      _buildNavItem(
                        Icons.miscellaneous_services_outlined,
                        Icons.miscellaneous_services_rounded,
                        translationService.translate('Services'),
                        2,
                        isRtl,
                      ),
                      _buildNavItem(
                        Icons.chat_bubble_outline_rounded,
                        Icons.chat_bubble_rounded,
                        translationService.translate('Chat'),
                        3,
                        isRtl,
                      ),
                      _buildNavItem(
                        Icons.settings_outlined,
                        Icons.settings_rounded,
                        translationService.translate('Settings'),
                        4,
                        isRtl,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  BottomNavigationBarItem _buildNavItem(
    IconData unselectedIcon,
    IconData selectedIcon,
    String label,
    int index,
    bool isRtl,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    final isSelected = _selectedIndex == index;

    return BottomNavigationBarItem(
      icon: Container(
        padding: const EdgeInsets.symmetric(vertical: 6),
        child: AnimatedCrossFade(
          firstChild: Icon(unselectedIcon, size: 24),
          secondChild: Icon(selectedIcon, size: 26),
          crossFadeState:
              isSelected ? CrossFadeState.showSecond : CrossFadeState.showFirst,
          duration: const Duration(milliseconds: 200),
        ),
      ),
      label: label,
    );
  }
}
