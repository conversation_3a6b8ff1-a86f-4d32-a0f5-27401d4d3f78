import {
  addDoc,
  collection,
  getDocs,
  limit,
  orderBy,
  query,
  serverTimestamp,
  where,
} from 'firebase/firestore';
import { db } from '../config/firebase';
import type { RequestModel } from '../types/request';

export interface FCMNotificationData {
  title: string;
  body: string;
  type: string;
  requestId: string;
  customerId: string;
  data?: Record<string, any>;
}

export interface FCMNotificationResponse {
  success: boolean;
  error?: string;
}

/**
 * Service for sending FCM notifications from web portal to mobile app users
 */
export class FCMNotificationService {
  /**
   * Send FCM notification to a specific user
   */
  async sendNotificationToUser(
    userId: string,
    notificationData: FCMNotificationData,
  ): Promise<FCMNotificationResponse> {
    try {
      console.warn(`Sending FCM notification to user: ${userId}`, notificationData);

      // For chat activation notifications, check for recent duplicates
      if (notificationData.type === 'chat_activation') {
        const recentNotifications = await getDocs(
          query(
            collection(db, 'notifications'),
            where('user_id', '==', userId),
            where('request_id', '==', notificationData.requestId),
            where('type', '==', 'chat_activation'),
            orderBy('created_at', 'desc'),
            limit(1),
          ),
        );

        if (!recentNotifications.empty) {
          const recentNotification = recentNotifications.docs[0];
          const recentTimestamp = recentNotification.data().created_at;
          const now = new Date();

          // If there's a notification within the last 30 seconds, skip creating another
          if (
            recentTimestamp &&
            recentTimestamp.toDate &&
            now.getTime() - recentTimestamp.toDate().getTime() < 30000
          ) {
            console.warn(
              `Recent chat activation notification already exists for request: ${notificationData.requestId}, skipping duplicate`,
            );
            return {
              success: true,
              message: 'Duplicate notification skipped',
            };
          }
        }
      }

      // Create notification document in Firestore
      // This will trigger the Cloud Function to send the actual FCM message
      await addDoc(collection(db, 'notifications'), {
        user_id: userId,
        title: notificationData.title,
        body: notificationData.body,
        type: notificationData.type,
        request_id: notificationData.requestId,
        timestamp: Date.now(), // Add timestamp for duplicate checking
        data: {
          requestId: notificationData.requestId,
          customerId: notificationData.customerId,
          type: notificationData.type,
          click_action: 'FLUTTER_NOTIFICATION_CLICK',
          ...notificationData.data,
        },
        created_at: serverTimestamp(),
        delivered: false,
        read: false,
      });

      console.warn(`FCM notification queued successfully for user: ${userId}`);
      return { success: true };
    } catch (error) {
      console.error('Error sending FCM notification:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Send request approval notification
   */
  async sendRequestApprovedNotification(
    request: RequestModel,
    technicianName: string,
  ): Promise<FCMNotificationResponse> {
    // Check for recent duplicate request_approved notifications
    try {
      const recentNotifications = await getDocs(
        query(
          collection(db, 'notifications'),
          where('user_id', '==', request.customer_id),
          where('request_id', '==', request.id),
          where('type', '==', 'request_approved'),
          orderBy('created_at', 'desc'),
          limit(1),
        ),
      );

      if (!recentNotifications.empty) {
        const recentNotification = recentNotifications.docs[0];
        const recentTimestamp = recentNotification.data().created_at;
        const now = new Date();

        // If there's a notification within the last 30 seconds, skip creating another
        if (
          recentTimestamp &&
          recentTimestamp.toDate &&
          now.getTime() - recentTimestamp.toDate().getTime() < 30000
        ) {
          console.warn(
            `Recent request_approved notification already exists for request: ${request.id}, skipping duplicate`,
          );
          return {
            success: true,
            message: 'Duplicate notification skipped',
          };
        }
      }
    } catch (duplicateCheckError) {
      console.error(
        'Error checking for duplicate request_approved notifications:',
        duplicateCheckError,
      );
      // Continue with sending the notification if duplicate check fails
    }

    const serviceName =
      typeof request.service_name === 'string'
        ? request.service_name
        : request.service_name?.en || 'Service';

    return this.sendNotificationToUser(request.customer_id, {
      title: 'Request Approved',
      body: `Your ${serviceName} request has been approved by ${technicianName}`,
      type: 'request_approved',
      requestId: request.id,
      customerId: request.customer_id,
      data: {
        technicianName,
        serviceName,
        status: 'approved',
      },
    });
  }

  /**
   * Send session started notification
   */
  async sendSessionStartedNotification(
    request: RequestModel,
    technicianName: string,
  ): Promise<FCMNotificationResponse> {
    const serviceName =
      typeof request.service_name === 'string'
        ? request.service_name
        : request.service_name?.en || 'Service';

    return this.sendNotificationToUser(request.customer_id, {
      title: 'Session Started',
      body: `${technicianName} has started working on your ${serviceName} request`,
      type: 'session_started',
      requestId: request.id,
      customerId: request.customer_id,
      data: {
        technicianName,
        serviceName,
        status: 'in_progress',
      },
    });
  }

  /**
   * Send request completed notification
   */
  async sendRequestCompletedNotification(
    request: RequestModel,
    technicianName: string,
  ): Promise<FCMNotificationResponse> {
    const serviceName =
      typeof request.service_name === 'string'
        ? request.service_name
        : request.service_name?.en || 'Service';

    return this.sendNotificationToUser(request.customer_id, {
      title: 'Request Completed',
      body: `Your ${serviceName} request has been completed by ${technicianName}`,
      type: 'request_completed',
      requestId: request.id,
      customerId: request.customer_id,
      data: {
        technicianName,
        serviceName,
        status: 'completed',
      },
    });
  }

  /**
   * Send chat activation notification
   */
  async sendChatActivationNotification(
    request: RequestModel,
    technicianName: string,
  ): Promise<FCMNotificationResponse> {
    const serviceName =
      typeof request.service_name === 'string'
        ? request.service_name
        : request.service_name?.en || 'Service';

    return this.sendNotificationToUser(request.customer_id, {
      title: 'Chat Started',
      body: `${technicianName} has started a chat for your ${serviceName} request`,
      type: 'chat_activation',
      requestId: request.id,
      customerId: request.customer_id,
      data: {
        technicianName,
        serviceName,
        chatActive: true,
      },
    });
  }
}

// Export singleton instance
export const fcmNotificationService = new FCMNotificationService();
