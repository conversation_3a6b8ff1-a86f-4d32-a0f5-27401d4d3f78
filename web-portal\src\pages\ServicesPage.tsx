import React, { useEffect, useState } from 'react';
import type { CreateServiceInput, ServiceModel, UpdateServiceInput } from '../types/service';
import { ServiceList } from '../components/services/ServiceList';
import { ServiceForm } from '../components/services/ServiceForm';
import { MultilingualTestComponent } from '../components/services/MultilingualTestComponent';
import serviceService from '../services/serviceService';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../components/ui/dialog';
// Using dialog for confirmation instead of alert-dialog
import { toast } from 'sonner';
import { Plus, RefreshCw, Search, Settings, TestTube } from 'lucide-react';

export const ServicesPage: React.FC = () => {
  const [services, setServices] = useState<ServiceModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedService, setSelectedService] = useState<ServiceModel | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [formLoading, setFormLoading] = useState(false);
  const [deleteService, setDeleteService] = useState<ServiceModel | null>(null);
  const [activeTab, setActiveTab] = useState<'management' | 'testing'>('management');

  // Load services
  const loadServices = async () => {
    try {
      setLoading(true);
      const allServices = await serviceService.getAll();
      setServices(allServices);
    } catch (error) {
      console.error('Error loading services:', error);
      toast.error('Failed to load services');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadServices();
  }, []);

  // Filter services based on search
  const filteredServices = services.filter((service) => {
    if (!searchTerm) return true;

    const searchLower = searchTerm.toLowerCase();
    const name =
      typeof service.name === 'string'
        ? service.name
        : service.name.en || service.name.ar || Object.values(service.name)[0] || '';
    const description =
      typeof service.description === 'string'
        ? service.description
        : service.description.en ||
          service.description.ar ||
          Object.values(service.description)[0] ||
          '';

    return (
      name.toLowerCase().includes(searchLower) ||
      description.toLowerCase().includes(searchLower) ||
      service.category.toLowerCase().includes(searchLower)
    );
  });

  // Handle service creation/update
  const handleSubmit = async (data: CreateServiceInput | UpdateServiceInput) => {
    try {
      setFormLoading(true);

      if (selectedService) {
        // Update existing service
        await serviceService.updateService(selectedService.id, data as UpdateServiceInput);
        toast.success('Service updated successfully');
      } else {
        // Create new service
        await serviceService.createService(data as CreateServiceInput);
        toast.success('Service created successfully');
      }

      await loadServices();
      handleCloseForm();
    } catch (error) {
      console.error('Error saving service:', error);
      toast.error(selectedService ? 'Failed to update service' : 'Failed to create service');
    } finally {
      setFormLoading(false);
    }
  };

  // Handle service deletion
  const handleDelete = async (serviceId: string) => {
    try {
      await serviceService.delete(serviceId);
      toast.success('Service deleted successfully');
      await loadServices();
      setDeleteService(null);
    } catch (error) {
      console.error('Error deleting service:', error);
      toast.error('Failed to delete service');
    }
  };

  // Handle toggle active status
  const handleToggleActive = async (serviceId: string) => {
    try {
      await serviceService.toggleActiveStatus(serviceId);
      toast.success('Service status updated');
      await loadServices();
    } catch (error) {
      console.error('Error updating service status:', error);
      toast.error('Failed to update service status');
    }
  };

  // Form handlers
  const handleCreateNew = () => {
    setSelectedService(null);
    setShowForm(true);
  };

  const handleEdit = (service: ServiceModel) => {
    setSelectedService(service);
    setShowForm(true);
  };

  const handleCloseForm = () => {
    setShowForm(false);
    setSelectedService(null);
  };

  const handleDeleteClick = (serviceId: string) => {
    const service = services.find((s) => s.id === serviceId);
    if (service) {
      setDeleteService(service);
    }
  };

  const getServiceName = (service: ServiceModel): string => {
    if (typeof service.name === 'string') return service.name;
    return (
      service.name.en || service.name.ar || Object.values(service.name)[0] || 'Unnamed Service'
    );
  };

  return (
    <div className='container mx-auto px-4 py-8 max-w-7xl'>
      {/* Header */}
      <div className='flex items-center justify-between mb-8'>
        <div>
          <h1 className='text-3xl font-bold text-gray-900'>Service Management</h1>
          <p className='text-gray-600 mt-2'>
            Manage your service catalog with multilingual support and pricing configuration
          </p>
        </div>
        {activeTab === 'management' && (
          <Button onClick={handleCreateNew} className='flex items-center gap-2'>
            <Plus className='w-4 h-4' />
            Add Service
          </Button>
        )}
      </div>

      {/* Tab Navigation */}
      <div className='flex space-x-1 mb-8 bg-gray-100 p-1 rounded-lg w-fit'>
        <button
          onClick={() => setActiveTab('management')}
          className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'management'
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <Settings className='w-4 h-4' />
          Service Management
        </button>
        <button
          onClick={() => setActiveTab('testing')}
          className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            activeTab === 'testing'
              ? 'bg-white text-gray-900 shadow-sm'
              : 'text-gray-600 hover:text-gray-900'
          }`}
        >
          <TestTube className='w-4 h-4' />
          Multilingual Testing
        </button>
      </div>

      {/* Tab Content */}
      {activeTab === 'management' ? (
        <>
          {/* Search and Controls */}
          <div className='flex items-center gap-4 mb-6'>
            <div className='relative flex-1 max-w-md'>
              <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4' />
              <Input
                placeholder='Search services...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className='pl-10'
              />
            </div>
            <Button
              variant='outline'
              onClick={loadServices}
              disabled={loading}
              className='flex items-center gap-2'
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>

          {/* Stats */}
          <div className='grid grid-cols-1 md:grid-cols-4 gap-4 mb-6'>
            <div className='bg-blue-50 p-4 rounded-lg'>
              <div className='text-2xl font-bold text-blue-600'>{services.length}</div>
              <div className='text-sm text-blue-600'>Total Services</div>
            </div>
            <div className='bg-green-50 p-4 rounded-lg'>
              <div className='text-2xl font-bold text-green-600'>
                {services.filter((s) => s.is_active).length}
              </div>
              <div className='text-sm text-green-600'>Active Services</div>
            </div>
            <div className='bg-orange-50 p-4 rounded-lg'>
              <div className='text-2xl font-bold text-orange-600'>
                {new Set(services.map((s) => s.category)).size}
              </div>
              <div className='text-sm text-orange-600'>Categories</div>
            </div>
            <div className='bg-purple-50 p-4 rounded-lg'>
              <div className='text-2xl font-bold text-purple-600'>
                ${services.reduce((sum, s) => sum + (s.is_active ? s.base_price : 0), 0).toFixed(2)}
              </div>
              <div className='text-sm text-purple-600'>Total Active Value</div>
            </div>
          </div>

          {/* Service List */}
          <ServiceList
            services={filteredServices}
            onEdit={handleEdit}
            onDelete={handleDeleteClick}
            onToggleActive={handleToggleActive}
            loading={loading}
          />
        </>
      ) : (
        /* Multilingual Testing Tab */
        <MultilingualTestComponent />
      )}

      {/* Service Form Dialog */}
      <Dialog open={showForm} onOpenChange={handleCloseForm}>
        <DialogContent className='max-w-4xl max-h-[90vh] overflow-y-auto'>
          <DialogHeader>
            <DialogTitle>{selectedService ? 'Edit Service' : 'Create New Service'}</DialogTitle>
          </DialogHeader>
          <ServiceForm
            service={selectedService}
            onSubmit={handleSubmit}
            onCancel={handleCloseForm}
            loading={formLoading}
          />
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={!!deleteService} onOpenChange={() => setDeleteService(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Service</DialogTitle>
          </DialogHeader>
          <div className='py-4'>
            <p className='text-sm text-gray-600'>
              Are you sure you want to delete "{deleteService ? getServiceName(deleteService) : ''}
              "? This action cannot be undone and will remove the service from your catalog.
            </p>
          </div>
          <div className='flex gap-3 justify-end pt-4'>
            <Button variant='outline' onClick={() => setDeleteService(null)}>
              Cancel
            </Button>
            <Button
              onClick={() => deleteService && handleDelete(deleteService.id)}
              className='bg-red-600 hover:bg-red-700'
            >
              Delete Service
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};
