{"buildFiles": ["D:\\Ibrahim\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Ibrahim\\mr.tech.v2\\mr_tech_mobile\\android\\app\\.cxx\\Debug\\554v3i2e\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\Ibrahim\\mr.tech.v2\\mr_tech_mobile\\android\\app\\.cxx\\Debug\\554v3i2e\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}