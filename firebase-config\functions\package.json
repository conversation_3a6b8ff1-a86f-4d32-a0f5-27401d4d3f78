{"name": "mr-tech-functions", "description": "Mr.Tech Firebase Cloud Functions", "scripts": {"lint": "eslint .", "serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "build": "echo 'Build completed'", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "index.js", "dependencies": {"firebase-admin": "^11.8.0", "firebase-functions": "^4.4.0"}, "devDependencies": {"eslint": "^8.9.0", "eslint-config-google": "^0.14.0", "firebase-functions-test": "^3.1.0"}, "private": true}