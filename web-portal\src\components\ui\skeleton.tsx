import React from 'react';
import { cn } from '../../lib/utils';

interface SkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  className?: string;
}

const Skeleton: React.FC<SkeletonProps> = ({ className, ...props }) => {
  return <div className={cn('animate-pulse rounded-md bg-muted', className)} {...props} />;
};

// Skeleton variants for common use cases
export const SkeletonText: React.FC<{
  lines?: number;
  className?: string;
  lastLineWidth?: string;
}> = ({ lines = 1, className = '', lastLineWidth = 'w-3/4' }) => (
  <div className={cn('space-y-2', className)}>
    {Array.from({ length: lines }).map((_, i) => (
      <Skeleton
        key={i}
        className={cn('h-4', i === lines - 1 && lines > 1 ? lastLineWidth : 'w-full')}
      />
    ))}
  </div>
);

export const SkeletonCard: React.FC<{
  className?: string;
  showHeader?: boolean;
  showFooter?: boolean;
}> = ({ className = '', showHeader = true, showFooter = false }) => (
  <div className={cn('rounded-lg border bg-card p-6 space-y-4', className)}>
    {showHeader && (
      <div className='space-y-2'>
        <Skeleton className='h-6 w-1/3' />
        <Skeleton className='h-4 w-2/3' />
      </div>
    )}
    <div className='space-y-2'>
      <Skeleton className='h-4 w-full' />
      <Skeleton className='h-4 w-5/6' />
      <Skeleton className='h-4 w-4/6' />
    </div>
    {showFooter && (
      <div className='flex justify-between items-center pt-2'>
        <Skeleton className='h-8 w-20' />
        <Skeleton className='h-8 w-16' />
      </div>
    )}
  </div>
);

export const SkeletonTable: React.FC<{
  rows?: number;
  columns?: number;
  className?: string;
}> = ({ rows = 5, columns = 4, className = '' }) => (
  <div className={cn('space-y-3', className)}>
    {/* Table header */}
    <div className='grid gap-4' style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
      {Array.from({ length: columns }).map((_, i) => (
        <Skeleton key={`header-${i}`} className='h-5 w-full' />
      ))}
    </div>

    {/* Table rows */}
    {Array.from({ length: rows }).map((_, rowIndex) => (
      <div
        key={`row-${rowIndex}`}
        className='grid gap-4'
        style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}
      >
        {Array.from({ length: columns }).map((_, colIndex) => (
          <Skeleton key={`cell-${rowIndex}-${colIndex}`} className='h-4 w-full' />
        ))}
      </div>
    ))}
  </div>
);

export const SkeletonList: React.FC<{
  items?: number;
  showAvatar?: boolean;
  className?: string;
}> = ({ items = 5, showAvatar = false, className = '' }) => (
  <div className={cn('space-y-4', className)}>
    {Array.from({ length: items }).map((_, i) => (
      <div key={i} className='flex items-center space-x-4'>
        {showAvatar && <Skeleton className='h-10 w-10 rounded-full' />}
        <div className='flex-1 space-y-2'>
          <Skeleton className='h-4 w-3/4' />
          <Skeleton className='h-3 w-1/2' />
        </div>
      </div>
    ))}
  </div>
);

export const SkeletonStats: React.FC<{
  cards?: number;
  className?: string;
}> = ({ cards = 4, className = '' }) => (
  <div className={cn('grid gap-4 sm:grid-cols-2 lg:grid-cols-4', className)}>
    {Array.from({ length: cards }).map((_, i) => (
      <div key={i} className='rounded-lg border bg-card p-6'>
        <div className='flex items-center justify-between space-y-0 pb-2'>
          <Skeleton className='h-4 w-24' />
          <Skeleton className='h-4 w-4 rounded' />
        </div>
        <div className='space-y-2'>
          <Skeleton className='h-8 w-16' />
          <Skeleton className='h-3 w-32' />
        </div>
      </div>
    ))}
  </div>
);

export const SkeletonChart: React.FC<{
  height?: string;
  className?: string;
}> = ({ height = 'h-64', className = '' }) => (
  <div className={cn('rounded-lg border bg-card p-6', className)}>
    <div className='space-y-4'>
      <div className='flex items-center justify-between'>
        <Skeleton className='h-6 w-32' />
        <Skeleton className='h-8 w-20' />
      </div>
      <Skeleton className={cn('w-full rounded', height)} />
      <div className='flex justify-center space-x-4'>
        <Skeleton className='h-3 w-16' />
        <Skeleton className='h-3 w-20' />
        <Skeleton className='h-3 w-18' />
      </div>
    </div>
  </div>
);

export const SkeletonForm: React.FC<{
  fields?: number;
  showSubmit?: boolean;
  className?: string;
}> = ({ fields = 3, showSubmit = true, className = '' }) => (
  <div className={cn('space-y-6', className)}>
    {Array.from({ length: fields }).map((_, i) => (
      <div key={i} className='space-y-2'>
        <Skeleton className='h-4 w-24' />
        <Skeleton className='h-10 w-full' />
      </div>
    ))}
    {showSubmit && (
      <div className='flex justify-end space-x-2'>
        <Skeleton className='h-10 w-20' />
        <Skeleton className='h-10 w-24' />
      </div>
    )}
  </div>
);

export const SkeletonProfile: React.FC<{ className?: string }> = ({ className = '' }) => (
  <div className={cn('flex items-center space-x-4', className)}>
    <Skeleton className='h-16 w-16 rounded-full' />
    <div className='space-y-2'>
      <Skeleton className='h-6 w-32' />
      <Skeleton className='h-4 w-24' />
      <Skeleton className='h-4 w-40' />
    </div>
  </div>
);

export const SkeletonChat: React.FC<{
  messages?: number;
  className?: string;
}> = ({ messages = 5, className = '' }) => (
  <div className={cn('space-y-4', className)}>
    {Array.from({ length: messages }).map((_, i) => (
      <div key={i} className={cn('flex', i % 2 === 0 ? 'justify-start' : 'justify-end')}>
        <div
          className={cn(
            'max-w-xs space-y-2 rounded-lg p-3',
            i % 2 === 0 ? 'bg-muted' : 'bg-primary/10',
          )}
        >
          <Skeleton className='h-4 w-full' />
          <Skeleton className='h-4 w-3/4' />
          <Skeleton className='h-3 w-16' />
        </div>
      </div>
    ))}
  </div>
);

export default Skeleton;
