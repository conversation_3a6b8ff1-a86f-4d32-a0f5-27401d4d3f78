import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// Refactoring utilities to reduce code duplication and improve maintainability
/// Provides common patterns and utilities to eliminate repetitive code
class RefactoringUtils {
  // Private constructor to prevent instantiation
  RefactoringUtils._();

  /// Common widget builders to reduce duplication
  static class WidgetBuilders {
    /// Build a standardized loading widget
    static Widget buildLoadingWidget({
      String? message,
      Color? color,
      double size = 24.0,
    }) {
      return Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: size,
              height: size,
              child: CircularProgressIndicator(
                strokeWidth: 2.0,
                valueColor: AlwaysStoppedAnimation<Color>(
                  color ?? Colors.blue,
                ),
              ),
            ),
            if (message != null) ...[
              const SizedBox(height: 16),
              Text(
                message,
                style: const TextStyle(fontSize: 14),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      );
    }

    /// Build a standardized error widget
    static Widget buildErrorWidget({
      required String message,
      VoidCallback? onRetry,
      String retryText = 'Retry',
      IconData icon = Icons.error_outline,
    }) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 48,
                color: Colors.red[400],
              ),
              const SizedBox(height: 16),
              Text(
                message,
                style: const TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
              if (onRetry != null) ...[
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: onRetry,
                  child: Text(retryText),
                ),
              ],
            ],
          ),
        ),
      );
    }

    /// Build a standardized empty state widget
    static Widget buildEmptyStateWidget({
      required String message,
      String? actionText,
      VoidCallback? onAction,
      IconData icon = Icons.inbox_outlined,
    }) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                message,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
              if (actionText != null && onAction != null) ...[
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: onAction,
                  child: Text(actionText),
                ),
              ],
            ],
          ),
        ),
      );
    }

    /// Build a standardized confirmation dialog
    static Future<bool?> showConfirmationDialog({
      required BuildContext context,
      required String title,
      required String message,
      String confirmText = 'Confirm',
      String cancelText = 'Cancel',
      bool isDestructive = false,
    }) {
      return showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(cancelText),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: isDestructive
                  ? ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    )
                  : null,
              child: Text(confirmText),
            ),
          ],
        ),
      );
    }

    /// Build a standardized info dialog
    static Future<void> showInfoDialog({
      required BuildContext context,
      required String title,
      required String message,
      String buttonText = 'OK',
    }) {
      return showDialog<void>(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(buttonText),
            ),
          ],
        ),
      );
    }

    /// Build a standardized bottom sheet
    static Future<T?> showStandardBottomSheet<T>({
      required BuildContext context,
      required Widget child,
      bool isScrollControlled = true,
      bool isDismissible = true,
      bool enableDrag = true,
    }) {
      return showModalBottomSheet<T>(
        context: context,
        isScrollControlled: isScrollControlled,
        isDismissible: isDismissible,
        enableDrag: enableDrag,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        builder: (context) => child,
      );
    }
  }

  /// Common form utilities to reduce duplication
  static class FormUtils {
    /// Build a standardized text field
    static Widget buildTextField({
      required TextEditingController controller,
      required String label,
      String? hint,
      String? Function(String?)? validator,
      TextInputType keyboardType = TextInputType.text,
      bool obscureText = false,
      Widget? suffixIcon,
      Widget? prefixIcon,
      int maxLines = 1,
      bool enabled = true,
    }) {
      return TextFormField(
        controller: controller,
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          suffixIcon: suffixIcon,
          prefixIcon: prefixIcon,
          border: const OutlineInputBorder(),
        ),
        validator: validator,
        keyboardType: keyboardType,
        obscureText: obscureText,
        maxLines: maxLines,
        enabled: enabled,
      );
    }

    /// Build a standardized dropdown field
    static Widget buildDropdownField<T>({
      required T? value,
      required List<T> items,
      required String Function(T) itemLabel,
      required void Function(T?) onChanged,
      required String label,
      String? hint,
      String? Function(T?)? validator,
    }) {
      return DropdownButtonFormField<T>(
        value: value,
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          border: const OutlineInputBorder(),
        ),
        items: items.map((item) {
          return DropdownMenuItem<T>(
            value: item,
            child: Text(itemLabel(item)),
          );
        }).toList(),
        onChanged: onChanged,
        validator: validator,
      );
    }

    /// Common form validators
    static String? validateRequired(String? value, {String? fieldName}) {
      if (value == null || value.trim().isEmpty) {
        return '${fieldName ?? 'This field'} is required';
      }
      return null;
    }

    static String? validateEmail(String? value) {
      if (value == null || value.trim().isEmpty) {
        return 'Email is required';
      }
      
      final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
      if (!emailRegex.hasMatch(value.trim())) {
        return 'Please enter a valid email address';
      }
      
      return null;
    }

    static String? validatePhone(String? value) {
      if (value == null || value.trim().isEmpty) {
        return 'Phone number is required';
      }
      
      final phoneRegex = RegExp(r'^\+?[\d\s\-\(\)]{10,}$');
      if (!phoneRegex.hasMatch(value.trim())) {
        return 'Please enter a valid phone number';
      }
      
      return null;
    }

    static String? validateMinLength(String? value, int minLength, {String? fieldName}) {
      if (value == null || value.length < minLength) {
        return '${fieldName ?? 'This field'} must be at least $minLength characters';
      }
      return null;
    }

    static String? validateMaxLength(String? value, int maxLength, {String? fieldName}) {
      if (value != null && value.length > maxLength) {
        return '${fieldName ?? 'This field'} must be no more than $maxLength characters';
      }
      return null;
    }
  }

  /// Common navigation utilities
  static class NavigationUtils {
    /// Navigate to a page with standard transition
    static Future<T?> navigateTo<T>(
      BuildContext context,
      Widget page, {
      bool replace = false,
      bool clearStack = false,
    }) {
      if (clearStack) {
        return Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (_) => page),
          (route) => false,
        );
      } else if (replace) {
        return Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => page),
        );
      } else {
        return Navigator.of(context).push(
          MaterialPageRoute(builder: (_) => page),
        );
      }
    }

    /// Navigate back with optional result
    static void navigateBack<T>(BuildContext context, [T? result]) {
      Navigator.of(context).pop(result);
    }

    /// Check if can navigate back
    static bool canNavigateBack(BuildContext context) {
      return Navigator.of(context).canPop();
    }
  }

  /// Common data processing utilities
  static class DataUtils {
    /// Format currency with proper symbol and decimals
    static String formatCurrency(double amount, {String symbol = 'L.E'}) {
      return '$symbol ${amount.toStringAsFixed(2)}';
    }

    /// Format date in a user-friendly way
    static String formatDate(DateTime date, {bool includeTime = false}) {
      final now = DateTime.now();
      final difference = now.difference(date);

      if (difference.inDays == 0) {
        if (includeTime) {
          return 'Today at ${_formatTime(date)}';
        }
        return 'Today';
      } else if (difference.inDays == 1) {
        if (includeTime) {
          return 'Yesterday at ${_formatTime(date)}';
        }
        return 'Yesterday';
      } else if (difference.inDays < 7) {
        final weekday = _getWeekdayName(date.weekday);
        if (includeTime) {
          return '$weekday at ${_formatTime(date)}';
        }
        return weekday;
      } else {
        final formatted = '${date.day}/${date.month}/${date.year}';
        if (includeTime) {
          return '$formatted at ${_formatTime(date)}';
        }
        return formatted;
      }
    }

    static String _formatTime(DateTime date) {
      final hour = date.hour.toString().padLeft(2, '0');
      final minute = date.minute.toString().padLeft(2, '0');
      return '$hour:$minute';
    }

    static String _getWeekdayName(int weekday) {
      const weekdays = [
        'Monday', 'Tuesday', 'Wednesday', 'Thursday',
        'Friday', 'Saturday', 'Sunday'
      ];
      return weekdays[weekday - 1];
    }

    /// Truncate text with ellipsis
    static String truncateText(String text, int maxLength) {
      if (text.length <= maxLength) return text;
      return '${text.substring(0, maxLength - 3)}...';
    }

    /// Capitalize first letter of each word
    static String capitalizeWords(String text) {
      return text.split(' ').map((word) {
        if (word.isEmpty) return word;
        return word[0].toUpperCase() + word.substring(1).toLowerCase();
      }).join(' ');
    }

    /// Generate initials from name
    static String generateInitials(String name, {int maxInitials = 2}) {
      final words = name.trim().split(' ');
      final initials = words
          .take(maxInitials)
          .map((word) => word.isNotEmpty ? word[0].toUpperCase() : '')
          .join();
      return initials;
    }

    /// Validate and sanitize phone number
    static String? sanitizePhoneNumber(String? phone) {
      if (phone == null || phone.trim().isEmpty) return null;
      
      // Remove all non-digit characters except +
      String cleaned = phone.replaceAll(RegExp(r'[^\d+]'), '');
      
      // Ensure it starts with + for international format
      if (!cleaned.startsWith('+')) {
        cleaned = '+$cleaned';
      }
      
      return cleaned;
    }
  }

  /// Common async operation utilities
  static class AsyncUtils {
    /// Debounce function calls
    static Timer? _debounceTimer;
    
    static void debounce(Duration delay, VoidCallback callback) {
      _debounceTimer?.cancel();
      _debounceTimer = Timer(delay, callback);
    }

    /// Throttle function calls
    static DateTime? _lastThrottleTime;
    
    static void throttle(Duration interval, VoidCallback callback) {
      final now = DateTime.now();
      if (_lastThrottleTime == null || 
          now.difference(_lastThrottleTime!) >= interval) {
        _lastThrottleTime = now;
        callback();
      }
    }

    /// Retry operation with exponential backoff
    static Future<T> retryWithBackoff<T>(
      Future<T> Function() operation, {
      int maxRetries = 3,
      Duration baseDelay = const Duration(seconds: 1),
      double backoffMultiplier = 2.0,
    }) async {
      for (int attempt = 0; attempt < maxRetries; attempt++) {
        try {
          return await operation();
        } catch (e) {
          if (attempt == maxRetries - 1) rethrow;
          
          final delay = Duration(
            milliseconds: (baseDelay.inMilliseconds * 
                          (backoffMultiplier * attempt)).round(),
          );
          
          await Future.delayed(delay);
        }
      }
      
      throw Exception('Max retries exceeded');
    }
  }
}
