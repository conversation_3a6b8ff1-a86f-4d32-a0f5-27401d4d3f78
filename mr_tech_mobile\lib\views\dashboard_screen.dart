import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/translation_service.dart';
import '../services/database_service.dart';
import '../models/request_model.dart';
import '../models/user_model.dart';
import '../widgets/text_styles.dart';
import '../utils/navigation_utils.dart';

import 'requests_screen.dart';
import 'services_screen.dart';
import 'package:intl/intl.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  bool _isLoading = true;

  // User data
  UserModel? _user;
  List<RequestModel> _userRequests = [];

  // Dashboard metrics
  int _totalRequests = 0;
  int _completedRequests = 0;
  int _pendingRequests = 0;
  double _totalSpent = 0.0;
  double _averageRating = 0.0;
  int _ratingCount = 0;

  // Chart data
  List<RequestModel> _recentRequests = [];
  final Map<String, int> _categoryStats = {};
  final Map<String, double> _monthlySpending = {};

  @override
  void initState() {
    super.initState();
    _loadDashboardData();
  }

  Future<void> _loadDashboardData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final databaseService = Provider.of<DatabaseService>(
        context,
        listen: false,
      );

      // Load user data
      _user = await databaseService.getCurrentUser();

      // Load user requests
      _userRequests = await databaseService.getUserRequests();

      // Calculate metrics
      _calculateMetrics();
    } catch (e) {
      debugPrint('Error loading dashboard data: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _calculateMetrics() {
    _totalRequests = _userRequests.length;
    _completedRequests =
        _userRequests.where((r) => r.status == 'completed').length;
    _pendingRequests =
        _userRequests
            .where((r) => r.status == 'pending' || r.status == 'in_progress')
            .length;

    _totalSpent = _userRequests
        .where((r) => r.status == RequestStatus.completed)
        .fold(0.0, (sum, r) => sum + r.amount);

    // Calculate average rating
    final ratedRequests =
        _userRequests.where((r) => r.rating != null && r.rating! > 0).toList();
    _ratingCount = ratedRequests.length;
    _averageRating =
        _ratingCount > 0
            ? ratedRequests.fold(0.0, (sum, r) => sum + (r.rating ?? 0.0)) /
                _ratingCount
            : 0.0;

    // Get recent requests (last 10)
    _recentRequests =
        _userRequests.where((r) => r.status == RequestStatus.completed).toList()
          ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
    _recentRequests = _recentRequests.take(10).toList();

    // Calculate category statistics
    _categoryStats.clear();
    for (final request in _userRequests) {
      // Use service name as category since serviceCategory doesn't exist
      final category =
          request.serviceName.isNotEmpty ? request.serviceName : 'Other';
      _categoryStats[category] = (_categoryStats[category] ?? 0) + 1;
    }

    // Calculate monthly spending (last 6 months)
    _monthlySpending.clear();
    final now = DateTime.now();
    for (int i = 5; i >= 0; i--) {
      final month = DateTime(now.year, now.month - i, 1);
      final monthKey = DateFormat('MMM yyyy').format(month);

      final monthlyTotal = _userRequests
          .where(
            (r) =>
                r.status == RequestStatus.completed &&
                r.createdAt.year == month.year &&
                r.createdAt.month == month.month,
          )
          .fold(0.0, (sum, r) => sum + r.amount);

      _monthlySpending[monthKey] = monthlyTotal;
    }
  }

  String _translate(String key) {
    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );
    return translationService.translate(key);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    if (_isLoading) {
      return Scaffold(
        backgroundColor: colorScheme.surface,
        appBar: AppBar(
          title: Text(_translate('Dashboard')),
          backgroundColor: colorScheme.surface,
          elevation: 0,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: CustomScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        slivers: [
          // Simple App Bar following home screen style
          SliverAppBar(
            expandedHeight: 80,
            floating: true,
            pinned: false,
            snap: true,
            elevation: 0,
            backgroundColor: colorScheme.surface,
            leading: BackButton(color: colorScheme.onSurface),
            flexibleSpace: FlexibleSpaceBar(
              background: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 10,
                  ),
                  child: Row(
                    children: [
                      const SizedBox(width: 56), // Space for back button
                      Expanded(
                        child: Text(
                          _translate('Dashboard'),
                          style: AppTextStyles.headingMedium(
                            context,
                            color: colorScheme.onSurface,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(width: 56), // Balance the layout
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Content
          SliverPadding(
            padding: EdgeInsets.fromLTRB(
              16,
              16,
              16,
              MediaQuery.of(context).padding.bottom + 100, // Account for bottom nav bar
            ),
            sliver: SliverList(
              delegate: SliverChildListDelegate([
                // Welcome Section
                _buildWelcomeSection(colorScheme),
                const SizedBox(height: 20),

                // Quick Stats
                _buildQuickStats(colorScheme),
                const SizedBox(height: 20),

                // Category Stats
                _buildCategoryStats(colorScheme),
                const SizedBox(height: 20),

                // Recent Activity
                _buildRecentActivity(colorScheme),
                const SizedBox(height: 20),

                // Quick Actions
                _buildQuickActions(colorScheme),

                // Add some bottom spacing for better visual separation
                const SizedBox(height: 24),
              ]),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWelcomeSection(ColorScheme colorScheme) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade300),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 24,
                  backgroundColor: colorScheme.primary,
                  child: Text(
                    _user?.displayName?.substring(0, 1).toUpperCase() ?? 'U',
                    style: AppTextStyles.headingSmall(
                      context,
                      color: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${_translate('Welcome back')}, ${_user?.displayName ?? _translate('User')}!',
                        style: AppTextStyles.headingSmall(context),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _translate('Here\'s your service overview'),
                        style: AppTextStyles.bodyMedium(
                          context,
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (_averageRating > 0) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.amber.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.amber.withValues(alpha: 0.3)),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.star, color: Colors.amber, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      '${_averageRating.toStringAsFixed(1)} ($_ratingCount ${_translate('reviews')})',
                      style: AppTextStyles.bodySmall(
                        context,
                        color: Colors.amber.shade700,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats(ColorScheme colorScheme) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            _translate('Total Requests'),
            _totalRequests.toString(),
            Icons.receipt_long,
            colorScheme.primary,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            _translate('Completed'),
            _completedRequests.toString(),
            Icons.check_circle,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            _translate('Total Spent'),
            '${_translate('L.E')} ${_totalSpent.toStringAsFixed(0)}',
            Icons.payments,
            Colors.orange,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade300),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 28),
            const SizedBox(height: 8),
            Text(
              value,
              style: AppTextStyles.headingSmall(context, color: color),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: AppTextStyles.bodySmall(context),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryStats(ColorScheme colorScheme) {
    if (_categoryStats.isEmpty) return const SizedBox.shrink();

    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade300),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _translate('Service Categories'),
              style: AppTextStyles.headingSmall(context),
            ),
            const SizedBox(height: 12),
            ...(_categoryStats.entries.take(5).map((entry) {
              final percentage = (entry.value / _totalRequests) * 100;
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: _getCategoryColor(entry.key),
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _translate(entry.key.toUpperCase()),
                        style: AppTextStyles.bodyMedium(context),
                      ),
                    ),
                    Text(
                      '${entry.value} (${percentage.toStringAsFixed(0)}%)',
                      style: AppTextStyles.bodySmall(
                        context,
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              );
            })),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentActivity(ColorScheme colorScheme) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade300),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _translate('Recent Activity'),
                  style: AppTextStyles.headingSmall(context),
                ),
                TextButton(
                  onPressed: () {
                    NavigationUtils.navigateKeepingStack(
                      context,
                      const RequestsScreen(),
                    );
                  },
                  child: Text(_translate('View All')),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (_recentRequests.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      Icon(
                        Icons.history,
                        size: 48,
                        color: colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _translate('No recent activity'),
                        style: AppTextStyles.bodyMedium(
                          context,
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              )
            else
              ...(_recentRequests
                  .take(5)
                  .map((request) => _buildActivityItem(request, colorScheme))),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(RequestModel request, ColorScheme colorScheme) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _getStatusColor(
                request.status.toString().split('.').last,
              ).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _getStatusIcon(request.status.toString().split('.').last),
              color: _getStatusColor(request.status.toString().split('.').last),
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  request.serviceName,
                  style: AppTextStyles.bodyMedium(context),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Text(
                  DateFormat('MMM dd, yyyy').format(request.createdAt),
                  style: AppTextStyles.bodySmall(
                    context,
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '${_translate('L.E')} ${request.amount.toStringAsFixed(0)}',
            style: AppTextStyles.bodyMedium(
              context,
              color: colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions(ColorScheme colorScheme) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade300),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _translate('Quick Actions'),
              style: AppTextStyles.headingSmall(context),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    _translate('New Request'),
                    Icons.add_circle_outline,
                    colorScheme.primary,
                    () {
                      NavigationUtils.navigateKeepingStack(
                        context,
                        const ServicesScreen(),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildActionButton(
                    _translate('My Requests'),
                    Icons.list_alt,
                    colorScheme.secondary,
                    () {
                      NavigationUtils.navigateKeepingStack(
                        context,
                        const RequestsScreen(),
                      );
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 6),
            Text(
              title,
              style: AppTextStyles.bodySmall(context, color: color),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Color _getCategoryColor(String category) {
    final colors = {
      'os': Colors.blue,
      'productivity': Colors.green,
      'hardware': Colors.orange,
      'network': Colors.purple,
      'security': Colors.red,
      'data': Colors.teal,
    };
    return colors[category] ?? Colors.grey;
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'completed':
        return Colors.green;
      case 'in_progress':
        return Colors.orange;
      case 'pending':
        return Colors.blue;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(String status) {
    switch (status) {
      case 'completed':
        return Icons.check_circle;
      case 'in_progress':
        return Icons.hourglass_empty;
      case 'pending':
        return Icons.schedule;
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.help;
    }
  }
}
