/**
 * <PERSON><PERSON><PERSON> to set up the Firebase Realtime Database structure for the chat feature
 * Run with: node scripts/setup_realtime_db.js
 */

const admin = require('firebase-admin');
const serviceAccount = require('../service-account.json'); // Path to your service account key file

// Initialize Firebase Admin
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  databaseURL: 'https://stopnow-be6b7-default-rtdb.firebaseio.com' // Update with your project's database URL
});

const db = admin.database();

// Example request ID for testing
const requestId = 'example-request-123';

// Example user IDs
const customerId = 'customer-123';
const technicianId = 'technician-456';

// Set up the database structure
async function setupDatabase() {
  try {
    console.log('Setting up Realtime Database structure for chat...');

    // Create/update sample messages
    const messagesRef = db.ref(`messages/${requestId}`);
    
    // Clear existing messages for this request (for testing purposes)
    await messagesRef.remove();
    
    // Add sample system message
    await messagesRef.push({
      id: 'system-msg-1',
      request_id: requestId,
      sender_type: 'system',
      sender_id: 'system',
      message_type: 'system',
      content: 'Chat is now active. The technician will assist you shortly.',
      read: true,
      timestamp: admin.database.ServerValue.TIMESTAMP
    });
    
    // Add sample technician message
    await messagesRef.push({
      id: 'tech-msg-1',
      request_id: requestId,
      sender_type: 'technician',
      sender_id: technicianId,
      message_type: 'text',
      content: 'Hello! How can I help you today?',
      read: false,
      timestamp: admin.database.ServerValue.TIMESTAMP
    });
    
    // Set up request metadata
    const requestMetaRef = db.ref(`requests_meta/${requestId}`);
    await requestMetaRef.set({
      chat_active: true,
      last_message: 'Hello! How can I help you today?',
      last_message_time: admin.database.ServerValue.TIMESTAMP,
      last_sender_id: technicianId,
      last_sender_type: 'technician',
      updated_at: admin.database.ServerValue.TIMESTAMP
    });
    
    // Set up user-specific chat references
    const customerChatRef = db.ref(`user_chats/${customerId}/${requestId}`);
    await customerChatRef.set({
      unread_count: 1,
      last_message: 'Hello! How can I help you today?',
      last_message_time: admin.database.ServerValue.TIMESTAMP,
      last_sender_id: technicianId,
      request_id: requestId
    });
    
    const technicianChatRef = db.ref(`user_chats/${technicianId}/${requestId}`);
    await technicianChatRef.set({
      unread_count: 0,
      last_message: 'Hello! How can I help you today?',
      last_message_time: admin.database.ServerValue.TIMESTAMP,
      last_sender_id: technicianId,
      request_id: requestId
    });
    
    console.log('Database setup completed successfully!');
    
  } catch (error) {
    console.error('Error setting up database:', error);
  } finally {
    // Disconnect from database
    admin.app().delete();
  }
}

// Run the setup
setupDatabase(); 