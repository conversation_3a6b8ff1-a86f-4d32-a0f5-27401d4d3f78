import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_model.dart';
import 'secure_storage_service.dart';
import 'dart:async';
import 'package:flutter/foundation.dart';

class AuthService {
  // Singleton instance
  static final AuthService _instance = AuthService._internal();

  factory AuthService() => _instance;

  AuthService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: ['email', 'profile'],
    // Remove web client ID on Android - use the one from google-services.json instead
  );
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final SecureStorageService _secureStorage = SecureStorageService();

  // Rate limiting for login attempts
  static const int _maxLoginAttempts = 5;
  static const int _lockoutDurationMinutes = 15;
  static const String _loginAttemptsKey = 'login_attempts';
  static const String _lastLoginAttemptKey = 'last_login_attempt';
  static const String _lockoutUntilKey = 'lockout_until';

  // Retry configuration for Firestore operations
  static const int _maxRetries = 3;
  static const Duration _baseDelay = Duration(seconds: 1);

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Get current user UID
  String? get currentUserId => _auth.currentUser?.uid;

  // Stream of auth state changes
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Check if user is signed in
  bool get isSignedIn => _auth.currentUser != null;

  // Sign in with email and password
  Future<UserCredential> signInWithEmailAndPassword(
    String email,
    String password,
  ) async {
    try {
      // Check for rate limiting
      await _checkRateLimiting();

      final UserCredential userCredential = await _auth
          .signInWithEmailAndPassword(email: email, password: password);

      // Check if the user document exists in Firestore
      final docSnapshot =
          await _firestore
              .collection('users')
              .doc(userCredential.user!.uid)
              .get();
      if (!docSnapshot.exists) {
        // If the user document doesn't exist, recreate it for existing users
        debugPrint('User document missing, recreating for existing user: ${userCredential.user!.uid}');
        await _createUserDocument(
          uid: userCredential.user!.uid,
          email: userCredential.user!.email!,
          fullName: userCredential.user!.displayName ?? 'User',
        );
      }

      // Reset login attempts on successful login
      await _resetLoginAttempts();

      // Store user ID in secure storage
      await _secureStorage.storeUserId(userCredential.user!.uid);

      // Store last login timestamp
      await _secureStorage.storeLastLogin(DateTime.now().toIso8601String());

      return userCredential;
    } catch (e) {
      // Increment failed login attempts
      await _incrementLoginAttempts();
      throw _handleAuthException(e);
    }
  }

  // Create user with email and password
  Future<UserCredential> createUserWithEmailAndPassword(
    String email,
    String password,
    String fullName,
  ) async {
    try {
      // Validate password requirements
      final String? passwordError = validatePassword(password);
      if (passwordError != null) {
        throw Exception(passwordError);
      }

      final UserCredential userCredential = await _auth
          .createUserWithEmailAndPassword(email: email, password: password);

      // Update user profile
      await userCredential.user?.updateDisplayName(fullName);

      // Create user document in Firestore
      await _createUserDocument(
        uid: userCredential.user!.uid,
        email: email,
        fullName: fullName,
      );

      // Send email verification
      if (userCredential.user != null) {
        await userCredential.user!.sendEmailVerification();
      }

      // Store user ID in secure storage
      await _secureStorage.storeUserId(userCredential.user!.uid);

      // Store last login timestamp
      await _secureStorage.storeLastLogin(DateTime.now().toIso8601String());

      return userCredential;
    } catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Sign in with Google
  Future<UserCredential> signInWithGoogle() async {
    try {
      // Verify GoogleSignIn is properly initialized
      debugPrint('Starting Google Sign In process...');

      // Trigger the Google authentication flow
      debugPrint('Calling GoogleSignIn.signIn()...');
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();

      debugPrint(
        'GoogleSignIn result: ${googleUser != null ? 'Success' : 'Null (cancelled)'}',
      );

      if (googleUser == null) {
        throw Exception('Google sign in was cancelled by user');
      }

      debugPrint('Google user email: ${googleUser.email}');

      // Obtain the auth details from the Google sign in
      debugPrint('Getting authentication tokens...');
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      if (googleAuth.accessToken == null || googleAuth.idToken == null) {
        throw Exception('Failed to obtain Google authentication tokens');
      }

      // Create a new credential
      final OAuthCredential credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Sign in to Firebase with the Google credential
      final UserCredential userCredential = await _auth.signInWithCredential(
        credential,
      );

      // Check if the user document exists in Firestore
      final docSnapshot =
          await _firestore
              .collection('users')
              .doc(userCredential.user!.uid)
              .get();

      if (!docSnapshot.exists) {
        // Create new user document if it doesn't exist
        await _createUserDocument(
          uid: userCredential.user!.uid,
          email: userCredential.user!.email!,
          fullName: userCredential.user!.displayName ?? 'User',
        );
      } else {
        // Update display name and photo URL in Firestore to ensure they match Firebase Auth
        await _firestore
            .collection('users')
            .doc(userCredential.user!.uid)
            .update({
              'display_name': userCredential.user!.displayName,
              'photo_url': userCredential.user!.photoURL,
              'updated_at': FieldValue.serverTimestamp(),
            });
      }

      // Store user ID in secure storage
      await _secureStorage.storeUserId(userCredential.user!.uid);

      // Store last login timestamp
      await _secureStorage.storeLastLogin(DateTime.now().toIso8601String());

      return userCredential;
    } catch (e) {
      debugPrint('Detailed Google Sign-In error: $e');

      // More specific error handling for Google Sign-In
      if (e.toString().contains('ApiException: 10')) {
        debugPrint(
          'API Exception 10: Developer Error - check your SHA-1 and package name in Firebase console',
        );
        throw Exception(
          'Google Sign In configuration error. Please verify app setup in Firebase console.',
        );
      } else if (e.toString().contains('ApiException: 12501')) {
        // Common when sign-in is canceled by user
        debugPrint('API Exception 12501: Sign-in canceled by user');
        throw Exception('Google sign in was cancelled');
      } else if (e.toString().contains('ApiException: 7')) {
        debugPrint(
          'API Exception 7: Network error - check internet connection',
        );
        throw Exception(
          'Network error during Google sign in. Please check your internet connection.',
        );
      } else if (e.toString().contains('PlatformException')) {
        debugPrint('Platform Exception during Google Sign-In: $e');
        throw Exception(
          'Google Sign In failed due to a platform error. Please try again.',
        );
      }

      throw _handleAuthException(e);
    }
  }

  // Rate limiting implementation
  Future<void> _checkRateLimiting() async {
    try {
      // Check if account is locked out
      final lockoutUntilStr = await _secureStorage.getData(_lockoutUntilKey);

      if (lockoutUntilStr != null) {
        final lockoutUntil = DateTime.parse(lockoutUntilStr);
        final now = DateTime.now();

        if (now.isBefore(lockoutUntil)) {
          // Account is still locked out
          final remainingMinutes = lockoutUntil.difference(now).inMinutes + 1;
          throw Exception(
            'Too many login attempts. Please try again in $remainingMinutes minutes.',
          );
        } else {
          // Lockout period has expired, reset attempts
          await _resetLoginAttempts();
        }
      }

      // Check number of recent attempts
      final attemptsStr = await _secureStorage.getData(_loginAttemptsKey);
      final attempts = attemptsStr != null ? int.parse(attemptsStr) : 0;

      if (attempts >= _maxLoginAttempts) {
        // Too many attempts, lock the account
        final lockoutUntil = DateTime.now().add(
          Duration(minutes: _lockoutDurationMinutes),
        );
        await _secureStorage.storeData(
          _lockoutUntilKey,
          lockoutUntil.toIso8601String(),
        );
        throw Exception(
          'Too many login attempts. Please try again in $_lockoutDurationMinutes minutes.',
        );
      }

      // Update last attempt timestamp
      await _secureStorage.storeData(
        _lastLoginAttemptKey,
        DateTime.now().toIso8601String(),
      );
    } catch (e) {
      if (e is Exception) {
        rethrow;
      }
      // If there's an error with the rate limiting system, allow the login to proceed
    }
  }

  // Increment failed login attempts
  Future<void> _incrementLoginAttempts() async {
    try {
      final attemptsStr = await _secureStorage.getData(_loginAttemptsKey);
      final attempts = attemptsStr != null ? int.parse(attemptsStr) : 0;

      await _secureStorage.storeData(
        _loginAttemptsKey,
        (attempts + 1).toString(),
      );
      await _secureStorage.storeData(
        _lastLoginAttemptKey,
        DateTime.now().toIso8601String(),
      );
    } catch (e) {
      // If there's an error, just log it but don't prevent login
    }
  }

  // Reset login attempts counter
  Future<void> _resetLoginAttempts() async {
    try {
      await _secureStorage.storeData(_loginAttemptsKey, '0');
      await _secureStorage.deleteData(_lockoutUntilKey);
    } catch (e) {
      // If there's an error, just log it but don't prevent login
    }
  }

  // Get current user details (including Firestore data)
  Future<UserModel?> getCurrentUserDetails() async {
    try {
      final User? user = _auth.currentUser;

      if (user != null) {
        // Get user data from Firestore
        final docSnapshot =
            await _firestore.collection('users').doc(user.uid).get();

        if (docSnapshot.exists) {
          final data = docSnapshot.data() as Map<String, dynamic>;

          return UserModel(
            id: user.uid,
            email: data['email'] ?? user.email ?? '',
            displayName: data['full_name'] ?? user.displayName ?? '',
            photoURL: user.photoURL,
            phoneNumber: data['phone_number'] ?? '',
            anydesk_id: data['anydesk_id'],
            isOnboarded: data['is_onboarded'] ?? false,
            createdAt:
                data['created_at'] != null
                    ? (data['created_at'] as Timestamp).toDate()
                    : DateTime.now(),
            preferredLanguage: data['preferred_language'] ?? 'en',
          );
        }
      }

      return null;
    } catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Retry mechanism for Firestore operations
  Future<void> _retryFirestoreOperation(Future<void> Function() operation) async {
    for (int attempt = 0; attempt < _maxRetries; attempt++) {
      try {
        await operation();
        return; // Success, exit retry loop
      } catch (e) {
        final isLastAttempt = attempt == _maxRetries - 1;
        final isRetryableError = _isRetryableError(e);

        debugPrint('Firestore operation failed (attempt ${attempt + 1}/$_maxRetries): $e');

        if (isLastAttempt || !isRetryableError) {
          debugPrint('Max retries reached or non-retryable error');
          rethrow;
        }

        // Exponential backoff
        final delay = Duration(
          milliseconds: _baseDelay.inMilliseconds * (1 << attempt),
        );
        debugPrint('Retrying in ${delay.inMilliseconds}ms...');
        await Future.delayed(delay);
      }
    }
  }

  // Check if error is retryable
  bool _isRetryableError(dynamic error) {
    final errorString = error.toString().toLowerCase();

    // Don't retry permission errors - they won't resolve with retries
    if (errorString.contains('permission-denied') ||
        errorString.contains('permission denied')) {
      return false;
    }

    return errorString.contains('unavailable') ||
           errorString.contains('timeout') ||
           errorString.contains('network') ||
           errorString.contains('connection') ||
           errorString.contains('deadline-exceeded');
  }

  // Update onboarding status with retry logic
  Future<void> updateOnboardingStatus(bool isOnboarded) async {
    try {
      final User? user = _auth.currentUser;

      if (user != null) {
        // Update Firestore document with retry logic
        await _retryFirestoreOperation(() async {
          await _firestore.collection('users').doc(user.uid).update({
            'is_onboarded': isOnboarded,
            'updated_at': FieldValue.serverTimestamp(),
          });
        });
      }
    } catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Handle permission errors by ensuring user document exists
  Future<void> handlePermissionError() async {
    final User? user = _auth.currentUser;
    if (user == null) return;

    try {
      // Check if user document exists
      final docSnapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .get();
      
      if (!docSnapshot.exists) {
        debugPrint('Creating missing user document due to permission error');
        await _createUserDocument(
          uid: user.uid,
          email: user.email ?? '',
          fullName: user.displayName ?? 'User',
        );
      }
    } catch (e) {
      debugPrint('Error handling permission error: $e');
      // If we can't create the document, the user might need admin assistance
    }
  }

  // Create user document in Firestore
  Future<void> _createUserDocument({
    required String uid,
    required String email,
    required String fullName,
  }) async {
    // Get current Firebase Auth user to access photoURL
    final User? currentUser = _auth.currentUser;

    await _firestore.collection('users').doc(uid).set({
      'firebase_uid': uid,
      'email': email,
      'full_name': fullName,
      'display_name':
          fullName, // Add display_name field to match with the field used in the app
      'photo_url': currentUser?.photoURL,
      'phone_number': '',
      'anydesk_id': null,
      'is_onboarded': false,
      'email_verified': currentUser?.emailVerified ?? false,
      'created_at': FieldValue.serverTimestamp(),
      'updated_at': FieldValue.serverTimestamp(),
      'preferred_language': 'en',
      'security': {
        'account_locked': false,
        'failed_login_attempts': 0,
        'password_changed_at': FieldValue.serverTimestamp(),
        'last_login_at': FieldValue.serverTimestamp(),
      },
    });
  }

  // Sign out
  Future<void> signOut() async {
    try {
      // Clear secure storage
      await _secureStorage.deleteData(_loginAttemptsKey);
      await _secureStorage.deleteData(_lastLoginAttemptKey);
      await _secureStorage.deleteData(_lockoutUntilKey);
      await _secureStorage.deleteData('user_id');

      // Remove device token
      await _removeCurrentDeviceToken();

      // Sign out
      await _googleSignIn.signOut();
      await _auth.signOut();
    } catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Remove current device token on logout
  Future<void> _removeCurrentDeviceToken() async {
    try {
      // Skip token removal as per FCM optimization
      // FCM tokens are now stored in service requests, not in user documents
    } catch (e) {
      // Just log the error, don't prevent logout
    }
  }

  // Get device token
  Future<String?> _getDeviceToken() async {
    try {
      // This would be implemented using Firebase Messaging
      // For now, just return null
      return null;
    } catch (e) {
      return null;
    }
  }

  // Password reset
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Update user profile
  Future<void> updateProfile({String? displayName, String? photoURL}) async {
    try {
      final User? user = _auth.currentUser;

      if (user != null) {
        await user.updateDisplayName(displayName);
        await user.updatePhotoURL(photoURL);

        // Update Firestore document
        await _firestore.collection('users').doc(user.uid).update({
          if (displayName != null) 'full_name': displayName,
          if (displayName != null) 'display_name': displayName,
          'updated_at': FieldValue.serverTimestamp(),
        });
      }
    } catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Update phone number
  Future<void> updatePhoneNumber(String phoneNumber) async {
    try {
      final User? user = _auth.currentUser;

      if (user != null) {
        // Update Firestore document
        await _firestore.collection('users').doc(user.uid).update({
          'phone_number': phoneNumber,
          'updated_at': FieldValue.serverTimestamp(),
        });
      }
    } catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Update AnyDesk ID
  Future<void> updateanydesk_id(String? anydeskId) async {
    try {
      final User? user = _auth.currentUser;

      if (user != null) {
        // Update Firestore document
        await _firestore.collection('users').doc(user.uid).update({
          'anydesk_id': anydeskId,
          'updated_at': FieldValue.serverTimestamp(),
        });
      } else {
        throw Exception('User not authenticated');
      }
    } catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Update user profile details (combined)
  Future<void> updateUserDetails({
    String? displayName,
    String? phoneNumber,
    String? anydesk_id,
  }) async {
    try {
      final User? user = _auth.currentUser;

      if (user != null) {
        // Update displayName in Firebase Auth if provided
        if (displayName != null) {
          await user.updateDisplayName(displayName);
        }

        // Create map of fields to update
        final Map<String, dynamic> updateData = {
          'updated_at': FieldValue.serverTimestamp(),
        };

        if (displayName != null) {
          updateData['full_name'] = displayName;
        }

        if (phoneNumber != null) {
          updateData['phone_number'] = phoneNumber;
        }

        // Add anydesk_id to update data (can be null to remove it)
        updateData['anydesk_id'] = anydesk_id;

        // Sync photoURL from Firebase Auth to Firestore
        if (user.photoURL != null) {
          updateData['photo_url'] = user.photoURL;
        }

        // Update Firestore document
        await _firestore.collection('users').doc(user.uid).update(updateData);
      }
    } catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Send email verification
  Future<void> sendEmailVerification() async {
    try {
      final User? user = _auth.currentUser;
      if (user != null && !user.emailVerified) {
        await user.sendEmailVerification();
      }
    } catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Check if email is verified
  Future<bool> isEmailVerified() async {
    try {
      final User? user = _auth.currentUser;
      if (user != null) {
        // Reload user data to get the latest email verification status
        await user.reload();
        return user.emailVerified;
      }
      return false;
    } catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Update user's email verification status in Firestore
  Future<void> updateEmailVerificationStatus() async {
    try {
      final User? user = _auth.currentUser;
      if (user != null && user.emailVerified) {
        await _firestore.collection('users').doc(user.uid).update({
          'email_verified': true,
          'updated_at': FieldValue.serverTimestamp(),
        });
      }
    } catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Delete user account
  Future<void> deleteUserAccount() async {
    try {
      final User? user = _auth.currentUser;

      if (user != null) {
        // First delete the user document from Firestore
        await _firestore.collection('users').doc(user.uid).delete();

        // Then delete the Firebase Auth user
        await user.delete();

        // Clear secure storage
        await _secureStorage.deleteAllData();

        // Sign out to clear any cached credentials
        await signOut();
      } else {
        throw Exception('No user is currently signed in');
      }
    } catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Check if user document exists in Firestore
  Future<bool> userDocumentExists() async {
    try {
      final User? user = _auth.currentUser;

      if (user != null) {
        final docSnapshot =
            await _firestore.collection('users').doc(user.uid).get();
        return docSnapshot.exists;
      }
      return false;
    } catch (e) {
      throw _handleAuthException(e);
    }
  }

  // Handle Firebase Auth exceptions
  Exception _handleAuthException(dynamic e) {
    if (e is FirebaseAuthException) {
      switch (e.code) {
        case 'user-not-found':
          return Exception('No user found with this email.');
        case 'wrong-password':
          return Exception('Wrong password. Please try again.');
        case 'email-already-in-use':
          return Exception('This email is already in use.');
        case 'weak-password':
          return Exception(
            'Password is too weak. Use at least 8 characters with uppercase, lowercase, numbers, and special characters.',
          );
        case 'invalid-email':
          return Exception('Invalid email address.');
        case 'user-disabled':
          return Exception('This account has been disabled.');
        case 'operation-not-allowed':
          return Exception('This operation is not allowed.');
        case 'too-many-requests':
          return Exception('Too many requests. Please try again later.');
        case 'network-request-failed':
          return Exception('Network error. Check your connection.');
        case 'account-exists-with-different-credential':
          return Exception(
            'An account already exists with the same email address but different sign-in credentials.',
          );
        case 'invalid-credential':
          return Exception(
            'The credential provided is malformed or has expired.',
          );
        case 'developer-error':
          return Exception(
            'A SHA certificate configuration issue has occurred. Please verify your SHA fingerprints in Firebase Console.',
          );
        default:
          return Exception('Authentication error: ${e.message}');
      }
    } else if (e.toString().contains('ApiException: 10')) {
      return Exception(
        'Google Sign-In configuration error. Please verify your SHA fingerprints in Firebase Console.',
      );
    } else if (e.toString().contains('sign_in_failed')) {
      return Exception('Google Sign-In failed. Please try again.');
    } else {
      return Exception('Authentication error: ${e.toString()}');
    }
  }

  // Validate password requirements
  String? validatePassword(String password) {
    if (password.isEmpty) {
      return 'Password cannot be empty';
    }
    if (password.length < 8) {
      return 'Password must be at least 8 characters';
    }
    if (!password.contains(RegExp(r'[0-9]'))) {
      return 'Password must contain at least one number';
    }
    if (!password.contains(RegExp(r'[A-Z]'))) {
      return 'Password must contain at least one uppercase letter';
    }
    if (!password.contains(RegExp(r'[a-z]'))) {
      return 'Password must contain at least one lowercase letter';
    }
    if (!password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      return 'Password must contain at least one special character';
    }
    return null; // Password is valid
  }
}
