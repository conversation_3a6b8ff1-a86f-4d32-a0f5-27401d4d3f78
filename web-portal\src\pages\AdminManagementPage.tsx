import React, { useEffect, useState } from 'react';
import { useUserManagement } from '../hooks/useUserManagement';
import {
  type AdminUser,
  type CreateUserInput,
  type UpdateUserInput,
  type User,
  type UserQueryOptions,
  type UserRole,
  UserStatus,
} from '../types/user';
import { Permission } from '../types/permissions';
import permissionService from '../services/permissionService';
import { useAuth } from '../contexts/AuthContext';
import { Badge } from '../components/ui/badge';
import { format } from 'date-fns';
import CreateAdminForm from '../components/auth/CreateAdminForm.tsx';
import EditAdminForm from '../components/auth/EditAdminForm.tsx';

// Helper function to safely check if a property exists
const hasProperty = (obj: any, prop: string): boolean => {
  return obj && typeof obj === 'object' && prop in obj;
};

// Helper function to get user's display name from various possible fields
const getUserDisplayName = (user: User): string => {
  if (user.name) return user.name;
  if (hasProperty(user, 'display_name') && (user as any).display_name)
    return (user as any).display_name;
  if (hasProperty(user, 'displayName') && (user as any).displayName)
    return (user as any).displayName;
  if (hasProperty(user, 'full_name') && (user as any).full_name) return (user as any).full_name;
  if (hasProperty(user, 'fullName') && (user as any).fullName) return (user as any).fullName;
  if (hasProperty(user, 'firebase_displayName') && (user as any).firebase_displayName)
    return (user as any).firebase_displayName;
  if (user.email) return user.email.split('@')[0]; // Fallback to email username if no name found
  return 'Unknown User';
};

const AdminManagementPage: React.FC = () => {
  const { user: currentUser } = useAuth();
  const {
    users,
    loading,
    error,
    queryUsers,
    createUser,
    updateUser,
    suspendUser,
    activateUser,
    getUserStats,
    canManageUser,
    canPerformAction,
    clearError,
    deleteUser,
  } = useUserManagement();

  const [searchTerm, setSearchTerm] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [userStats, setUserStats] = useState<any>(null);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);
  const [formLoading, setFormLoading] = useState(false);

  // Check permissions
  const canViewUsers = permissionService.hasPermission(currentUser, Permission.VIEW_USERS);
  const canCreateUsers = permissionService.hasPermission(currentUser, Permission.CREATE_USER);
  const canEditUsers = permissionService.hasPermission(currentUser, Permission.UPDATE_USER);
  const canDeleteUsers = permissionService.hasPermission(currentUser, Permission.DELETE_USER);

  useEffect(() => {
    if (canViewUsers) {
      loadUsers();
      loadStats();
    }
  }, [canViewUsers]);

  const loadUsers = async () => {
    try {
      // Only load admin users
      const options: UserQueryOptions = { role: 'admin' as UserRole };
      await queryUsers(options);
    } catch (err) {
      console.error('Failed to load admin users:', err);
    }
  };

  const loadStats = async () => {
    try {
      const stats = await getUserStats();
      setUserStats(stats);
    } catch (err) {
      console.error('Failed to load stats:', err);
    }
  };

  const handleCreateUser = async (userData: CreateUserInput) => {
    try {
      setFormLoading(true);
      // Force the role to be admin
      const adminData: CreateUserInput = { ...userData, role: 'admin' as UserRole };
      await createUser(adminData, true);
      setShowCreateModal(false);
      await loadUsers();
    } catch (err) {
      console.error('Failed to create admin:', err);
    } finally {
      setFormLoading(false);
    }
  };

  const handleUpdateUser = async (userId: string, userData: UpdateUserInput) => {
    try {
      setFormLoading(true);
      await updateUser(userId, userData);
      setShowEditModal(false);
      setSelectedUser(null);
      await loadUsers();
    } catch (err) {
      console.error('Failed to update admin:', err);
    } finally {
      setFormLoading(false);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    try {
      setFormLoading(true);
      await deleteUser(userId);
      setShowDeleteConfirmation(false);
      setSelectedUser(null);
      await loadUsers();
    } catch (err) {
      console.error('Failed to delete admin:', err);
    } finally {
      setFormLoading(false);
    }
  };

  const handleSuspendUser = async (user: User) => {
    if (!canManageUser(user)) return;

    try {
      await suspendUser(user.id, 'Suspended by admin');
      await loadUsers();
    } catch (err) {
      console.error('Failed to suspend admin:', err);
    }
  };

  const handleActivateUser = async (user: User) => {
    if (!canManageUser(user)) return;

    try {
      await activateUser(user.id);
      await loadUsers();
    } catch (err) {
      console.error('Failed to activate admin:', err);
    }
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';

    try {
      // Handle Firestore timestamp
      const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
      return format(date, 'MMM dd, yyyy HH:mm');
    } catch (error) {
      return 'Invalid date';
    }
  };

  const filteredUsers = users.filter((user) => {
    const displayName = getUserDisplayName(user);
    const matchesSearch =
      searchTerm === '' ||
      (displayName && displayName.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (user.email && user.email.toLowerCase().includes(searchTerm.toLowerCase()));

    return matchesSearch;
  });

  if (!canViewUsers) {
    return (
      <div className='flex items-center justify-center h-64'>
        <div className='text-center'>
          <h2 className='text-xl font-semibold text-gray-900 mb-2'>Access Denied</h2>
          <p className='text-gray-600'>You don't have permission to view admin users.</p>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex justify-between items-center'>
        <div>
          <h1 className='text-2xl font-bold text-gray-900'>Admin Management</h1>
          <p className='text-gray-600'>Manage admin accounts and permissions</p>
        </div>
        {canCreateUsers && (
          <button
            onClick={() => setShowCreateModal(true)}
            className='bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors'
          >
            Create Admin
          </button>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className='bg-red-50 border border-red-200 rounded-lg p-4'>
          <div className='flex justify-between items-center'>
            <p className='text-red-800'>{error}</p>
            <button onClick={clearError} className='text-red-600 hover:text-red-800'>
              ×
            </button>
          </div>
        </div>
      )}

      {/* Stats Cards */}
      {userStats && (
        <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4'>
          <div className='bg-white p-4 rounded-lg shadow border'>
            <h3 className='text-sm font-medium text-gray-500'>Total Admins</h3>
            <p className='text-2xl font-bold'>{userStats.byRole.admins || 0}</p>
            <div className='mt-2 flex space-x-2'>
              <Badge variant='outline' className='bg-green-50'>
                {userStats.active} Active
              </Badge>
              <Badge variant='outline' className='bg-red-50'>
                {userStats.suspended} Suspended
              </Badge>
            </div>
          </div>
          <div className='bg-white p-4 rounded-lg shadow border'>
            <h3 className='text-sm font-medium text-gray-500'>Admin Levels</h3>
            <div className='mt-2 flex flex-col space-y-1'>
              <Badge variant='outline' className='bg-purple-50 w-fit'>
                Super: {users.filter((u) => (u as AdminUser).admin_level === 'super').length}
              </Badge>
              <Badge variant='outline' className='bg-blue-50 w-fit'>
                Standard: {users.filter((u) => (u as AdminUser).admin_level === 'standard').length}
              </Badge>
              <Badge variant='outline' className='bg-gray-50 w-fit'>
                Limited: {users.filter((u) => (u as AdminUser).admin_level === 'limited').length}
              </Badge>
            </div>
          </div>
          <div className='bg-white p-4 rounded-lg shadow border'>
            <h3 className='text-sm font-medium text-gray-500'>Recent Activity</h3>
            <p className='text-2xl font-bold'>{userStats.recentSignups || 0}</p>
            <div className='mt-2'>
              <Badge variant='outline' className='bg-blue-50'>
                Last 30 days
              </Badge>
            </div>
          </div>
        </div>
      )}

      {/* Search */}
      <div className='bg-white p-4 rounded-lg shadow border'>
        <div className='flex flex-col sm:flex-row gap-4'>
          <div className='flex-1'>
            <input
              type='text'
              placeholder='Search admins by name or email...'
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className='w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent'
            />
          </div>
        </div>
      </div>

      {/* Users Table */}
      <div className='bg-white rounded-lg shadow border overflow-hidden'>
        {loading ? (
          <div className='flex items-center justify-center h-32'>
            <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600'></div>
          </div>
        ) : (
          <div className='overflow-x-auto'>
            <table className='min-w-full divide-y divide-gray-200'>
              <thead className='bg-gray-50'>
                <tr>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Admin
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Status
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Admin Level
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Permissions
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Last Activity
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Created
                  </th>
                  <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className='bg-white divide-y divide-gray-200'>
                {filteredUsers.length === 0 ? (
                  <tr>
                    <td colSpan={7} className='px-6 py-4 text-center text-gray-500'>
                      No admin users found
                    </td>
                  </tr>
                ) : (
                  filteredUsers.map((user) => (
                    <tr key={user.id} className='hover:bg-gray-50'>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='flex items-center'>
                          <div className='flex-shrink-0 h-10 w-10'>
                            {user.photo_url ? (
                              <img
                                src={user.photo_url}
                                alt={getUserDisplayName(user)}
                                className='h-10 w-10 rounded-full object-cover'
                              />
                            ) : (
                              <div className='h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center'>
                                <span className='text-sm font-medium text-gray-700'>
                                  {getUserDisplayName(user).charAt(0).toUpperCase()}
                                </span>
                              </div>
                            )}
                          </div>
                          <div className='ml-4'>
                            <div className='text-sm font-medium text-gray-900'>
                              {getUserDisplayName(user)}
                            </div>
                            <div className='text-sm text-gray-500'>{user.email}</div>
                            {user.phone_number && (
                              <div className='text-xs text-gray-400'>{user.phone_number}</div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <span
                          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            user.status === UserStatus.ACTIVE
                              ? 'bg-green-100 text-green-800'
                              : user.status === UserStatus.SUSPENDED
                                ? 'bg-red-100 text-red-800'
                                : user.status === UserStatus.INACTIVE
                                  ? 'bg-gray-100 text-gray-800'
                                  : 'bg-yellow-100 text-yellow-800'
                          }`}
                        >
                          {user.status}
                        </span>
                        <div className='mt-1 text-xs text-gray-500'>
                          {user.is_verified ? 'Verified' : 'Not verified'}
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <span
                          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            (user as AdminUser).admin_level === 'super'
                              ? 'bg-purple-100 text-purple-800'
                              : (user as AdminUser).admin_level === 'standard'
                                ? 'bg-blue-100 text-blue-800'
                                : 'bg-gray-100 text-gray-800'
                          }`}
                        >
                          {(user as AdminUser).admin_level || 'standard'}
                        </span>
                      </td>
                      <td className='px-6 py-4'>
                        <div className='text-sm text-gray-900'>
                          {(user as AdminUser).permissions ? (
                            <div className='flex flex-wrap gap-1'>
                              {(user as AdminUser).permissions
                                .slice(0, 3)
                                .map((permission, idx) => (
                                  <Badge key={idx} variant='outline' className='bg-blue-50'>
                                    {permission.replace(/_/g, ' ')}
                                  </Badge>
                                ))}
                              {(user as AdminUser).permissions.length > 3 && (
                                <Badge variant='outline' className='bg-gray-50'>
                                  +{(user as AdminUser).permissions.length - 3} more
                                </Badge>
                              )}
                            </div>
                          ) : (
                            <span className='text-gray-500'>No specific permissions</span>
                          )}
                        </div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                        {formatDate(
                          user.last_login ||
                            (hasProperty(user, 'last_activity')
                              ? (user as any).last_activity
                              : null) ||
                            (hasProperty(user, 'lastLoginAt') ? (user as any).lastLoginAt : null) ||
                            (hasProperty(user, 'security') && (user as any).security?.last_login_at
                              ? (user as any).security.last_login_at
                              : null),
                        )}
                        {hasProperty(user, 'device_type') && (
                          <div className='mt-1 text-xs text-gray-400'>
                            via {(user as any).device_type}
                          </div>
                        )}
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap text-sm text-gray-500'>
                        {formatDate(user.created_at)}
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap text-sm font-medium'>
                        <div className='flex space-x-2'>
                          {canManageUser(user) && user.status === UserStatus.ACTIVE && (
                            <button
                              onClick={() => handleSuspendUser(user)}
                              className='text-red-600 hover:text-red-900'
                            >
                              Suspend
                            </button>
                          )}
                          {canManageUser(user) && user.status === UserStatus.SUSPENDED && (
                            <button
                              onClick={() => handleActivateUser(user)}
                              className='text-green-600 hover:text-green-900'
                            >
                              Activate
                            </button>
                          )}
                          {canEditUsers && (
                            <button
                              onClick={() => {
                                setSelectedUser(user);
                                setShowEditModal(true);
                              }}
                              className='text-blue-600 hover:text-blue-900'
                            >
                              Edit
                            </button>
                          )}
                          {canDeleteUsers && canManageUser(user) && (
                            <button
                              onClick={() => {
                                setSelectedUser(user);
                                setShowDeleteConfirmation(true);
                              }}
                              className='text-red-600 hover:text-red-900'
                            >
                              Delete
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Create Admin Modal */}
      {showCreateModal && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
          <div className='bg-white p-6 rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto'>
            <div className='flex justify-between items-center mb-4'>
              <h2 className='text-xl font-bold'>Create New Admin</h2>
              <button
                onClick={() => setShowCreateModal(false)}
                className='text-gray-500 hover:text-gray-700'
              >
                ×
              </button>
            </div>
            <CreateAdminForm
              onSubmit={handleCreateUser}
              onCancel={() => setShowCreateModal(false)}
              loading={formLoading}
            />
          </div>
        </div>
      )}

      {/* Edit Admin Modal */}
      {showEditModal && selectedUser && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
          <div className='bg-white p-6 rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto'>
            <div className='flex justify-between items-center mb-4'>
              <h2 className='text-xl font-bold'>Edit Admin: {getUserDisplayName(selectedUser)}</h2>
              <button
                onClick={() => {
                  setShowEditModal(false);
                  setSelectedUser(null);
                }}
                className='text-gray-500 hover:text-gray-700'
              >
                ×
              </button>
            </div>
            <EditAdminForm
              user={selectedUser as AdminUser}
              onSubmit={handleUpdateUser}
              onCancel={() => {
                setShowEditModal(false);
                setSelectedUser(null);
              }}
              loading={formLoading}
            />
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteConfirmation && selectedUser && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50'>
          <div className='bg-white p-6 rounded-lg max-w-md w-full mx-4'>
            <h2 className='text-xl font-bold mb-4 text-red-600'>Delete Admin</h2>
            <p className='text-gray-800 mb-4'>
              Are you sure you want to delete the admin user "{getUserDisplayName(selectedUser)}"?
              This action cannot be undone.
            </p>
            <div className='flex justify-end space-x-2'>
              <button
                onClick={() => {
                  setShowDeleteConfirmation(false);
                  setSelectedUser(null);
                }}
                className='px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50'
                disabled={formLoading}
              >
                Cancel
              </button>
              <button
                onClick={() => handleDeleteUser(selectedUser.id)}
                className='px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700'
                disabled={formLoading}
              >
                {formLoading ? 'Deleting...' : 'Delete'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminManagementPage;
