import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../utils/spacing.dart';
import '../utils/typography.dart';

/// Visual hierarchy utility for consistent visual emphasis and organization
/// Provides methods to create clear visual hierarchy using color, size, and spacing
class VisualHierarchy {
  // Private constructor to prevent instantiation
  VisualHierarchy._();

  /// Creates a primary heading with maximum visual emphasis
  static Widget primaryHeading(
    BuildContext context, {
    required String text,
    Color? color,
    TextAlign? textAlign,
    EdgeInsetsGeometry? margin,
  }) {
    return Container(
      margin: margin ?? Spacing.verticalPaddingL,
      child: Text(
        text,
        style: Typography.displayMedium(context, color: color),
        textAlign: textAlign,
      ),
    );
  }

  /// Creates a secondary heading with high visual emphasis
  static Widget secondaryHeading(
    BuildContext context, {
    required String text,
    Color? color,
    TextAlign? textAlign,
    EdgeInsetsGeometry? margin,
  }) {
    return Container(
      margin: margin ?? Spacing.verticalPaddingM,
      child: Text(
        text,
        style: Typography.headlineLarge(context, color: color),
        textAlign: textAlign,
      ),
    );
  }

  /// Creates a section heading with medium visual emphasis
  static Widget sectionHeading(
    BuildContext context, {
    required String text,
    Color? color,
    TextAlign? textAlign,
    EdgeInsetsGeometry? margin,
    Widget? action,
  }) {
    final heading = Text(
      text,
      style: Typography.titleLarge(context, color: color),
      textAlign: textAlign,
    );

    if (action != null) {
      return Container(
        margin: margin ?? Spacing.verticalPaddingM,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(child: heading),
            action,
          ],
        ),
      );
    }

    return Container(
      margin: margin ?? Spacing.verticalPaddingM,
      child: heading,
    );
  }

  /// Creates a subsection heading with lower visual emphasis
  static Widget subsectionHeading(
    BuildContext context, {
    required String text,
    Color? color,
    TextAlign? textAlign,
    EdgeInsetsGeometry? margin,
  }) {
    return Container(
      margin: margin ?? Spacing.verticalPaddingS,
      child: Text(
        text,
        style: Typography.titleMedium(context, color: color),
        textAlign: textAlign,
      ),
    );
  }

  /// Creates emphasized body text
  static Widget emphasizedBody(
    BuildContext context, {
    required String text,
    Color? color,
    TextAlign? textAlign,
    EdgeInsetsGeometry? margin,
    int? maxLines,
  }) {
    return Container(
      margin: margin,
      child: Text(
        text,
        style: Typography.bodyLarge(context, color: color).emphasized,
        textAlign: textAlign,
        maxLines: maxLines,
        overflow: maxLines != null ? TextOverflow.ellipsis : null,
      ),
    );
  }

  /// Creates regular body text
  static Widget body(
    BuildContext context, {
    required String text,
    Color? color,
    TextAlign? textAlign,
    EdgeInsetsGeometry? margin,
    int? maxLines,
  }) {
    return Container(
      margin: margin,
      child: Text(
        text,
        style: Typography.bodyMedium(context, color: color),
        textAlign: textAlign,
        maxLines: maxLines,
        overflow: maxLines != null ? TextOverflow.ellipsis : null,
      ),
    );
  }

  /// Creates subtle body text with reduced emphasis
  static Widget subtleBody(
    BuildContext context, {
    required String text,
    Color? color,
    TextAlign? textAlign,
    EdgeInsetsGeometry? margin,
    int? maxLines,
  }) {
    return Container(
      margin: margin,
      child: Text(
        text,
        style: Typography.bodyMedium(context, color: color).subtle(context),
        textAlign: textAlign,
        maxLines: maxLines,
        overflow: maxLines != null ? TextOverflow.ellipsis : null,
      ),
    );
  }

  /// Creates caption text with minimal emphasis
  static Widget caption(
    BuildContext context, {
    required String text,
    Color? color,
    TextAlign? textAlign,
    EdgeInsetsGeometry? margin,
    int? maxLines,
  }) {
    return Container(
      margin: margin,
      child: Text(
        text,
        style: Typography.caption(context, color: color),
        textAlign: textAlign,
        maxLines: maxLines,
        overflow: maxLines != null ? TextOverflow.ellipsis : null,
      ),
    );
  }

  /// Creates a visual separator with optional label
  static Widget separator(
    BuildContext context, {
    String? label,
    EdgeInsetsGeometry? margin,
    Color? color,
  }) {
    final theme = Theme.of(context);
    final dividerColor = color ?? theme.colorScheme.outline.withOpacity(0.2);

    if (label != null) {
      return Container(
        margin: margin ?? Spacing.verticalPaddingM,
        child: Row(
          children: [
            Expanded(
              child: Divider(
                color: dividerColor,
                thickness: 1,
              ),
            ),
            Padding(
              padding: Spacing.horizontalPaddingM,
              child: Text(
                label,
                style: Typography.labelSmall(context).subtle(context),
              ),
            ),
            Expanded(
              child: Divider(
                color: dividerColor,
                thickness: 1,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      margin: margin ?? Spacing.verticalPaddingM,
      child: Divider(
        color: dividerColor,
        thickness: 1,
      ),
    );
  }

  /// Creates a content section with proper spacing and hierarchy
  static Widget section(
    BuildContext context, {
    String? title,
    Widget? titleAction,
    required List<Widget> children,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    Color? backgroundColor,
    BorderRadius? borderRadius,
  }) {
    final content = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null)
          sectionHeading(
            context,
            text: title,
            action: titleAction,
            margin: EdgeInsets.zero,
          ),
        if (title != null) Spacing.verticalM,
        ...children.map((child) => Padding(
          padding: EdgeInsets.only(bottom: Spacing.s),
          child: child,
        )),
      ],
    );

    if (backgroundColor != null) {
      return Container(
        margin: margin ?? Spacing.verticalPaddingM,
        padding: padding ?? Spacing.cardPadding,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: borderRadius ?? AppTheme.mediumRadius,
        ),
        child: content,
      );
    }

    return Container(
      margin: margin ?? Spacing.verticalPaddingM,
      child: content,
    );
  }

  /// Creates a highlighted content block
  static Widget highlight(
    BuildContext context, {
    required Widget child,
    Color? backgroundColor,
    Color? borderColor,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    BorderRadius? borderRadius,
  }) {
    final theme = Theme.of(context);
    final effectiveBackgroundColor = backgroundColor ?? 
        theme.colorScheme.primaryContainer.withOpacity(0.1);
    final effectiveBorderColor = borderColor ?? 
        theme.colorScheme.primary.withOpacity(0.3);

    return Container(
      margin: margin ?? Spacing.verticalPaddingS,
      padding: padding ?? Spacing.cardPadding,
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: borderRadius ?? AppTheme.smallRadius,
        border: Border.all(
          color: effectiveBorderColor,
          width: 1,
        ),
      ),
      child: child,
    );
  }

  /// Creates an emphasis container with visual weight
  static Widget emphasis(
    BuildContext context, {
    required Widget child,
    EmphasisLevel level = EmphasisLevel.medium,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
  }) {
    final theme = Theme.of(context);
    
    Color backgroundColor;
    Color? borderColor;
    double borderWidth = 0;

    switch (level) {
      case EmphasisLevel.low:
        backgroundColor = theme.colorScheme.surfaceContainer.withOpacity(0.5);
        break;
      case EmphasisLevel.medium:
        backgroundColor = theme.colorScheme.surfaceContainer;
        borderColor = theme.colorScheme.outline.withOpacity(0.2);
        borderWidth = 1;
        break;
      case EmphasisLevel.high:
        backgroundColor = theme.colorScheme.primaryContainer.withOpacity(0.2);
        borderColor = theme.colorScheme.primary.withOpacity(0.3);
        borderWidth = 2;
        break;
    }

    return Container(
      margin: margin ?? Spacing.verticalPaddingS,
      padding: padding ?? Spacing.cardPadding,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: AppTheme.mediumRadius,
        border: borderColor != null 
          ? Border.all(color: borderColor, width: borderWidth)
          : null,
      ),
      child: child,
    );
  }

  /// Creates a status indicator with color coding
  static Widget statusIndicator(
    BuildContext context, {
    required String text,
    required StatusType status,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
  }) {
    final theme = Theme.of(context);
    final colors = _getStatusColors(theme, status);

    return Container(
      margin: margin,
      padding: padding ?? EdgeInsets.symmetric(
        horizontal: Spacing.s,
        vertical: Spacing.xs,
      ),
      decoration: BoxDecoration(
        color: colors.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colors.borderColor,
          width: 1,
        ),
      ),
      child: Text(
        text,
        style: Typography.labelSmall(context).copyWith(
          color: colors.textColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  // Helper method to get status colors
  static _StatusColors _getStatusColors(ThemeData theme, StatusType status) {
    switch (status) {
      case StatusType.success:
        return _StatusColors(
          backgroundColor: Colors.green.withOpacity(0.1),
          borderColor: Colors.green.withOpacity(0.3),
          textColor: Colors.green.shade700,
        );
      case StatusType.warning:
        return _StatusColors(
          backgroundColor: Colors.orange.withOpacity(0.1),
          borderColor: Colors.orange.withOpacity(0.3),
          textColor: Colors.orange.shade700,
        );
      case StatusType.error:
        return _StatusColors(
          backgroundColor: theme.colorScheme.errorContainer.withOpacity(0.1),
          borderColor: theme.colorScheme.error.withOpacity(0.3),
          textColor: theme.colorScheme.error,
        );
      case StatusType.info:
        return _StatusColors(
          backgroundColor: Colors.blue.withOpacity(0.1),
          borderColor: Colors.blue.withOpacity(0.3),
          textColor: Colors.blue.shade700,
        );
      case StatusType.neutral:
        return _StatusColors(
          backgroundColor: theme.colorScheme.surfaceContainer,
          borderColor: theme.colorScheme.outline.withOpacity(0.3),
          textColor: theme.colorScheme.onSurface,
        );
    }
  }
}

/// Emphasis levels for visual hierarchy
enum EmphasisLevel {
  low,
  medium,
  high,
}

/// Status types for status indicators
enum StatusType {
  success,
  warning,
  error,
  info,
  neutral,
}

/// Helper class for status colors
class _StatusColors {
  final Color backgroundColor;
  final Color borderColor;
  final Color textColor;

  _StatusColors({
    required this.backgroundColor,
    required this.borderColor,
    required this.textColor,
  });
}
