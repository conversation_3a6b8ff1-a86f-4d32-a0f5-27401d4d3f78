const functions = require("firebase-functions");
const admin = require("firebase-admin");
const crypto = require("crypto");

const db = admin.firestore();

/**
 * Paymob Transaction Processed Callback (Server-to-Server)
 * This endpoint receives payment notifications from Paymob after payment processing
 * URL: https://your-project.cloudfunctions.net/paymobTransactionProcessed
 */
exports.paymobTransactionProcessed = functions.https.onRequest(async (req, res) => {
  try {
    console.log('🔔 Paymob Transaction Processed Callback received');
    console.log('Method:', req.method);
    console.log('Headers:', JSON.stringify(req.headers, null, 2));
    console.log('Body:', JSON.stringify(req.body, null, 2));

    // Only accept POST requests
    if (req.method !== 'POST') {
      console.log('❌ Invalid method:', req.method);
      return res.status(405).json({ error: 'Method not allowed' });
    }

    const callbackData = req.body;

    // Validate required fields
    if (!callbackData || !callbackData.obj) {
      console.log('❌ Invalid callback data structure');
      return res.status(400).json({ error: 'Invalid callback data' });
    }

    const transactionData = callbackData.obj;
    const {
      id: paymobTransactionId,
      success,
      amount_cents,
      currency,
      order,
      created_at,
      txn_response_code,
      data_message,
      integration_id,
      profile_id,
      source_data_type,
      source_data_sub_type,
      source_data_pan,
      merchant_commission,
      is_3d_secure,
      is_settled,
      is_refunded,
      error_occured,
      is_live,
      refunded_amount_cents,
      paid_amount_cents,
      extra,
      merchant_order_id,
      owner
    } = transactionData;

    console.log('📊 Transaction Details:');
    console.log('  ID:', paymobTransactionId);
    console.log('  Success:', success);
    console.log('  Amount:', amount_cents / 100, currency);
    console.log('  Order ID:', order?.id);
    console.log('  Merchant Order ID:', merchant_order_id);
    console.log('  Integration ID:', integration_id);
    console.log('  Owner:', owner);
    console.log('  Error Occurred:', error_occured);
    console.log('  Is Live:', is_live);

    // Extract request ID from order data or extras
    let requestId = null;
    if (order && order.merchant_order_id) {
      // Try to extract from merchant_order_id if it contains request ID
      const merchantOrderId = order.merchant_order_id.toString();
      if (merchantOrderId.startsWith('mr_tech_')) {
        const parts = merchantOrderId.split('_');
        if (parts.length >= 3) {
          requestId = parts[2]; // mr_tech_{requestId}_{timestamp}
        }
      }
    }

    // Also check extras for request_id
    if (!requestId && extra && extra.request_id) {
      requestId = extra.request_id;
    }

    console.log('🔍 Extracted Request ID:', requestId);

    // Create payment transaction record
    const paymentTransaction = {
      id: `paymob_${paymobTransactionId}`,
      paymobTransactionId: paymobTransactionId.toString(),
      requestId: requestId,
      amount: amount_cents / 100,
      currency: currency || 'EGP',
      status: success ? 'completed' : (error_occured ? 'failed' : 'pending'),
      paymentMethod: 'paymob',
      merchantOrderId: merchant_order_id,
      integrationId: integration_id,
      profileId: profile_id,
      owner: owner,
      isLive: is_live,
      errorOccurred: error_occured,
      txnResponseCode: txn_response_code,
      dataMessage: data_message,
      sourceDataType: source_data_type,
      sourceDataSubType: source_data_sub_type,
      sourceDataPan: source_data_pan,
      is3dSecure: is_3d_secure,
      isSettled: is_settled,
      isRefunded: is_refunded,
      refundedAmountCents: refunded_amount_cents,
      paidAmountCents: paid_amount_cents,
      merchantCommission: merchant_commission,
      createdAt: created_at ? admin.firestore.Timestamp.fromDate(new Date(created_at)) : admin.firestore.FieldValue.serverTimestamp(),
      processedAt: admin.firestore.FieldValue.serverTimestamp(),
      callbackData: callbackData, // Store full callback for debugging
    };

    // Save to Firestore
    await db.collection('payment_transactions').doc(paymentTransaction.id).set(paymentTransaction);
    console.log('💾 Payment transaction saved:', paymentTransaction.id);

    // If we have a request ID and payment was successful, update the request status
    if (requestId && success) {
      try {
        const requestRef = db.collection('requests').doc(requestId);
        const requestDoc = await requestRef.get();
        
        if (requestDoc.exists) {
          await requestRef.update({
            paymentStatus: 'completed',
            paymobTransactionId: paymobTransactionId.toString(),
            paymentCompletedAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          console.log('✅ Request payment status updated:', requestId);
        } else {
          console.log('⚠️ Request not found:', requestId);
        }
      } catch (error) {
        console.error('❌ Error updating request:', error);
      }
    }

    // Send success response to Paymob
    res.status(200).json({
      success: true,
      message: 'Payment callback processed successfully',
      transactionId: paymentTransaction.id
    });

  } catch (error) {
    console.error('❌ Error processing payment callback:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error.message
    });
  }
});

/**
 * Paymob Transaction Response Callback (Customer Redirect)
 * This endpoint handles customer redirects after payment completion
 * URL: https://your-project.cloudfunctions.net/paymobTransactionResponse
 */
exports.paymobTransactionResponse = functions.https.onRequest(async (req, res) => {
  try {
    console.log('🔄 Paymob Transaction Response Callback received');
    console.log('Method:', req.method);
    console.log('Query params:', JSON.stringify(req.query, null, 2));
    console.log('Body:', JSON.stringify(req.body, null, 2));

    // Extract transaction details from query parameters or body
    const params = req.method === 'GET' ? req.query : req.body;

    const {
      success,
      error_occured,
      txn_response_code,
      'data.message': dataMessage,
      order,
      id: transactionId,
      integration_id,
      amount_cents,
      currency,
      merchant_order_id,
      hmac
    } = params;

    console.log('📋 Response Details:');
    console.log('  Success:', success);
    console.log('  Error Occurred:', error_occured);
    console.log('  Transaction ID:', transactionId);
    console.log('  Response Code:', txn_response_code);
    console.log('  Message:', dataMessage);
    console.log('  Amount:', amount_cents);
    console.log('  Merchant Order ID:', merchant_order_id);

    // Determine payment status
    let paymentStatus = 'pending';
    let statusMessage = 'Payment is being processed';

    if (success === 'true' || success === true) {
      paymentStatus = 'completed';
      statusMessage = 'Payment completed successfully!';
    } else if (error_occured === 'true' || error_occured === true) {
      paymentStatus = 'failed';
      statusMessage = dataMessage || 'Payment failed. Please try again.';
    }

    // Create a simple HTML response page for customer
    const htmlResponse = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Payment ${paymentStatus === 'completed' ? 'Successful' : paymentStatus === 'failed' ? 'Failed' : 'Processing'}</title>
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                margin: 0;
                padding: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .container {
                background: white;
                padding: 40px;
                border-radius: 12px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                text-align: center;
                max-width: 400px;
                width: 100%;
            }
            .icon {
                font-size: 64px;
                margin-bottom: 20px;
            }
            .success { color: #4CAF50; }
            .failed { color: #f44336; }
            .pending { color: #ff9800; }
            h1 {
                color: #333;
                margin-bottom: 10px;
                font-size: 24px;
            }
            p {
                color: #666;
                line-height: 1.6;
                margin-bottom: 30px;
            }
            .details {
                background: #f5f5f5;
                padding: 15px;
                border-radius: 8px;
                margin: 20px 0;
                text-align: left;
                font-size: 14px;
            }
            .button {
                background: #667eea;
                color: white;
                padding: 12px 24px;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                text-decoration: none;
                display: inline-block;
                font-size: 16px;
                transition: background 0.3s;
            }
            .button:hover {
                background: #5a6fd8;
            }
            .footer {
                margin-top: 30px;
                font-size: 12px;
                color: #999;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="icon ${paymentStatus}">
                ${paymentStatus === 'completed' ? '✅' : paymentStatus === 'failed' ? '❌' : '⏳'}
            </div>
            <h1>Payment ${paymentStatus === 'completed' ? 'Successful' : paymentStatus === 'failed' ? 'Failed' : 'Processing'}</h1>
            <p>${statusMessage}</p>

            ${transactionId ? `
            <div class="details">
                <strong>Transaction Details:</strong><br>
                Transaction ID: ${transactionId}<br>
                ${amount_cents ? `Amount: ${(amount_cents / 100).toFixed(2)} ${currency || 'EGP'}<br>` : ''}
                Status: ${paymentStatus.toUpperCase()}<br>
                ${txn_response_code ? `Response Code: ${txn_response_code}<br>` : ''}
            </div>
            ` : ''}

            <a href="#" onclick="closeWindow()" class="button">
                ${paymentStatus === 'completed' ? 'Continue' : 'Try Again'}
            </a>

            <div class="footer">
                Mr.Tech - Technical Support Services<br>
                Thank you for choosing our services!
            </div>
        </div>

        <script>
            function closeWindow() {
                // Try to close the window/tab
                if (window.opener) {
                    window.close();
                } else {
                    // If can't close, redirect to a success page or app
                    window.location.href = 'about:blank';
                }
            }

            // Auto-close after 3 seconds for mobile WebView optimization
            ${paymentStatus === 'completed' ? `
            setTimeout(() => {
                closeWindow();
            }, 3000);
            ` : ''}

            // Also try to close for failed payments after 5 seconds
            ${paymentStatus === 'failed' ? `
            setTimeout(() => {
                closeWindow();
            }, 5000);
            ` : ''}
        </script>
    </body>
    </html>
    `;

    // Create or update payment transaction record
    if (transactionId) {
      try {
        const paymentTransactionId = `paymob_${transactionId}`;

        // Check if transaction already exists (from server-to-server callback)
        const existingTransactionRef = db.collection('payment_transactions').doc(paymentTransactionId);
        const existingTransaction = await existingTransactionRef.get();

        if (existingTransaction.exists) {
          // Update existing transaction with response data
          await existingTransactionRef.update({
            responseReceived: true,
            responseReceivedAt: admin.firestore.FieldValue.serverTimestamp(),
            finalStatus: paymentStatus,
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
          });
          console.log('✅ Updated existing payment transaction:', paymentTransactionId);
        } else {
          // Create new transaction record (in case server-to-server callback failed)
          const paymentTransaction = {
            id: paymentTransactionId,
            paymobTransactionId: transactionId.toString(),
            requestId: merchant_order_id?.startsWith('mr_tech_') ? merchant_order_id : undefined,
            amount: amount_cents ? parseFloat(amount_cents) / 100 : 0,
            currency: currency || 'EGP',
            status: paymentStatus,
            paymentMethod: 'paymob',
            merchantOrderId: merchant_order_id,
            integrationId: integration_id,
            isLive: true, // Assume live for response callbacks
            errorOccurred: error_occured === 'true',
            txnResponseCode: txn_response_code,
            dataMessage: dataMessage,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            processedAt: admin.firestore.FieldValue.serverTimestamp(),
            responseReceived: true,
            responseReceivedAt: admin.firestore.FieldValue.serverTimestamp(),
            callbackData: { responseParams: params }, // Store response params
          };

          await existingTransactionRef.set(paymentTransaction);
          console.log('💾 Created new payment transaction from response:', paymentTransactionId);
        }

        // Also log the response for debugging (keep existing functionality)
        await db.collection('payment_responses').add({
          transactionId: transactionId,
          success: success,
          errorOccurred: error_occured,
          txnResponseCode: txn_response_code,
          dataMessage: dataMessage,
          paymentStatus: paymentStatus,
          merchantOrderId: merchant_order_id,
          amountCents: amount_cents,
          currency: currency,
          integrationId: integration_id,
          responseParams: params,
          createdAt: admin.firestore.FieldValue.serverTimestamp(),
        });
        console.log('💾 Payment response logged for debugging');

      } catch (error) {
        console.error('❌ Error processing payment response:', error);
      }
    }

    // Send HTML response
    res.set('Content-Type', 'text/html');
    res.status(200).send(htmlResponse);

  } catch (error) {
    console.error('❌ Error processing payment response:', error);

    // Send error page
    const errorHtml = `
    <!DOCTYPE html>
    <html>
    <head>
        <title>Payment Error</title>
        <style>
            body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
            .error { color: #f44336; font-size: 18px; }
        </style>
    </head>
    <body>
        <h1>Payment Processing Error</h1>
        <p class="error">An error occurred while processing your payment response.</p>
        <p>Please contact support if you believe this is an error.</p>
    </body>
    </html>
    `;

    res.set('Content-Type', 'text/html');
    res.status(500).send(errorHtml);
  }
});
