# Field Naming Unification Guide - Mr. Tech Mobile App

## Overview

This guide provides a comprehensive analysis of the field naming inconsistencies between snake_case and camelCase in the Mr. Tech mobile application, and proposes a unified approach to resolve these inconsistencies.

## Current Field Naming Analysis

### 1. Core Identity Fields

| Field Purpose | snake_case (Firestore) | camelCase (Models/Queries) | Usage Pattern |
|---------------|------------------------|----------------------------|---------------|
| Customer ID | `customer_id` | `customerId` | **DUAL** - Both used extensively |
| Technician ID | `technician_id` | `technicianId` | **DUAL** - Both used extensively |
| Service ID | `service_id` | `serviceId` | **DUAL** - Both used extensively |
| Request ID | `request_id` | `requestId` | **DUAL** - Both used extensively |

**Analysis**: Identity fields show the most inconsistency with both formats actively used.

### 2. Timestamp Fields

| Field Purpose | snake_case (Firestore) | camelCase (Models/Queries) | Usage Pattern |
|---------------|------------------------|----------------------------|---------------|
| Created At | `created_at` | `createdAt` | **DUAL** - Both used extensively |
| Updated At | `updated_at` | `updatedAt` | **DUAL** - Both used extensively |

**Analysis**: Timestamp fields are consistently duplicated in both formats.

### 3. User Profile Fields

| Field Purpose | snake_case (Firestore) | camelCase (Models/Queries) | Usage Pattern |
|---------------|------------------------|----------------------------|---------------|
| Display Name | `display_name` | `displayName` | **DUAL** - Plus `full_name` variant |
| Phone Number | `phone_number` | `phoneNumber` | **DUAL** - Both used extensively |
| Photo URL | `photo_url` | `photoUrl` / `photoURL` | **TRIPLE** - Three variants! |

**Analysis**: User profile fields have the most variants, especially photo URL with three different formats.

### 4. Chat System Fields

| Field Purpose | snake_case (Firestore) | camelCase (Models/Queries) | Usage Pattern |
|---------------|------------------------|----------------------------|---------------|
| Chat Active | `chat_active` | `chatActive` | **DUAL** - Both used extensively |
| Sender Type | `sender_type` | `senderType` | **DUAL** - Both used extensively |
| Message Type | `message_type` | `messageType` | **DUAL** - Both used extensively |
| Sender ID | `sender_id` | `senderId` | **DUAL** - Both used extensively |
| File URL | `file_url` | `fileUrl` | **DUAL** - Both used extensively |

**Analysis**: Chat system consistently maintains dual naming throughout.

### 5. Status and State Fields

| Field Purpose | snake_case (Firestore) | camelCase (Models/Queries) | Usage Pattern |
|---------------|------------------------|----------------------------|---------------|
| Is Paid | `is_paid` | `isPaid` | **DUAL** - Both used extensively |
| Session Active | `session_active` | `sessionActive` | **DUAL** - Both used extensively |
| Is Complete | `is_complete` | `isComplete` | **DUAL** - Both used extensively |
| Is Available | `is_available` | `isAvailable` | **DUAL** - Both used extensively |

**Analysis**: Boolean fields consistently use dual naming.

### 6. Service-Specific Fields

| Field Purpose | snake_case (Firestore) | camelCase (Models/Queries) | Usage Pattern |
|---------------|------------------------|----------------------------|---------------|
| Service Name | `service_name` | `serviceName` | **DUAL** - Both used extensively |
| Customer Issue | `customer_issue` | `customerIssue` | **DUAL** - Both used extensively |
| Base Price | `base_price` | `basePrice` | **DUAL** - Both used extensively |
| Image URL | `image_url` | `imageUrl` | **DUAL** - Both used extensively |

## Usage Patterns by Component

### Models (Dart Classes)
- **Primary**: camelCase for property names
- **Firestore Methods**: Use snake_case for Firestore operations
- **Compatibility**: Support both formats in `fromMap()` constructors

### Database Queries
- **Firestore Queries**: Exclusively snake_case (e.g., `.where('customer_id', isEqualTo: userId)`)
- **Firestore Updates**: Write both formats for compatibility
- **Firestore Reads**: Read both formats with fallbacks

### Security Rules
- **Primary**: snake_case (e.g., `resource.data.customer_id`)
- **Compatibility**: Also check camelCase variants where needed

### Indexes
- **Exclusive**: snake_case only (e.g., `customer_id + created_at`)
- **No camelCase**: Firestore indexes only use snake_case fields

## Current Compatibility Strategy

The app currently implements a **dual-write, dual-read** strategy:

```dart
// Example from RequestModel.toFirestore()
Map<String, dynamic> toFirestore() {
  return {
    'customer_id': customerId,           // Primary (snake_case)
    'customerId': customerId,            // Compatibility (camelCase)
    'chat_active': chatActive,           // Primary (snake_case)
    'chatActive': chatActive,            // Compatibility (camelCase)
    'created_at': createdAt,             // Primary (snake_case)
    'updated_at': updatedAt ?? FieldValue.serverTimestamp(),
  };
}

// Example from RequestModel.fromMap()
factory RequestModel.fromMap(String id, Map<String, dynamic> data) {
  return RequestModel(
    customerId: data['customer_id'] ?? data['customerId'] ?? '',
    chatActive: data['chatActive'] == true || data['chat_active'] == true,
    createdAt: _parseTimestamp(data['created_at'] ?? data['createdAt']),
  );
}
```

## Problems with Current Approach

### 1. Storage Inefficiency
- **Duplicate Data**: Every document stores the same data twice
- **Increased Document Size**: ~30-50% larger documents
- **Higher Costs**: More storage and bandwidth usage

### 2. Maintenance Complexity
- **Synchronization Risk**: Risk of snake_case and camelCase values getting out of sync
- **Code Duplication**: Every write operation must handle both formats
- **Testing Complexity**: Must test both field formats

### 3. Query Performance
- **Index Duplication**: Need separate indexes for both formats in some cases
- **Query Confusion**: Developers must remember which format to use for queries

### 4. Developer Experience
- **Cognitive Load**: Developers must remember both formats
- **Error Prone**: Easy to forget one format during updates
- **Inconsistent APIs**: Some operations use snake_case, others camelCase

## Recommended Unification Strategy

### Phase 1: Standardize on snake_case (Recommended)

**Rationale**: 
- Firestore queries already use snake_case exclusively
- Security rules primarily use snake_case
- Indexes are snake_case only
- Aligns with Firestore best practices

**Implementation Plan**:

#### Step 1: Update Models (Low Risk)
```dart
// Before: Dual field support
factory RequestModel.fromMap(String id, Map<String, dynamic> data) {
  return RequestModel(
    customerId: data['customer_id'] ?? data['customerId'] ?? '',
  );
}

// After: Primary snake_case with camelCase fallback
factory RequestModel.fromMap(String id, Map<String, dynamic> data) {
  return RequestModel(
    customerId: data['customer_id'] ?? data['customerId'] ?? '', // Keep fallback for migration
  );
}
```

#### Step 2: Update Write Operations (Medium Risk)
```dart
// Before: Dual write
Map<String, dynamic> toFirestore() {
  return {
    'customer_id': customerId,
    'customerId': customerId,  // Remove this
  };
}

// After: Single write with migration support
Map<String, dynamic> toFirestore() {
  return {
    'customer_id': customerId,
    // Temporarily keep camelCase for backward compatibility
    if (_isMigrationPhase) 'customerId': customerId,
  };
}
```

#### Step 3: Data Migration (High Risk)
```dart
// Migration script to ensure all documents have snake_case fields
Future<void> migrateFieldNaming() async {
  final batch = FirebaseFirestore.instance.batch();
  
  // Process service_requests collection
  final requests = await FirebaseFirestore.instance
      .collection('service_requests')
      .get();
  
  for (final doc in requests.docs) {
    final data = doc.data();
    final updates = <String, dynamic>{};
    
    // Migrate customer_id
    if (data['customer_id'] == null && data['customerId'] != null) {
      updates['customer_id'] = data['customerId'];
    }
    
    // Migrate other fields...
    
    if (updates.isNotEmpty) {
      batch.update(doc.reference, updates);
    }
  }
  
  await batch.commit();
}
```

#### Step 4: Remove Compatibility Code (Low Risk)
```dart
// Final state: Clean snake_case only
factory RequestModel.fromMap(String id, Map<String, dynamic> data) {
  return RequestModel(
    customerId: data['customer_id'] ?? '',  // No fallback needed
  );
}
```

### Phase 2: Update Security Rules

```javascript
// Before: Dual field checks
match /service_requests/{requestId} {
  allow read: if isAuthenticated() && (
    resource.data.customer_id == request.auth.uid ||
    resource.data.customerId == request.auth.uid  // Remove this
  );
}

// After: Single field check
match /service_requests/{requestId} {
  allow read: if isAuthenticated() && 
    resource.data.customer_id == request.auth.uid;
}
```

### Phase 3: Clean Up Indexes

Remove any redundant indexes that were created for camelCase fields.

## Alternative Strategy: Standardize on camelCase

**Rationale**: 
- More consistent with Dart/Flutter conventions
- Matches model property names
- Better developer experience in Dart code

**Challenges**:
- Requires updating all Firestore queries
- Security rules need complete rewrite
- All indexes need recreation
- Higher migration complexity

**Implementation**: Similar phases but converting TO camelCase instead of FROM it.

## Migration Timeline

### Week 1-2: Preparation
- [ ] Audit all field usage across codebase
- [ ] Create comprehensive test suite
- [ ] Develop migration scripts
- [ ] Set up monitoring for data consistency

### Week 3-4: Model Updates
- [ ] Update all model classes to prefer snake_case
- [ ] Keep compatibility fallbacks
- [ ] Update unit tests
- [ ] Deploy and monitor

### Week 5-6: Data Migration
- [ ] Run migration scripts on staging
- [ ] Validate data consistency
- [ ] Run migration on production
- [ ] Monitor for issues

### Week 7-8: Cleanup
- [ ] Remove compatibility code
- [ ] Update security rules
- [ ] Clean up redundant indexes
- [ ] Final testing and validation

## Field-by-Field Migration Priority

### High Priority (Core Functionality)
1. `customer_id` / `customerId`
2. `created_at` / `createdAt`
3. `updated_at` / `updatedAt`
4. `chat_active` / `chatActive`

### Medium Priority (User Experience)
1. `technician_id` / `technicianId`
2. `service_id` / `serviceId`
3. `is_paid` / `isPaid`
4. `sender_type` / `senderType`

### Low Priority (Cosmetic)
1. `display_name` / `displayName` / `full_name`
2. `photo_url` / `photoUrl` / `photoURL`
3. `phone_number` / `phoneNumber`
4. `message_type` / `messageType`

## Monitoring and Validation

### Data Consistency Checks
```dart
Future<void> validateFieldConsistency() async {
  final inconsistencies = <String>[];
  
  final requests = await FirebaseFirestore.instance
      .collection('service_requests')
      .get();
  
  for (final doc in requests.docs) {
    final data = doc.data();
    
    // Check customer_id consistency
    if (data['customer_id'] != data['customerId']) {
      inconsistencies.add('${doc.id}: customer_id mismatch');
    }
    
    // Check other fields...
  }
  
  if (inconsistencies.isNotEmpty) {
    // Alert/log inconsistencies
  }
}
```

### Performance Impact Measurement
- Document size before/after migration
- Query performance metrics
- Storage cost analysis
- Network bandwidth usage

## Conclusion

**Recommended Approach**: Standardize on **snake_case** for all Firestore fields while maintaining camelCase in Dart model properties.

**Benefits**:
- Reduces storage costs by ~30-50%
- Simplifies maintenance and reduces bugs
- Aligns with Firestore best practices
- Improves query performance
- Better developer experience long-term

**Timeline**: 8-week migration with careful testing and monitoring at each phase.

**Risk Mitigation**: Maintain backward compatibility during migration and implement comprehensive monitoring to catch any issues early. 