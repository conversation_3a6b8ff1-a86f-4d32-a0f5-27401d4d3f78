import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'text_styles.dart';

/// A luxury-styled button with customizable appearance and behaviors.
/// 
/// This widget provides a consistent, premium look for buttons throughout the app
/// with support for various styles, gradients, and loading states.
class LuxuryButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isLoading;
  final bool isGradient;
  final ButtonType type;
  final Color? customColor;
  final IconData? icon;
  final bool iconLeading;
  final double? width;
  final double height;
  final double? borderRadius;
  final TextStyle? textStyle;
  final EdgeInsetsGeometry? padding;
  final bool showShadow;
  final Gradient? customGradient;
  final bool fullWidth;
  final Widget? customContent;
  
  const LuxuryButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isLoading = false,
    this.isGradient = false,
    this.type = ButtonType.primary,
    this.customColor,
    this.icon,
    this.iconLeading = true,
    this.width,
    this.height = 50,
    this.borderRadius,
    this.textStyle,
    this.padding,
    this.showShadow = true,
    this.customGradient,
    this.fullWidth = false,
    this.customContent,
  });
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final baseRadius = borderRadius ?? 12.0;
    
    // Determine button styling based on type
    Color backgroundColor;
    Color textColor;
    Color borderColor;
    
    switch (type) {
      case ButtonType.primary:
        backgroundColor = customColor ?? theme.colorScheme.primary;
        textColor = Colors.white;
        borderColor = Colors.transparent;
        break;
      case ButtonType.secondary:
        backgroundColor = theme.colorScheme.secondary;
        textColor = Colors.white;
        borderColor = Colors.transparent;
        break;
      case ButtonType.outline:
        backgroundColor = Colors.transparent;
        textColor = customColor ?? theme.colorScheme.primary;
        borderColor = customColor ?? theme.colorScheme.primary;
        break;
      case ButtonType.text:
        backgroundColor = Colors.transparent;
        textColor = customColor ?? theme.colorScheme.primary;
        borderColor = Colors.transparent;
        break;
      case ButtonType.accent:
        backgroundColor = customColor ?? theme.colorScheme.tertiary;
        textColor = Colors.white;
        borderColor = Colors.transparent;
        break;
      case ButtonType.success:
        backgroundColor = Colors.green.shade700;
        textColor = Colors.white;
        borderColor = Colors.transparent;
        break;
      case ButtonType.warning:
        backgroundColor = Colors.orange.shade800;
        textColor = Colors.white;
        borderColor = Colors.transparent;
        break;
      case ButtonType.error:
        backgroundColor = theme.colorScheme.error;
        textColor = Colors.white;
        borderColor = Colors.transparent;
        break;
    }
    
    // Set button style based on type
    final ButtonStyle buttonStyle = ButtonStyle(
      backgroundColor: type == ButtonType.text || type == ButtonType.outline
          ? WidgetStateProperty.all(Colors.transparent)
          : isGradient
              ? WidgetStateProperty.all(Colors.transparent)
              : WidgetStateProperty.resolveWith((states) {
                  if (states.contains(WidgetState.disabled)) {
                    return backgroundColor.withOpacity(0.5);
                  }
                  return backgroundColor;
                }),
      foregroundColor: WidgetStateProperty.resolveWith((states) {
        if (states.contains(WidgetState.disabled)) {
          return textColor.withOpacity(0.5);
        }
        return textColor;
      }),
      overlayColor: WidgetStateProperty.resolveWith((states) {
        if (type == ButtonType.outline || type == ButtonType.text) {
          return textColor.withOpacity(0.1);
        }
        return Colors.white.withOpacity(0.1);
      }),
      shape: WidgetStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(baseRadius),
          side: BorderSide(
            color: type == ButtonType.outline ? borderColor : Colors.transparent,
            width: type == ButtonType.outline ? 1.5 : 0,
          ),
        ),
      ),
      elevation: WidgetStateProperty.all(type != ButtonType.text && showShadow ? 2 : 0),
      shadowColor: WidgetStateProperty.all(backgroundColor.withOpacity(0.5)),
      padding: WidgetStateProperty.all(
        padding ?? (width != null && width! < 150 
          ? EdgeInsets.symmetric(horizontal: 12, vertical: 0)  // Smaller padding for small buttons
          : EdgeInsets.symmetric(horizontal: 24, vertical: 0)),
      ),
      minimumSize: WidgetStateProperty.all(
        Size(fullWidth ? double.infinity : (width ?? 120), height),
      ),
      maximumSize: WidgetStateProperty.all(
        Size(fullWidth ? double.infinity : (width ?? double.infinity), height),
      ),
    );
    
    // Determine if we should use smaller sizing based on width
    final bool useSmallSizing = width != null && width! < 150;
    final double iconSpacing = useSmallSizing ? 4 : 8;
    final double iconSize = useSmallSizing ? 14 : 18;
    final double fontSize = useSmallSizing ? 13 : 15;
    
    // Create button content
    Widget buttonContent;
    
    // Use custom content if provided, otherwise use the default layout
    if (customContent != null && !isLoading) {
      buttonContent = customContent!;
    } else {
      buttonContent = Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (isLoading)
            SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2.5,
                valueColor: AlwaysStoppedAnimation<Color>(textColor),
              ),
            )
          else ...[
            if (icon != null && iconLeading) ...[
              Icon(icon, size: iconSize),
              SizedBox(width: iconSpacing),
            ],
            Flexible(
              child: Text(
                text,
                style: textStyle ??
                    TextStyle(
                      fontSize: fontSize,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 0.2,
                    ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            ),
            if (icon != null && !iconLeading) ...[
              SizedBox(width: iconSpacing),
              Icon(icon, size: iconSize),
            ],
          ],
        ],
      );
    }
    
    // Create base button widget
    Widget buttonWidget;
    
    if (type == ButtonType.text) {
      buttonWidget = TextButton(
        onPressed: isLoading ? null : onPressed,
        style: buttonStyle,
        child: buttonContent,
      );
    } else if (type == ButtonType.outline) {
      buttonWidget = OutlinedButton(
        onPressed: isLoading ? null : onPressed,
        style: buttonStyle,
        child: buttonContent,
      );
    } else {
      buttonWidget = ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: buttonStyle,
        child: buttonContent,
      );
    }
    
    // Add gradient if needed
    if (isGradient && type != ButtonType.text && type != ButtonType.outline) {
      final gradientToUse = customGradient ?? AppTheme.primaryGradient;
      
      return DecoratedBox(
        decoration: BoxDecoration(
          gradient: onPressed == null ? null : gradientToUse,
          borderRadius: BorderRadius.circular(baseRadius),
          boxShadow: showShadow && onPressed != null
              ? [
                  BoxShadow(
                    color: backgroundColor.withOpacity(0.3),
                    blurRadius: 8,
                    offset: Offset(0, 4),
                    spreadRadius: -2,
                  ),
                ]
              : null,
        ),
        child: buttonWidget,
      );
    }
    
    return buttonWidget;
  }
}

/// Custom button styles for the app
enum ButtonType {
  primary,
  secondary,
  outline,
  text,
  accent,
  success,
  warning,
  error,
}

/// A circular icon button with luxury styling
class LuxuryIconButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final Color? iconColor;
  final Color? backgroundColor;
  final double size;
  final double iconSize;
  final bool showShadow;
  final bool isGradient;
  final Gradient? customGradient;
  
  const LuxuryIconButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.iconColor,
    this.backgroundColor,
    this.size = 48,
    this.iconSize = 24,
    this.showShadow = true,
    this.isGradient = false,
    this.customGradient,
  });
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bgColor = backgroundColor ?? theme.colorScheme.surface;
    final iColor = iconColor ?? theme.colorScheme.primary;
    
    Widget buttonWidget = SizedBox(
      width: size,
      height: size,
      child: Material(
        color: isGradient ? Colors.transparent : bgColor,
        shape: CircleBorder(),
        elevation: showShadow ? 2 : 0,
        shadowColor: bgColor.withOpacity(0.3),
        clipBehavior: Clip.antiAlias,
        child: InkWell(
          onTap: onPressed,
          customBorder: CircleBorder(),
          child: Center(
            child: Icon(
              icon,
              size: iconSize,
              color: iColor,
            ),
          ),
        ),
      ),
    );
    
    if (isGradient) {
      return Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: customGradient ?? AppTheme.primaryGradient,
          boxShadow: showShadow
              ? [
                  BoxShadow(
                    color: bgColor.withOpacity(0.3),
                    blurRadius: 8,
                    offset: Offset(0, 4),
                    spreadRadius: -2,
                  ),
                ]
              : null,
        ),
        child: buttonWidget,
      );
    }
    
    return buttonWidget;
  }
}

/// A floating action button with luxury styling
class LuxuryFloatingButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final String? label;
  final Color? backgroundColor;
  final Color? iconColor;
  final bool isExtended;
  final bool isGradient;
  final Gradient? customGradient;
  
  const LuxuryFloatingButton({
    super.key,
    required this.icon,
    this.onPressed,
    this.label,
    this.backgroundColor,
    this.iconColor,
    this.isExtended = false,
    this.isGradient = false,
    this.customGradient,
  });
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bgColor = backgroundColor ?? theme.colorScheme.primary;
    final iColor = iconColor ?? Colors.white;
    
    if (isExtended && label != null) {
      Widget fabWidget = FloatingActionButton.extended(
        onPressed: onPressed,
        icon: Icon(icon, color: iColor),
        label: Text(
          label!,
          style: AppTextStyles.buttonMedium(context, color: iColor),
        ),
        backgroundColor: isGradient ? Colors.transparent : bgColor,
        elevation: 4,
        highlightElevation: 8,
      );
      
      if (isGradient) {
        return Container(
          decoration: ShapeDecoration(
            shape: StadiumBorder(),
            gradient: customGradient ?? AppTheme.primaryGradient,
            shadows: [
              BoxShadow(
                color: bgColor.withOpacity(0.3),
                blurRadius: 12,
                offset: Offset(0, 6),
                spreadRadius: -3,
              ),
            ],
          ),
          child: fabWidget,
        );
      }
      
      return fabWidget;
    } else {
      Widget fabWidget = FloatingActionButton(
        onPressed: onPressed,
        backgroundColor: isGradient ? Colors.transparent : bgColor,
        elevation: 4,
        highlightElevation: 8,
        child: Icon(icon, color: iColor),
      );
      
      if (isGradient) {
        return Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: customGradient ?? AppTheme.primaryGradient,
            boxShadow: [
              BoxShadow(
                color: bgColor.withOpacity(0.3),
                blurRadius: 12,
                offset: Offset(0, 6),
                spreadRadius: -3,
              ),
            ],
          ),
          child: fabWidget,
        );
      }
      
      return fabWidget;
    }
  }
} 