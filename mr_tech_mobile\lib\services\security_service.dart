import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

import 'secure_storage_service.dart';
import 'auth_service.dart';
import 'integrity_service.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Service for handling security-related features
class SecurityService {
  // Singleton instance
  static final SecurityService _instance = SecurityService._internal();
  factory SecurityService() => _instance;

  SecurityService._internal() {
    // Initialize auto-lock timer
    _initAutoLock();
  }

  final SecureStorageService _secureStorage = SecureStorageService();
  final AuthService _authService = AuthService();
  final IntegrityService _integrityService = IntegrityService();

  // App lock settings
  static const String _appLockEnabledKey = 'app_lock_enabled';
  static const String _lastActivityKey = 'last_activity_timestamp';
  static const int _autoLockTimeout =
      5 * 60 * 1000; // 5 minutes in milliseconds

  // Security policy settings
  static const String _allowRootedDevicesKey = 'allow_rooted_devices';
  static const String _requireStoreInstallKey = 'require_store_install';

  bool _isAppLocked = false;
  Timer? _inactivityTimer;

  // Getters
  bool get isAppLocked => _isAppLocked;

  // Initialize auto-lock timer
  void _initAutoLock() {
    _inactivityTimer = Timer.periodic(const Duration(minutes: 1), (_) {
      _checkInactivityTimeout();
    });
  }

  // Register user activity to prevent auto-lock
  Future<void> registerUserActivity() async {
    final now = DateTime.now().millisecondsSinceEpoch;
    await _secureStorage.storeData(_lastActivityKey, now.toString());
  }

  // Check if app should be locked due to inactivity
  Future<bool> shouldLockApp() async {
    // Check if app lock is enabled
    final appLockEnabledStr = await _secureStorage.getData(_appLockEnabledKey);
    final appLockEnabled = appLockEnabledStr == 'true';

    if (!appLockEnabled) {
      return false;
    }

    // Check inactivity time
    final lastActivityStr = await _secureStorage.getData(_lastActivityKey);
    if (lastActivityStr == null) {
      return false;
    }

    final lastActivity = int.tryParse(lastActivityStr) ?? 0;
    final now = DateTime.now().millisecondsSinceEpoch;

    return (now - lastActivity) > _autoLockTimeout;
  }

  // Check inactivity timeout
  Future<void> _checkInactivityTimeout() async {
    if (_authService.isSignedIn && !_isAppLocked) {
      final shouldLock = await shouldLockApp();
      if (shouldLock) {
        lockApp();
      }
    }
  }

  // Lock the app
  void lockApp() {
    _isAppLocked = true;
  }

  // Unlock the app
  void unlockApp() {
    _isAppLocked = false;
    registerUserActivity();
  }

  // Enable or disable app lock
  Future<void> setAppLockEnabled(bool enabled) async {
    await _secureStorage.storeData(_appLockEnabledKey, enabled.toString());
    if (enabled) {
      await registerUserActivity();
    }
  }

  // Check if app lock is enabled
  Future<bool> isAppLockEnabled() async {
    final appLockEnabledStr = await _secureStorage.getData(_appLockEnabledKey);
    return appLockEnabledStr == 'true';
  }

  // Set security policy for rooted devices
  Future<void> setAllowRootedDevices(bool allow) async {
    await _secureStorage.storeData(_allowRootedDevicesKey, allow.toString());
  }

  // Get security policy for rooted devices
  Future<bool> getAllowRootedDevices() async {
    final allowRootedStr = await _secureStorage.getData(_allowRootedDevicesKey);
    // Default to false (don't allow rooted devices)
    return allowRootedStr == 'true';
  }

  // Set security policy for store installation requirement
  Future<void> setRequireStoreInstall(bool require) async {
    await _secureStorage.storeData(_requireStoreInstallKey, require.toString());
  }

  // Get security policy for store installation requirement
  Future<bool> getRequireStoreInstall() async {
    final requireStoreStr = await _secureStorage.getData(
      _requireStoreInstallKey,
    );
    // Default to true (require store installation)
    return requireStoreStr != 'false';
  }

  // Check for security vulnerabilities
  Future<Map<String, dynamic>> performSecurityCheck() async {
    final Map<String, dynamic> results = {
      'isSecure': true,
      'issues': <String>[],
      'deviceIntegrity': <String, bool>{},
    };

    try {
      // Check if app is running in debug mode
      if (kDebugMode) {
        debugPrint('[SecurityService] Warning: App is running in debug mode');
        results['issues'].add('Debug mode enabled');
      }

      // Check if Firebase user is valid
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser != null) {
        try {
          await currentUser.reload();
        } catch (e) {
          // User may have been deleted or disabled
          await _authService.signOut();
          results['issues'].add('User account invalid');
        }
      }

      // Check if secure storage is working
      final testKey = 'security_check_test';
      await _secureStorage.storeData(testKey, 'test_value');
      final testValue = await _secureStorage.getData(testKey);
      if (testValue != 'test_value') {
        if (kDebugMode) {
          debugPrint(
            '[SecurityService] Warning: Secure storage not working correctly',
          );
        }
        results['issues'].add('Secure storage compromised');
      }
      await _secureStorage.deleteData(testKey);

      // Check device integrity
      final deviceIntegrityResults =
          await _integrityService.performFullSecurityCheck();
      results['deviceIntegrity'] = deviceIntegrityResults;

      // Check if device is rooted
      if (deviceIntegrityResults['isRooted'] == true) {
        final allowRooted = await getAllowRootedDevices();
        if (!allowRooted) {
          results['issues'].add('Device is rooted/jailbroken');
          results['isSecure'] = false;
        }
      }

      // Check if app is installed from store
      if (deviceIntegrityResults['isInstalledFromStore'] == false) {
        final requireStore = await getRequireStoreInstall();
        if (requireStore) {
          results['issues'].add('App not installed from official store');
          results['isSecure'] = false;
        }
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('[SecurityService] Security check error: $e');
      }
      results['issues'].add('Security check error: $e');
    }

    return results;
  }

  // Make secure HTTP request with proper headers
  Future<http.Response> secureHttpGet(
    String url, {
    Map<String, String>? headers,
  }) async {
    final Map<String, String> secureHeaders = {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'Content-Security-Policy': "default-src 'self'",
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
      'Cache-Control': 'no-store, no-cache',
      'Pragma': 'no-cache',
      ...?headers,
    };

    return await http.get(Uri.parse(url), headers: secureHeaders);
  }

  // Make secure HTTP post with proper headers
  Future<http.Response> secureHttpPost(
    String url,
    dynamic body, {
    Map<String, String>? headers,
  }) async {
    final Map<String, String> secureHeaders = {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'Content-Security-Policy': "default-src 'self'",
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
      'Cache-Control': 'no-store, no-cache',
      'Pragma': 'no-cache',
      'Content-Type': 'application/json',
      ...?headers,
    };

    return await http.post(
      Uri.parse(url),
      headers: secureHeaders,
      body: jsonEncode(body),
    );
  }
}
