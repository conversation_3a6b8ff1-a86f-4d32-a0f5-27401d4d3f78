import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

/// Enhanced state management system for improved app state handling
/// Provides centralized state management, persistence, and synchronization
class StateManager extends ChangeNotifier {
  // Private constructor to prevent instantiation
  StateManager._();

  static StateManager? _instance;
  static StateManager get instance => _instance ??= StateManager._();

  // State storage
  final Map<String, dynamic> _state = {};
  final Map<String, StreamController<dynamic>> _stateStreams = {};
  final Map<String, List<VoidCallback>> _stateListeners = {};
  
  // Persistence
  SharedPreferences? _prefs;
  final Set<String> _persistentKeys = {};
  
  // State validation
  final Map<String, StateValidator> _validators = {};
  
  // State history for undo/redo
  final Map<String, List<dynamic>> _stateHistory = {};
  final int _maxHistorySize = 10;

  /// Initialize state manager
  static Future<void> initialize() async {
    final instance = StateManager.instance;
    instance._prefs = await SharedPreferences.getInstance();
    await instance._loadPersistedState();
    debugPrint('StateManager initialized');
  }

  /// Set state value with optional persistence and validation
  Future<bool> setState<T>(
    String key,
    T value, {
    bool persist = false,
    bool notify = true,
    bool addToHistory = true,
  }) async {
    // Validate state if validator exists
    final validator = _validators[key];
    if (validator != null) {
      final validationResult = await validator.validate(value);
      if (!validationResult.isValid) {
        debugPrint('StateManager: Validation failed for $key: ${validationResult.error}');
        return false;
      }
    }

    // Add to history before changing
    if (addToHistory && _state.containsKey(key)) {
      _addToHistory(key, _state[key]);
    }

    // Set the state
    _state[key] = value;

    // Persist if required
    if (persist) {
      _persistentKeys.add(key);
      await _persistState(key, value);
    }

    // Notify listeners
    if (notify) {
      _notifyStateListeners(key, value);
      notifyListeners();
    }

    // Emit to stream
    _emitToStream(key, value);

    return true;
  }

  /// Get state value with type safety
  T? getState<T>(String key, {T? defaultValue}) {
    final value = _state[key];
    if (value is T) {
      return value;
    }
    return defaultValue;
  }

  /// Get state value or throw if not found
  T getRequiredState<T>(String key) {
    final value = _state[key];
    if (value is T) {
      return value;
    }
    throw StateError('Required state $key not found or wrong type');
  }

  /// Check if state exists
  bool hasState(String key) {
    return _state.containsKey(key);
  }

  /// Remove state
  Future<void> removeState(String key, {bool notify = true}) async {
    if (!_state.containsKey(key)) return;

    // Add to history before removing
    _addToHistory(key, _state[key]);

    // Remove from state
    _state.remove(key);

    // Remove from persistence
    if (_persistentKeys.contains(key)) {
      _persistentKeys.remove(key);
      await _prefs?.remove(key);
    }

    // Close stream
    _stateStreams[key]?.close();
    _stateStreams.remove(key);

    // Remove listeners
    _stateListeners.remove(key);

    // Remove history
    _stateHistory.remove(key);

    // Notify
    if (notify) {
      notifyListeners();
    }
  }

  /// Clear all state
  Future<void> clearState({bool clearPersistent = false}) async {
    _state.clear();
    
    if (clearPersistent) {
      for (final key in _persistentKeys) {
        await _prefs?.remove(key);
      }
      _persistentKeys.clear();
    }

    // Close all streams
    for (final stream in _stateStreams.values) {
      stream.close();
    }
    _stateStreams.clear();

    // Clear listeners and history
    _stateListeners.clear();
    _stateHistory.clear();

    notifyListeners();
  }

  /// Listen to state changes
  void addStateListener(String key, VoidCallback listener) {
    _stateListeners.putIfAbsent(key, () => []).add(listener);
  }

  /// Remove state listener
  void removeStateListener(String key, VoidCallback listener) {
    _stateListeners[key]?.remove(listener);
  }

  /// Get state stream for reactive programming
  Stream<T> getStateStream<T>(String key) {
    if (!_stateStreams.containsKey(key)) {
      _stateStreams[key] = StreamController<dynamic>.broadcast();
    }
    return _stateStreams[key]!.stream.cast<T>();
  }

  /// Update state with a function
  Future<bool> updateState<T>(
    String key,
    T Function(T? current) updater, {
    bool persist = false,
    bool notify = true,
  }) async {
    final current = getState<T>(key);
    final newValue = updater(current);
    return setState(key, newValue, persist: persist, notify: notify);
  }

  /// Batch state updates
  Future<void> batchUpdate(
    Map<String, dynamic> updates, {
    bool persist = false,
    bool notify = true,
  }) async {
    for (final entry in updates.entries) {
      await setState(
        entry.key,
        entry.value,
        persist: persist,
        notify: false, // Don't notify for individual updates
      );
    }

    if (notify) {
      notifyListeners();
    }
  }

  /// Add state validator
  void addValidator(String key, StateValidator validator) {
    _validators[key] = validator;
  }

  /// Remove state validator
  void removeValidator(String key) {
    _validators.remove(key);
  }

  /// Undo last state change
  bool undoState(String key) {
    final history = _stateHistory[key];
    if (history == null || history.isEmpty) return false;

    final previousValue = history.removeLast();
    _state[key] = previousValue;

    _notifyStateListeners(key, previousValue);
    _emitToStream(key, previousValue);
    notifyListeners();

    return true;
  }

  /// Get state history
  List<dynamic> getStateHistory(String key) {
    return List.unmodifiable(_stateHistory[key] ?? []);
  }

  /// Clear state history
  void clearStateHistory(String key) {
    _stateHistory[key]?.clear();
  }

  /// Persist state to storage
  Future<void> _persistState(String key, dynamic value) async {
    if (_prefs == null) return;

    try {
      final jsonValue = jsonEncode(value);
      await _prefs!.setString(key, jsonValue);
    } catch (e) {
      debugPrint('StateManager: Failed to persist state $key: $e');
    }
  }

  /// Load persisted state from storage
  Future<void> _loadPersistedState() async {
    if (_prefs == null) return;

    final keys = _prefs!.getKeys();
    for (final key in keys) {
      try {
        final jsonValue = _prefs!.getString(key);
        if (jsonValue != null) {
          final value = jsonDecode(jsonValue);
          _state[key] = value;
          _persistentKeys.add(key);
        }
      } catch (e) {
        debugPrint('StateManager: Failed to load persisted state $key: $e');
      }
    }
  }

  /// Add value to history
  void _addToHistory(String key, dynamic value) {
    _stateHistory.putIfAbsent(key, () => []);
    final history = _stateHistory[key]!;
    
    history.add(value);
    
    // Limit history size
    if (history.length > _maxHistorySize) {
      history.removeAt(0);
    }
  }

  /// Notify state listeners
  void _notifyStateListeners(String key, dynamic value) {
    final listeners = _stateListeners[key];
    if (listeners != null) {
      for (final listener in listeners) {
        try {
          listener();
        } catch (e) {
          debugPrint('StateManager: Error in state listener for $key: $e');
        }
      }
    }
  }

  /// Emit value to stream
  void _emitToStream(String key, dynamic value) {
    final stream = _stateStreams[key];
    if (stream != null && !stream.isClosed) {
      stream.add(value);
    }
  }

  /// Get all state keys
  Set<String> get stateKeys => Set.unmodifiable(_state.keys);

  /// Get all persistent keys
  Set<String> get persistentKeys => Set.unmodifiable(_persistentKeys);

  /// Get state snapshot
  Map<String, dynamic> getStateSnapshot() {
    return Map.unmodifiable(_state);
  }

  /// Restore state from snapshot
  Future<void> restoreStateSnapshot(
    Map<String, dynamic> snapshot, {
    bool notify = true,
  }) async {
    _state.clear();
    _state.addAll(snapshot);

    if (notify) {
      notifyListeners();
    }

    // Emit to all streams
    for (final entry in snapshot.entries) {
      _emitToStream(entry.key, entry.value);
    }
  }

  @override
  void dispose() {
    // Close all streams
    for (final stream in _stateStreams.values) {
      stream.close();
    }
    _stateStreams.clear();

    // Clear all data
    _state.clear();
    _stateListeners.clear();
    _stateHistory.clear();
    _validators.clear();
    _persistentKeys.clear();

    super.dispose();
  }
}

/// State validator interface
abstract class StateValidator<T> {
  Future<ValidationResult> validate(T value);
}

/// Validation result
class ValidationResult {
  final bool isValid;
  final String? error;

  const ValidationResult({
    required this.isValid,
    this.error,
  });

  factory ValidationResult.valid() => const ValidationResult(isValid: true);
  
  factory ValidationResult.invalid(String error) => ValidationResult(
    isValid: false,
    error: error,
  );
}

/// Common state validators
class CommonValidators {
  /// Required field validator
  static StateValidator<String> required() => _RequiredValidator();
  
  /// Email validator
  static StateValidator<String> email() => _EmailValidator();
  
  /// Range validator for numbers
  static StateValidator<num> range(num min, num max) => _RangeValidator(min, max);
  
  /// Custom validator
  static StateValidator<T> custom<T>(
    Future<ValidationResult> Function(T value) validator,
  ) => _CustomValidator(validator);
}

class _RequiredValidator implements StateValidator<String> {
  @override
  Future<ValidationResult> validate(String value) async {
    if (value.trim().isEmpty) {
      return ValidationResult.invalid('This field is required');
    }
    return ValidationResult.valid();
  }
}

class _EmailValidator implements StateValidator<String> {
  @override
  Future<ValidationResult> validate(String value) async {
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return ValidationResult.invalid('Please enter a valid email address');
    }
    return ValidationResult.valid();
  }
}

class _RangeValidator implements StateValidator<num> {
  final num min;
  final num max;

  _RangeValidator(this.min, this.max);

  @override
  Future<ValidationResult> validate(num value) async {
    if (value < min || value > max) {
      return ValidationResult.invalid('Value must be between $min and $max');
    }
    return ValidationResult.valid();
  }
}

class _CustomValidator<T> implements StateValidator<T> {
  final Future<ValidationResult> Function(T value) _validator;

  _CustomValidator(this._validator);

  @override
  Future<ValidationResult> validate(T value) => _validator(value);
}
