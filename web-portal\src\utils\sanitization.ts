import type { CreateUserInput, UpdateUserInput } from '../types/user';
import { inputValidation, xssProtection } from './security';

class SanitizationService {
  // Sanitize string input - trim whitespace and remove dangerous characters
  sanitizeString(input: string | undefined | null): string | undefined {
    if (!input || typeof input !== 'string') {
      return undefined;
    }

    // Remove control characters using a more ESLint-friendly approach
    const removeControlChars = (str: string) => {
      return str
        .split('')
        .filter((char) => {
          const code = char.charCodeAt(0);
          return code >= 32 && code !== 127; // Keep printable characters only
        })
        .join('');
    };

    // Use XSS protection for additional security
    const cleaned = removeControlChars(input.trim()).replace(/\s+/g, ' '); // Replace multiple spaces with single space

    return xssProtection.sanitizeUserInput(cleaned);
  }

  // Sanitize email - convert to lowercase and trim
  sanitizeEmail(email: string | undefined | null): string | undefined {
    const sanitized = this.sanitizeString(email);
    if (!sanitized) return undefined;

    // Use enhanced email validation
    return inputValidation.isValidEmail(sanitized) ? sanitized.toLowerCase() : undefined;
  }

  // Sanitize name - proper case formatting
  sanitizeName(name: string | undefined | null): string | undefined {
    const sanitized = this.sanitizeString(name);
    if (!sanitized) return undefined;

    // Convert to proper case (first letter of each word capitalized)
    return sanitized
      .toLowerCase()
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  // Sanitize phone number - remove non-digit characters except + and spaces
  sanitizePhoneNumber(phone: string | undefined | null): string | undefined {
    if (!phone || typeof phone !== 'string') {
      return undefined;
    }

    return phone
      .trim()
      .replace(/[^\d+\s\-()]/g, '') // Keep only digits, +, spaces, hyphens, parentheses
      .replace(/\s+/g, ' '); // Replace multiple spaces with single space
  }

  // Sanitize URL - ensure proper format
  sanitizeUrl(url: string | undefined | null): string | undefined {
    const sanitized = this.sanitizeString(url);
    if (!sanitized) return undefined;

    // Use enhanced URL validation
    return inputValidation.isValidUrl(sanitized) ? sanitized : undefined;
  }

  // Sanitize array of strings
  sanitizeStringArray(array: string[] | undefined | null): string[] | undefined {
    if (!array || !Array.isArray(array)) {
      return undefined;
    }

    return array
      .map((item) => this.sanitizeString(item))
      .filter((item): item is string => item !== undefined && item.length > 0);
  }

  // Sanitize AnyDesk ID - keep only digits
  sanitizeanydesk_id(anydesk_id: string | undefined | null): string | undefined {
    if (!anydesk_id || typeof anydesk_id !== 'string') {
      return undefined;
    }

    const digitsOnly = anydesk_id.replace(/\D/g, '');
    return digitsOnly.length > 0 ? digitsOnly : undefined;
  }

  // Sanitize address - basic string sanitization with some special characters allowed
  sanitizeAddress(address: string | undefined | null): string | undefined {
    if (!address || typeof address !== 'string') {
      return undefined;
    }

    // Remove control characters using a more ESLint-friendly approach
    const removeControlChars = (str: string) => {
      return str
        .split('')
        .filter((char) => {
          const code = char.charCodeAt(0);
          return code >= 32 && code !== 127; // Keep printable characters only
        })
        .join('');
    };

    return removeControlChars(address.trim())
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/\s+/g, ' '); // Replace multiple spaces with single space
  }

  // Sanitize city/country names
  sanitizeLocationName(location: string | undefined | null): string | undefined {
    const sanitized = this.sanitizeString(location);
    if (!sanitized) return undefined;

    // Allow letters, spaces, hyphens, apostrophes, and periods
    const cleaned = sanitized.replace(/[^a-zA-Z\s\-'.]/g, '');

    // Convert to proper case
    return cleaned
      .toLowerCase()
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  // Sanitize create user input
  sanitizeCreateUserInput(input: CreateUserInput): CreateUserInput {
    const sanitized: CreateUserInput = {
      email: this.sanitizeEmail(input.email) || '',
      name: this.sanitizeName(input.name) || '',
      role: input.role, // Role is validated separately
    };

    // Optional fields
    if (input.password !== undefined) {
      sanitized.password = input.password; // Don't sanitize passwords, just validate
    }

    if (input.phone_number !== undefined) {
      sanitized.phone_number = this.sanitizePhoneNumber(input.phone_number);
    }

    if (input.photo_url !== undefined) {
      sanitized.photo_url = this.sanitizeUrl(input.photo_url);
    }

    if (input.address !== undefined) {
      sanitized.address = this.sanitizeAddress(input.address);
    }

    if (input.city !== undefined) {
      sanitized.city = this.sanitizeLocationName(input.city);
    }

    if (input.country !== undefined) {
      sanitized.country = this.sanitizeLocationName(input.country);
    }

    if (input.preferred_language !== undefined) {
      sanitized.preferred_language = input.preferred_language; // Validated separately
    }

    if (input.specialties !== undefined) {
      sanitized.specialties = this.sanitizeStringArray(input.specialties);
    }

    if (input.permissions !== undefined) {
      sanitized.permissions = this.sanitizeStringArray(input.permissions);
    }

    if (input.admin_level !== undefined) {
      sanitized.admin_level = input.admin_level; // Validated separately
    }

    if (input.anydesk_id !== undefined) {
      sanitized.anydesk_id = this.sanitizeanydesk_id(input.anydesk_id);
    }

    return sanitized;
  }

  // Sanitize update user input
  sanitizeUpdateUserInput(input: UpdateUserInput): UpdateUserInput {
    const sanitized: UpdateUserInput = {};

    // Only sanitize fields that are being updated
    if (input.name !== undefined) {
      sanitized.name = this.sanitizeName(input.name);
    }

    if (input.phone_number !== undefined) {
      sanitized.phone_number = this.sanitizePhoneNumber(input.phone_number);
    }

    if (input.photo_url !== undefined) {
      sanitized.photo_url = this.sanitizeUrl(input.photo_url);
    }

    if (input.address !== undefined) {
      sanitized.address = this.sanitizeAddress(input.address);
    }

    if (input.city !== undefined) {
      sanitized.city = this.sanitizeLocationName(input.city);
    }

    if (input.country !== undefined) {
      sanitized.country = this.sanitizeLocationName(input.country);
    }

    if (input.preferred_language !== undefined) {
      sanitized.preferred_language = input.preferred_language; // Validated separately
    }

    if (input.status !== undefined) {
      sanitized.status = input.status; // Validated separately
    }

    if (input.is_active !== undefined) {
      sanitized.is_active = input.is_active;
    }

    if (input.specialties !== undefined) {
      sanitized.specialties = this.sanitizeStringArray(input.specialties);
    }

    if (input.permissions !== undefined) {
      sanitized.permissions = this.sanitizeStringArray(input.permissions);
    }

    if (input.admin_level !== undefined) {
      sanitized.admin_level = input.admin_level; // Validated separately
    }

    if (input.anydesk_id !== undefined) {
      sanitized.anydesk_id = this.sanitizeanydesk_id(input.anydesk_id);
    }

    if (input.is_available !== undefined) {
      sanitized.is_available = input.is_available;
    }

    if (input.technician_status !== undefined) {
      sanitized.technician_status = input.technician_status; // Validated separately
    }

    return sanitized;
  }

  // Sanitize search query
  sanitizeSearchQuery(query: string | undefined | null): string | undefined {
    const sanitized = this.sanitizeString(query);
    if (!sanitized || sanitized.length < 2) {
      return undefined;
    }

    // Remove special characters that could be used for injection
    return sanitized.replace(/[<>'"&]/g, '');
  }

  // Sanitize HTML content (basic)
  sanitizeHtml(html: string | undefined | null): string | undefined {
    if (!html || typeof html !== 'string') {
      return undefined;
    }

    return html
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/&/g, '&amp;');
  }

  // Remove null and undefined values from object
  removeNullValues<T extends Record<string, any>>(obj: T): Partial<T> {
    const cleaned: Partial<T> = {};

    for (const [key, value] of Object.entries(obj)) {
      if (value !== null && value !== undefined) {
        cleaned[key as keyof T] = value;
      }
    }

    return cleaned;
  }

  // Deep sanitize object (recursive)
  deepSanitize<T>(obj: T): T {
    if (obj === null || obj === undefined) {
      return obj;
    }

    if (typeof obj === 'string') {
      return this.sanitizeString(obj) as T;
    }

    if (Array.isArray(obj)) {
      return obj.map((item) => this.deepSanitize(item)) as T;
    }

    if (typeof obj === 'object') {
      const sanitized: Record<string, unknown> = {};
      for (const [key, value] of Object.entries(obj)) {
        sanitized[key] = this.deepSanitize(value);
      }
      return sanitized as T;
    }

    return obj;
  }
}

// Export singleton instance
export default new SanitizationService();
