import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

enum MessageType { text, image, file, system }

enum SenderType { customer, technician, system }

class ChatMessageModel {
  final String id;
  final String requestId;
  final SenderType senderType;
  final String senderId;
  final MessageType messageType;
  final String content;
  final String? fileUrl;
  final bool isRead;
  final DateTime createdAt;

  ChatMessageModel({
    this.id = '',
    required this.requestId,
    required this.senderType,
    required this.senderId,
    required this.messageType,
    required this.content,
    this.fileUrl,
    this.isRead = false,
    required this.createdAt,
  });

  // Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'request_id': requestId,
      'sender_type': senderType.toString().split('.').last,
      'sender_id': senderId,
      'message_type': messageType.toString().split('.').last,
      'content': content,
      'file_url': fileUrl,
      'read': isRead,
      'created_at': createdAt.toIso8601String(),
      'senderType': senderType.toString().split('.').last,
      'senderId': senderId,
      'messageType': messageType.toString().split('.').last,
      'fileUrl': fileUrl,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  // Create model from JSON
  factory ChatMessageModel.fromJson(Map<String, dynamic> json) {
    return ChatMessageModel(
      id: json['id'] ?? '',
      requestId: json['request_id'] ?? '',
      senderType: _senderTypeFromString(json['sender_type']),
      senderId: json['sender_id'] ?? '',
      messageType: _messageTypeFromString(json['message_type']),
      content: json['content'] ?? '',
      fileUrl: json['file_url'],
      isRead: json['read'] ?? false,
      createdAt:
          json['created_at'] is String
              ? DateTime.parse(json['created_at'])
              : (json['created_at'] as Timestamp).toDate(),
    );
  }

  // Convert to Firestore
  // PHASE 2: Write ONLY snake_case (no more dual-write)
  Map<String, dynamic> toFirestore() {
    final Map<String, dynamic> data = {
      // Use ONLY snake_case fields - standardized format
      'request_id': requestId,
      'sender_type': senderType.toString().split('.').last,
      'sender_id': senderId,
      'message_type': messageType.toString().split('.').last,
      'content': content,
      'file_url': fileUrl,
      'read': isRead,
      'created_at': createdAt,
    };

    return data;
  }

  // Create from Firestore
  factory ChatMessageModel.fromFirestore(
    DocumentSnapshot<Map<String, dynamic>> doc,
  ) {
    final data = doc.data() ?? {};

    // Support both camelCase and snake_case field names for compatibility
    final String requestId = data['request_id'] ?? data['requestId'] ?? '';
    final senderType = _senderTypeFromString(
      data['sender_type'] ?? data['senderType'],
    );
    final String senderId = data['sender_id'] ?? data['senderId'] ?? '';
    final messageType = _messageTypeFromString(
      data['message_type'] ?? data['messageType'],
    );
    final String content = data['content'] ?? '';
    final String? fileUrl = data['file_url'] ?? data['fileUrl'];
    final bool isRead = data['read'] ?? false;

    // Handle timestamp - support both camelCase and snake_case
    DateTime createdAt;
    final createdAtField = data['created_at'] ?? data['createdAt'];

    if (createdAtField is Timestamp) {
      createdAt = createdAtField.toDate();
    } else if (createdAtField is DateTime) {
      createdAt = createdAtField;
    } else {
      createdAt = DateTime.now();
    }

    return ChatMessageModel(
      id: doc.id,
      requestId: requestId,
      senderType: senderType,
      senderId: senderId,
      messageType: messageType,
      content: content,
      fileUrl: fileUrl,
      isRead: isRead,
      createdAt: createdAt,
    );
  }

  // Create from Realtime Database
  factory ChatMessageModel.fromRealtime(Map<String, dynamic> data) {
    try {
      // Support both camelCase and snake_case field names for compatibility
      final String id = data['id'] ?? '';
      final String requestId = data['request_id'] ?? data['requestId'] ?? '';
      final senderType = _senderTypeFromString(
        data['sender_type'] ?? data['senderType'],
      );
      final String senderId = data['sender_id'] ?? data['senderId'] ?? '';
      final messageType = _messageTypeFromString(
        data['message_type'] ?? data['messageType'],
      );
      final String content = data['content'] ?? '';
      final String? fileUrl = data['file_url'] ?? data['fileUrl'];
      final bool isRead = data['read'] ?? false;

      // Convert timestamp to DateTime - handle multiple formats
      DateTime createdAt;

      // Try different timestamp fields in order of preference
      final timestamp =
          data['timestamp'] ?? data['created_at'] ?? data['createdAt'];

      if (timestamp is int) {
        createdAt = DateTime.fromMillisecondsSinceEpoch(timestamp);
      } else if (timestamp is String) {
        try {
          createdAt = DateTime.parse(timestamp);
        } catch (_) {
          createdAt = DateTime.now();
        }
      } else if (timestamp is Map && timestamp['_seconds'] is num) {
        // Handle Firestore timestamp format
        final seconds = timestamp['_seconds'] as num;
        final nanoseconds = timestamp['_nanoseconds'] as num? ?? 0;
        createdAt = DateTime.fromMillisecondsSinceEpoch(
          seconds.toInt() * 1000 + (nanoseconds.toInt() ~/ 1000000),
        );
      } else {
        createdAt = DateTime.now();
      }

      return ChatMessageModel(
        id: id,
        requestId: requestId,
        senderType: senderType,
        senderId: senderId,
        messageType: messageType,
        content: content,
        fileUrl: fileUrl,
        isRead: isRead,
        createdAt: createdAt,
      );
    } catch (e) {
      debugPrint('Error creating message from Realtime DB: $e');
      debugPrint('Data: $data');
      // Return a fallback message in case of parsing errors
      return ChatMessageModel(
        id: 'error-${DateTime.now().millisecondsSinceEpoch}',
        requestId: data['request_id'] ?? data['requestId'] ?? '',
        senderType: SenderType.system,
        senderId: 'system',
        messageType: MessageType.system,
        content: 'Error loading message',
        isRead: true,
        createdAt: DateTime.now(),
      );
    }
  }

  // Helper to convert string to SenderType enum
  static SenderType _senderTypeFromString(String? senderType) {
    switch (senderType) {
      case 'customer':
        return SenderType.customer;
      case 'technician':
        return SenderType.technician;
      case 'system':
        return SenderType.system;
      default:
        return SenderType.system;
    }
  }

  // Helper to convert string to MessageType enum
  static MessageType _messageTypeFromString(String? messageType) {
    switch (messageType) {
      case 'text':
        return MessageType.text;
      case 'image':
        return MessageType.image;
      case 'file':
        return MessageType.file;
      case 'system':
        return MessageType.system;
      default:
        return MessageType.text;
    }
  }

  // Create a copy of the model with updated fields
  ChatMessageModel copyWith({
    String? id,
    String? requestId,
    SenderType? senderType,
    String? senderId,
    MessageType? messageType,
    String? content,
    String? fileUrl,
    bool? isRead,
    DateTime? createdAt,
  }) {
    return ChatMessageModel(
      id: id ?? this.id,
      requestId: requestId ?? this.requestId,
      senderType: senderType ?? this.senderType,
      senderId: senderId ?? this.senderId,
      messageType: messageType ?? this.messageType,
      content: content ?? this.content,
      fileUrl: fileUrl ?? this.fileUrl,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
