const admin = require('firebase-admin');
const path = require('path');

// Initialize Firebase Admin SDK
async function initializeFirebase() {
  try {
    // Try to initialize with service account key if it exists
    const serviceAccountPath = path.join(__dirname, '..', 'firebase-service-account.json');
    try {
      const serviceAccount = require(serviceAccountPath);
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
      });
      console.log('✅ Firebase Admin initialized with service account');
    } catch (e) {
      // Fallback to default credentials (Firebase CLI login)
      admin.initializeApp({
        projectId: 'stopnow-be6b7'
      });
      console.log('✅ Firebase Admin initialized with CLI credentials');
    }
  } catch (error) {
    console.error('❌ Failed to initialize Firebase Admin:', error.message);
    console.log('Please run: firebase login');
    process.exit(1);
  }
}

let db;

// Migration configuration for all collections
const MIGRATION_CONFIG = {
  services: {
    fields: {
      'basePrice': 'base_price',
      'isActive': 'is_active',
      'estimatedDuration': 'estimated_duration',
      'imageUrl': 'image_url'
    },
    addTimestamps: true
  },
  service_requests: {
    fields: {
      'customerId': 'customer_id',
      'technicianId': 'technician_id',
      'serviceId': 'service_id',
      'deviceType': 'device_type',
      'issueDescription': 'issue_description',
      'createdAt': 'created_at',
      'updatedAt': 'updated_at',
      'scheduledDate': 'scheduled_date',
      'completedAt': 'completed_at',
      'totalPrice': 'total_price',
      'chatActive': 'chat_active',
      'photoUrl': 'photo_url'
    },
    addTimestamps: false // Already has timestamps
  },
  users: {
    fields: {
      'firstName': 'first_name',
      'lastName': 'last_name',
      'phoneNumber': 'phone_number',
      'photoUrl': 'photo_url',
      'photoURL': 'photo_url', // Handle the triple variant
      'createdAt': 'created_at',
      'updatedAt': 'updated_at',
      'lastLogin': 'last_login',
      'emailVerified': 'email_verified'
    },
    addTimestamps: false
  },
  technicians: {
    fields: {
      'firstName': 'first_name',
      'lastName': 'last_name',
      'phoneNumber': 'phone_number',
      'photoUrl': 'photo_url',
      'isActive': 'is_active',
      'joinedDate': 'joined_date',
      'lastActive': 'last_active',
      'completedJobs': 'completed_jobs',
      'averageRating': 'average_rating'
    },
    addTimestamps: true
  },
  chat_messages: {
    fields: {
      'senderId': 'sender_id',
      'receiverId': 'receiver_id',
      'requestId': 'request_id',
      'messageType': 'message_type',
      'createdAt': 'created_at',
      'isRead': 'is_read',
      'photoUrl': 'photo_url'
    },
    addTimestamps: false
  },
  reviews: {
    fields: {
      'customerId': 'customer_id',
      'technicianId': 'technician_id',
      'requestId': 'request_id',
      'createdAt': 'created_at'
    },
    addTimestamps: false
  },
  notifications: {
    fields: {
      'userId': 'user_id',
      'targetType': 'target_type',
      'isRead': 'is_read',
      'createdAt': 'created_at',
      'actionUrl': 'action_url'
    },
    addTimestamps: false
  }
};

async function migrateCollection(collectionName, config) {
  console.log(`\n🔄 Migrating ${collectionName} collection...`);
  
  try {
    const collectionRef = db.collection(collectionName);
    const snapshot = await collectionRef.get();
    
    if (snapshot.empty) {
      console.log(`   ⚠️ No documents found in ${collectionName}`);
      return { total: 0, migrated: 0, errors: 0 };
    }
    
    console.log(`   📊 Found ${snapshot.docs.length} documents`);
    
    let migrated = 0;
    let errors = 0;
    let alreadyMigrated = 0;
    
    for (const doc of snapshot.docs) {
      try {
        const data = doc.data();
        const updates = {};
        let needsUpdate = false;
        
        // Process field mappings
        for (const [camelCase, snakeCase] of Object.entries(config.fields)) {
          if (data[camelCase] !== undefined && data[snakeCase] === undefined) {
            updates[snakeCase] = data[camelCase];
            needsUpdate = true;
          }
        }
        
        // Add timestamps if configured
        if (config.addTimestamps) {
          if (data.created_at === undefined && data.createdAt === undefined) {
            updates.created_at = admin.firestore.FieldValue.serverTimestamp();
            needsUpdate = true;
          }
          if (data.updated_at === undefined) {
            updates.updated_at = admin.firestore.FieldValue.serverTimestamp();
            needsUpdate = true;
          }
        }
        
        // Perform update if needed
        if (needsUpdate) {
          await doc.ref.update(updates);
          migrated++;
          
          const fieldList = Object.keys(updates).join(', ');
          console.log(`   ✅ ${doc.id}: Added ${fieldList}`);
        } else {
          alreadyMigrated++;
        }
        
      } catch (error) {
        errors++;
        console.log(`   ❌ Error migrating ${doc.id}: ${error.message}`);
      }
    }
    
    console.log(`   📈 ${collectionName} Summary:`);
    console.log(`      - Total: ${snapshot.docs.length}`);
    console.log(`      - Migrated: ${migrated}`);
    console.log(`      - Already migrated: ${alreadyMigrated}`);
    console.log(`      - Errors: ${errors}`);
    
    return { 
      total: snapshot.docs.length, 
      migrated, 
      errors, 
      alreadyMigrated 
    };
    
  } catch (error) {
    console.log(`   ❌ Failed to migrate ${collectionName}: ${error.message}`);
    return { total: 0, migrated: 0, errors: 1, alreadyMigrated: 0 };
  }
}

async function validateMigration() {
  console.log('\n🔍 VALIDATING MIGRATION...');
  
  let totalValid = 0;
  let totalInvalid = 0;
  
  for (const [collectionName, config] of Object.entries(MIGRATION_CONFIG)) {
    try {
      const snapshot = await db.collection(collectionName).get();
      let valid = 0;
      let invalid = 0;
      
      snapshot.docs.forEach(doc => {
        const data = doc.data();
        let hasAllSnakeCase = true;
        
        // Check if all expected snake_case fields exist
        for (const snakeCase of Object.values(config.fields)) {
          if (data[snakeCase] === undefined) {
            // Check if the original camelCase field exists
            const camelCase = Object.keys(config.fields).find(key => config.fields[key] === snakeCase);
            if (data[camelCase] !== undefined) {
              hasAllSnakeCase = false;
              break;
            }
          }
        }
        
        if (hasAllSnakeCase) {
          valid++;
        } else {
          invalid++;
        }
      });
      
      console.log(`   ${collectionName}: ${valid} valid, ${invalid} need migration`);
      totalValid += valid;
      totalInvalid += invalid;
      
    } catch (error) {
      console.log(`   ❌ Error validating ${collectionName}: ${error.message}`);
    }
  }
  
  console.log(`\n📊 VALIDATION SUMMARY:`);
  console.log(`   - Total valid documents: ${totalValid}`);
  console.log(`   - Documents needing migration: ${totalInvalid}`);
  
  return totalInvalid === 0;
}

async function runCompleteMigration() {
  console.log('🚀 COMPLETE MR. TECH DATABASE MIGRATION');
  console.log('═'.repeat(60));
  console.log('This script will migrate ALL collections to snake_case field naming');
  console.log('Original camelCase fields will be PRESERVED for compatibility');
  console.log('');
  
  await initializeFirebase();
  
  // Initialize Firestore after Firebase Admin
  db = admin.firestore();
  
  // Pre-migration validation
  console.log('📊 PRE-MIGRATION STATUS:');
  await validateMigration();
  
  console.log('\n🔄 STARTING MIGRATION OF ALL COLLECTIONS...');
  
  let totalStats = {
    collections: 0,
    documents: 0,
    migrated: 0,
    errors: 0,
    alreadyMigrated: 0
  };
  
  // Migrate each collection
  for (const [collectionName, config] of Object.entries(MIGRATION_CONFIG)) {
    const stats = await migrateCollection(collectionName, config);
    totalStats.collections++;
    totalStats.documents += stats.total;
    totalStats.migrated += stats.migrated;
    totalStats.errors += stats.errors;
    totalStats.alreadyMigrated += stats.alreadyMigrated;
  }
  
  console.log('\n📊 COMPLETE MIGRATION SUMMARY:');
  console.log('═'.repeat(60));
  console.log(`   - Collections processed: ${totalStats.collections}`);
  console.log(`   - Total documents: ${totalStats.documents}`);
  console.log(`   - Documents migrated: ${totalStats.migrated}`);
  console.log(`   - Already migrated: ${totalStats.alreadyMigrated}`);
  console.log(`   - Errors: ${totalStats.errors}`);
  
  // Post-migration validation
  console.log('\n🔍 POST-MIGRATION VALIDATION:');
  const isValid = await validateMigration();
  
  if (isValid && totalStats.errors === 0) {
    console.log('\n🎉 MIGRATION COMPLETED SUCCESSFULLY!');
    console.log('═'.repeat(60));
    console.log('✅ ALL collections now support both camelCase AND snake_case fields');
    console.log('✅ Your app will work with both field formats');
    console.log('✅ Models will prefer snake_case but fallback to camelCase');
    console.log('✅ No data was lost or removed');
    console.log('');
    console.log('🧪 NEXT STEPS:');
    console.log('1. Run your Flutter app: flutter run');
    console.log('2. Test all screens (services, requests, users, etc.)');
    console.log('3. Check debug console for field usage patterns');
    console.log('4. Verify app functionality is unchanged');
    console.log('');
    console.log('🎯 EXPECTED RESULTS:');
    console.log('- All screens load normally');
    console.log('- No crashes or errors');
    console.log('- Debug logs show snake_case field preferences');
    console.log('- 30-50% smaller documents after Phase 3 cleanup');
  } else {
    console.log('\n⚠️ MIGRATION COMPLETED WITH SOME ISSUES');
    console.log('Please review the errors above and run validation again');
  }
}

// Handle script termination gracefully
process.on('SIGINT', () => {
  console.log('\n🛑 Migration interrupted by user');
  process.exit(0);
});

process.on('uncaughtException', (error) => {
  console.error('\n💥 Uncaught exception:', error.message);
  process.exit(1);
});

// Run the migration
runCompleteMigration()
  .then(() => {
    console.log('\n🏁 Migration script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Migration failed:', error.message);
    process.exit(1);
  }); 