import React, { useMemo, useState } from 'react';
import {
  Bell,
  BellRing,
  Check,
  Check<PERSON>heck,
  Eye,
  EyeOff,
  Filter,
  Search,
  Settings,
  Trash2,
  Volume2,
  VolumeX,
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Badge } from '../components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Separator } from '../components/ui/separator';
import { useNotifications } from '../contexts/NotificationContext';
import { format, formatDistanceToNow } from 'date-fns';
import { cn } from '../lib/utils';
import type {
  NotificationPriority,
  NotificationType,
} from '../services/realtimeNotificationService';

const NotificationsPage: React.FC = () => {
  const {
    notifications,
    unreadCount,
    settings,
    markAsRead,
    markAllAsRead,
    clearAllNotifications,
    updateSettings,
    testNotification,
  } = useNotifications();

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedPriority, setSelectedPriority] = useState<string>('all');
  const [showRead, setShowRead] = useState(true);
  const [activeTab, setActiveTab] = useState('all');

  // Filter notifications based on search and filters
  const filteredNotifications = useMemo(() => {
    let filtered = notifications;

    // Filter by tab
    if (activeTab === 'unread') {
      filtered = filtered.filter((n) => !n.read);
    } else if (activeTab === 'read') {
      filtered = filtered.filter((n) => n.read);
    }

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(
        (n) =>
          n.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          n.message.toLowerCase().includes(searchQuery.toLowerCase()),
      );
    }

    // Filter by type
    if (selectedType !== 'all') {
      filtered = filtered.filter((n) => n.type === selectedType);
    }

    // Filter by priority
    if (selectedPriority !== 'all') {
      filtered = filtered.filter((n) => n.priority === selectedPriority);
    }

    // Filter by read status
    if (!showRead) {
      filtered = filtered.filter((n) => !n.read);
    }

    return filtered;
  }, [notifications, searchQuery, selectedType, selectedPriority, showRead, activeTab]);

  const getPriorityColor = (priority: NotificationPriority) => {
    switch (priority) {
      case 'urgent':
        return 'border-red-500 bg-red-50';
      case 'high':
        return 'border-orange-500 bg-orange-50';
      case 'medium':
        return 'border-blue-500 bg-blue-50';
      case 'low':
        return 'border-gray-500 bg-gray-50';
      default:
        return 'border-gray-300 bg-white';
    }
  };

  const getTypeIcon = (type: NotificationType) => {
    switch (type) {
      case 'request_created':
        return '🆕';
      case 'request_updated':
        return '🔄';
      case 'request_completed':
        return '✅';
      case 'request_cancelled':
        return '❌';
      case 'chat_message':
        return '💬';
      case 'payment_received':
        return '💰';
      case 'technician_assigned':
        return '👨‍🔧';
      case 'system_alert':
        return '⚠️';
      case 'maintenance_mode':
        return '🔧';
      default:
        return '📢';
    }
  };

  const getTypeLabel = (type: NotificationType) => {
    switch (type) {
      case 'request_created':
        return 'New Request';
      case 'request_updated':
        return 'Request Updated';
      case 'request_completed':
        return 'Request Completed';
      case 'request_cancelled':
        return 'Request Cancelled';
      case 'chat_message':
        return 'Chat Message';
      case 'payment_received':
        return 'Payment Received';
      case 'technician_assigned':
        return 'Technician Assigned';
      case 'system_alert':
        return 'System Alert';
      case 'maintenance_mode':
        return 'Maintenance';
      default:
        return 'Notification';
    }
  };

  const toggleSounds = () => {
    updateSettings({ enableSounds: !settings.enableSounds });
  };

  const handleNotificationClick = (notification: any) => {
    if (!notification.read) {
      markAsRead(notification.id);
    }

    // Navigate to relevant page if requestId exists
    if (notification.requestId) {
      window.location.href = `/requests/${notification.requestId}`;
    }
  };

  const uniqueTypes = Array.from(new Set(notifications.map((n) => n.type)));
  const uniquePriorities = Array.from(new Set(notifications.map((n) => n.priority)));

  return (
    <div className='space-y-6 p-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>Notifications</h1>
          <p className='text-muted-foreground mt-2'>Manage your notifications and preferences</p>
        </div>

        <div className='flex items-center gap-2'>
          <Button
            variant='outline'
            size='sm'
            onClick={toggleSounds}
            className='flex items-center gap-2'
          >
            {settings.enableSounds ? (
              <>
                <Volume2 className='h-4 w-4' />
                Sounds On
              </>
            ) : (
              <>
                <VolumeX className='h-4 w-4' />
                Sounds Off
              </>
            )}
          </Button>

          <Button
            variant='outline'
            size='sm'
            onClick={() => testNotification()}
            className='flex items-center gap-2'
          >
            <Bell className='h-4 w-4' />
            Test
          </Button>

          <Button
            variant='outline'
            size='sm'
            onClick={() => (window.location.href = '/settings?tab=notifications')}
            className='flex items-center gap-2'
          >
            <Settings className='h-4 w-4' />
            Settings
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className='grid gap-4 md:grid-cols-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total</CardTitle>
            <Bell className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{notifications.length}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Unread</CardTitle>
            <BellRing className='h-4 w-4 text-orange-600' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold text-orange-600'>{unreadCount}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>High Priority</CardTitle>
            <BellRing className='h-4 w-4 text-red-600' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold text-red-600'>
              {notifications.filter((n) => n.priority === 'high' || n.priority === 'urgent').length}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Today</CardTitle>
            <Bell className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {
                notifications.filter((n) => {
                  const today = new Date();
                  const notificationDate = new Date(n.timestamp);
                  return notificationDate.toDateString() === today.toDateString();
                }).length
              }
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Filter className='h-5 w-5' />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='flex flex-wrap gap-4'>
            <div className='flex items-center gap-2'>
              <Search className='h-4 w-4 text-muted-foreground' />
              <Input
                placeholder='Search notifications...'
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className='w-64'
              />
            </div>

            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger className='w-48'>
                <SelectValue placeholder='Filter by type' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All Types</SelectItem>
                {uniqueTypes.map((type) => (
                  <SelectItem key={type} value={type}>
                    {getTypeIcon(type)} {getTypeLabel(type)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedPriority} onValueChange={setSelectedPriority}>
              <SelectTrigger className='w-48'>
                <SelectValue placeholder='Filter by priority' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All Priorities</SelectItem>
                {uniquePriorities.map((priority) => (
                  <SelectItem key={priority} value={priority}>
                    <Badge variant='secondary' className='capitalize'>
                      {priority}
                    </Badge>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button
              variant='outline'
              size='sm'
              onClick={() => setShowRead(!showRead)}
              className='flex items-center gap-2'
            >
              {showRead ? <Eye className='h-4 w-4' /> : <EyeOff className='h-4 w-4' />}
              {showRead ? 'Hide Read' : 'Show Read'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Bulk Actions */}
      {notifications.length > 0 && (
        <div className='flex items-center gap-2'>
          {unreadCount > 0 && (
            <Button
              variant='outline'
              size='sm'
              onClick={markAllAsRead}
              className='flex items-center gap-2'
            >
              <CheckCheck className='h-4 w-4' />
              Mark All Read ({unreadCount})
            </Button>
          )}

          <Button
            variant='outline'
            size='sm'
            onClick={clearAllNotifications}
            className='flex items-center gap-2 text-destructive hover:text-destructive'
          >
            <Trash2 className='h-4 w-4' />
            Clear All
          </Button>
        </div>
      )}

      {/* Notifications Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value='all'>All ({notifications.length})</TabsTrigger>
          <TabsTrigger value='unread'>Unread ({unreadCount})</TabsTrigger>
          <TabsTrigger value='read'>Read ({notifications.length - unreadCount})</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className='space-y-4'>
          {filteredNotifications.length === 0 ? (
            <Card>
              <CardContent className='p-8 text-center'>
                <Bell className='h-12 w-12 mx-auto mb-4 opacity-50' />
                <h3 className='text-lg font-medium mb-2'>No notifications found</h3>
                <p className='text-muted-foreground mb-4'>
                  {searchQuery || selectedType !== 'all' || selectedPriority !== 'all'
                    ? 'Try adjusting your filters or search query.'
                    : "You'll see notifications here when they arrive."}
                </p>
                {!searchQuery && selectedType === 'all' && selectedPriority === 'all' && (
                  <Button
                    variant='outline'
                    onClick={() => testNotification()}
                    className='flex items-center gap-2'
                  >
                    <Bell className='h-4 w-4' />
                    Test Notification
                  </Button>
                )}
              </CardContent>
            </Card>
          ) : (
            <div className='space-y-2'>
              {filteredNotifications.map((notification) => (
                <Card
                  key={notification.id}
                  className={cn(
                    'cursor-pointer transition-all hover:shadow-md border-l-4',
                    notification.read ? 'opacity-75' : '',
                    getPriorityColor(notification.priority),
                  )}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <CardContent className='p-4'>
                    <div className='flex items-start gap-4'>
                      <div className='flex-shrink-0 mt-1'>
                        <span className='text-2xl'>{getTypeIcon(notification.type)}</span>
                      </div>

                      <div className='flex-1 min-w-0'>
                        <div className='flex items-center gap-2 mb-1'>
                          <h3 className='font-medium truncate'>{notification.title}</h3>
                          {!notification.read && (
                            <div className='w-2 h-2 bg-blue-600 rounded-full flex-shrink-0' />
                          )}
                        </div>

                        <p className='text-sm text-muted-foreground mb-2'>{notification.message}</p>

                        <div className='flex items-center justify-between'>
                          <div className='flex items-center gap-2'>
                            <Badge variant='secondary' className='text-xs'>
                              {getTypeLabel(notification.type)}
                            </Badge>
                            <Badge
                              variant={
                                notification.priority === 'urgent' ||
                                notification.priority === 'high'
                                  ? 'destructive'
                                  : 'secondary'
                              }
                              className='text-xs capitalize'
                            >
                              {notification.priority}
                            </Badge>
                          </div>

                          <div className='flex items-center gap-4 text-xs text-muted-foreground'>
                            <span>
                              {formatDistanceToNow(notification.timestamp, { addSuffix: true })}
                            </span>
                            <span>{format(notification.timestamp, 'MMM d, yyyy HH:mm')}</span>
                          </div>
                        </div>
                      </div>

                      <div className='flex-shrink-0'>
                        <Button
                          variant='ghost'
                          size='icon'
                          className='h-8 w-8'
                          onClick={(e) => {
                            e.stopPropagation();
                            if (!notification.read) {
                              markAsRead(notification.id);
                            }
                          }}
                        >
                          <Check className='h-4 w-4' />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default NotificationsPage;
