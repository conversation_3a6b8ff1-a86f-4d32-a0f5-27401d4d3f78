import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/translation_service.dart';
import '../services/database_service.dart';
import '../models/enhanced_review_model.dart';
import '../models/request_model.dart';
import '../widgets/text_styles.dart';

class EnhancedRatingScreen extends StatefulWidget {
  final RequestModel request;
  final EnhancedReviewModel? existingReview;

  const EnhancedRatingScreen({
    super.key,
    required this.request,
    this.existingReview,
  });

  @override
  State<EnhancedRatingScreen> createState() => _EnhancedRatingScreenState();
}

class _EnhancedRatingScreenState extends State<EnhancedRatingScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  final _commentController = TextEditingController();
  bool _isSubmitting = false;

  // Overall rating
  double _overallRating = 0.0;

  // Category ratings
  final Map<String, double> _categoryRatings = {
    'communication': 0.0,
    'technical_skill': 0.0,
    'timeliness': 0.0,
    'professionalism': 0.0,
    'problem_solving': 0.0,
  };

  // Selected tags
  final Set<String> _selectedTags = {};

  // Available tags
  final Map<String, List<String>> _availableTags = {
    'positive': [
      'Excellent communication',
      'Very knowledgeable',
      'Quick response',
      'Professional',
      'Problem solved completely',
      'Great value for money',
      'Would recommend',
      'Exceeded expectations',
    ],
    'negative': [
      'Poor communication',
      'Took too long',
      'Problem not fully resolved',
      'Unprofessional behavior',
      'Overpriced',
      'Would not recommend',
      'Below expectations',
      'Technical issues',
    ],
  };

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );

    // Load existing review data if editing
    if (widget.existingReview != null) {
      _loadExistingReview();
    }

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _commentController.dispose();
    super.dispose();
  }

  void _loadExistingReview() {
    final review = widget.existingReview!;
    _overallRating = review.overallRating;
    _categoryRatings.addAll(review.categoryRatings);
    _commentController.text = review.comment;
    _selectedTags.addAll(review.tags);
  }

  String _translate(String key) {
    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );
    return translationService.translate(key);
  }

  String _getCategoryTitle(String category) {
    switch (category) {
      case 'communication':
        return _translate('Communication');
      case 'technical_skill':
        return _translate('Technical Skill');
      case 'timeliness':
        return _translate('Timeliness');
      case 'professionalism':
        return _translate('Professionalism');
      case 'problem_solving':
        return _translate('Problem Solving');
      default:
        return category;
    }
  }

  String _getCategoryDescription(String category) {
    switch (category) {
      case 'communication':
        return _translate('How well did the technician communicate?');
      case 'technical_skill':
        return _translate('How skilled was the technician?');
      case 'timeliness':
        return _translate('Was the service completed on time?');
      case 'professionalism':
        return _translate('How professional was the technician?');
      case 'problem_solving':
        return _translate('How well was your problem solved?');
      default:
        return '';
    }
  }

  Future<void> _submitReview() async {
    if (_overallRating == 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_translate('Please provide an overall rating')),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final databaseService = Provider.of<DatabaseService>(
        context,
        listen: false,
      );

      // Create enhanced review
      final review = EnhancedReviewModel(
        id: widget.existingReview?.id ?? '',
        requestId: widget.request.id,
        customerId: widget.request.customerId,
        customerName: '', // RequestModel doesn't have customerName
        technicianId: widget.request.technicianId ?? '',
        serviceId: widget.request.serviceId,
        serviceName: widget.request.serviceName,
        overallRating: _overallRating,
        categoryRatings: Map.from(_categoryRatings),
        comment: _commentController.text.trim(),
        tags: _selectedTags.toList(),
        createdAt: widget.existingReview?.createdAt ?? DateTime.now(),
        updatedAt: widget.existingReview != null ? DateTime.now() : null,
        isVerified: true, // Service was completed
        isPublic: true,
      );

      // Submit review to database using the new method
      final success = await databaseService.createServiceReview(
        requestId: widget.request.id,
        customerId: widget.request.customerId,
        technicianId: widget.request.technicianId ?? '',
        rating: _overallRating,
        comment: _commentController.text.trim().isEmpty 
            ? null 
            : _commentController.text.trim(),
        categoryRatings: Map.from(_categoryRatings),
        tags: _selectedTags.toList(),
      );

      if (!success) {
        throw Exception('Failed to create review');
      }

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_translate('Review submitted successfully')),
            backgroundColor: Colors.green,
          ),
        );

        // Navigate back
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      debugPrint('Error submitting review: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              _translate('Error submitting review. Please try again.'),
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        title: Text(
          widget.existingReview != null
              ? _translate('Edit Review')
              : _translate('Rate Service'),
        ),
        backgroundColor: colorScheme.surface,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Service info
                _buildServiceInfo(colorScheme),
                const SizedBox(height: 24),

                // Overall rating
                _buildOverallRating(colorScheme),
                const SizedBox(height: 24),

                // Category ratings
                _buildCategoryRatings(colorScheme),
                const SizedBox(height: 24),

                // Tags selection
                _buildTagsSelection(colorScheme),
                const SizedBox(height: 24),

                // Comment section
                _buildCommentSection(colorScheme),
                const SizedBox(height: 32),

                // Submit button
                _buildSubmitButton(colorScheme),
                const SizedBox(height: 100),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildServiceInfo(ColorScheme colorScheme) {
    return Transform.translate(
      offset: Offset(0, 50 * (1 - _animationController.value)),
      child: Opacity(
        opacity: _animationController.value,
        child: Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _translate('Service Details'),
                  style: AppTextStyles.headingSmall(context),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Icon(Icons.build, color: colorScheme.primary),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        widget.request.serviceName,
                        style: AppTextStyles.bodyMedium(context),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.person, color: colorScheme.onSurfaceVariant),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        widget.request.technicianName ??
                            _translate('Technician'),
                        style: AppTextStyles.bodyMedium(context),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      Icons.calendar_today,
                      color: colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${widget.request.createdAt.day}/${widget.request.createdAt.month}/${widget.request.createdAt.year}',
                      style: AppTextStyles.bodyMedium(context),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOverallRating(ColorScheme colorScheme) {
    return Transform.translate(
      offset: Offset(0, 50 * (1 - _animationController.value)),
      child: Opacity(
        opacity: _animationController.value,
        child: Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _translate('Overall Rating'),
                  style: AppTextStyles.headingSmall(context),
                ),
                const SizedBox(height: 8),
                Text(
                  _translate('How would you rate this service overall?'),
                  style: AppTextStyles.bodyMedium(
                    context,
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(height: 16),
                Center(
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(5, (index) {
                          return GestureDetector(
                            onTap: () {
                              setState(() {
                                _overallRating = index + 1.0;
                              });
                            },
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 4,
                              ),
                              child: Icon(
                                index < _overallRating
                                    ? Icons.star
                                    : Icons.star_border,
                                size: 40,
                                color:
                                    index < _overallRating
                                        ? Colors.amber
                                        : Colors.grey,
                              ),
                            ),
                          );
                        }),
                      ),
                      const SizedBox(height: 8),
                      if (_overallRating > 0)
                        Text(
                          _getRatingText(_overallRating),
                          style: AppTextStyles.bodyMedium(
                            context,
                            color: colorScheme.primary,
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryRatings(ColorScheme colorScheme) {
    return Transform.translate(
      offset: Offset(0, 50 * (1 - _animationController.value)),
      child: Opacity(
        opacity: _animationController.value,
        child: Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _translate('Detailed Ratings'),
                  style: AppTextStyles.headingSmall(context),
                ),
                const SizedBox(height: 8),
                Text(
                  _translate('Rate specific aspects of the service'),
                  style: AppTextStyles.bodyMedium(
                    context,
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(height: 16),
                ..._categoryRatings.keys.map(
                  (category) => _buildCategoryRating(category, colorScheme),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryRating(String category, ColorScheme colorScheme) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _getCategoryTitle(category),
            style: AppTextStyles.bodyMedium(context),
          ),
          const SizedBox(height: 4),
          Text(
            _getCategoryDescription(category),
            style: AppTextStyles.bodySmall(
              context,
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: List.generate(5, (index) {
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _categoryRatings[category] = index + 1.0;
                  });
                },
                child: Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: Icon(
                    index < _categoryRatings[category]!
                        ? Icons.star
                        : Icons.star_border,
                    size: 24,
                    color:
                        index < _categoryRatings[category]!
                            ? Colors.amber
                            : Colors.grey,
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildTagsSelection(ColorScheme colorScheme) {
    return Transform.translate(
      offset: Offset(0, 50 * (1 - _animationController.value)),
      child: Opacity(
        opacity: _animationController.value,
        child: Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _translate('Quick Feedback'),
                  style: AppTextStyles.headingSmall(context),
                ),
                const SizedBox(height: 8),
                Text(
                  _translate('Select tags that describe your experience'),
                  style: AppTextStyles.bodyMedium(
                    context,
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(height: 16),

                // Positive tags
                Text(
                  _translate('Positive aspects'),
                  style: AppTextStyles.bodyMedium(context),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children:
                      _availableTags['positive']!
                          .map(
                            (tag) =>
                                _buildTagChip(tag, Colors.green, colorScheme),
                          )
                          .toList(),
                ),

                const SizedBox(height: 16),

                // Negative tags
                Text(
                  _translate('Areas for improvement'),
                  style: AppTextStyles.bodyMedium(context),
                ),
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children:
                      _availableTags['negative']!
                          .map(
                            (tag) =>
                                _buildTagChip(tag, Colors.orange, colorScheme),
                          )
                          .toList(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTagChip(String tag, Color color, ColorScheme colorScheme) {
    final isSelected = _selectedTags.contains(tag);

    return FilterChip(
      label: Text(
        _translate(tag),
        style: AppTextStyles.bodySmall(
          context,
          color: isSelected ? Colors.white : colorScheme.onSurface,
        ),
      ),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          if (selected) {
            _selectedTags.add(tag);
          } else {
            _selectedTags.remove(tag);
          }
        });
      },
      backgroundColor: colorScheme.surface,
      selectedColor: color,
      side: BorderSide(color: color.withOpacity(0.5)),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    );
  }

  Widget _buildCommentSection(ColorScheme colorScheme) {
    return Transform.translate(
      offset: Offset(0, 50 * (1 - _animationController.value)),
      child: Opacity(
        opacity: _animationController.value,
        child: Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _translate('Additional Comments'),
                  style: AppTextStyles.headingSmall(context),
                ),
                const SizedBox(height: 8),
                Text(
                  _translate(
                    'Share more details about your experience (optional)',
                  ),
                  style: AppTextStyles.bodyMedium(
                    context,
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(height: 16),
                TextField(
                  controller: _commentController,
                  maxLines: 4,
                  decoration: InputDecoration(
                    hintText: _translate('Tell us about your experience...'),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: colorScheme.primary,
                        width: 2,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSubmitButton(ColorScheme colorScheme) {
    return Transform.translate(
      offset: Offset(0, 50 * (1 - _animationController.value)),
      child: Opacity(
        opacity: _animationController.value,
        child: SizedBox(
          width: double.infinity,
          child: FilledButton(
            onPressed: _isSubmitting ? null : _submitReview,
            style: FilledButton.styleFrom(
              backgroundColor: colorScheme.primary,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child:
                _isSubmitting
                    ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                    : Text(
                      widget.existingReview != null
                          ? _translate('Update Review')
                          : _translate('Submit Review'),
                      style: AppTextStyles.buttonMedium(
                        context,
                        color: Colors.white,
                      ),
                    ),
          ),
        ),
      ),
    );
  }

  String _getRatingText(double rating) {
    if (rating >= 5) return _translate('Excellent');
    if (rating >= 4) return _translate('Very Good');
    if (rating >= 3) return _translate('Good');
    if (rating >= 2) return _translate('Fair');
    return _translate('Poor');
  }
}
