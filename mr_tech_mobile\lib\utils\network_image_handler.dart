import 'package:flutter/material.dart';

class NetworkImageWithFallback extends StatelessWidget {
  final String? imageUrl;
  final double width;
  final double height;
  final BoxFit fit;
  final Widget Function()? fallbackBuilder;
  final Color? fallbackColor;
  final IconData fallbackIcon;
  final double fallbackIconSize;
  final bool isCircular;
  final Widget Function(BuildContext, Widget, ImageChunkEvent?)? loadingBuilder;

  const NetworkImageWithFallback({
    super.key,
    required this.imageUrl,
    this.width = double.infinity,
    this.height = 200,
    this.fit = BoxFit.cover,
    this.fallbackBuilder,
    this.fallbackColor,
    this.fallbackIcon = Icons.image_not_supported,
    this.fallbackIconSize = 40,
    this.isCircular = false,
    this.loadingBuilder,
  });

  @override
  Widget build(BuildContext context) {
    if (imageUrl == null || imageUrl!.isEmpty) {
      return _buildFallback(context);
    }

    final Widget networkImageWidget = Image.network(
      imageUrl!,
      width: width,
      height: height,
      fit: fit,
      loadingBuilder:
          loadingBuilder ??
          (context, child, loadingProgress) {
            if (loadingProgress == null) return child;
            return Container(
              width: width,
              height: height,
              color: fallbackColor ?? Colors.grey.shade200,
              child: Center(
                child: CircularProgressIndicator(
                  value:
                      loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                ),
              ),
            );
          },
      errorBuilder: (context, error, stackTrace) {
        return _buildFallback(context);
      },
    );

    return isCircular
        ? ClipOval(child: networkImageWidget)
        : networkImageWidget;
  }

  Widget _buildFallback(BuildContext context) {
    if (fallbackBuilder != null) {
      return fallbackBuilder!();
    }

    return Container(
      width: width,
      height: height,
      color: fallbackColor ?? Colors.grey.shade200,
      child: Center(
        child: Icon(fallbackIcon, color: Colors.grey, size: fallbackIconSize),
      ),
    );
  }
}

// Extension method for CircleAvatar with network image and fallback
extension NetworkImageCircleAvatarExtension on CircleAvatar {
  static CircleAvatar withNetworkImage({
    required BuildContext context,
    required String? imageUrl,
    required String initials,
    double radius = 45,
    Color? backgroundColor,
    TextStyle? initialsStyle,
    Widget Function(BuildContext)? loadingBuilder,
  }) {
    // If we have a valid URL, we'll try to load it with error handling
    if (imageUrl != null && imageUrl.isNotEmpty) {
      return CircleAvatar(
        radius: radius,
        backgroundColor:
            backgroundColor ?? Theme.of(context).colorScheme.surface,
        child: ClipOval(
          child: Image.network(
            imageUrl,
            width: radius * 2,
            height: radius * 2,
            fit: BoxFit.cover,
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) {
                return child;
              }
              return Center(
                child:
                    loadingBuilder?.call(context) ??
                    CircularProgressIndicator(
                      value:
                          loadingProgress.expectedTotalBytes != null
                              ? loadingProgress.cumulativeBytesLoaded /
                                  loadingProgress.expectedTotalBytes!
                              : null,
                      strokeWidth: 2.0,
                    ),
              );
            },
            errorBuilder: (context, error, stackTrace) {
              // On error, show initials
              return Center(
                child: Text(
                  initials,
                  style:
                      initialsStyle ??
                      TextStyle(
                        fontSize: radius * 0.6,
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                ),
              );
            },
          ),
        ),
      );
    }

    // If no URL, just show initials directly
    return CircleAvatar(
      radius: radius,
      backgroundColor: backgroundColor ?? Theme.of(context).colorScheme.surface,
      child: Text(
        initials,
        style:
            initialsStyle ??
            TextStyle(
              fontSize: radius * 0.6,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.primary,
            ),
      ),
    );
  }
}
