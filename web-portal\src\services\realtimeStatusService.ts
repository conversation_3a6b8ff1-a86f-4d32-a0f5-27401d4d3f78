import {
  type DocumentSnapshot,
  type QuerySnapshot,
  type Unsubscribe,
  collection,
  doc,
  onSnapshot,
  orderBy,
  query,
  where,
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { type RequestModel, RequestStatus } from '../types/request';
import { normalizeStatus } from '../utils/statusMapping';
import requestService from '../services/requestService';
import { type RetryOptions, withRetry, withRetryDecorator } from '../utils/retryUtils';

/**
 * Real-time status update service for synchronizing request statuses
 * between mobile app and web portal using Firestore onSnapshot listeners
 */
class RealtimeStatusService {
  private requestListeners: Map<string, Unsubscribe> = new Map();
  private statusListeners: Map<string, Unsubscribe> = new Map();
  private globalListeners: Map<string, Unsubscribe> = new Map();
  private pendingOperations: Map<string, Promise<any>> = new Map();
  private retryOptions: RetryOptions = {
    maxRetries: 5,
    initialDelay: 1000,
    maxDelay: 30000,
    backoffFactor: 1.5,
  };

  /**
   * Listen to real-time status changes for a specific request
   */
  listenToRequestStatus(
    requestId: string,
    callback: (request: RequestModel | null) => void,
  ): () => void {
    // Clean up existing listener if any
    this.stopListeningToRequest(requestId);

    const requestRef = doc(db, 'service_requests', requestId);

    const unsubscribe = onSnapshot(
      requestRef,
      (snapshot: DocumentSnapshot) => {
        if (snapshot.exists()) {
          const data = snapshot.data();
          const request: RequestModel = {
            id: snapshot.id,
            ...data,
            // Normalize status for web portal compatibility
            status: normalizeStatus(data.status || 'pending'),
          } as RequestModel;
          callback(request);
        } else {
          callback(null);
        }
      },
      (error) => {
        console.error(`Error listening to request ${requestId}:`, error);
        // Attempt to reconnect after a short delay
        setTimeout(() => {
          console.warn(`Attempting to reconnect listener for request ${requestId}`);
          this.listenToRequestStatus(requestId, callback);
        }, 5000);
        callback(null);
      },
    );

    // Store the unsubscribe function
    this.requestListeners.set(requestId, unsubscribe);

    return unsubscribe;
  }

  /**
   * Listen to real-time status changes for requests with specific status
   */
  listenToRequestsByStatus(
    status: RequestStatus,
    callback: (requests: RequestModel[]) => void,
    listenerId?: string,
  ): () => void {
    const id = listenerId || `status_${status}_${Date.now()}`;

    // Clean up existing listener if any
    if (this.statusListeners.has(id)) {
      this.statusListeners.get(id)!();
      this.statusListeners.delete(id);
    }

    const requestsQuery = query(
      collection(db, 'service_requests'),
      where('status', '==', status),
      where('is_visible', '!=', false),
      orderBy('updated_at', 'desc'),
    );

    const unsubscribe = onSnapshot(
      requestsQuery,
      (snapshot: QuerySnapshot) => {
        const requests: RequestModel[] = [];

        snapshot.forEach((doc) => {
          const data = doc.data();
          const request: RequestModel = {
            id: doc.id,
            ...data,
            // Normalize status for web portal compatibility
            status: normalizeStatus(data.status || 'pending'),
          } as RequestModel;
          requests.push(request);
        });

        callback(requests);
      },
      (error) => {
        console.error(`Error listening to requests with status ${status}:`, error);
        // Attempt to reconnect after a short delay
        setTimeout(() => {
          console.warn(`Attempting to reconnect listener for status ${status}`);
          this.listenToRequestsByStatus(status, callback, listenerId);
        }, 5000);
        callback([]);
      },
    );

    // Store the unsubscribe function
    this.statusListeners.set(id, unsubscribe);

    return unsubscribe;
  }

  /**
   * Listen to real-time changes for all requests (with optional filters)
   */
  listenToAllRequests(
    callback: (requests: RequestModel[]) => void,
    filters?: {
      technicianId?: string;
      customerId?: string;
      statuses?: RequestStatus[];
    },
    listenerId?: string,
  ): () => void {
    const id = listenerId || `all_requests_${Date.now()}`;

    // Clean up existing listener if any
    if (this.globalListeners.has(id)) {
      this.globalListeners.get(id)!();
      this.globalListeners.delete(id);
    }

    // Build query constraints
    const constraints = [where('is_visible', '!=', false), orderBy('updated_at', 'desc')];

    if (filters?.technicianId) {
      constraints.unshift(where('technician_id', '==', filters.technicianId));
    }

    if (filters?.customerId) {
      constraints.unshift(where('customer_id', '==', filters.customerId));
    }

    if (filters?.statuses && filters.statuses.length > 0) {
      constraints.unshift(where('status', 'in', filters.statuses));
    }

    const requestsQuery = query(collection(db, 'service_requests'), ...constraints);

    const unsubscribe = onSnapshot(
      requestsQuery,
      (snapshot: QuerySnapshot) => {
        const requests: RequestModel[] = [];

        snapshot.forEach((doc) => {
          const data = doc.data();
          const request: RequestModel = {
            id: doc.id,
            ...data,
            // Normalize status for web portal compatibility
            status: normalizeStatus(data.status || 'pending'),
          } as RequestModel;
          requests.push(request);
        });

        callback(requests);
      },
      (error) => {
        console.error('Error listening to all requests:', error);
        // Attempt to reconnect after a short delay
        setTimeout(() => {
          console.warn(`Attempting to reconnect global listener ${id}`);
          this.listenToAllRequests(callback, filters, listenerId);
        }, 5000);
        callback([]);
      },
    );

    // Store the unsubscribe function
    this.globalListeners.set(id, unsubscribe);

    return unsubscribe;
  }

  /**
   * Listen to real-time changes for technician's assigned requests
   */
  listenToTechnicianRequests(
    technicianId: string,
    callback: (requests: RequestModel[]) => void,
    statuses?: RequestStatus[],
  ): () => void {
    return this.listenToAllRequests(
      callback,
      {
        technicianId,
        statuses,
      },
      `technician_${technicianId}`,
    );
  }

  /**
   * Listen to real-time changes for customer's requests
   */
  listenToCustomerRequests(
    customerId: string,
    callback: (requests: RequestModel[]) => void,
  ): () => void {
    return this.listenToAllRequests(
      callback,
      {
        customerId,
      },
      `customer_${customerId}`,
    );
  }

  /**
   * Stop listening to a specific request
   */
  stopListeningToRequest(requestId: string): void {
    const unsubscribe = this.requestListeners.get(requestId);
    if (unsubscribe) {
      unsubscribe();
      this.requestListeners.delete(requestId);
    }
  }

  /**
   * Stop listening to requests by status
   */
  stopListeningToStatus(listenerId: string): void {
    const unsubscribe = this.statusListeners.get(listenerId);
    if (unsubscribe) {
      unsubscribe();
      this.statusListeners.delete(listenerId);
    }
  }

  /**
   * Stop listening to global request changes
   */
  stopListeningToGlobal(listenerId: string): void {
    const unsubscribe = this.globalListeners.get(listenerId);
    if (unsubscribe) {
      unsubscribe();
      this.globalListeners.delete(listenerId);
    }
  }

  /**
   * Stop all active listeners
   */
  stopAllListeners(): void {
    // Stop request listeners
    this.requestListeners.forEach((unsubscribe) => unsubscribe());
    this.requestListeners.clear();

    // Stop status listeners
    this.statusListeners.forEach((unsubscribe) => unsubscribe());
    this.statusListeners.clear();

    // Stop global listeners
    this.globalListeners.forEach((unsubscribe) => unsubscribe());
    this.globalListeners.clear();
  }

  /**
   * Get active listener counts for monitoring
   */
  getActiveListenerCounts(): {
    requests: number;
    statuses: number;
    global: number;
    total: number;
  } {
    const requests = this.requestListeners.size;
    const statuses = this.statusListeners.size;
    const global = this.globalListeners.size;

    return {
      requests,
      statuses,
      global,
      total: requests + statuses + global,
    };
  }

  /**
   * Update request status with real-time notification and retry logic
   * This method wraps requestService.updateStatus to ensure all status updates
   * go through the real-time service for proper synchronization with mobile app
   */
  async updateRequestStatus(requestId: string, newStatus: RequestStatus): Promise<RequestModel> {
    // Generate a unique operation ID for this update
    const operationId = `update_${requestId}_${Date.now()}`;

    // Check if there's already a pending operation for this request
    if (this.pendingOperations.has(requestId)) {
      console.warn(`Operation already in progress for request ${requestId}, queuing this update`);
      // Wait for the current operation to complete before proceeding
      await this.pendingOperations.get(requestId);
    }

    // Create a new operation with retry logic
    const operation = this.updateRequestStatusWithRetry(requestId, newStatus);

    // Store the operation promise
    this.pendingOperations.set(requestId, operation);

    try {
      // Execute the operation with retries
      const result = await operation;

      // Operation completed successfully
      console.warn(`Status update operation ${operationId} completed successfully`);
      return result;
    } catch (error) {
      console.error(`Status update operation ${operationId} failed after all retries:`, error);
      throw error;
    } finally {
      // Clean up the pending operation
      if (this.pendingOperations.get(requestId) === operation) {
        this.pendingOperations.delete(requestId);
      }
    }
  }

  /**
   * Implementation of status update with retry logic
   * @private
   */
  private async updateRequestStatusWithRetry(
    requestId: string,
    newStatus: RequestStatus,
  ): Promise<RequestModel> {
    const updateFn = async () => {
      try {
        // Get current request to compare old and new status
        const currentRequest = await requestService.getById(requestId);
        const oldStatus = currentRequest?.status || RequestStatus.PENDING;

        // Update status through requestService (which handles mobile app format conversion)
        const updatedRequest = await requestService.updateStatus(requestId, newStatus);

        console.warn(
          `RealtimeStatusService: Status updated for request ${requestId} from ${oldStatus} to ${newStatus}`,
        );

        return updatedRequest;
      } catch (error: any) {
        console.error(
          `RealtimeStatusService: Error updating status for request ${requestId}:`,
          error,
        );
        // Add a custom flag to help identify retryable errors
        if (
          error.name === 'FirebaseError' &&
          (error.code === 'unavailable' ||
            error.code === 'resource-exhausted' ||
            error.code.includes('network') ||
            error.code.includes('timeout'))
        ) {
          error.isRetryable = true;
        }
        throw error;
      }
    };

    const retryResult = await withRetry(updateFn, this.retryOptions);

    if (!retryResult.success) {
      throw retryResult.error;
    }

    return retryResult.result!;
  }

  /**
   * Get the status of a pending operation for a request
   */
  isOperationPending(requestId: string): boolean {
    return this.pendingOperations.has(requestId);
  }

  /**
   * Get the count of pending operations
   */
  getPendingOperationsCount(): number {
    return this.pendingOperations.size;
  }

  /**
   * Perform health check on the real-time status service
   */
  healthCheck(): {
    isHealthy: boolean;
    activeListeners: number;
    pendingOperations: number;
    timestamp: Date;
  } {
    const counts = this.getActiveListenerCounts();

    return {
      isHealthy: true,
      activeListeners: counts.total,
      pendingOperations: this.pendingOperations.size,
      timestamp: new Date(),
    };
  }
}

// Export singleton instance
export default new RealtimeStatusService();
export { RealtimeStatusService };
