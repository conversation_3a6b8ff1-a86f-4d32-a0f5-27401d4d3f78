import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/translation_service.dart';
import '../services/chat_service.dart';
import '../services/request_service.dart';
import '../utils/navigation_utils.dart';
import 'package:intl/intl.dart';
import 'chat_screen.dart';
import 'main_navigation.dart';
import '../widgets/text_styles.dart';

class ChatsListScreen extends StatefulWidget {
  const ChatsListScreen({super.key});

  @override
  State<ChatsListScreen> createState() => _ChatsListScreenState();
}

class _ChatsListScreenState extends State<ChatsListScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  final ChatService _chatService = ChatService();
  final RequestService _requestService = RequestService();

  bool _isLoading = true;
  List<Map<String, dynamic>> _activeChats = [];
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );
    _loadActiveChats();
  }

  Future<void> _loadActiveChats() async {
    try {
      setState(() => _isLoading = true);

      // Load active chats from the service
      final chats = await _chatService.getActiveChats();

      setState(() {
        _activeChats = chats;
        _isLoading = false;
      });

      // Start animation after data is loaded
      _animationController.forward();
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading chats: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // Helper method for translations
  String _translate(String key) {
    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );
    return translationService.translate(key);
  }

  @override
  Widget build(BuildContext context) {
    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );
    final isRtl = translationService.isRtl;
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: CustomScrollView(
        slivers: [
          // Floating header
          SliverAppBar(
            expandedHeight: 80,
            floating: true,
            pinned: false,
            snap: true,
            elevation: 0,
            backgroundColor: colorScheme.surface,
            leading: BackButton(color: colorScheme.onSurface),
            flexibleSpace: FlexibleSpaceBar(
              background: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 10,
                  ),
                  child: Row(
                    children: [
                      const SizedBox(width: 56), // Space for back button
                      Expanded(
                        child: Text(
                          _translate('Chat'),
                          style: AppTextStyles.headingMedium(
                            context,
                            color: colorScheme.onSurface,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(width: 56), // Balance the layout
                    ],
                  ),
                ),
              ),
            ),
          ),

          // Main content
          _isLoading
              ? const SliverFillRemaining(
                child: Center(child: CircularProgressIndicator()),
              )
              : _errorMessage != null
              ? SliverFillRemaining(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 48,
                        color: colorScheme.error,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _errorMessage!,
                        style: AppTextStyles.bodyMedium(
                          context,
                          color: colorScheme.onSurface,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      FilledButton.icon(
                        onPressed: _loadActiveChats,
                        icon: const Icon(Icons.refresh),
                        label: Text(_translate('Retry')),
                        style: FilledButton.styleFrom(
                          backgroundColor: colorScheme.primary,
                          elevation: 0,
                        ),
                      ),
                    ],
                  ),
                ),
              )
              : _activeChats.isEmpty
              ? SliverFillRemaining(child: _buildEmptyChatList(colorScheme))
              : SliverToBoxAdapter(
                child: RefreshIndicator(
                  onRefresh: _loadActiveChats,
                  child: ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    padding: const EdgeInsets.symmetric(
                      vertical: 12,
                      horizontal: 20,
                    ),
                    itemCount: _activeChats.length,
                    itemBuilder: (context, index) {
                      final chat = _activeChats[index];

                      // Calculate animation delay based on index
                      final itemAnimation = CurvedAnimation(
                        parent: _animationController,
                        curve: Interval(
                          index * 0.1,
                          0.6 + index * 0.1,
                          curve: Curves.easeOutCubic,
                        ),
                      );

                      return SlideTransition(
                        position: Tween<Offset>(
                          begin: const Offset(0, 0.1),
                          end: Offset.zero,
                        ).animate(itemAnimation),
                        child: FadeTransition(
                          opacity: itemAnimation,
                          child: _buildChatListItem(chat, isRtl),
                        ),
                      );
                    },
                  ),
                ),
              ),
        ],
      ),
    );
  }

  Widget _buildEmptyChatList(ColorScheme colorScheme) {
    return FadeTransition(
      opacity: CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOut,
      ),
      child: Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: colorScheme.primary.withOpacity(0.1),
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: Icon(
                  Icons.chat_bubble_outline_rounded,
                  size: 48,
                  color: colorScheme.primary,
                ),
              ),
              const SizedBox(height: 24),
              Text(
                _translate('No active chats'),
                style: AppTextStyles.headingMedium(
                  context,
                  color: colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                _translate(
                  'Chat will be enabled when a technician accepts your request',
                ),
                textAlign: TextAlign.center,
                style: AppTextStyles.bodyMedium(
                  context,
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 24),
              FilledButton.icon(
                onPressed: () {
                  // Navigate to active requests - using bottom nav
                  Navigator.of(context).pushReplacement(
                    MaterialPageRoute(
                      builder:
                          (context) => const MainNavigation(
                            initialIndex: 1,
                          ), // Requests tab
                    ),
                  );
                },
                icon: const Icon(Icons.list_alt_rounded),
                label: Text(_translate('View Active Requests')),
                style: FilledButton.styleFrom(
                  backgroundColor: colorScheme.primary,
                  elevation: 0,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChatListItem(Map<String, dynamic> chat, bool isRtl) {
    // Format timestamp
    final now = DateTime.now();
    final timestamp = chat['timestamp'] as DateTime;
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Get formatted time or date
    String formattedTime;
    if (now.difference(timestamp).inDays > 0) {
      final formatter = DateFormat.MMMd(); // Nov 8
      formattedTime = formatter.format(timestamp);
    } else {
      final formatter = DateFormat.jm(); // 2:35 PM
      formattedTime = formatter.format(timestamp);
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: InkWell(
        onTap: () async {
          // Navigate to individual chat
          final requestId = chat['requestId'];

          try {
            // Get request from the database
            final request = await _requestService.getRequest(requestId);

            if (request != null && mounted) {
              // Use NavigationUtils with callback
              NavigationUtils.navigateKeepingStack(
                context,
                ChatScreen(request: request),
                onReturn: (_) => _loadActiveChats(),
              );
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(_translate('Request not found')),
                  duration: const Duration(seconds: 2),
                  behavior: SnackBarBehavior.floating,
                ),
              );
            }
          } catch (e) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  _translate('Error opening chat: ${e.toString()}'),
                ),
                duration: const Duration(seconds: 2),
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // Avatar with online indicator
              Stack(
                children: [
                  CircleAvatar(
                    radius: 24,
                    backgroundColor: colorScheme.primary,
                    child: Text(
                      chat['technicianAvatar'],
                      style: AppTextStyles.labelLarge(
                        context,
                        color: Colors.white,
                      ),
                    ),
                  ),
                  if (chat['isOnline'])
                    Positioned(
                      right: isRtl ? null : 0,
                      left: isRtl ? 0 : null,
                      bottom: 0,
                      child: Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(
                          color: const Color(0xFF4ADE80), // Green
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: colorScheme.surface,
                            width: 2,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(width: 16),
              // Chat content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Top row with name and time
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          chat['technician'],
                          style: AppTextStyles.headingSmall(
                            context,
                            color: colorScheme.onSurface,
                          ),
                        ),
                        Text(
                          formattedTime,
                          style: AppTextStyles.labelSmall(
                            context,
                            color: colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    // Service name tag
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: colorScheme.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: Text(
                        _translate(chat['serviceName']),
                        style: AppTextStyles.labelSmall(
                          context,
                          color: colorScheme.primary,
                        ),
                      ),
                    ),
                    const SizedBox(height: 6),
                    // Last message with unread count
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            chat['lastMessage'],
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: AppTextStyles.bodyMedium(
                              context,
                              color: colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        if (chat['unreadCount'] > 0)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: colorScheme.primary,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Text(
                              chat['unreadCount'].toString(),
                              style: AppTextStyles.labelSmall(
                                context,
                                color: colorScheme.onPrimary,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
