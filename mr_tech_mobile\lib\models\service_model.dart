import 'package:cloud_firestore/cloud_firestore.dart';

class ServiceModel {
  final String id;
  final Map<String, String>
  nameTranslations; // Map of language codes to translated names
  final String name; // Default name (for backward compatibility)
  final Map<String, String>
  descriptionTranslations; // Map of language codes to translated descriptions
  final String description; // Default description (for backward compatibility)
  final String category;
  final double basePrice;
  final String imageUrl;
  final bool active;
  final Map<String, dynamic>? metadata;
  final int estimatedDuration; // in minutes
  final DateTime createdAt;
  final DateTime? updatedAt;

  ServiceModel({
    required this.id,
    required this.name,
    this.nameTranslations = const {},
    required this.description,
    this.descriptionTranslations = const {},
    required this.category,
    required this.basePrice,
    required this.imageUrl,
    required this.active,
    this.metadata,
    required this.estimatedDuration,
    required this.createdAt,
    this.updatedAt,
  });

  // Factory constructor to create a ServiceModel from a Firestore document
  factory ServiceModel.fromFirestore(
    DocumentSnapshot<Map<String, dynamic>> doc,
  ) {
    final data = doc.data() ?? {};

    // Handle both old and new format for name
    String name = '';
    Map<String, String> nameTranslations = {};

    if (data['name'] is String) {
      name = data['name'] ?? '';
    } else if (data['name'] is Map) {
      nameTranslations = Map<String, String>.from(data['name'] as Map);
      name = nameTranslations['en'] ?? nameTranslations.values.first ?? '';
    }

    // Handle both old and new format for description
    String description = '';
    Map<String, String> descriptionTranslations = {};

    if (data['description'] is String) {
      description = data['description'] ?? '';
    } else if (data['description'] is Map) {
      descriptionTranslations = Map<String, String>.from(
        data['description'] as Map,
      );
      description =
          descriptionTranslations['en'] ??
          descriptionTranslations.values.first ??
          '';
    }

    return ServiceModel(
      id: doc.id,
      name: name,
      nameTranslations: nameTranslations,
      description: description,
      descriptionTranslations: descriptionTranslations,
      category: data['category'] ?? '',
      basePrice: (data['base_price'] ?? 0).toDouble(),
      imageUrl: data['image_url'] ?? '',
      active: data['active'] ?? true,
      metadata: data['metadata'],
      estimatedDuration: data['estimated_duration'] ?? 60,
      createdAt: (data['created_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updated_at'] as Timestamp?)?.toDate(),
    );
  }

  // Factory method to create ServiceModel from a document ID and Map
  // PHASE 1: Prefer snake_case but maintain full backward compatibility
  factory ServiceModel.fromMap(String id, Map<String, dynamic> data) {
    try {
      // Phase 1 Migration: Prefer snake_case, fallback to camelCase for safety

    // Handle both old and new format for name
    String name = '';
    Map<String, String> nameTranslations = {};

    if (data['name'] is String) {
      name = data['name'] ?? '';
    } else if (data['name'] is Map) {
      nameTranslations = Map<String, String>.from(data['name'] as Map);
      name = nameTranslations['en'] ?? nameTranslations.values.first ?? '';
    }

    // Handle both old and new format for description
    String description = '';
    Map<String, String> descriptionTranslations = {};

    if (data['description'] is String) {
      description = data['description'] ?? '';
    } else if (data['description'] is Map) {
      descriptionTranslations = Map<String, String>.from(
        data['description'] as Map,
      );
      description =
          descriptionTranslations['en'] ??
          descriptionTranslations.values.first ??
          '';
    }

      // Log field access patterns for monitoring migration progress
      if (data['base_price'] != null) {
        print(
          'ServiceModel.fromMap: Using snake_case base_price in service $id',
        );
      } else if (data['basePrice'] != null) {
        print(
          'ServiceModel.fromMap: Using camelCase fallback for base_price in service $id',
        );
      }

      if (data['is_active'] != null) {
        print(
          'ServiceModel.fromMap: Using snake_case is_active in service $id',
        );
      } else if (data['isActive'] != null) {
        print(
          'ServiceModel.fromMap: Using camelCase fallback for is_active in service $id',
        );
      } else if (data['active'] != null) {
        print('ServiceModel.fromMap: Using legacy active field in service $id');
      }

      if (data['estimated_duration'] != null) {
        print(
          'ServiceModel.fromMap: Using snake_case estimated_duration in service $id',
        );
      } else if (data['estimatedDuration'] != null) {
        print(
          'ServiceModel.fromMap: Using camelCase fallback for estimated_duration in service $id',
        );
      }

    return ServiceModel(
      id: id,
      name: name,
      nameTranslations: nameTranslations,
      description: description,
      descriptionTranslations: descriptionTranslations,
      category: data['category'] ?? '',
        // Phase 2: PRIMARY snake_case fields with safe fallbacks for migration period
        // Priority: snake_case → camelCase → legacy → default
        basePrice: (data['base_price'] ?? data['basePrice'] ?? 0).toDouble(),
        imageUrl: data['image_url'] ?? data['imageUrl'] ?? '',
        active: data['is_active'] ?? data['isActive'] ?? data['active'] ?? true,
      metadata: data['metadata'],
      estimatedDuration:
            data['estimated_duration'] ?? data['estimatedDuration'] ?? 60,
      createdAt:
            _parseDateTime(data['created_at'] ?? data['createdAt']) ??
          DateTime.now(),
        updatedAt: _parseDateTime(data['updated_at'] ?? data['updatedAt']),
    );
    } catch (e) {
      print('Error parsing ServiceModel from map: $e for ID: $id');
      // Return safe empty service instead of crashing
      return ServiceModel(
        id: id,
        name: 'Error Loading Service',
        description: 'Service could not be loaded',
        category: '',
        basePrice: 0.0,
        imageUrl: '',
        active: false,
        estimatedDuration: 60,
        createdAt: DateTime.now(),
      );
    }
  }

  // Convert ServiceModel to a Map for Firestore
  // PHASE 2: Write ONLY snake_case (no more dual-write)
  Map<String, dynamic> toFirestore() {
    final Map<String, dynamic> data = {
      // Use ONLY snake_case fields - standardized format
      'name': nameTranslations.isEmpty ? name : nameTranslations,
      'description':
          descriptionTranslations.isEmpty
              ? description
              : descriptionTranslations,
      'category': category,
      'base_price': basePrice,
      'image_url': imageUrl,
      'is_active':
          active, // Changed from 'active' to 'is_active' for consistency
      'metadata': metadata,
      'estimated_duration': estimatedDuration,
      'created_at': createdAt,
      'updated_at': updatedAt ?? FieldValue.serverTimestamp(),
    };

    return data;
  }

  // Convert ServiceModel to a Map with camelCase keys for consistency
  Map<String, dynamic> toMap() {
    return {
      'name': nameTranslations.isEmpty ? name : nameTranslations,
      'description':
          descriptionTranslations.isEmpty
              ? description
              : descriptionTranslations,
      'category': category,
      'basePrice': basePrice,
      'imageUrl': imageUrl,
      'isActive': active,
      'metadata': metadata,
      'estimatedDuration': estimatedDuration,
      'createdAt': createdAt,
      'updatedAt': updatedAt ?? DateTime.now(),
    };
  }

  // Create a copy of the service with updated fields
  ServiceModel copyWith({
    String? id,
    String? name,
    Map<String, String>? nameTranslations,
    String? description,
    Map<String, String>? descriptionTranslations,
    String? category,
    double? basePrice,
    String? imageUrl,
    bool? active,
    Map<String, dynamic>? metadata,
    int? estimatedDuration,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ServiceModel(
      id: id ?? this.id,
      name: name ?? this.name,
      nameTranslations: nameTranslations ?? this.nameTranslations,
      description: description ?? this.description,
      descriptionTranslations:
          descriptionTranslations ?? this.descriptionTranslations,
      category: category ?? this.category,
      basePrice: basePrice ?? this.basePrice,
      imageUrl: imageUrl ?? this.imageUrl,
      active: active ?? this.active,
      metadata: metadata ?? this.metadata,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Get the translated name based on current locale
  String getTranslatedName(String languageCode) {
    if (nameTranslations.isEmpty) {
      return name; // Return default name if no translations
    }

    // Return translation for the specified language code, or default to English, or first available
    return nameTranslations[languageCode] ??
        nameTranslations['en'] ??
        nameTranslations.values.first ??
        name;
  }

  // Get the translated description based on current locale
  String getTranslatedDescription(String languageCode) {
    if (descriptionTranslations.isEmpty) {
      return description; // Return default description if no translations
    }

    // Return translation for the specified language code, or default to English, or first available
    return descriptionTranslations[languageCode] ??
        descriptionTranslations['en'] ??
        descriptionTranslations.values.first ??
        description;
  }

  /// Helper method to parse DateTime from various formats
  static DateTime? _parseDateTime(dynamic value) {
    if (value == null) return null;

    if (value is Timestamp) {
      return value.toDate();
    } else if (value is int) {
      // Handle cached data stored as milliseconds since epoch
      return DateTime.fromMillisecondsSinceEpoch(value);
    } else if (value is String) {
      try {
        return DateTime.parse(value);
      } catch (e) {
        return null;
      }
    } else if (value is DateTime) {
      return value;
    }

    return null;
  }
}
