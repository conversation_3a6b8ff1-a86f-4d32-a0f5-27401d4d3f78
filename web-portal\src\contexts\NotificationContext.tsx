import type { ReactNode } from 'react';
import React, { createContext, useContext, useEffect, useState } from 'react';
import type {
  NotificationSettings,
  NotificationType,
  RealtimeNotification,
} from '../services/realtimeNotificationService';
import { realtimeNotificationService } from '../services/realtimeNotificationService';

interface NotificationContextType {
  notifications: RealtimeNotification[];
  unreadCount: number;
  settings: NotificationSettings;
  isInitialized: boolean;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  clearAllNotifications: () => void;
  updateSettings: (settings: Partial<NotificationSettings>) => void;
  testNotification: (type?: NotificationType) => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const [notifications, setNotifications] = useState<RealtimeNotification[]>([]);
  const [settings, setSettings] = useState<NotificationSettings>(
    realtimeNotificationService.getSettings(),
  );
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Initialize the notification service
    const initializeService = async () => {
      try {
        await realtimeNotificationService.initialize();
        setIsInitialized(true);

        // Load initial notifications
        setNotifications(realtimeNotificationService.getNotifications());
        setSettings(realtimeNotificationService.getSettings());
      } catch (error) {
        console.error('Failed to initialize notification service:', error);
      }
    };

    initializeService();

    // Subscribe to new notifications
    const unsubscribe = realtimeNotificationService.subscribe((notification) => {
      setNotifications((prev) => [notification, ...prev]);
    });

    // Cleanup on unmount
    return () => {
      unsubscribe();
    };
  }, []);

  const markAsRead = (notificationId: string) => {
    realtimeNotificationService.markAsRead(notificationId);
    setNotifications((prev) =>
      prev.map((n) => (n.id === notificationId ? { ...n, read: true } : n)),
    );
  };

  const markAllAsRead = () => {
    realtimeNotificationService.markAllAsRead();
    setNotifications((prev) => prev.map((n) => ({ ...n, read: true })));
  };

  const clearAllNotifications = () => {
    realtimeNotificationService.clearAllNotifications();
    setNotifications([]);
  };

  const updateSettings = (newSettings: Partial<NotificationSettings>) => {
    realtimeNotificationService.updateSettings(newSettings);
    setSettings(realtimeNotificationService.getSettings());
  };

  const testNotification = (type?: NotificationType) => {
    realtimeNotificationService.testNotification(type);
  };

  const unreadCount = notifications.filter((n) => !n.read).length;

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    settings,
    isInitialized,
    markAsRead,
    markAllAsRead,
    clearAllNotifications,
    updateSettings,
    testNotification,
  };

  return <NotificationContext.Provider value={value}>{children}</NotificationContext.Provider>;
};

export const useNotifications = (): NotificationContextType => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};
