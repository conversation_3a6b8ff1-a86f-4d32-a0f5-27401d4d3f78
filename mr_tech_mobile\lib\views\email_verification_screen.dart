import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../services/translation_service.dart';
import 'onboarding_screen.dart';
import '../widgets/text_styles.dart';

class EmailVerificationScreen extends StatefulWidget {
  const EmailVerificationScreen({super.key});

  @override
  State<EmailVerificationScreen> createState() => _EmailVerificationScreenState();
}

class _EmailVerificationScreenState extends State<EmailVerificationScreen> {
  final AuthService _authService = AuthService();
  bool _isEmailVerified = false;
  bool _isLoading = false;
  bool _canResendEmail = true;
  Timer? _timer;
  int _timeLeft = 60;
  
  @override
  void initState() {
    super.initState();
    // Check if email is already verified
    _checkEmailVerified();
    
    // Start timer to check email verification status periodically
    _timer = Timer.periodic(const Duration(seconds: 3), (_) {
      _checkEmailVerified();
    });
  }
  
  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }
  
  Future<void> _checkEmailVerified() async {
    if (_isLoading) return;
    
    setState(() {
      _isLoading = true;
    });
    
    try {
      final isVerified = await _authService.isEmailVerified();
      
      if (isVerified) {
        // Update verification status in Firestore
        await _authService.updateEmailVerificationStatus();
        
        // Cancel timer as verification is complete
        _timer?.cancel();
        
        setState(() {
          _isEmailVerified = true;
        });
        
        // Navigate to onboarding screen after a short delay
        await Future.delayed(const Duration(seconds: 2));
        if (mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const OnboardingScreen()),
          );
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: ${e.toString()}')),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
  
  Future<void> _resendVerificationEmail() async {
    if (!_canResendEmail) return;
    
    setState(() {
      _isLoading = true;
      _canResendEmail = false;
      _timeLeft = 60;
    });
    
    try {
      await _authService.sendEmailVerification();
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Verification email sent! Check your inbox.'),
          backgroundColor: Colors.green,
        ),
      );
      
      // Start cooldown timer for resend button
      _startResendCooldown();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: ${e.toString()}')),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
  
  void _startResendCooldown() {
    Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_timeLeft > 0) {
        if (mounted) {
          setState(() {
            _timeLeft--;
          });
        }
      } else {
        timer.cancel();
        if (mounted) {
          setState(() {
            _canResendEmail = true;
          });
        }
      }
    });
  }
  
  @override
  Widget build(BuildContext context) {
    final translationService = Provider.of<TranslationService>(context);
    final translate = translationService.translate;
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Simplified logo without shadow
                  Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.grey.shade300, width: 2),
                      color: colorScheme.surface,
                    ),
                    child: ClipOval(
                      child: Image.asset(
                        'assets/images/logo.png',
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Icon(
                            Icons.engineering,
                            size: 50,
                            color: colorScheme.primary,
                          );
                        },
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 40),
                  
                  // Title without gradient
                  Text(
                    translate('Verify Your Email'),
                    style: AppTextStyles.headingLarge(context, color: colorScheme.onSurface),
                    textAlign: TextAlign.center,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Status message card with light border
                  Card(
                    margin: EdgeInsets.zero,
                    elevation: 0,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                      side: BorderSide(color: Colors.grey.shade300),
                    ),
                    child: Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: _isEmailVerified 
                            ? Colors.green.withOpacity(0.1)
                            : colorScheme.surface,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: (_isEmailVerified ? Colors.green : colorScheme.primary).withOpacity(0.1),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              _isEmailVerified 
                                  ? Icons.check_circle_outline 
                                  : Icons.email_outlined,
                              size: 40,
                              color: _isEmailVerified 
                                  ? Colors.green 
                                  : colorScheme.primary,
                            ),
                          ),
                          
                          const SizedBox(height: 16),
                          
                          Text(
                            _isEmailVerified
                                ? translate('Email Verified Successfully!')
                                : translate('A verification link has been sent to your email address.'),
                            textAlign: TextAlign.center,
                            style: AppTextStyles.headingSmall(context, color: colorScheme.onSurface),
                          ),
                          
                          if (!_isEmailVerified) ...[
                            const SizedBox(height: 8),
                            Text(
                              translate('Please check your inbox and click the link to verify your email.'),
                              textAlign: TextAlign.center,
                              style: AppTextStyles.bodyMedium(
                                context,
                                color: colorScheme.onSurface.withOpacity(0.7),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 32),
                  
                  // Action buttons
                  if (!_isEmailVerified) ...[
                    // Refresh button
                    SizedBox(
                      width: double.infinity,
                      height: 52,
                      child: FilledButton.icon(
                        onPressed: _isLoading ? null : _checkEmailVerified,
                        style: FilledButton.styleFrom(
                          backgroundColor: colorScheme.primary,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          elevation: 0,
                        ),
                        icon: _isLoading
                            ? const SizedBox(
                                width: 16,
                                height: 16,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                            : const Icon(Icons.refresh, size: 20),
                        label: Text(
                          translate('Check Verification Status'),
                          style: AppTextStyles.buttonMedium(context, color: Colors.white),
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Resend button
                    SizedBox(
                      width: double.infinity,
                      height: 52,
                      child: OutlinedButton.icon(
                        onPressed: _canResendEmail ? _resendVerificationEmail : null,
                        style: OutlinedButton.styleFrom(
                          side: BorderSide(color: Colors.grey.shade300),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          backgroundColor: colorScheme.surface,
                        ),
                        icon: Icon(
                          Icons.send,
                          size: 20,
                          color: _canResendEmail 
                              ? colorScheme.primary 
                              : colorScheme.onSurface.withOpacity(0.5),
                        ),
                        label: Text(
                          _canResendEmail 
                              ? translate('Resend Verification Email') 
                              : translate('Resend in $_timeLeft seconds'),
                          style: AppTextStyles.buttonMedium(
                            context,
                            color: _canResendEmail 
                                ? colorScheme.primary 
                                : colorScheme.onSurface.withOpacity(0.5),
                          ),
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // Sign out option
                    TextButton(
                      onPressed: () async {
                        await _authService.signOut();
                        if (mounted) {
                          Navigator.of(context).pop();
                        }
                      },
                      child: Text(
                        translate('Back to Login'),
                        style: AppTextStyles.buttonMedium(context, color: colorScheme.primary),
                      ),
                    ),
                  ],
                  if (_isEmailVerified) ...[
                    // Success message
                    Text(
                      translate('Redirecting to app...'),
                      style: AppTextStyles.bodyMedium(
                        context,
                        color: colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // Loading indicator
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
} 