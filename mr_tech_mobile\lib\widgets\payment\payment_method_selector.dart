import 'package:flutter/material.dart';
import '../text_styles.dart';
import '../../services/translation_service.dart';

/// Payment method selection widget
class PaymentMethodSelector extends StatefulWidget {
  final String? selectedMethod;
  final Function(String) onMethodSelected;
  final bool enabled;

  const PaymentMethodSelector({
    super.key,
    this.selectedMethod,
    required this.onMethodSelected,
    this.enabled = true,
  });

  @override
  State<PaymentMethodSelector> createState() => _PaymentMethodSelectorState();
}

class _PaymentMethodSelectorState extends State<PaymentMethodSelector>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  List<PaymentMethodOption> get _paymentMethods {
    final translationService = TranslationService();
    final translate = translationService.translate;

    return [
      PaymentMethodOption(
        id: 'card',
        name: translate('Credit/Debit Card'),
        description: translate('Visa, Mastercard, American Express'),
        icon: Icons.credit_card,
        color: Colors.blue,
        isRecommended: true,
      ),
      PaymentMethodOption(
        id: 'wallet',
        name: translate('Mobile Wallet'),
        description: translate('Vodafone Cash, Orange Money, Etisalat Cash'),
        icon: Icons.account_balance_wallet,
        color: Colors.green,
        isComingSoon: true,
      ),
      PaymentMethodOption(
        id: 'bank',
        name: translate('Bank Transfer'),
        description: translate('Direct bank transfer'),
        icon: Icons.account_balance,
        color: Colors.purple,
        isComingSoon: true,
      ),
    ];
  }

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Card(
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
            side: BorderSide(color: Colors.grey.shade300),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: colorScheme.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.payment,
                        color: colorScheme.primary,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      TranslationService().translate('Select Payment Method'),
                      style: AppTextStyles.headingSmall(
                        context,
                        color: colorScheme.onSurface,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 20),

                // Payment Methods
                ...List.generate(_paymentMethods.length, (index) {
                  final method = _paymentMethods[index];
                  final isSelected = widget.selectedMethod == method.id;
                  
                  return Padding(
                    padding: EdgeInsets.only(
                      bottom: index < _paymentMethods.length - 1 ? 12 : 0,
                    ),
                    child: _buildPaymentMethodTile(
                      method,
                      isSelected,
                      colorScheme,
                    ),
                  );
                }),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentMethodTile(
    PaymentMethodOption method,
    bool isSelected,
    ColorScheme colorScheme,
  ) {
    final isEnabled = widget.enabled && !method.isComingSoon;
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected
              ? colorScheme.primary
              : method.isComingSoon
                  ? Colors.grey.shade300
                  : Colors.grey.shade300,
          width: isSelected ? 2 : 1,
        ),
        color: isSelected
            ? colorScheme.primary.withOpacity(0.05)
            : method.isComingSoon
                ? Colors.grey.shade50
                : colorScheme.surface,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isEnabled ? () => widget.onMethodSelected(method.id) : null,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Method Icon
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: method.isComingSoon
                        ? Colors.grey.shade200
                        : method.color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    method.icon,
                    color: method.isComingSoon
                        ? Colors.grey.shade400
                        : method.color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                
                // Method Details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            method.name,
                            style: AppTextStyles.buttonMedium(
                              context,
                              color: method.isComingSoon
                                  ? Colors.grey.shade600
                                  : colorScheme.onSurface,
                            ),
                          ),
                          if (method.isRecommended) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.green.shade100,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                TranslationService().translate('Recommended'),
                                style: AppTextStyles.bodySmall(
                                  context,
                                  color: Colors.green.shade700,
                                ),
                              ),
                            ),
                          ],
                          if (method.isComingSoon) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.grey.shade200,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                TranslationService().translate('Coming Soon'),
                                style: AppTextStyles.bodySmall(
                                  context,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        method.description,
                        style: AppTextStyles.bodySmall(
                          context,
                          color: method.isComingSoon
                              ? Colors.grey.shade500
                              : colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Selection Indicator
                AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected
                          ? colorScheme.primary
                          : Colors.grey.shade400,
                      width: 2,
                    ),
                    color: isSelected ? colorScheme.primary : Colors.transparent,
                  ),
                  child: isSelected
                      ? Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 16,
                        )
                      : null,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// Data class for payment method options
class PaymentMethodOption {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final Color color;
  final bool isRecommended;
  final bool isComingSoon;

  PaymentMethodOption({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    this.isRecommended = false,
    this.isComingSoon = false,
  });
}