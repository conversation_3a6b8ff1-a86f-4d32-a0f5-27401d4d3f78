name: Firebase CI/CD

on:
  push:
    branches:
      - main  # Production deployment
      - develop  # Staging deployment
  pull_request:
    branches:
      - main
      - develop

jobs:
  build_and_test:
    name: Build and Test
    runs-on: ubuntu-latest
    
    defaults:
      run:
        working-directory: web-portal
        
    steps:
      - name: Checkout repository
        uses: actions/checkout@v2
        
      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: web-portal/package-lock.json
          
      - name: Install dependencies
        run: npm ci
        
      - name: Lint code
        run: npm run lint || echo "Linting failed but continuing..."
        
      - name: Run tests
        run: npm test -- --watchAll=false
        
      - name: Build
        run: npm run build
        
      - name: Archive build artifacts
        uses: actions/upload-artifact@v2
        with:
          name: build
          path: web-portal/build
  
  deploy:
    name: Deploy to Firebase
    needs: build_and_test
    runs-on: ubuntu-latest
    if: github.event_name == 'push'
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v2
        
      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
          
      - name: Download build artifacts
        uses: actions/download-artifact@v2
        with:
          name: build
          path: web-portal/build
      
      - name: Install Firebase CLI
        run: npm install -g firebase-tools

      - name: Set Firebase project - Production
        if: github.ref == 'refs/heads/main'
        run: echo "FIREBASE_PROJECT=mr-tech-web-portal-prod" >> $GITHUB_ENV
        
      - name: Set Firebase project - Staging
        if: github.ref == 'refs/heads/develop'
        run: echo "FIREBASE_PROJECT=mr-tech-web-portal-staging" >> $GITHUB_ENV
        
      - name: Deploy to Firebase Hosting
        run: |
          cd web-portal
          firebase use ${{ env.FIREBASE_PROJECT }} --token ${{ secrets.FIREBASE_TOKEN }}
          firebase deploy --only hosting --token ${{ secrets.FIREBASE_TOKEN }}
        
      - name: Deploy Cloud Functions (Production only)
        if: github.ref == 'refs/heads/main'
        run: |
          cd web-portal
          firebase deploy --only functions --token ${{ secrets.FIREBASE_TOKEN }} 