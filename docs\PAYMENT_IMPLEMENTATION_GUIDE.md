# Payment Implementation Guide - Mr.Tech

This guide explains the complete payment implementation including Paymob callback endpoints and web portal payment management.

## 🎯 Overview

The payment system consists of:
1. **Mobile App**: Initiates payments using Paymob V2 API
2. **Firebase Cloud Functions**: Handles payment callbacks from Paymob
3. **Web Portal**: Displays and manages payment transactions
4. **Firestore Database**: Stores payment transaction data

## 🔧 Implementation Components

### 1. Firebase Cloud Functions (Payment Callbacks)

**Location**: `firebase-config/functions/src/paymentCallbacks.js`

Two main functions:
- `paymobTransactionProcessed`: Server-to-server notification from Paymob
- `paymobTransactionResponse`: Customer redirect after payment completion

**Callback URLs**:
- Transaction Processed: `https://us-central1-stopnow-be6b7.cloudfunctions.net/paymobTransactionProcessed`
- Transaction Response: `https://us-central1-stopnow-be6b7.cloudfunctions.net/paymobTransactionResponse`

### 2. Mobile App Integration

**Location**: `mr_tech_mobile/lib/services/payment_service.dart`

Updated to include callback URLs in the payment intention payload:
```dart
'notification_url': 'https://us-central1-stopnow-be6b7.cloudfunctions.net/paymobTransactionProcessed',
'redirection_url': 'https://us-central1-stopnow-be6b7.cloudfunctions.net/paymobTransactionResponse',
```

### 3. Web Portal Payment Management

**Location**: `web-portal/src/pages/PaymentsPage.tsx`

Features:
- View all payment transactions
- Filter by status, payment method, environment
- Search by transaction ID or request ID
- View detailed transaction information
- Payment summary statistics
- Export capabilities (permission-based)

### 4. Data Models

**Location**: `web-portal/src/types/payment.ts`

Comprehensive TypeScript interfaces for:
- PaymentTransaction
- PaymentSummary
- PaymentFilters
- RefundRequest/Response
- Paymob callback data structures

## 🚀 Deployment Steps

### Step 1: Deploy Firebase Functions

```bash
cd firebase-config/functions
chmod +x deploy-payment-callbacks.sh
./deploy-payment-callbacks.sh
```

### Step 2: Configure Paymob Dashboard

1. Go to your Paymob dashboard
2. Navigate to **Developers** > **Payment Integrations**
3. Select **Test mode**
4. Choose Integration ID **1164997**
5. Click **Edit**
6. Update the callback URLs:
   - **Transaction processed callback**: `https://us-central1-stopnow-be6b7.cloudfunctions.net/paymobTransactionProcessed`
   - **Transaction response callback**: `https://us-central1-stopnow-be6b7.cloudfunctions.net/paymobTransactionResponse`
7. Click **Submit**

### Step 3: Test the Integration

1. Make a test payment from the mobile app
2. Check Firebase Functions logs for callback processing
3. Verify transaction data in Firestore (`payment_transactions` collection)
4. Check the web portal Payments page for the transaction

## 📊 Database Structure

### Firestore Collections

#### `payment_transactions/{transactionId}`
```typescript
{
  id: string;                    // Document ID (paymob_{paymobTransactionId})
  paymobTransactionId: string;   // Paymob transaction ID
  requestId?: string;            // Associated service request ID
  amount: number;                // Transaction amount
  currency: string;              // Currency (EGP)
  status: PaymentStatus;         // completed | failed | pending | refunded
  paymentMethod: string;         // paymob | test
  merchantOrderId?: string;      // Merchant order ID
  integrationId?: string;        // Paymob integration ID
  isLive: boolean;              // Live vs test environment
  errorOccurred?: boolean;       // Whether an error occurred
  txnResponseCode?: string;      // Transaction response code
  dataMessage?: string;          // Error/success message
  createdAt: Timestamp;          // Transaction creation time
  processedAt: Timestamp;        // Callback processing time
  callbackData?: any;           // Full callback data for debugging
}
```

#### `payment_responses/{responseId}`
```typescript
{
  transactionId: string;         // Related transaction ID
  success: boolean;              // Payment success status
  paymentStatus: PaymentStatus;  // Processed payment status
  responseParams: any;           // Full response parameters
  createdAt: Timestamp;          // Response creation time
}
```

## 🔍 Monitoring and Debugging

### Firebase Functions Logs

View logs for payment callbacks:
```bash
firebase functions:log --only paymobTransactionProcessed,paymobTransactionResponse
```

### Mobile App Logs

Look for these debug messages:
- `🔔 Paymob Transaction Processed Callback received`
- `🔄 Paymob Transaction Response Callback received`
- `📦 INTENTION PAYLOAD (V2)`
- `🔗 Callback URLs configured`

### Web Portal

- Check the Payments page for transaction visibility
- Use filters to find specific transactions
- View detailed transaction information in the dialog

## 🛡️ Security Considerations

1. **HMAC Validation**: Consider implementing HMAC validation for callback authenticity
2. **Rate Limiting**: Implement rate limiting on callback endpoints
3. **Input Validation**: Validate all incoming callback data
4. **Error Handling**: Comprehensive error handling and logging
5. **Permissions**: Web portal payment access is permission-based

## 🔄 Payment Flow

1. **Mobile App**: User initiates payment
2. **Paymob**: Processes payment and sends callbacks
3. **Firebase Functions**: Receives and processes callbacks
4. **Firestore**: Stores transaction data
5. **Web Portal**: Displays transaction information
6. **Request Update**: Updates related service request status

## 📝 Testing Checklist

- [ ] Deploy Firebase Functions successfully
- [ ] Configure Paymob callback URLs
- [ ] Test payment from mobile app
- [ ] Verify callback processing in logs
- [ ] Check transaction data in Firestore
- [ ] View transaction in web portal
- [ ] Test different payment scenarios (success, failure)
- [ ] Verify request status updates

## 🆘 Troubleshooting

### Common Issues

1. **Callback not received**: Check Paymob dashboard configuration
2. **Function deployment fails**: Check Firebase CLI authentication
3. **Transaction not showing in portal**: Check Firestore permissions
4. **Mobile app payment fails**: Verify callback URLs in payload

### Support

For issues with this implementation, check:
1. Firebase Functions logs
2. Mobile app debug logs
3. Paymob dashboard transaction history
4. Firestore database console

## 📚 Additional Resources

- [Paymob API Documentation](https://developers.paymob.com/)
- [Firebase Functions Documentation](https://firebase.google.com/docs/functions)
- [Firestore Documentation](https://firebase.google.com/docs/firestore)
