import React from 'react';
import { <PERSON>ertCircle, CheckCircle, Clock, Loader2, Wifi, WifiOff } from 'lucide-react';
import { cn } from '../../lib/utils';
import { Button } from './button';
import { Card, CardContent } from './card';

// Loading spinner component
export interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  text?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  className = '',
  text,
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
  };

  return (
    <div className={cn('flex items-center justify-center gap-2', className)}>
      <Loader2 className={cn('animate-spin', sizeClasses[size])} />
      {text && <span className='text-sm text-muted-foreground'>{text}</span>}
    </div>
  );
};

// Progress bar component
export interface ProgressBarProps {
  progress: number;
  className?: string;
  showPercentage?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'success' | 'warning' | 'error';
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  className = '',
  showPercentage = true,
  size = 'md',
  variant = 'default',
}) => {
  const sizeClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3',
  };

  const variantClasses = {
    default: 'bg-primary',
    success: 'bg-green-500',
    warning: 'bg-yellow-500',
    error: 'bg-red-500',
  };

  const clampedProgress = Math.max(0, Math.min(100, progress));

  return (
    <div className={cn('w-full', className)}>
      <div className={cn('bg-gray-200 rounded-full overflow-hidden', sizeClasses[size])}>
        <div
          className={cn(
            'transition-all duration-300 ease-out rounded-full',
            variantClasses[variant],
            sizeClasses[size],
          )}
          style={{ width: `${clampedProgress}%` }}
        />
      </div>
      {showPercentage && (
        <div className='flex justify-between items-center mt-1'>
          <span className='text-xs text-muted-foreground'>{Math.round(clampedProgress)}%</span>
        </div>
      )}
    </div>
  );
};

// Loading overlay component
export interface LoadingOverlayProps {
  isLoading: boolean;
  text?: string;
  progress?: number;
  stage?: string;
  className?: string;
  children: React.ReactNode;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isLoading,
  text = 'Loading...',
  progress,
  stage,
  className = '',
  children,
}) => {
  return (
    <div className={cn('relative', className)}>
      {children}
      {isLoading && (
        <div className='absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50'>
          <Card className='p-6 min-w-[200px]'>
            <CardContent className='space-y-4'>
              <LoadingSpinner size='lg' />
              <div className='text-center space-y-2'>
                {stage && <p className='text-sm font-medium'>{stage}</p>}
                <p className='text-sm text-muted-foreground'>{text}</p>
                {typeof progress === 'number' && <ProgressBar progress={progress} size='sm' />}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
};

// Button loading states
export interface LoadingButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  isLoading?: boolean;
  loadingText?: string;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  children: React.ReactNode;
}

export const LoadingButton: React.FC<LoadingButtonProps> = ({
  isLoading = false,
  loadingText,
  children,
  disabled,
  className,
  ...props
}) => {
  return (
    <Button disabled={disabled || isLoading} className={cn(className)} {...props}>
      {isLoading && <Loader2 className='w-4 h-4 animate-spin mr-2' />}
      {isLoading ? loadingText || 'Loading...' : children}
    </Button>
  );
};

// Connection status indicator
export interface ConnectionStatusProps {
  isConnected: boolean;
  className?: string;
  showText?: boolean;
}

export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  isConnected,
  className = '',
  showText = true,
}) => {
  return (
    <div className={cn('flex items-center gap-2', className)}>
      {isConnected ? (
        <>
          <Wifi className='w-4 h-4 text-green-500' />
          {showText && <span className='text-sm text-green-600'>Connected</span>}
        </>
      ) : (
        <>
          <WifiOff className='w-4 h-4 text-red-500' />
          {showText && <span className='text-sm text-red-600'>Disconnected</span>}
        </>
      )}
    </div>
  );
};

// Status indicator with different states
export interface StatusIndicatorProps {
  status: 'loading' | 'success' | 'error' | 'warning' | 'idle';
  text?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export const StatusIndicator: React.FC<StatusIndicatorProps> = ({
  status,
  text,
  className = '',
  size = 'md',
}) => {
  const sizeClasses = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5',
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'loading':
        return <Loader2 className={cn('animate-spin text-blue-500', sizeClasses[size])} />;
      case 'success':
        return <CheckCircle className={cn('text-green-500', sizeClasses[size])} />;
      case 'error':
        return <AlertCircle className={cn('text-red-500', sizeClasses[size])} />;
      case 'warning':
        return <AlertCircle className={cn('text-yellow-500', sizeClasses[size])} />;
      case 'idle':
        return <Clock className={cn('text-gray-400', sizeClasses[size])} />;
      default:
        return null;
    }
  };

  const getStatusText = () => {
    if (text) return text;

    switch (status) {
      case 'loading':
        return 'Loading...';
      case 'success':
        return 'Success';
      case 'error':
        return 'Error';
      case 'warning':
        return 'Warning';
      case 'idle':
        return 'Idle';
      default:
        return '';
    }
  };

  return (
    <div className={cn('flex items-center gap-2', className)}>
      {getStatusIcon()}
      {text !== '' && (
        <span
          className={cn('text-sm', {
            'text-blue-600': status === 'loading',
            'text-green-600': status === 'success',
            'text-red-600': status === 'error',
            'text-yellow-600': status === 'warning',
            'text-gray-500': status === 'idle',
          })}
        >
          {getStatusText()}
        </span>
      )}
    </div>
  );
};

// Loading state for empty states
export interface EmptyStateProps {
  title: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
    isLoading?: boolean;
  };
  icon?: React.ReactNode;
  className?: string;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  title,
  description,
  action,
  icon,
  className = '',
}) => {
  return (
    <div className={cn('flex flex-col items-center justify-center p-8 text-center', className)}>
      {icon && <div className='mb-4 text-gray-400'>{icon}</div>}
      <h3 className='text-lg font-semibold text-gray-900 mb-2'>{title}</h3>
      {description && <p className='text-sm text-gray-500 mb-4 max-w-sm'>{description}</p>}
      {action && (
        <LoadingButton onClick={action.onClick} isLoading={action.isLoading} variant='outline'>
          {action.label}
        </LoadingButton>
      )}
    </div>
  );
};
