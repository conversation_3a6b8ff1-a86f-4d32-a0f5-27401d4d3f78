import type { CreateUserInput, UpdateUserInput, UserRole } from '../types/user';

// Validation error interface
export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

// Validation result interface
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

// Email validation regex
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

// Phone number validation regex (international format)
const PHONE_REGEX = /^\+?[\d\s\-()]{10,}$/;

// Password validation regex (at least 8 chars, 1 uppercase, 1 lowercase, 1 number)
const PASSWORD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;

// Name validation regex (letters, spaces, hyphens, apostrophes)
const NAME_REGEX = /^[a-zA-Z\s\-']{2,50}$/;

// AnyDesk ID validation regex
const ANYDESK_REGEX = /^\d{9}$/;

class ValidationService {
  // Validate email format
  validateEmail(email: string): ValidationError | null {
    if (!email) {
      return { field: 'email', message: 'Email is required', code: 'REQUIRED' };
    }

    if (!EMAIL_REGEX.test(email)) {
      return { field: 'email', message: 'Invalid email format', code: 'INVALID_FORMAT' };
    }

    if (email.length > 254) {
      return { field: 'email', message: 'Email is too long', code: 'TOO_LONG' };
    }

    return null;
  }

  // Validate password strength
  validatePassword(password: string): ValidationError | null {
    if (!password) {
      return { field: 'password', message: 'Password is required', code: 'REQUIRED' };
    }

    if (password.length < 8) {
      return {
        field: 'password',
        message: 'Password must be at least 8 characters long',
        code: 'TOO_SHORT',
      };
    }

    if (!PASSWORD_REGEX.test(password)) {
      return {
        field: 'password',
        message:
          'Password must contain at least one uppercase letter, one lowercase letter, and one number',
        code: 'WEAK_PASSWORD',
      };
    }

    if (password.length > 128) {
      return { field: 'password', message: 'Password is too long', code: 'TOO_LONG' };
    }

    return null;
  }

  // Validate name
  validateName(name: string): ValidationError | null {
    if (!name) {
      return { field: 'name', message: 'Name is required', code: 'REQUIRED' };
    }

    if (!NAME_REGEX.test(name)) {
      return {
        field: 'name',
        message: 'Name can only contain letters, spaces, hyphens, and apostrophes',
        code: 'INVALID_FORMAT',
      };
    }

    if (name.length < 2) {
      return {
        field: 'name',
        message: 'Name must be at least 2 characters long',
        code: 'TOO_SHORT',
      };
    }

    if (name.length > 50) {
      return { field: 'name', message: 'Name is too long', code: 'TOO_LONG' };
    }

    return null;
  }

  // Validate phone number
  validatePhoneNumber(phone: string): ValidationError | null {
    if (!phone) {
      return null; // Phone is optional
    }

    if (!PHONE_REGEX.test(phone)) {
      return {
        field: 'phone_number',
        message: 'Invalid phone number format',
        code: 'INVALID_FORMAT',
      };
    }

    if (phone.length > 20) {
      return { field: 'phone_number', message: 'Phone number is too long', code: 'TOO_LONG' };
    }

    return null;
  }

  // Validate user role
  validateUserRole(role: string): ValidationError | null {
    const validRoles: UserRole[] = ['admin', 'technician', 'customer'];

    if (!role) {
      return { field: 'role', message: 'Role is required', code: 'REQUIRED' };
    }

    if (!validRoles.includes(role as UserRole)) {
      return { field: 'role', message: 'Invalid user role', code: 'INVALID_VALUE' };
    }

    return null;
  }

  // Validate AnyDesk ID
  validateanydesk_id(anydesk_id: string): ValidationError | null {
    if (!anydesk_id) {
      return null; // AnyDesk ID is optional
    }

    if (!ANYDESK_REGEX.test(anydesk_id)) {
      return {
        field: 'anydesk_id',
        message: 'AnyDesk ID must be 9 digits',
        code: 'INVALID_FORMAT',
      };
    }

    return null;
  }

  // Validate specialties array
  validateSpecialties(specialties: string[]): ValidationError | null {
    if (!specialties || !Array.isArray(specialties)) {
      return null; // Specialties are optional
    }

    if (specialties.length > 10) {
      return {
        field: 'specialties',
        message: 'Too many specialties (maximum 10)',
        code: 'TOO_MANY',
      };
    }

    for (const specialty of specialties) {
      if (typeof specialty !== 'string' || specialty.length < 2 || specialty.length > 50) {
        return {
          field: 'specialties',
          message: 'Each specialty must be 2-50 characters long',
          code: 'INVALID_FORMAT',
        };
      }
    }

    return null;
  }

  // Validate permissions array
  validatePermissions(permissions: string[]): ValidationError | null {
    if (!permissions || !Array.isArray(permissions)) {
      return null; // Permissions are optional
    }

    if (permissions.length > 50) {
      return {
        field: 'permissions',
        message: 'Too many permissions (maximum 50)',
        code: 'TOO_MANY',
      };
    }

    for (const permission of permissions) {
      if (typeof permission !== 'string' || permission.length < 2 || permission.length > 100) {
        return {
          field: 'permissions',
          message: 'Each permission must be 2-100 characters long',
          code: 'INVALID_FORMAT',
        };
      }
    }

    return null;
  }

  // Validate admin level
  validateAdminLevel(adminLevel: string): ValidationError | null {
    const validLevels = ['super', 'standard', 'limited'];

    if (!adminLevel) {
      return null; // Admin level is optional (defaults to 'standard')
    }

    if (!validLevels.includes(adminLevel)) {
      return { field: 'admin_level', message: 'Invalid admin level', code: 'INVALID_VALUE' };
    }

    return null;
  }

  // Validate preferred language
  validatePreferredLanguage(language: string): ValidationError | null {
    const validLanguages = ['en', 'ar'];

    if (!language) {
      return null; // Language is optional (defaults to 'en')
    }

    if (!validLanguages.includes(language)) {
      return { field: 'preferred_language', message: 'Invalid language', code: 'INVALID_VALUE' };
    }

    return null;
  }

  // Validate create user input
  validateCreateUserInput(input: CreateUserInput): ValidationResult {
    const errors: ValidationError[] = [];

    // Required field validations
    const emailError = this.validateEmail(input.email);
    if (emailError) errors.push(emailError);

    const nameError = this.validateName(input.name);
    if (nameError) errors.push(nameError);

    const roleError = this.validateUserRole(input.role);
    if (roleError) errors.push(roleError);

    // Optional field validations
    if (input.password) {
      const passwordError = this.validatePassword(input.password);
      if (passwordError) errors.push(passwordError);
    }

    if (input.phone_number) {
      const phoneError = this.validatePhoneNumber(input.phone_number);
      if (phoneError) errors.push(phoneError);
    }

    if (input.anydesk_id) {
      const anydeskError = this.validateanydesk_id(input.anydesk_id);
      if (anydeskError) errors.push(anydeskError);
    }

    if (input.specialties) {
      const specialtiesError = this.validateSpecialties(input.specialties);
      if (specialtiesError) errors.push(specialtiesError);
    }

    if (input.permissions) {
      const permissionsError = this.validatePermissions(input.permissions);
      if (permissionsError) errors.push(permissionsError);
    }

    if (input.admin_level) {
      const adminLevelError = this.validateAdminLevel(input.admin_level);
      if (adminLevelError) errors.push(adminLevelError);
    }

    if (input.preferred_language) {
      const languageError = this.validatePreferredLanguage(input.preferred_language);
      if (languageError) errors.push(languageError);
    }

    // Role-specific validations
    if (input.role === 'technician' && (!input.specialties || input.specialties.length === 0)) {
      errors.push({
        field: 'specialties',
        message: 'Technicians must have at least one specialty',
        code: 'REQUIRED',
      });
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // Validate update user input
  validateUpdateUserInput(input: UpdateUserInput): ValidationResult {
    const errors: ValidationError[] = [];

    // Only validate fields that are being updated
    if (input.name !== undefined) {
      const nameError = this.validateName(input.name);
      if (nameError) errors.push(nameError);
    }

    if (input.phone_number !== undefined) {
      const phoneError = this.validatePhoneNumber(input.phone_number);
      if (phoneError) errors.push(phoneError);
    }

    if (input.anydesk_id !== undefined) {
      const anydeskError = this.validateanydesk_id(input.anydesk_id);
      if (anydeskError) errors.push(anydeskError);
    }

    if (input.specialties !== undefined) {
      const specialtiesError = this.validateSpecialties(input.specialties);
      if (specialtiesError) errors.push(specialtiesError);
    }

    if (input.permissions !== undefined) {
      const permissionsError = this.validatePermissions(input.permissions);
      if (permissionsError) errors.push(permissionsError);
    }

    if (input.admin_level !== undefined) {
      const adminLevelError = this.validateAdminLevel(input.admin_level);
      if (adminLevelError) errors.push(adminLevelError);
    }

    if (input.preferred_language !== undefined) {
      const languageError = this.validatePreferredLanguage(input.preferred_language);
      if (languageError) errors.push(languageError);
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}

// Export singleton instance
export default new ValidationService();
