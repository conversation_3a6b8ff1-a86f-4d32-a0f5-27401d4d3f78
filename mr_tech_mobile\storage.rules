rules_version = '2';

// Craft rules based on data in your Firestore database
// allow write: if firestore.get(
//    /databases/(default)/documents/users/$(request.auth.uid)).data.isAdmin;
service firebase.storage {
  match /b/{bucket}/o {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }

    function isAdmin() {
      return isAuthenticated() && (
        firestore.exists(/databases/(default)/documents/admins/$(request.auth.uid)) || 
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'admin'
      );
    }

    function isRequestParticipant(requestId) {
      // First try service_requests collection
      let serviceRequest = firestore.get(/databases/(default)/documents/service_requests/$(requestId));
      
      if (serviceRequest.exists) {
        return isAuthenticated() && (
          serviceRequest.data.customerId == request.auth.uid ||
          serviceRequest.data.customer_id == request.auth.uid ||
          serviceRequest.data.technicianId == request.auth.uid ||
          serviceRequest.data.technician_id == request.auth.uid ||
          isAdmin()
        );
      }
      
      // Fallback to requests collection
      let request = firestore.get(/databases/(default)/documents/requests/$(requestId));
      return isAuthenticated() && (
        request.data.clientId == request.auth.uid ||
        request.data.assignedTo == request.auth.uid ||
        isAdmin()
      );
    }

    // Chat files - mobile path
    match /chat_files/{requestId}/{fileName} {
      allow read: if isRequestParticipant(requestId);
      allow write: if isRequestParticipant(requestId) &&
        // Limit file size to 10MB
        request.resource.size <= 10 * 1024 * 1024 &&
        // Only allow certain file types
        (request.resource.contentType.matches('image/.*') ||
         request.resource.contentType.matches('application/pdf') ||
         request.resource.contentType.matches('text/plain'));
    }

    // Profile images - mobile path
    match /profile_images/{userId}/{fileName} {
      allow read: if true;
      allow write: if isAuthenticated() && 
        (request.auth.uid == userId || isAdmin()) &&
        request.resource.size <= 5 * 1024 * 1024 && // 5MB max
        request.resource.contentType.matches('image/.*');
    }

    // Profile images - web path
    match /profile-images/{userId}/{fileName} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && 
        (request.auth.uid == userId || isAdmin()) &&
        request.resource.size <= 5 * 1024 * 1024; // 5MB max
    }

    // Service images - mobile path
    match /service_images/{serviceId}/{fileName} {
      allow read: if true;
      allow write: if isAuthenticated() && isAdmin() &&
        request.resource.size <= 5 * 1024 * 1024 && // 5MB max
        request.resource.contentType.matches('image/.*');
    }

    // Service images - web path
    match /service-images/{serviceId}/{fileName} {
      allow read: if true;
      allow write: if isAuthenticated() && isAdmin() &&
        request.resource.size <= 5 * 1024 * 1024; // 5MB max
    }

    // Request images - web path
    match /request-images/{requestId}/{fileName} {
      allow read: if isRequestParticipant(requestId);
      allow create: if isAuthenticated() && isRequestParticipant(requestId);
      allow update, delete: if isAdmin();
    }

    // Default deny
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
