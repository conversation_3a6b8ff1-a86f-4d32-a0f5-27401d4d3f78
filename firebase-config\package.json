{"name": "web-portal", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.7", "@mui/lab": "^5.0.0-alpha.165", "@mui/material": "^5.15.7", "@mui/system": "^5.17.1", "@mui/x-date-pickers": "^6.20.2", "@reduxjs/toolkit": "^2.1.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.6.7", "date-fns": "^2.30.0", "emoji-picker-react": "^4.12.2", "firebase": "^10.8.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-redux": "^9.1.0", "react-router-dom": "^6.22.0", "react-scripts": "5.0.1", "recharts": "^2.15.3", "socket.io-client": "^4.7.4", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-app-rewired start", "build": "react-app-rewired build", "test": "react-app-rewired test", "eject": "react-scripts eject", "lint": "eslint src/**/*.{js,jsx}", "deploy": "npm run build && firebase deploy", "deploy:hosting": "npm run build && firebase deploy --only hosting", "deploy:staging": "npm run build && firebase use staging && firebase deploy", "deploy:production": "npm run build && firebase use production && firebase deploy", "emulate": "firebase emulators:start", "fix-chat-active": "node src/scripts/fix-chat-active.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"customize-cra": "^1.0.0", "dotenv": "^16.5.0", "react-app-rewired": "^2.2.1"}}