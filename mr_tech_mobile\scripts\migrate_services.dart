import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';

/// Standalone script to migrate services from camelCase to snake_case
/// Run this to add snake_case fields to your existing Firestore services
void main() async {
  print('🚀 SERVICES MIGRATION SCRIPT');
  print('═' * 50);
  print('This script will add snake_case fields to your existing services');
  print('Your original camelCase fields will be PRESERVED');
  print('');

  try {
    // Initialize Firebase
    print('🔧 Initializing Firebase...');
    await Firebase.initializeApp();
    print('✅ Firebase initialized successfully');
    print('');

    final firestore = FirebaseFirestore.instance;

    // Check current status
    print('📊 BEFORE MIGRATION - Current Services:');
    await _printServicesStatus(firestore);
    print('');

    // Ask for confirmation
    print('⚠️  IMPORTANT: This will modify your Firestore database');
    print('   - Adds snake_case fields alongside existing camelCase fields');
    print('   - Does NOT remove any existing data');
    print('   - Safe operation with rollback capability');
    print('');
    stdout.write('Do you want to proceed? (y/N): ');
    final input = stdin.readLineSync() ?? 'n';

    if (input.toLowerCase() != 'y' && input.toLowerCase() != 'yes') {
      print('❌ Migration cancelled by user');
      exit(0);
    }

    print('');
    print('🔄 STARTING MIGRATION...');

    // Run the migration
    await _migrateServices(firestore);

    print('');
    print('📊 AFTER MIGRATION - Updated Services:');
    await _printServicesStatus(firestore);

    print('');
    print('🔍 VALIDATING MIGRATION...');
    final isValid = await _validateMigration(firestore);

    if (isValid) {
      print('');
      print('🎉 MIGRATION COMPLETED SUCCESSFULLY!');
      print('═' * 50);
      print('✅ All services now have both camelCase AND snake_case fields');
      print('✅ Your app will work with both field formats');
      print('✅ ServiceModel will prefer snake_case but fallback to camelCase');
      print('✅ No data was lost or removed');
      print('');
      print('🧪 VERIFICATION:');
      print('- Load your app and check services screen');
      print('- Services should load normally');
      print('- Debug console will show field usage patterns');
      print('- Both field formats are now available');
    } else {
      print('');
      print('⚠️ MIGRATION NEEDS ATTENTION');
      print('Some services may need manual review');
    }
  } catch (e) {
    print('');
    print('❌ ERROR DURING MIGRATION: $e');
    print('Your original data is safe');
    exit(1);
  }
}

/// Print current status of services
Future<void> _printServicesStatus(FirebaseFirestore firestore) async {
  try {
    final servicesCollection = firestore.collection('services');
    final querySnapshot = await servicesCollection.get();

    print('   Total Services: ${querySnapshot.docs.length}');

    int hasSnakeCase = 0;
    int hasCamelCaseOnly = 0;

    for (final doc in querySnapshot.docs) {
      final data = doc.data();

      // Check for snake_case fields
      final hasSnakeCaseFields =
          data.containsKey('base_price') ||
          data.containsKey('is_active') ||
          data.containsKey('estimated_duration');

      if (hasSnakeCaseFields) {
        hasSnakeCase++;
      } else {
        hasCamelCaseOnly++;
      }
    }

    print('   - With snake_case fields: $hasSnakeCase');
    print('   - CamelCase only: $hasCamelCaseOnly');
    print(
      '   - Migration progress: ${querySnapshot.docs.isNotEmpty ? (hasSnakeCase / querySnapshot.docs.length * 100).round() : 0}%',
    );
  } catch (e) {
    print('   Error getting status: $e');
  }
}

/// Migrate all services to include snake_case fields
Future<void> _migrateServices(FirebaseFirestore firestore) async {
  final servicesCollection = firestore.collection('services');
  final querySnapshot = await servicesCollection.get();

  int migrated = 0;
  int alreadyMigrated = 0;
  int errors = 0;

  for (final doc in querySnapshot.docs) {
    try {
      final data = doc.data();
      final updates = <String, dynamic>{};
      bool needsUpdate = false;

      // Add snake_case version of basePrice
      if (data.containsKey('basePrice') && !data.containsKey('base_price')) {
        updates['base_price'] = data['basePrice'];
        needsUpdate = true;
      }

      // Add snake_case version of isActive
      if (data.containsKey('isActive') && !data.containsKey('is_active')) {
        updates['is_active'] = data['isActive'];
        needsUpdate = true;
      }

      // Add snake_case version of estimatedDuration
      if (data.containsKey('estimatedDuration') &&
          !data.containsKey('estimated_duration')) {
        updates['estimated_duration'] = data['estimatedDuration'];
        needsUpdate = true;
      }

      // Add snake_case version of imageUrl if it exists
      if (data.containsKey('imageUrl') && !data.containsKey('image_url')) {
        updates['image_url'] = data['imageUrl'];
        needsUpdate = true;
      }

      // Add created_at if missing
      if (!data.containsKey('created_at') && !data.containsKey('createdAt')) {
        updates['created_at'] = FieldValue.serverTimestamp();
        needsUpdate = true;
      } else if (data.containsKey('createdAt') &&
          !data.containsKey('created_at')) {
        updates['created_at'] = data['createdAt'];
        needsUpdate = true;
      }

      // Add updated_at
      if (!data.containsKey('updated_at')) {
        updates['updated_at'] = FieldValue.serverTimestamp();
        needsUpdate = true;
      }

      // Perform update if needed
      if (needsUpdate) {
        await doc.reference.update(updates);
        migrated++;

        // Get service name for logging
        String serviceName = 'Unknown';
        if (data['name'] is String) {
          serviceName = data['name'];
        } else if (data['name'] is Map) {
          final nameMap = data['name'] as Map;
          serviceName = nameMap['en'] ?? nameMap.values.first ?? 'Unknown';
        }

        print('   ✅ Migrated: $serviceName');
        final fieldList = updates.keys.join(', ');
        print('      Added: $fieldList');
      } else {
        alreadyMigrated++;
        print('   ⏭️ Already migrated: ${doc.id}');
      }
    } catch (e) {
      errors++;
      print('   ❌ Error migrating ${doc.id}: $e');
    }
  }

  print('');
  print('📊 MIGRATION SUMMARY:');
  print('   - Total services: ${querySnapshot.docs.length}');
  print('   - Migrated: $migrated');
  print('   - Already migrated: $alreadyMigrated');
  print('   - Errors: $errors');
}

/// Validate that migration completed successfully
Future<bool> _validateMigration(FirebaseFirestore firestore) async {
  try {
    final servicesCollection = firestore.collection('services');
    final querySnapshot = await servicesCollection.get();

    int valid = 0;
    int invalid = 0;

    for (final doc in querySnapshot.docs) {
      final data = doc.data();

      // Check required snake_case fields
      final hasBasePrice = data.containsKey('base_price');
      final hasIsActive = data.containsKey('is_active');
      final hasEstimatedDuration = data.containsKey('estimated_duration');
      final hasTimestamp =
          data.containsKey('created_at') || data.containsKey('updated_at');

      if (hasBasePrice && hasIsActive && hasEstimatedDuration && hasTimestamp) {
        valid++;
      } else {
        invalid++;
        print('   ❌ Service ${doc.id} missing some snake_case fields');
      }
    }

    print('   - Valid services: $valid');
    print('   - Invalid services: $invalid');

    return invalid == 0;
  } catch (e) {
    print('   Error during validation: $e');
    return false;
  }
}
