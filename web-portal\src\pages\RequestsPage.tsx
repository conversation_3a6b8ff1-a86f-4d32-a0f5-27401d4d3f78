import React, { useC<PERSON>back, useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { usePermissions } from '../hooks/usePermissions';
import PermissionGate from '../components/auth/PermissionGate';
import { Permission } from '../types/permissions';
import {
  AlertCircle,
  Bell,
  Calendar,
  CheckCircle,
  Clock,
  CreditCard,
  DollarSign,
  Edit,
  Eye,
  FileText,
  Filter,
  MoreHorizontal,
  PlayCircle,
  Plus,
  RefreshCw,
  Search,
  Star,
  Trash2,
  User,
  UserCheck,
  Wifi,
  WifiOff,
  X,
  XCircle,
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../components/ui/dropdown-menu';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../components/ui/dialog';
import { Collapsible, CollapsibleContent } from '../components/ui/collapsible';

// Import services and types
import requestService from '../services/requestService';
import technicianService from '../services/technicianService';
import serviceService from '../services/serviceService';
import statusNotificationService, {
  type StatusChangeNotification,
} from '../services/statusNotificationService';
import {
  type RequestFilters as RequestFiltersType,
  type RequestModel,
  RequestStatus,
} from '../types/request.js';
import { type TechnicianModel } from '../types/technician.js';
import { type ServiceModel } from '../types/service.js';
import StatusUpdateDialog from '../components/requests/StatusUpdateDialog';
import RequestDetailsDialog from '../components/requests/RequestDetailsDialog';
import {
  useRealtimeRequests,
  useRealtimeRequestsByStatus,
  useRealtimeStatusHealth,
  useRealtimeTechnicianRequests,
} from '../hooks/useRealtimeStatus';
import { Pagination, usePagination } from '../components/ui/pagination';
import { SkeletonCard, SkeletonStats, SkeletonTable } from '../components/ui/skeleton';

interface RequestFilters {
  status: string;
  technician: string;
  service: string;
  search: string;
  isPaid: string;
}

const RequestsPage: React.FC = () => {
  const { user } = useAuth();
  const { hasPermission } = usePermissions();
  const navigate = useNavigate();

  const [requests, setRequests] = useState<RequestModel[]>([]);
  const [filteredRequests, setFilteredRequests] = useState<RequestModel[]>([]);
  const [totalRequests, setTotalRequests] = useState(0);
  const [technicians, setTechnicians] = useState<TechnicianModel[]>([]);
  const [services, setServices] = useState<ServiceModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Pagination
  const {
    currentPage,
    pageSize,
    handlePageChange,
    handlePageSizeChange,
    resetPagination,
    createPaginationInfo,
  } = usePagination(20);
  const [errorBanner, setErrorBanner] = useState<string | null>(null);
  const [isRealtimeEnabled, setIsRealtimeEnabled] = useState(false);
  const [notifications, setNotifications] = useState<StatusChangeNotification[]>([]);
  const [activeTab, setActiveTab] = useState('requests');
  const [showFilters, setShowFilters] = useState(false);

  // Dialog states
  const [selectedRequest, setSelectedRequest] = useState<RequestModel | null>(null);
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [requestToDelete, setRequestToDelete] = useState<RequestModel | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const [filters, setFilters] = useState<RequestFilters>({
    status: 'all',
    technician: 'all',
    service: 'all',
    search: '',
    isPaid: 'all',
  });

  // Real-time hooks - always call hooks unconditionally
  const { requests: realtimeAllRequests } = useRealtimeRequests();
  const { requests: realtimeTechnicianRequests } = useRealtimeTechnicianRequests(user?.uid || '');

  const { requests: realtimePendingRequests } = useRealtimeRequestsByStatus(RequestStatus.PENDING);
  const { requests: realtimeInProgressRequests } = useRealtimeRequestsByStatus(
    RequestStatus.IN_PROGRESS,
  );
  const { health } = useRealtimeStatusHealth();

  // Helper function to handle multilingual fields
  const getLocalizedText = useCallback((text: unknown, fallback: string = ''): string => {
    // Handle null or undefined
    if (text == null) {
      return fallback;
    }

    // Handle string values
    if (typeof text === 'string') {
      return text;
    }

    // Handle multilingual objects
    if (typeof text === 'object' && text !== null) {
      // Check if it has the expected structure
      if (
        typeof (text as { en?: string; ar?: string }).en === 'string' ||
        typeof (text as { en?: string; ar?: string }).ar === 'string'
      ) {
        return (
          (text as { en?: string; ar?: string }).en ||
          (text as { en?: string; ar?: string }).ar ||
          fallback
        );
      }

      // If it's an object but not the expected structure, try to convert to string
      try {
        return String(text) || fallback;
      } catch {
        return fallback;
      }
    }

    // For any other type, try to convert to string
    try {
      return String(text) || fallback;
    } catch {
      return fallback;
    }
  }, []);

  // Load functions defined with useCallback
  const loadRequests = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      setErrorBanner(null);

      const requestFilters: RequestFiltersType = {};

      // Apply role-based filtering
      if (!hasPermission(Permission.VIEW_ALL_REQUESTS) && user?.uid) {
        if (user.role === 'technician') {
          requestFilters.technician_id = user.uid;
        } else if (user.role === 'customer') {
          requestFilters.customer_id = user.uid;
        }
      }

      // Apply UI filters to the request filters
      if (filters.status !== 'all') {
        requestFilters.status = [filters.status as RequestStatus];
      }

      if (filters.technician !== 'all') {
        requestFilters.technician_id = filters.technician;
      }

      if (filters.isPaid !== 'all') {
        requestFilters.is_paid = filters.isPaid === 'true';
      }

      // Use search if provided, otherwise use filtered pagination
      let response;
      if (filters.search.trim()) {
        response = await requestService.searchRequests(
          filters.search.trim(),
          requestFilters,
          currentPage,
          pageSize,
        );
      } else {
        response = await requestService.getRequestsPaginated(requestFilters, currentPage, pageSize);
      }

      setRequests(response.requests);
      setTotalRequests(response.total);
      setFilteredRequests(response.requests); // Since we're filtering on the server side now
    } catch (err) {
      console.error('Error loading requests:', err);
      const errorMessage = `Failed to load requests: ${err instanceof Error ? err.message : 'Unknown error'}`;
      setErrorBanner(errorMessage);
      setError(errorMessage);
      // Clear error banner after 10 seconds
      setTimeout(() => setErrorBanner(null), 10000);
    } finally {
      setLoading(false);
    }
  }, [hasPermission, user?.uid, user?.role, filters, currentPage, pageSize]);

  const loadTechnicians = useCallback(async () => {
    try {
      const techniciansData = await technicianService.getAll({
        orderBy: { field: 'name', direction: 'asc' },
      });
      setTechnicians(techniciansData);
    } catch (err) {
      console.error('Error loading technicians:', err);
      const errorMessage = `Failed to load technicians: ${err instanceof Error ? err.message : 'Unknown error'}`;
      setErrorBanner(errorMessage);
      setTimeout(() => setErrorBanner(null), 8000);
    }
  }, []);

  const loadServices = useCallback(async () => {
    try {
      const servicesData = await serviceService.getActiveServices();
      setServices(servicesData);
    } catch (err) {
      console.error('Error loading services:', err);
      const errorMessage = `Failed to load services: ${err instanceof Error ? err.message : 'Unknown error'}`;
      setErrorBanner(errorMessage);
      setTimeout(() => setErrorBanner(null), 8000);
    }
  }, []);

  // Combine real-time data based on user role
  const combinedRealtimeRequests = useMemo(() => {
    if (user?.role === 'technician') {
      // Combine technician's assigned requests with pending requests, avoiding duplicates
      const requestMap = new Map();

      // Add technician's assigned requests
      realtimeTechnicianRequests.forEach((request) => {
        requestMap.set(request.id, request);
      });

      // Add pending requests (only if not already assigned to this technician)
      realtimePendingRequests.forEach((request) => {
        if (!requestMap.has(request.id)) {
          requestMap.set(request.id, request);
        }
      });

      return Array.from(requestMap.values());
    }
    return realtimeAllRequests;
  }, [user?.role, realtimeTechnicianRequests, realtimePendingRequests, realtimeAllRequests]);

  const applyFilters = useCallback(() => {
    let filtered = [...requests];

    // Apply status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter((request) => request.status === filters.status);
    }

    // Apply technician filter
    if (filters.technician !== 'all') {
      if (filters.technician === 'unassigned') {
        filtered = filtered.filter((request) => !request.technician_id);
      } else {
        filtered = filtered.filter((request) => request.technician_id === filters.technician);
      }
    }

    // Apply service filter
    if (filters.service !== 'all') {
      filtered = filtered.filter((request) => request.service_id === filters.service);
    }

    // Apply payment status filter
    if (filters.isPaid !== 'all') {
      const isPaidFilter = filters.isPaid === 'paid';
      filtered = filtered.filter((request) => request.is_paid === isPaidFilter);
    }

    // Apply search filter
    if (filters.search.trim()) {
      const searchTerm = filters.search.toLowerCase().trim();
      filtered = filtered.filter((request) => {
        const serviceName = getLocalizedText(request.service_name).toLowerCase();
        const serviceDescription = getLocalizedText(request.service_description).toLowerCase();
        const customerIssue = (request.customer_issue || '').toLowerCase();
        const technicianName = (request.technician_name || '').toLowerCase();
        const requestId = request.id.toLowerCase();

        return (
          serviceName.includes(searchTerm) ||
          serviceDescription.includes(searchTerm) ||
          customerIssue.includes(searchTerm) ||
          technicianName.includes(searchTerm) ||
          requestId.includes(searchTerm)
        );
      });
    }

    setFilteredRequests(filtered);
  }, [requests, filters, getLocalizedText]);

  // Subscribe to notifications
  useEffect(() => {
    const unsubscribe = statusNotificationService.subscribe((notification) => {
      setNotifications((prev) => [notification, ...prev.slice(0, 49)]); // Keep last 50

      // If we receive a notification, refresh the requests if not in realtime mode
      if (!isRealtimeEnabled) {
        void loadRequests();
      }
    });

    return unsubscribe;
  }, [isRealtimeEnabled, loadRequests]);

  // Load data on component mount and when page/filters change
  useEffect(() => {
    void loadRequests();
  }, [loadRequests]);

  // Load technicians and services on mount
  useEffect(() => {
    void loadTechnicians();
    void loadServices();
  }, [loadTechnicians, loadServices]);

  // Reset to first page when filters change
  useEffect(() => {
    resetPagination();
  }, [filters, resetPagination]);

  // Update requests from real-time data when enabled
  useEffect(() => {
    if (isRealtimeEnabled && combinedRealtimeRequests.length > 0) {
      setRequests(combinedRealtimeRequests);
    }
  }, [isRealtimeEnabled, combinedRealtimeRequests]);

  // Toggle real-time monitoring
  const toggleRealtimeMode = () => {
    if (isRealtimeEnabled) {
      // Disable real-time mode
      statusNotificationService.stopAllMonitoring();
      setIsRealtimeEnabled(false);
      // Reload data from API
      void loadRequests();
    } else {
      // Enable real-time mode
      // Use role-based monitoring to avoid duplicate listeners
      if (user?.role === 'technician' && user?.uid) {
        statusNotificationService.startTechnicianMonitoring(user.uid);
      } else if (user?.role === 'customer' && user?.uid) {
        statusNotificationService.startCustomerMonitoring(user.uid);
      } else {
        // For admin users, monitor specific requests to avoid too many notifications
        const requestIds = requests.slice(0, 50).map((r) => r.id); // Limit to first 50 requests
        statusNotificationService.startMonitoring(requestIds);
      }
      setIsRealtimeEnabled(true);
    }
  };

  // Clear notifications
  const clearNotifications = () => {
    setNotifications([]);
    statusNotificationService.clearNotifications();
  };

  // Mark all as read
  const markAllAsRead = () => {
    notifications.forEach((n) => {
      statusNotificationService.markAsRead(n.id);
    });
    setNotifications((prev) => prev.map((n) => ({ ...n, read: true })));
  };

  const getStatusBadge = (status: RequestStatus) => {
    const statusConfig = {
      [RequestStatus.PAYMENT_PENDING]: {
        label: 'Payment Pending',
        variant: 'outline' as const,
        icon: CreditCard,
        color: 'text-gray-600 bg-gray-100 border-gray-300',
      },
      [RequestStatus.PENDING]: {
        label: 'Pending',
        variant: 'outline' as const,
        icon: Clock,
        color: 'text-amber-600 bg-amber-100 border-amber-300',
      },
      [RequestStatus.APPROVED]: {
        label: 'Approved',
        variant: 'outline' as const,
        icon: UserCheck,
        color: 'text-blue-600 bg-blue-100 border-blue-300',
      },
      [RequestStatus.IN_PROGRESS]: {
        label: 'In Progress',
        variant: 'outline' as const,
        icon: PlayCircle,
        color: 'text-indigo-600 bg-indigo-100 border-indigo-300',
      },
      [RequestStatus.COMPLETED]: {
        label: 'Completed',
        variant: 'outline' as const,
        icon: CheckCircle,
        color: 'text-green-600 bg-green-100 border-green-300',
      },
      [RequestStatus.CANCELLED]: {
        label: 'Cancelled',
        variant: 'outline' as const,
        icon: XCircle,
        color: 'text-red-600 bg-red-100 border-red-300',
      },
      [RequestStatus.REFUSED]: {
        label: 'Refused',
        variant: 'outline' as const,
        icon: XCircle,
        color: 'text-red-600 bg-red-100 border-red-300',
      },
    };

    const config = statusConfig[status];
    const IconComponent = config.icon;

    return (
      <Badge className={`flex items-center gap-1 ${config.color}`}>
        <IconComponent className='h-3 w-3' />
        {config.label}
      </Badge>
    );
  };

  const formatDate = (date: Date | { toDate: () => Date }) => {
    const dateObj = date instanceof Date ? date : date.toDate();
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const handleStatusUpdate = (request: RequestModel) => {
    setSelectedRequest(request);
    setIsStatusDialogOpen(true);
  };

  const handleStatusDialogClose = () => {
    setSelectedRequest(null);
    setIsStatusDialogOpen(false);
  };

  const handleRequestUpdated = () => {
    void loadRequests(); // Reload requests after update
  };

  const handleViewDetails = (request: RequestModel) => {
    setSelectedRequest(request);
    setIsDetailsDialogOpen(true);
  };

  const handleDetailsDialogClose = () => {
    setSelectedRequest(null);
    setIsDetailsDialogOpen(false);
  };

  const handleEditFromDetails = () => {
    setIsDetailsDialogOpen(false);
    setIsStatusDialogOpen(true);
  };

  const handleDeleteRequest = (request: RequestModel) => {
    setRequestToDelete(request);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteDialogClose = () => {
    setRequestToDelete(null);
    setIsDeleteDialogOpen(false);
    setIsDeleting(false);
  };

  const confirmDeleteRequest = async () => {
    if (!requestToDelete) return;

    setIsDeleting(true);
    try {
      await requestService.delete(requestToDelete.id);

      // Remove from local state immediately for better UX
      setRequests((prevRequests) => prevRequests.filter((r) => r.id !== requestToDelete.id));
      setFilteredRequests((prevFiltered) =>
        prevFiltered.filter((r) => r.id !== requestToDelete.id),
      );

      // Request deleted successfully - no need to log

      handleDeleteDialogClose();

      // Show success message
      setErrorBanner(`Request #${requestToDelete.id.slice(-8)} has been deleted successfully`);
      setTimeout(() => setErrorBanner(null), 5000);

      // Reload data to ensure consistency
      await loadRequests();
    } catch (error) {
      console.error('Error deleting request:', error);
      const errorMessage = `Failed to delete request: ${error instanceof Error ? error.message : 'Unknown error'}`;
      setErrorBanner(errorMessage);
      setIsDeleting(false);
      setTimeout(() => setErrorBanner(null), 8000);
    }
  };

  const handleOpenChat = (request: RequestModel) => {
    // Navigate to chat page with the specific request
    navigate(`/chat?requestId=${request.id}`);
  };

  const handleQuickStatusUpdate = async (request: RequestModel, newStatus: RequestStatus) => {
    try {
      // Optimistic UI update
      setRequests((prevRequests) =>
        prevRequests.map((r) =>
          r.id === request.id ? { ...r, status: newStatus, updated_at: new Date() } : r,
        ),
      );
      setFilteredRequests((prevFiltered) =>
        prevFiltered.map((r) =>
          r.id === request.id ? { ...r, status: newStatus, updated_at: new Date() } : r,
        ),
      );

      await requestService.updateStatus(request.id, newStatus);

      // Reload for consistency
      await loadRequests();
    } catch (error) {
      console.error('Error updating request status:', error);

      // Revert optimistic update on error
      setRequests((prevRequests) =>
        prevRequests.map((r) => (r.id === request.id ? { ...r, status: request.status } : r)),
      );
      setFilteredRequests((prevFiltered) =>
        prevFiltered.map((r) => (r.id === request.id ? { ...r, status: request.status } : r)),
      );

      const errorMessage = `Failed to update request status: ${error instanceof Error ? error.message : 'Unknown error'}`;
      setErrorBanner(errorMessage);
      setTimeout(() => setErrorBanner(null), 8000);
    }
  };

  // Update the stats cards section to use real-time data when enabled
  const pendingCount = isRealtimeEnabled
    ? realtimePendingRequests.length
    : requests.filter((r) => r.status === RequestStatus.PENDING).length;

  const inProgressCount = isRealtimeEnabled
    ? realtimeInProgressRequests.length
    : requests.filter((r) => r.status === RequestStatus.IN_PROGRESS).length;

  if (loading) {
    return (
      <div className='space-y-6 p-6'>
        {/* Header skeleton */}
        <div className='flex justify-between items-center'>
          <div>
            <SkeletonCard className='h-8 w-48' />
            <SkeletonCard className='h-4 w-64 mt-2' />
          </div>
          <SkeletonCard className='h-10 w-32' />
        </div>

        {/* Stats skeleton */}
        <SkeletonStats cards={4} />

        {/* Filters skeleton */}
        <div className='flex gap-4'>
          <SkeletonCard className='h-10 w-64' />
          <SkeletonCard className='h-10 w-32' />
          <SkeletonCard className='h-10 w-32' />
        </div>

        {/* Table skeleton */}
        <SkeletonTable rows={8} columns={6} />
      </div>
    );
  }

  return (
    <div className='space-y-6 p-6'>
      {/* Error/Success Banner */}
      {(error || errorBanner) && (
        <div
          className={`${errorBanner?.includes('successfully') || errorBanner?.includes('copied') ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'} border rounded-lg p-4 transition-all duration-300`}
        >
          <div className='flex items-center'>
            {errorBanner?.includes('successfully') || errorBanner?.includes('copied') ? (
              <CheckCircle className='h-5 w-5 text-green-500 mr-3' />
            ) : (
              <AlertCircle className='h-5 w-5 text-red-500 mr-3' />
            )}
            <div className='flex-1'>
              <p
                className={`text-sm ${errorBanner?.includes('successfully') || errorBanner?.includes('copied') ? 'text-green-700' : 'text-red-700'}`}
              >
                {error || errorBanner}
              </p>
            </div>
            <button
              onClick={() => {
                setError(null);
                setErrorBanner(null);
              }}
              className={`${errorBanner?.includes('successfully') || errorBanner?.includes('copied') ? 'text-green-500 hover:text-green-700' : 'text-red-500 hover:text-red-700'} ml-3`}
            >
              <X className='h-4 w-4' />
            </button>
          </div>
        </div>
      )}

      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>Service Requests</h1>
          <p className='text-muted-foreground mt-1'>
            Manage and track service requests with real-time updates
          </p>
        </div>

        <div className='flex items-center gap-3'>
          {/* Real-time monitoring toggle */}
          <div className='flex items-center gap-2'>
            {isRealtimeEnabled && health.isHealthy ? (
              <Wifi className='h-4 w-4 text-green-500' />
            ) : isRealtimeEnabled ? (
              <WifiOff className='h-4 w-4 text-red-500' />
            ) : null}
            <Button
              variant={isRealtimeEnabled ? 'secondary' : 'outline'}
              size='sm'
              onClick={toggleRealtimeMode}
            >
              {isRealtimeEnabled ? 'Live' : 'Refresh'}
            </Button>
          </div>

          {/* Notification indicator */}
          <Button
            variant='ghost'
            size='sm'
            className='relative'
            onClick={() => setActiveTab('notifications')}
          >
            <Bell className='h-4 w-4' />
            {notifications.filter((n) => !n.read).length > 0 && (
              <Badge
                className='absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs'
                variant='destructive'
              >
                {notifications.filter((n) => !n.read).length}
              </Badge>
            )}
          </Button>

          <PermissionGate permissions={[Permission.CREATE_REQUEST]}>
            <Button size='sm'>
              <Plus className='h-4 w-4 mr-2' />
              New Request
            </Button>
          </PermissionGate>
        </div>
      </div>

      {/* Stats Cards */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Requests</CardTitle>
            <FileText className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{requests.length}</div>
            <p className='text-xs text-muted-foreground'>
              {isRealtimeEnabled ? 'Live count' : 'All requests'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Pending</CardTitle>
            <Clock className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{pendingCount}</div>
            <p className='text-xs text-muted-foreground'>Awaiting approval</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>In Progress</CardTitle>
            <PlayCircle className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{inProgressCount}</div>
            <p className='text-xs text-muted-foreground'>Currently active</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Completed</CardTitle>
            <CheckCircle className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {requests.filter((r) => r.status === RequestStatus.COMPLETED).length}
            </div>
            <p className='text-xs text-muted-foreground'>Successfully finished</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className='space-y-4'>
        <TabsList>
          <TabsTrigger value='requests'>Requests</TabsTrigger>
          <TabsTrigger value='notifications' className='relative'>
            Notifications
            {notifications.filter((n) => !n.read).length > 0 && (
              <Badge className='ml-2 h-4 w-4 rounded-full p-0 text-xs'>
                {notifications.filter((n) => !n.read).length}
              </Badge>
            )}
          </TabsTrigger>
        </TabsList>

        <TabsContent value='requests' className='space-y-4'>
          {/* Compact Filter Bar */}
          <Card>
            <CardHeader className='pb-3'>
              <div className='flex items-center justify-between'>
                <CardTitle className='text-lg'>Filters & Search</CardTitle>
                <Button variant='ghost' size='sm' onClick={() => setShowFilters(!showFilters)}>
                  <Filter className='h-4 w-4 mr-2' />
                  {showFilters ? 'Hide' : 'Show'} Filters
                </Button>
              </div>
            </CardHeader>
            <CardContent className='space-y-4'>
              {/* Search Bar - Always Visible */}
              <div className='relative'>
                <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground' />
                <Input
                  placeholder='Search requests, services, technicians...'
                  value={filters.search}
                  onChange={(e) => setFilters((prev) => ({ ...prev, search: e.target.value }))}
                  className='pl-10'
                />
              </div>

              {/* Collapsible Advanced Filters */}
              <Collapsible open={showFilters} onOpenChange={setShowFilters}>
                <CollapsibleContent className='space-y-4'>
                  <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
                    <div className='space-y-2'>
                      <label className='text-sm font-medium'>Status</label>
                      <Select
                        value={filters.status}
                        onValueChange={(value) =>
                          setFilters((prev) => ({ ...prev, status: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder='All statuses' />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='all'>All Statuses</SelectItem>
                          <SelectItem value={RequestStatus.PAYMENT_PENDING}>
                            Payment Pending
                          </SelectItem>
                          <SelectItem value={RequestStatus.PENDING}>Pending</SelectItem>
                          <SelectItem value={RequestStatus.APPROVED}>Approved</SelectItem>
                          <SelectItem value={RequestStatus.IN_PROGRESS}>In Progress</SelectItem>
                          <SelectItem value={RequestStatus.COMPLETED}>Completed</SelectItem>
                          <SelectItem value={RequestStatus.CANCELLED}>Cancelled</SelectItem>
                          <SelectItem value={RequestStatus.REFUSED}>Refused</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className='space-y-2'>
                      <label className='text-sm font-medium'>Technician</label>
                      <Select
                        value={filters.technician}
                        onValueChange={(value) =>
                          setFilters((prev) => ({ ...prev, technician: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder='All technicians' />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='all'>All Technicians</SelectItem>
                          <SelectItem value='unassigned'>Unassigned</SelectItem>
                          {technicians.map((technician) => (
                            <SelectItem key={technician.id} value={technician.id}>
                              {getLocalizedText(technician.name, 'Unknown Technician')}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className='space-y-2'>
                      <label className='text-sm font-medium'>Service</label>
                      <Select
                        value={filters.service}
                        onValueChange={(value) =>
                          setFilters((prev) => ({ ...prev, service: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder='All services' />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='all'>All Services</SelectItem>
                          {services.map((service) => (
                            <SelectItem key={service.id} value={service.id}>
                              {getLocalizedText(service.name, 'Unknown Service')}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className='space-y-2'>
                      <label className='text-sm font-medium'>Payment</label>
                      <Select
                        value={filters.isPaid}
                        onValueChange={(value) =>
                          setFilters((prev) => ({ ...prev, isPaid: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder='All payments' />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='all'>All Payments</SelectItem>
                          <SelectItem value='paid'>Paid</SelectItem>
                          <SelectItem value='unpaid'>Unpaid</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className='flex gap-2'>
                    <Button
                      variant='outline'
                      size='sm'
                      onClick={() =>
                        setFilters({
                          status: 'all',
                          technician: 'all',
                          service: 'all',
                          search: '',
                          isPaid: 'all',
                        })
                      }
                    >
                      Clear Filters
                    </Button>
                    <Button
                      variant='outline'
                      size='sm'
                      onClick={async () => {
                        setErrorBanner('Refreshing data...');
                        try {
                          await Promise.all([loadRequests(), loadTechnicians(), loadServices()]);
                          setErrorBanner('Data refreshed successfully');
                          setTimeout(() => setErrorBanner(null), 3000);
                        } catch {
                          setErrorBanner('Failed to refresh some data. Please try again.');
                          setTimeout(() => setErrorBanner(null), 8000);
                        }
                      }}
                    >
                      <RefreshCw className='h-4 w-4 mr-2' />
                      Refresh
                    </Button>
                  </div>
                </CollapsibleContent>
              </Collapsible>
            </CardContent>
          </Card>

          {/* Requests Table */}
          <Card>
            <CardHeader>
              <CardTitle className='flex items-center gap-2'>
                <FileText className='h-5 w-5' />
                Service Requests ({totalRequests})
              </CardTitle>
              <CardDescription>
                {totalRequests > 0
                  ? `Showing ${(currentPage - 1) * pageSize + 1} to ${Math.min(currentPage * pageSize, totalRequests)} of ${totalRequests} requests`
                  : 'No requests found'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {requests.length === 0 ? (
                <div className='text-center py-12'>
                  <FileText className='h-12 w-12 text-muted-foreground mx-auto mb-4' />
                  <h3 className='text-lg font-semibold mb-2'>No requests found</h3>
                  <p className='text-muted-foreground'>
                    {totalRequests === 0
                      ? 'No requests have been created yet.'
                      : 'Try adjusting your filters to see more results.'}
                  </p>
                </div>
              ) : (
                <div className='rounded-md border overflow-hidden'>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className='w-20'>ID</TableHead>
                        <TableHead>Service</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Technician</TableHead>
                        <TableHead>Payment</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Rating</TableHead>
                        <TableHead>Created</TableHead>
                        <TableHead className='w-20'>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {requests.map((request) => (
                        <TableRow key={request.id} className='hover:bg-muted/50'>
                          <TableCell className='font-mono text-xs'>
                            #{request.id.slice(-6)}
                          </TableCell>
                          <TableCell>
                            <div className='max-w-[200px]'>
                              <div className='font-medium truncate'>
                                {getLocalizedText(request.service_name, 'Unknown Service')}
                              </div>
                              <div className='text-sm text-muted-foreground truncate'>
                                {request.customer_issue || 'No description'}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className='flex items-center gap-2'>
                              {getStatusBadge(request.status)}
                              {/* Quick Actions */}
                              <PermissionGate permissions={[Permission.UPDATE_REQUEST]}>
                                {request.status === RequestStatus.PENDING && (
                                  <Button
                                    size='sm'
                                    variant='ghost'
                                    onClick={() =>
                                      handleQuickStatusUpdate(request, RequestStatus.APPROVED)
                                    }
                                    className='h-6 px-2 text-xs'
                                  >
                                    Approve
                                  </Button>
                                )}
                                {request.status === RequestStatus.APPROVED &&
                                  request.technician_id && (
                                    <Button
                                      size='sm'
                                      variant='ghost'
                                      onClick={() =>
                                        handleQuickStatusUpdate(request, RequestStatus.IN_PROGRESS)
                                      }
                                      className='h-6 px-2 text-xs'
                                    >
                                      Start
                                    </Button>
                                  )}
                                {request.status === RequestStatus.IN_PROGRESS && (
                                  <Button
                                    size='sm'
                                    variant='ghost'
                                    onClick={() =>
                                      handleQuickStatusUpdate(request, RequestStatus.COMPLETED)
                                    }
                                    className='h-6 px-2 text-xs'
                                  >
                                    Complete
                                  </Button>
                                )}
                              </PermissionGate>
                            </div>
                          </TableCell>
                          <TableCell>
                            {request.technician_name ? (
                              <div className='flex items-center gap-2'>
                                <User className='h-3 w-3 text-muted-foreground' />
                                <span className='text-sm'>{request.technician_name}</span>
                              </div>
                            ) : (
                              <span className='text-muted-foreground text-sm'>Unassigned</span>
                            )}
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={request.is_paid ? 'default' : 'secondary'}
                              className='text-xs'
                            >
                              {request.is_paid ? 'Paid' : 'Unpaid'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className='flex items-center gap-1'>
                              <DollarSign className='h-3 w-3 text-muted-foreground' />
                              <span className='font-mono text-sm'>
                                {formatCurrency(request.amount)}
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            {request.rating ? (
                              <div className='flex items-center gap-2'>
                                <div className='flex items-center'>
                                  {[1, 2, 3, 4, 5].map((star) => (
                                    <Star
                                      key={star}
                                      className={`h-3 w-3 ${
                                        star <= (request.rating || 0)
                                          ? 'text-yellow-400 fill-yellow-400'
                                          : 'text-gray-300'
                                      }`}
                                    />
                                  ))}
                                </div>
                                <span className='text-xs text-muted-foreground'>
                                  {request.rating}/5
                                </span>
                              </div>
                            ) : (
                              <span className='text-muted-foreground text-xs'>-</span>
                            )}
                          </TableCell>
                          <TableCell>
                            <div className='flex items-center gap-1'>
                              <Calendar className='h-3 w-3 text-muted-foreground' />
                              <span className='text-xs'>{formatDate(request.created_at)}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant='ghost' className='h-8 w-8 p-0'>
                                  <MoreHorizontal className='h-4 w-4' />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align='end'>
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem onClick={() => handleViewDetails(request)}>
                                  <Eye className='mr-2 h-4 w-4' />
                                  View Details
                                </DropdownMenuItem>
                                <PermissionGate permissions={[Permission.UPDATE_REQUEST]}>
                                  <DropdownMenuItem onClick={() => handleStatusUpdate(request)}>
                                    <Edit className='mr-2 h-4 w-4' />
                                    Update Status
                                  </DropdownMenuItem>
                                </PermissionGate>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={() => navigator.clipboard.writeText(request.id)}
                                >
                                  Copy Request ID
                                </DropdownMenuItem>
                                <PermissionGate permissions={[Permission.DELETE_REQUEST]}>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem
                                    onClick={() => handleDeleteRequest(request)}
                                    className='text-red-600 focus:text-red-600'
                                  >
                                    <Trash2 className='mr-2 h-4 w-4' />
                                    Delete Request
                                  </DropdownMenuItem>
                                </PermissionGate>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}

              {/* Pagination */}
              {totalRequests > 0 && (
                <Pagination
                  pagination={createPaginationInfo(totalRequests)}
                  onPageChange={handlePageChange}
                  onPageSizeChange={handlePageSizeChange}
                  className='border-t pt-4 mt-6'
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='notifications' className='space-y-4'>
          <div className='flex items-center justify-between'>
            <h3 className='text-lg font-semibold'>Status Change Notifications</h3>
            <div className='flex gap-2'>
              <Button variant='outline' size='sm' onClick={markAllAsRead}>
                Mark All Read
              </Button>
              <Button variant='outline' size='sm' onClick={clearNotifications}>
                Clear All
              </Button>
            </div>
          </div>

          <Card>
            <CardContent className='p-0'>
              {notifications.length === 0 ? (
                <div className='p-8 text-center text-muted-foreground'>
                  <Bell className='h-12 w-12 mx-auto mb-4 opacity-50' />
                  <p>No notifications yet</p>
                  <p className='text-sm'>
                    {isRealtimeEnabled
                      ? "You'll see real-time status updates here"
                      : 'Enable real-time monitoring to see status updates'}
                  </p>
                </div>
              ) : (
                <div className='divide-y'>
                  {notifications.map((notification) => (
                    <div
                      key={notification.id}
                      className={`p-4 ${notification.read ? '' : 'bg-blue-50'}`}
                    >
                      <div className='flex items-start justify-between'>
                        <div className='flex items-start gap-3'>
                          <div
                            className={`p-2 rounded-full ${
                              notification.priority === 'high'
                                ? 'bg-red-100'
                                : notification.priority === 'medium'
                                  ? 'bg-yellow-100'
                                  : 'bg-green-100'
                            }`}
                          >
                            {notification.priority === 'high' ? (
                              <AlertCircle className='h-4 w-4 text-red-500' />
                            ) : notification.priority === 'medium' ? (
                              <Clock className='h-4 w-4 text-yellow-500' />
                            ) : (
                              <CheckCircle className='h-4 w-4 text-green-500' />
                            )}
                          </div>
                          <div>
                            <p className='font-medium'>{notification.message}</p>
                            <div className='flex items-center gap-2 mt-1 text-xs text-muted-foreground'>
                              <span>{new Date(notification.timestamp).toLocaleString()}</span>
                              <span>•</span>
                              <span>Request ID: {notification.requestId.slice(-8)}</span>
                            </div>
                          </div>
                        </div>
                        <Button
                          variant='ghost'
                          size='sm'
                          onClick={() => {
                            const request = requests.find((r) => r.id === notification.requestId);
                            if (request) {
                              handleViewDetails(request);
                            }
                          }}
                        >
                          View
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Dialogs */}
      <StatusUpdateDialog
        request={selectedRequest}
        technicians={technicians}
        isOpen={isStatusDialogOpen}
        onClose={handleStatusDialogClose}
        onUpdate={handleRequestUpdated}
      />

      <RequestDetailsDialog
        request={selectedRequest}
        isOpen={isDetailsDialogOpen}
        onClose={handleDetailsDialogClose}
        onEdit={handleEditFromDetails}
        onOpenChat={handleOpenChat}
      />

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Request</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this request? This action cannot be undone.
              {requestToDelete && (
                <div className='mt-4 p-3 bg-gray-50 rounded-md'>
                  <p>
                    <strong>Request ID:</strong> #{requestToDelete.id.slice(-8)}
                  </p>
                  <p>
                    <strong>Service:</strong> {requestToDelete.service_name}
                  </p>
                  <p>
                    <strong>Customer Issue:</strong> {requestToDelete.customer_issue}
                  </p>
                  <p>
                    <strong>Status:</strong> {requestToDelete.status}
                  </p>
                </div>
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant='outline' onClick={handleDeleteDialogClose} disabled={isDeleting}>
              Cancel
            </Button>
            <Button variant='destructive' onClick={confirmDeleteRequest} disabled={isDeleting}>
              {isDeleting ? (
                <>
                  <div className='mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent' />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className='mr-2 h-4 w-4' />
                  Delete Request
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default RequestsPage;
