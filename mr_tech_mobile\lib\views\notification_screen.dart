import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:mr_tech_mobile/models/notification_model.dart';
import 'package:mr_tech_mobile/services/notification_service.dart';
import 'package:mr_tech_mobile/utils/navigation_utils.dart';
import 'package:provider/provider.dart';
import 'package:mr_tech_mobile/services/translation_service.dart';
import '../widgets/text_styles.dart';

class NotificationScreen extends StatefulWidget {
  const NotificationScreen({super.key});

  @override
  State<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  final NotificationService _notificationService = NotificationService();
  bool _isLoading = false;

  Future<void> _markAsRead(String notificationId) async {
    setState(() => _isLoading = true);
    try {
      await _notificationService.markAsRead(notificationId);
    } catch (e) {
      debugPrint('Error marking notification as read: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _markAllAsRead(List<NotificationModel> notifications) async {
    setState(() => _isLoading = true);
    try {
      await _notificationService.markAllAsRead();
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_translate('all_notifications_marked_as_read')),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error marking all notifications as read: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _deleteAllNotifications() async {
    setState(() => _isLoading = true);
    try {
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) {
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            title: Text(_translate('clear_all')),
            content: Text(_translate('clear_all_confirmation')),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text(_translate('cancel')),
              ),
              FilledButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: FilledButton.styleFrom(
                  backgroundColor: Colors.red,
                ),
                child: Text(_translate('clear')),
              ),
            ],
          );
        },
      );
      
      if (confirmed == true) {
        await _notificationService.markAllAsDeleted();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(_translate('all_notifications_deleted')),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Error deleting all notifications: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _permanentlyDeleteAllNotifications() async {
    setState(() => _isLoading = true);
    try {
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) {
          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            title: Text(_translate('permanently_delete_all')),
            content: Text(_translate('permanently_delete_all_confirmation')),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text(_translate('cancel')),
              ),
              FilledButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: FilledButton.styleFrom(
                  backgroundColor: Colors.red,
                ),
                child: Text(_translate('delete')),
              ),
            ],
          );
        },
      );
      
      if (confirmed == true) {
        await _notificationService.deleteAllNotifications();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(_translate('all_notifications_permanently_deleted')),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('Error permanently deleting all notifications: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _navigateToRequestDetails(String? requestId) {
    if (requestId == null) return;
    NavigationUtils.navigateToRequestDetails(context, requestId);
  }

  String _formatNotificationTime(Timestamp timestamp) {
    final now = DateTime.now();
    final date = timestamp.toDate();
    final diff = now.difference(date);

    if (diff.inDays > 0) {
      return _translate('days_ago').replaceAll('{count}', diff.inDays.toString());
    } else if (diff.inHours > 0) {
      return _translate('hours_ago').replaceAll('{count}', diff.inHours.toString());
    } else {
      final minutes = diff.inMinutes == 0 ? 1 : diff.inMinutes;
      return _translate('minutes_ago').replaceAll('{count}', minutes.toString());
    }
  }

  String _translate(String key) {
    final translationService = Provider.of<TranslationService>(context, listen: false);
    return translationService.translate(key);
  }

  IconData _getNotificationIconData(String type) {
    if (type.contains('completed')) {
      return Icons.check_circle_outline;
    } else if (type.contains('accepted') || type.contains('approved')) {
      return Icons.task_alt;
    } else if (type.contains('message')) {
      return Icons.chat_bubble_outline;
    } else if (type.contains('in_progress')) {
      return Icons.play_circle_outline;
    } else if (type.contains('payment')) {
      return Icons.payments_outlined;
    } else if (type.contains('cancelled')) {
      return Icons.cancel_outlined;
    } else {
      return Icons.notifications_outlined;
    }
  }

  Color _getNotificationColor(String type) {
    if (type.contains('completed')) {
      return Colors.green.shade700;
    } else if (type.contains('accepted') || type.contains('approved')) {
      return Colors.blue.shade700;
    } else if (type.contains('message')) {
      return Colors.purple.shade700;
    } else if (type.contains('in_progress')) {
      return Colors.amber.shade700;
    } else if (type.contains('payment')) {
      return Colors.teal.shade700;
    } else if (type.contains('cancelled')) {
      return Colors.red.shade700;
    } else {
      return Colors.blueGrey.shade700;
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isRtl = Provider.of<TranslationService>(context, listen: false).isRtl;
    
    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: CustomScrollView(
        slivers: [
          // Floating header
          SliverAppBar(
            expandedHeight: 80,
            floating: true,
            pinned: false,
            snap: true,
            elevation: 0,
            backgroundColor: colorScheme.surface,
            flexibleSpace: FlexibleSpaceBar(
              background: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                  child: Row(
                    children: [
                      Text(
                        _translate('notifications'),
                        style: AppTextStyles.headingMedium(
                          context,
                          color: colorScheme.onSurface,
                        ),
                      ),
                      const Spacer(),
                      // Action buttons
                      StreamBuilder<List<NotificationModel>>(
                        stream: _notificationService.getNotificationsStream(),
                        builder: (context, snapshot) {
                          if (!snapshot.hasData || snapshot.data!.isEmpty) {
                            return const SizedBox.shrink();
                          }
                          
                          return PopupMenuButton<String>(
                            icon: Icon(
                              Icons.more_vert,
                              color: colorScheme.onSurface,
                            ),
                            onSelected: (value) {
                              switch (value) {
                                case 'mark_all_read':
                                  _markAllAsRead(snapshot.data!);
                                  break;
                                case 'clear_all':
                                  _deleteAllNotifications();
                                  break;
                              }
                            },
                            itemBuilder: (context) => [
                              if (snapshot.data!.any((n) => !n.read))
                                PopupMenuItem(
                                  value: 'mark_all_read',
                                  child: Row(
                                    children: [
                                      Icon(Icons.done_all, size: 20),
                                      const SizedBox(width: 12),
                                      Text(_translate('mark_all_read')),
                                    ],
                                  ),
                                ),
                              PopupMenuItem(
                                value: 'clear_all',
                                child: Row(
                                  children: [
                                    Icon(Icons.clear_all, size: 20),
                                    const SizedBox(width: 12),
                                    Text(_translate('clear_all')),
                                  ],
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          
          // Notifications list
          SliverFillRemaining(
            child: StreamBuilder<List<NotificationModel>>(
              stream: _notificationService.getNotificationsStream(),
              builder: (context, snapshot) {
                if (snapshot.hasError) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Colors.red.shade300,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _translate('error_loading_notifications'),
                          style: AppTextStyles.headingMedium(context),
                        ),
                      ],
                    ),
                  );
                }

                if (snapshot.connectionState == ConnectionState.waiting && !snapshot.hasData) {
                  return const Center(child: CircularProgressIndicator());
                }

                final notifications = snapshot.data ?? [];
                
                if (notifications.isEmpty) {
                  return Center(
                    child: Padding(
                      padding: const EdgeInsets.all(32),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(32),
                            decoration: BoxDecoration(
                              color: colorScheme.primary.withOpacity(0.1),
                              shape: BoxShape.circle,
                              border: Border.all(color: Colors.grey.shade300),
                            ),
                            child: Icon(
                              Icons.notifications_off_outlined,
                              size: 64,
                              color: colorScheme.primary,
                            ),
                          ),
                          const SizedBox(height: 24),
                          Text(
                            _translate('no_notifications'),
                            style: AppTextStyles.headingMedium(
                              context,
                              color: colorScheme.onSurface,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _translate('notifications_will_appear_here'),
                            style: AppTextStyles.bodyMedium(
                              context,
                              color: colorScheme.onSurfaceVariant,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  );
                }

                return RefreshIndicator(
                  onRefresh: () async {
                    setState(() {});
                  },
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: notifications.length,
                    itemBuilder: (context, index) {
                      final notification = notifications[index];
                      final notifColor = _getNotificationColor(notification.type);
                      
                      return Dismissible(
                        key: Key(notification.id),
                        direction: DismissDirection.endToStart,
                        background: Container(
                          alignment: Alignment.centerRight,
                          padding: const EdgeInsets.only(right: 20),
                          margin: const EdgeInsets.only(bottom: 12),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(Icons.delete_outline, color: Colors.white),
                        ),
                        onDismissed: (_) {
                          _notificationService.markAsDeleted(notification.id);
                        },
                        child: Card(
                          margin: const EdgeInsets.only(bottom: 12),
                          elevation: 0,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                            side: BorderSide(
                              color: notification.read 
                                  ? Colors.grey.shade300 
                                  : notifColor.withOpacity(0.3),
                              width: notification.read ? 1 : 2,
                            ),
                          ),
                          child: InkWell(
                            borderRadius: BorderRadius.circular(12),
                            onTap: () {
                              if (!notification.read) {
                                _markAsRead(notification.id);
                              }
                              if (notification.requestId != null) {
                                _navigateToRequestDetails(notification.requestId);
                              }
                            },
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(10),
                                    decoration: BoxDecoration(
                                      color: notification.read 
                                          ? notifColor.withOpacity(0.1) 
                                          : notifColor,
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(color: Colors.grey.shade300),
                                    ),
                                    child: Icon(
                                      _getNotificationIconData(notification.type),
                                      size: 20,
                                      color: notification.read ? notifColor : Colors.white,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            Expanded(
                                              child: Text(
                                                notification.title,
                                                style: AppTextStyles.labelLarge(
                                                  context,
                                                  color: colorScheme.onSurface,
                                                ),
                                                maxLines: 1,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                            const SizedBox(width: 8),
                                            Container(
                                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                              decoration: BoxDecoration(
                                                color: colorScheme.surface,
                                                borderRadius: BorderRadius.circular(8),
                                                border: Border.all(color: Colors.grey.shade300),
                                              ),
                                              child: Text(
                                                _formatNotificationTime(notification.createdAt),
                                                style: AppTextStyles.bodySmall(
                                                  context,
                                                  color: colorScheme.onSurfaceVariant,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 8),
                                        Text(
                                          notification.body,
                                          style: AppTextStyles.bodyMedium(
                                            context,
                                            color: colorScheme.onSurfaceVariant,
                                          ),
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        if (notification.requestId != null) ...[
                                          const SizedBox(height: 12),
                                          Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              OutlinedButton.icon(
                                                onPressed: () => _navigateToRequestDetails(notification.requestId),
                                                icon: Icon(Icons.arrow_forward, size: 16),
                                                label: Text(_translate('view_request')),
                                                style: OutlinedButton.styleFrom(
                                                  side: BorderSide(color: Colors.grey.shade300),
                                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                                ),
                                              ),
                                              if (!notification.read)
                                                IconButton(
                                                  onPressed: () => _markAsRead(notification.id),
                                                  icon: Icon(
                                                    Icons.check,
                                                    color: notifColor,
                                                    size: 20,
                                                  ),
                                                  style: IconButton.styleFrom(
                                                    backgroundColor: notifColor.withOpacity(0.1),
                                                    padding: const EdgeInsets.all(8),
                                                  ),
                                                ),
                                            ],
                                          ),
                                        ],
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
} 