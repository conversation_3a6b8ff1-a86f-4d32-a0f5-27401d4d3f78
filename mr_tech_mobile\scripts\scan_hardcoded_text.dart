import 'dart:io';
import 'dart:convert';

/// <PERSON><PERSON><PERSON> to scan for hardcoded text that needs translation
/// Excludes debug prints, technical identifiers, and system strings
void main() async {
  final directory = Directory('lib');
  final hardcodedTexts = <String, List<String>>{};
  final excludePatterns = [
    // Debug and logging patterns
    RegExp(r'debugPrint\s*\('),
    RegExp(r'print\s*\('),
    RegExp(r'log\s*\('),
    RegExp(r'console\.log'),
    
    // Technical identifiers
    RegExp(r'[\'"][a-z_]+[\'"]'), // snake_case identifiers
    RegExp(r'[\'"][A-Z_]+[\'"]'), // CONSTANT_CASE identifiers
    RegExp(r'[\'"]\w+\.\w+[\'"]'), // file.extension
    RegExp(r'[\'"]https?://[\'"]'), // URLs
    RegExp(r'[\'"]assets/[\'"]'), // Asset paths
    RegExp(r'[\'"]/[a-z-_/]+[\'"]'), // Route paths
    
    // Firebase/Database fields
    RegExp(r'[\'"][a-z_]+_[a-z_]+[\'"]'), // database_field_names
    
    // Technical strings
    RegExp(r'[\'"]Exception[\'"]'),
    RegExp(r'[\'"]Error[\'"]'),
    RegExp(r'[\'"]\w+Exception[\'"]'),
    
    // Single characters and very short strings
    RegExp(r'[\'"][a-zA-Z]{1,2}[\'"]'),
    
    // Numbers and technical values
    RegExp(r'[\'"]\d+[\'"]'),
    RegExp(r'[\'"][\d.]+[\'"]'),
  ];

  // User-facing text patterns to include
  final includePatterns = [
    // UI text patterns
    RegExp(r'Text\s*\(\s*[\'"]([^\'\"]+)[\'"]'),
    RegExp(r'title:\s*Text\s*\(\s*[\'"]([^\'\"]+)[\'"]'),
    RegExp(r'label:\s*[\'"]([^\'\"]+)[\'"]'),
    RegExp(r'hint:\s*[\'"]([^\'\"]+)[\'"]'),
    RegExp(r'hintText:\s*[\'"]([^\'\"]+)[\'"]'),
    RegExp(r'errorText:\s*[\'"]([^\'\"]+)[\'"]'),
    RegExp(r'helperText:\s*[\'"]([^\'\"]+)[\'"]'),
    
    // Button and action text
    RegExp(r'child:\s*Text\s*\(\s*[\'"]([^\'\"]+)[\'"]'),
    RegExp(r'text:\s*[\'"]([^\'\"]+)[\'"]'),
    
    // Dialog and alert text
    RegExp(r'title:\s*[\'"]([^\'\"]+)[\'"]'),
    RegExp(r'content:\s*[\'"]([^\'\"]+)[\'"]'),
    RegExp(r'message:\s*[\'"]([^\'\"]+)[\'"]'),
    
    // Validation and error messages
    RegExp(r'return\s+[\'"]([^\'\"]+)[\'"]'),
    RegExp(r'_errorMessage\s*=\s*[\'"]([^\'\"]+)[\'"]'),
    RegExp(r'_statusMessage\s*=\s*[\'"]([^\'\"]+)[\'"]'),
    
    // SnackBar and toast messages
    RegExp(r'SnackBar\s*\(\s*content:\s*Text\s*\(\s*[\'"]([^\'\"]+)[\'"]'),
    RegExp(r'showSnackBar.*[\'"]([^\'\"]+)[\'"]'),
    
    // General string literals that look like user text
    RegExp(r'[\'"]([A-Z][^\'\"]*[a-z][^\'\"]*)[\'"]'), // Starts with capital, contains lowercase
  ];

  print('🔍 Scanning for hardcoded text that needs translation...\n');

  await for (final entity in directory.list(recursive: true)) {
    if (entity is File && entity.path.endsWith('.dart')) {
      final content = await entity.readAsString();
      final lines = content.split('\n');
      
      for (int i = 0; i < lines.length; i++) {
        final line = lines[i];
        final lineNumber = i + 1;
        
        // Skip comment lines
        if (line.trim().startsWith('//') || line.trim().startsWith('*')) {
          continue;
        }
        
        // Find all string literals in the line
        final stringMatches = RegExp(r'[\'"]([^\'\"]+)[\'"]').allMatches(line);
        
        for (final match in stringMatches) {
          final text = match.group(1)!;
          
          // Skip if text matches exclude patterns
          bool shouldExclude = false;
          for (final pattern in excludePatterns) {
            if (pattern.hasMatch('"$text"') || pattern.hasMatch("'$text'")) {
              shouldExclude = true;
              break;
            }
          }
          
          if (shouldExclude) continue;
          
          // Check if it's user-facing text
          bool isUserFacing = false;
          
          // Check if it matches include patterns
          for (final pattern in includePatterns) {
            if (pattern.hasMatch(line)) {
              isUserFacing = true;
              break;
            }
          }
          
          // Additional heuristics for user-facing text
          if (!isUserFacing) {
            // Text that starts with capital and has spaces (likely user text)
            if (RegExp(r'^[A-Z].*\s.*').hasMatch(text)) {
              isUserFacing = true;
            }
            
            // Text that looks like sentences or phrases
            if (text.length > 10 && text.contains(' ') && 
                !text.contains('_') && !text.contains('/')) {
              isUserFacing = true;
            }
            
            // Text in common UI contexts
            if (line.contains('AppBar') || line.contains('Dialog') || 
                line.contains('AlertDialog') || line.contains('showDialog')) {
              isUserFacing = true;
            }
          }
          
          if (isUserFacing && text.length > 2) {
            final filePath = entity.path.replaceAll('\\', '/');
            hardcodedTexts.putIfAbsent(filePath, () => []);
            hardcodedTexts[filePath]!.add('Line $lineNumber: "$text"');
          }
        }
      }
    }
  }

  // Output results
  if (hardcodedTexts.isEmpty) {
    print('✅ No hardcoded text found that needs translation!');
  } else {
    print('📝 Found hardcoded text in ${hardcodedTexts.length} files:\n');
    
    int totalTexts = 0;
    for (final entry in hardcodedTexts.entries) {
      print('📁 ${entry.key}:');
      for (final text in entry.value) {
        print('   $text');
        totalTexts++;
      }
      print('');
    }
    
    print('📊 Summary: $totalTexts hardcoded text strings found across ${hardcodedTexts.length} files');
    
    // Generate translation keys
    print('\n🔧 Suggested translation keys to add:\n');
    final allTexts = <String>{};
    for (final texts in hardcodedTexts.values) {
      for (final text in texts) {
        final cleanText = text.split(': "')[1].replaceAll('"', '');
        allTexts.add(cleanText);
      }
    }
    
    for (final text in allTexts.toList()..sort()) {
      final key = _generateTranslationKey(text);
      print("'$key': '$text',");
    }
  }
}

String _generateTranslationKey(String text) {
  // Generate a reasonable translation key from the text
  String key = text
      .replaceAll(RegExp(r'[^\w\s]'), '') // Remove punctuation
      .trim()
      .split(' ')
      .where((word) => word.isNotEmpty)
      .take(4) // Take first 4 words max
      .join(' ');
  
  // If key is too long, use first few words
  if (key.length > 30) {
    key = key.split(' ').take(3).join(' ');
  }
  
  return key;
}
