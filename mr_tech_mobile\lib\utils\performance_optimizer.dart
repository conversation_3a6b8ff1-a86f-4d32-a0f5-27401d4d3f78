import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:io';

/// Comprehensive performance optimization utilities for the Mr. Tech app
/// Provides methods to optimize startup time, memory usage, and overall performance
class PerformanceOptimizer {
  // Private constructor to prevent instantiation
  PerformanceOptimizer._();

  static bool _isInitialized = false;
  static Timer? _memoryOptimizationTimer;
  static Timer? _cacheCleanupTimer;

  /// Initialize performance optimizations
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Optimize Flutter engine settings
      await _optimizeFlutterEngine();
      
      // Setup periodic optimizations
      _setupPeriodicOptimizations();
      
      // Optimize image cache
      _optimizeImageCache();
      
      // Setup memory monitoring
      if (kDebugMode) {
        _setupMemoryMonitoring();
      }

      _isInitialized = true;
      debugPrint('PerformanceOptimizer initialized successfully');
    } catch (e) {
      debugPrint('Error initializing PerformanceOptimizer: $e');
    }
  }

  /// Optimize Flutter engine settings for better performance
  static Future<void> _optimizeFlutterEngine() async {
    try {
      // Optimize rendering performance
      WidgetsBinding.instance.addPostFrameCallback((_) {
        // Reduce unnecessary rebuilds
        WidgetsBinding.instance.buildOwner?.reassemble();
      });

      // Optimize system UI overlay style for better performance
      SystemChrome.setSystemUIOverlayStyle(
        const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          systemNavigationBarColor: Colors.transparent,
        ),
      );

      // Optimize preferred orientations
      await SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);

    } catch (e) {
      debugPrint('Error optimizing Flutter engine: $e');
    }
  }

  /// Setup periodic optimizations
  static void _setupPeriodicOptimizations() {
    // Memory optimization every 5 minutes
    _memoryOptimizationTimer = Timer.periodic(
      const Duration(minutes: 5),
      (_) => optimizeMemory(),
    );

    // Cache cleanup every 10 minutes
    _cacheCleanupTimer = Timer.periodic(
      const Duration(minutes: 10),
      (_) => cleanupCaches(),
    );
  }

  /// Optimize image cache settings
  static void _optimizeImageCache() {
    try {
      final imageCache = PaintingBinding.instance.imageCache;
      
      // Set optimal cache size based on device capabilities
      imageCache.maximumSize = _getOptimalImageCacheSize();
      imageCache.maximumSizeBytes = _getOptimalImageCacheSizeBytes();
      
      debugPrint('Image cache optimized: ${imageCache.maximumSize} images, ${imageCache.maximumSizeBytes} bytes');
    } catch (e) {
      debugPrint('Error optimizing image cache: $e');
    }
  }

  /// Get optimal image cache size based on device capabilities
  static int _getOptimalImageCacheSize() {
    // Base cache size
    int cacheSize = 100;
    
    try {
      // Adjust based on platform
      if (Platform.isAndroid) {
        cacheSize = 150; // Android typically has more RAM
      } else if (Platform.isIOS) {
        cacheSize = 120; // iOS has efficient memory management
      }
    } catch (e) {
      debugPrint('Error determining platform for cache size: $e');
    }
    
    return cacheSize;
  }

  /// Get optimal image cache size in bytes
  static int _getOptimalImageCacheSizeBytes() {
    // Base cache size: 50MB
    int cacheSizeBytes = 50 * 1024 * 1024;
    
    try {
      // Adjust based on platform and available memory
      if (Platform.isAndroid) {
        cacheSizeBytes = 75 * 1024 * 1024; // 75MB for Android
      } else if (Platform.isIOS) {
        cacheSizeBytes = 60 * 1024 * 1024; // 60MB for iOS
      }
    } catch (e) {
      debugPrint('Error determining platform for cache size bytes: $e');
    }
    
    return cacheSizeBytes;
  }

  /// Setup memory monitoring in debug mode
  static void _setupMemoryMonitoring() {
    if (!kDebugMode) return;

    Timer.periodic(const Duration(minutes: 2), (_) {
      _logMemoryUsage();
    });
  }

  /// Log memory usage for debugging
  static void _logMemoryUsage() {
    if (!kDebugMode) return;

    try {
      final imageCache = PaintingBinding.instance.imageCache;
      debugPrint('Memory Usage - Image Cache: ${imageCache.currentSize}/${imageCache.maximumSize} images, '
          '${(imageCache.currentSizeBytes / 1024 / 1024).toStringAsFixed(2)}MB/'
          '${(imageCache.maximumSizeBytes / 1024 / 1024).toStringAsFixed(2)}MB');
    } catch (e) {
      debugPrint('Error logging memory usage: $e');
    }
  }

  /// Optimize memory usage
  static void optimizeMemory() {
    try {
      // Clear unnecessary image cache entries
      final imageCache = PaintingBinding.instance.imageCache;
      
      // If cache is over 80% full, clear some entries
      if (imageCache.currentSizeBytes > imageCache.maximumSizeBytes * 0.8) {
        imageCache.clear();
        debugPrint('Image cache cleared due to high memory usage');
      }

      // Force garbage collection in debug mode
      if (kDebugMode) {
        _forceGarbageCollection();
      }

    } catch (e) {
      debugPrint('Error optimizing memory: $e');
    }
  }

  /// Force garbage collection (debug mode only)
  static void _forceGarbageCollection() {
    if (!kDebugMode) return;

    try {
      // Create and discard objects to trigger GC
      final temp = List.generate(1000, (i) => Object());
      temp.clear();
    } catch (e) {
      debugPrint('Error forcing garbage collection: $e');
    }
  }

  /// Clean up various caches
  static void cleanupCaches() {
    try {
      // Clean up image cache if it's getting too large
      final imageCache = PaintingBinding.instance.imageCache;
      
      if (imageCache.currentSize > imageCache.maximumSize * 0.9) {
        // Remove oldest entries
        imageCache.clearLiveImages();
        debugPrint('Live images cleared from cache');
      }

    } catch (e) {
      debugPrint('Error cleaning up caches: $e');
    }
  }

  /// Optimize widget build performance
  static Widget optimizedBuilder({
    required Widget Function() builder,
    List<Object?>? dependencies,
  }) {
    return Builder(
      builder: (context) {
        // Use RepaintBoundary to isolate repaints
        return RepaintBoundary(
          child: builder(),
        );
      },
    );
  }

  /// Create an optimized list view for better performance
  static Widget optimizedListView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    ScrollController? controller,
    EdgeInsetsGeometry? padding,
    bool shrinkWrap = false,
  }) {
    return ListView.builder(
      controller: controller,
      padding: padding,
      shrinkWrap: shrinkWrap,
      itemCount: itemCount,
      itemBuilder: (context, index) {
        // Wrap each item in RepaintBoundary for better performance
        return RepaintBoundary(
          key: ValueKey('list_item_$index'),
          child: itemBuilder(context, index),
        );
      },
      // Add caching for better performance
      cacheExtent: 250.0,
    );
  }

  /// Create an optimized grid view
  static Widget optimizedGridView({
    required int itemCount,
    required Widget Function(BuildContext, int) itemBuilder,
    required SliverGridDelegate gridDelegate,
    ScrollController? controller,
    EdgeInsetsGeometry? padding,
    bool shrinkWrap = false,
  }) {
    return GridView.builder(
      controller: controller,
      padding: padding,
      shrinkWrap: shrinkWrap,
      gridDelegate: gridDelegate,
      itemCount: itemCount,
      itemBuilder: (context, index) {
        return RepaintBoundary(
          key: ValueKey('grid_item_$index'),
          child: itemBuilder(context, index),
        );
      },
      cacheExtent: 250.0,
    );
  }

  /// Optimize image loading with caching and compression
  static Widget optimizedNetworkImage({
    required String imageUrl,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    return Image.network(
      imageUrl,
      width: width,
      height: height,
      fit: fit,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) {
          return RepaintBoundary(child: child);
        }
        return placeholder ?? 
          SizedBox(
            width: width,
            height: height,
            child: const Center(
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          );
      },
      errorBuilder: (context, error, stackTrace) {
        return errorWidget ?? 
          SizedBox(
            width: width,
            height: height,
            child: const Icon(Icons.error),
          );
      },
      // Enable caching
      cacheWidth: width?.toInt(),
      cacheHeight: height?.toInt(),
    );
  }

  /// Debounce function calls to improve performance
  static Timer? _debounceTimer;
  
  static void debounce({
    required VoidCallback callback,
    Duration delay = const Duration(milliseconds: 300),
  }) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(delay, callback);
  }

  /// Throttle function calls to improve performance
  static DateTime? _lastThrottleTime;
  
  static void throttle({
    required VoidCallback callback,
    Duration delay = const Duration(milliseconds: 100),
  }) {
    final now = DateTime.now();
    if (_lastThrottleTime == null || 
        now.difference(_lastThrottleTime!) >= delay) {
      _lastThrottleTime = now;
      callback();
    }
  }

  /// Check if device has sufficient resources for heavy operations
  static bool shouldPerformHeavyOperation() {
    try {
      // Check image cache usage
      final imageCache = PaintingBinding.instance.imageCache;
      final cacheUsageRatio = imageCache.currentSizeBytes / imageCache.maximumSizeBytes;
      
      // Don't perform heavy operations if cache is over 90% full
      if (cacheUsageRatio > 0.9) {
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('Error checking device resources: $e');
      return true; // Default to allowing operations
    }
  }

  /// Dispose resources and cleanup
  static void dispose() {
    _memoryOptimizationTimer?.cancel();
    _cacheCleanupTimer?.cancel();
    _debounceTimer?.cancel();
    _isInitialized = false;
    debugPrint('PerformanceOptimizer disposed');
  }
}
