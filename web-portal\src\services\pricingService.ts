import { type QueryConstraint, orderBy, where } from 'firebase/firestore';
import { FirebaseService, type QueryOptions } from './firebaseService';
import type {
  CreateServicePricingInput,
  Currency,
  CurrencyRate,
  Discount,
  PriceCalculation,
  PriceCalculationInput,
  PricingStrategy,
  ServicePricing,
  TaxConfiguration,
  TimePeriod,
  UpdateServicePricingInput,
} from '../types/pricing';

class PricingService extends FirebaseService<ServicePricing> {
  constructor() {
    super('service_pricing');
  }

  // Get pricing configuration for a service
  async getServicePricing(serviceId: string): Promise<ServicePricing | null> {
    const results = await this.query([where('service_id', '==', serviceId)]);
    return results[0] || null;
  }

  // Create pricing configuration for a service
  async createServicePricing(data: CreateServicePricingInput): Promise<ServicePricing> {
    const pricingData = {
      ...data,
      default_zone_multiplier: data.default_zone_multiplier ?? 1.0,
      tax_configuration_ids: data.tax_configuration_ids ?? [],
      emergency_multiplier: data.emergency_multiplier ?? 1.5,
      is_active: data.is_active ?? true,
    };

    return this.create(pricingData as Omit<ServicePricing, 'id'>);
  }

  // Update pricing configuration
  async updateServicePricing(id: string, data: UpdateServicePricingInput): Promise<ServicePricing> {
    return this.update(id, data);
  }

  // Calculate price for a service request
  async calculatePrice(input: PriceCalculationInput): Promise<PriceCalculation> {
    const pricing = await this.getServicePricing(input.service_id);
    if (!pricing || !pricing.is_active) {
      throw new Error('Pricing configuration not found or inactive');
    }

    const calculation: PriceCalculation = {
      base_price: pricing.base_price || 0,
      currency: pricing.currency,
      discounts_applied: [],
      taxes: [],
      subtotal: 0,
      total_discount: 0,
      price_after_discount: 0,
      total_tax: 0,
      final_price: 0,
      calculation_breakdown: [],
    };

    let currentPrice = pricing.base_price || 0;
    calculation.subtotal = currentPrice;
    calculation.calculation_breakdown.push(`Base price: ${currentPrice} ${pricing.currency}`);

    // Apply strategy-specific pricing
    switch (pricing.strategy) {
      case 'fixed':
        // No additional calculations needed
        break;

      case 'tiered':
        if (input.selected_tier_id && pricing.tiers) {
          const selectedTier = pricing.tiers.find((tier) => tier.id === input.selected_tier_id);
          if (selectedTier) {
            currentPrice = selectedTier.base_price;
            calculation.base_price = selectedTier.base_price;
            calculation.subtotal = selectedTier.base_price;
            calculation.tier_selected = selectedTier.id;
            calculation.calculation_breakdown = [
              `Selected tier: ${this.getTierName(selectedTier)} - ${selectedTier.base_price} ${selectedTier.currency}`,
            ];
          }
        }
        break;

      case 'time_based':
        if (pricing.time_rules && input.requested_datetime) {
          const timeMultiplier = this.calculateTimeMultiplier(
            pricing.time_rules,
            input.requested_datetime,
            input.is_emergency,
          );
          if (timeMultiplier !== 1.0) {
            calculation.time_multiplier = timeMultiplier;
            currentPrice *= timeMultiplier;
            calculation.calculation_breakdown.push(`Time-based multiplier: ${timeMultiplier}x`);
          }
        }
        break;

      case 'distance_based':
        if (pricing.distance_pricing && input.customer_location) {
          const distanceMultiplier = this.calculateDistanceMultiplier(
            pricing.distance_pricing,
            input.customer_location,
          );
          if (distanceMultiplier !== 1.0) {
            calculation.distance_multiplier = distanceMultiplier;
            currentPrice *= distanceMultiplier;
            calculation.calculation_breakdown.push(
              `Distance-based adjustment: ${distanceMultiplier}x`,
            );
          }
        }
        break;
    }

    // Apply geographic zone multiplier
    if (pricing.geographic_zones && input.customer_location) {
      const zoneMultiplier = this.calculateZoneMultiplier(
        pricing.geographic_zones,
        input.customer_location,
        pricing.default_zone_multiplier,
      );
      if (zoneMultiplier !== 1.0) {
        calculation.zone_multiplier = zoneMultiplier;
        currentPrice *= zoneMultiplier;
        calculation.calculation_breakdown.push(`Zone multiplier: ${zoneMultiplier}x`);
      }
    }

    // Apply emergency multiplier
    if (input.is_emergency && pricing.emergency_multiplier !== 1.0) {
      calculation.emergency_multiplier = pricing.emergency_multiplier;
      currentPrice *= pricing.emergency_multiplier;
      calculation.calculation_breakdown.push(`Emergency service: ${pricing.emergency_multiplier}x`);
    }

    // Apply min/max price constraints
    if (pricing.min_price && currentPrice < pricing.min_price) {
      currentPrice = pricing.min_price;
      calculation.calculation_breakdown.push(
        `Applied minimum price: ${pricing.min_price} ${pricing.currency}`,
      );
    }
    if (pricing.max_price && currentPrice > pricing.max_price) {
      currentPrice = pricing.max_price;
      calculation.calculation_breakdown.push(
        `Applied maximum price: ${pricing.max_price} ${pricing.currency}`,
      );
    }

    calculation.price_after_discount = currentPrice;
    calculation.final_price = currentPrice;

    return calculation;
  }

  // Get available currencies
  getAvailableCurrencies(): Currency[] {
    return ['USD', 'EUR', 'GBP', 'AED', 'SAR', 'QAR', 'KWD', 'BHD'];
  }

  // Get available pricing strategies
  getAvailableStrategies(): { value: PricingStrategy; label: string; description: string }[] {
    return [
      {
        value: 'fixed',
        label: 'Fixed Pricing',
        description: 'Simple fixed price for all requests',
      },
      {
        value: 'tiered',
        label: 'Tiered Pricing',
        description: 'Multiple pricing tiers with different features',
      },
      {
        value: 'time_based',
        label: 'Time-based Pricing',
        description: 'Different prices based on time of day/week',
      },
      {
        value: 'distance_based',
        label: 'Distance-based Pricing',
        description: 'Price varies based on distance to customer',
      },
      {
        value: 'dynamic',
        label: 'Dynamic Pricing',
        description: 'AI-powered dynamic pricing based on demand',
      },
    ];
  }

  // Format price for display
  formatPrice(amount: number, currency: Currency): string {
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });

    return formatter.format(amount);
  }

  // Helper methods
  private getTierName(tier: { name: string | Record<string, string> }): string {
    if (typeof tier.name === 'string') {
      return tier.name;
    }
    return tier.name['en'] || Object.values(tier.name)[0] || 'Unnamed Tier';
  }

  private calculateTimeMultiplier(
    timeRules: any[],
    requestedDate: Date,
    isEmergency: boolean = false,
  ): number {
    if (isEmergency) {
      const emergencyRule = timeRules.find((rule) => rule.period === 'emergency' && rule.is_active);
      if (emergencyRule) return emergencyRule.multiplier;
    }

    const dayOfWeek = requestedDate.getDay();
    const hour = requestedDate.getHours();
    const timeString = `${hour.toString().padStart(2, '0')}:${requestedDate.getMinutes().toString().padStart(2, '0')}`;

    // Check for specific time-based rules
    for (const rule of timeRules) {
      if (!rule.is_active) continue;

      if (rule.days_of_week && !rule.days_of_week.includes(dayOfWeek)) continue;

      switch (rule.period) {
        case 'business_hours':
          if (dayOfWeek >= 1 && dayOfWeek <= 5 && hour >= 9 && hour < 17) {
            return rule.multiplier;
          }
          break;
        case 'after_hours':
          if (dayOfWeek >= 1 && dayOfWeek <= 5 && (hour < 9 || hour >= 17)) {
            return rule.multiplier;
          }
          break;
        case 'weekends':
          if (dayOfWeek === 0 || dayOfWeek === 6) {
            return rule.multiplier;
          }
          break;
        case 'holidays':
          // This would need a holiday calendar integration
          // For now, returning default
          break;
      }
    }

    return 1.0;
  }

  private calculateDistanceMultiplier(
    distancePricing: any,
    customerLocation: { lat: number; lng: number },
  ): number {
    // This would need integration with a mapping service to calculate actual distance
    // For now, returning a simple calculation
    // In production, you'd use Google Maps Distance Matrix API or similar

    // Placeholder: assume base radius is free, then charge per km
    const estimatedDistance = 5; // km - this should be calculated

    if (estimatedDistance <= distancePricing.base_radius) {
      return 1.0;
    }

    const extraDistance = estimatedDistance - distancePricing.base_radius;
    const extraCost = extraDistance * distancePricing.price_per_km;

    // Return as multiplier (this is simplified - in practice you'd add to base price)
    return 1.0 + extraCost / 100; // Assuming base price is around 100 for calculation
  }

  private calculateZoneMultiplier(
    zones: any[],
    customerLocation: { lat: number; lng: number },
    defaultMultiplier: number,
  ): number {
    // Check if customer location falls within any defined zone
    for (const zone of zones) {
      if (zone.boundaries) {
        for (const boundary of zone.boundaries) {
          const distance = this.calculateHaversineDistance(
            customerLocation.lat,
            customerLocation.lng,
            boundary.lat,
            boundary.lng,
          );

          if (distance <= boundary.radius) {
            return zone.multiplier;
          }
        }
      }
    }

    return defaultMultiplier;
  }

  private calculateHaversineDistance(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number,
  ): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);

    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) *
        Math.cos(this.toRadians(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }
}

// Tax Configuration Service
class TaxConfigurationService extends FirebaseService<TaxConfiguration> {
  constructor() {
    super('tax_configurations');
  }

  async getActiveTaxConfigurations(): Promise<TaxConfiguration[]> {
    return this.query([where('is_active', '==', true)]);
  }

  async getTaxConfigurationsByRegion(region: string): Promise<TaxConfiguration[]> {
    return this.query([
      where('applicable_regions', 'array-contains', region),
      where('is_active', '==', true),
    ]);
  }
}

// Discount Service
class DiscountService extends FirebaseService<Discount> {
  constructor() {
    super('discounts');
  }

  async getActiveDiscounts(): Promise<Discount[]> {
    const now = new Date();
    return this.query([
      where('is_active', '==', true),
      where('valid_from', '<=', now),
      where('valid_until', '>=', now),
    ]);
  }

  async validateDiscountCode(code: string): Promise<Discount | null> {
    // This would typically search by a discount code field
    // For now, searching by name
    const discounts = await this.getActiveDiscounts();
    return (
      discounts.find((discount) =>
        typeof discount.name === 'string'
          ? discount.name.toLowerCase() === code.toLowerCase()
          : Object.values(discount.name).some((name) => name.toLowerCase() === code.toLowerCase()),
      ) || null
    );
  }
}

// Export service instances
export const pricingService = new PricingService();
export const taxConfigurationService = new TaxConfigurationService();
export const discountService = new DiscountService();
export default pricingService;
