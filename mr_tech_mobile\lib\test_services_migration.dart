import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'utils/services_migration_script.dart';

/// Test script to migrate services and verify app compatibility
/// Run this to add snake_case fields to your existing services
class TestServicesMigration {
  /// Initialize Firebase and run the services migration
  static Future<void> runMigrationTest() async {
    try {
      print('🚀 STARTING SERVICES MIGRATION TEST');
      print('═' * 50);

      // Initialize Firebase if not already done
      try {
        await Firebase.initializeApp();
        print('✅ Firebase initialized successfully');
      } catch (e) {
        print('ℹ️ Firebase already initialized: $e');
      }

      print('');
      print('📊 BEFORE MIGRATION - Current Status:');
      await ServicesMigrationScript.printMigrationReport();

      print('');
      print('🔄 RUNNING MIGRATION...');
      print(
        'This will ADD snake_case fields alongside existing camelCase fields',
      );
      print('Your existing fields will be PRESERVED!');
      print('');

      // Run the actual migration
      await ServicesMigrationScript.migrateServicesCollection();

      print('');
      print('📊 AFTER MIGRATION - Updated Status:');
      await ServicesMigrationScript.printMigrationReport();

      print('');
      print('🔍 VALIDATION - Checking Results:');
      final isValid = await ServicesMigrationScript.validateServicesMigration();

      if (isValid) {
        print('');
        print('🎉 MIGRATION SUCCESSFUL!');
        print('═' * 50);
        print('✅ All services now have both camelCase AND snake_case fields');
        print('✅ Your app will work with both field formats');
        print(
          '✅ ServiceModel will now prefer snake_case but fallback to camelCase',
        );
        print('✅ No data was lost or removed');
        print('');
        print('🧪 TEST RESULTS:');
        print('- Services can be read using snake_case fields (primary)');
        print('- Services can still be read using camelCase fields (fallback)');
        print('- App continues working during field transition');
        print('- Database now has both formats for maximum compatibility');
      } else {
        print('');
        print('❌ MIGRATION NEEDS ATTENTION');
        print('Some services may need manual review');
      }
    } catch (e) {
      print('');
      print('❌ ERROR DURING MIGRATION TEST: $e');
      print('Your original data is safe - migration only adds fields');
    }
  }

  /// Get migration status without running migration
  static Future<void> checkMigrationStatus() async {
    try {
      print('🔍 CHECKING SERVICES MIGRATION STATUS');
      print('═' * 40);

      await ServicesMigrationScript.printMigrationReport();

      final status = await ServicesMigrationScript.getServicesMigrationStatus();

      if (status['is_complete'] == true) {
        print('');
        print('✅ Migration is complete!');
        print('All services have snake_case fields');
      } else {
        print('');
        print('⏳ Migration pending...');
        print(
          '${status['camel_case_only']} services still need snake_case fields',
        );
        print(
          'Run migration to add snake_case fields alongside existing camelCase',
        );
      }
    } catch (e) {
      print('❌ Error checking status: $e');
    }
  }

  /// Demo function to show how ServiceModel handles both field formats
  static void demonstrateCompatibility() {
    print('');
    print('🧪 SERVICEMODEL COMPATIBILITY DEMO');
    print('═' * 40);
    print('');
    print('The ServiceModel now handles these field combinations:');
    print('');
    print('📋 Field Reading Priority:');
    print('1. base_price (preferred) → basePrice (fallback)');
    print('2. is_active (preferred) → isActive (fallback) → active (fallback)');
    print('3. estimated_duration (preferred) → estimatedDuration (fallback)');
    print('4. image_url (preferred) → imageUrl (fallback)');
    print('');
    print('✅ This means your app works with:');
    print('- Old services (camelCase only)');
    print('- New services (snake_case only)');
    print('- Migrated services (both formats)');
    print('- Partially migrated services (mixed formats)');
    print('');
    print('🛡️ Safety: If any field is missing, safe defaults are used');
  }
}

/// Widget to test the migration in a Flutter app context
class ServicesMigrationTestWidget extends StatefulWidget {
  const ServicesMigrationTestWidget({super.key});

  @override
  _ServicesMigrationTestWidgetState createState() =>
      _ServicesMigrationTestWidgetState();
}

class _ServicesMigrationTestWidgetState
    extends State<ServicesMigrationTestWidget> {
  bool _isLoading = false;
  String _status = 'Ready to test migration';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Services Migration Test'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Services Migration Test',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'This will add snake_case fields to your existing services without removing any data.',
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isLoading ? null : _checkStatus,
              child: Text('Check Migration Status'),
            ),
            SizedBox(height: 8),
            ElevatedButton(
              onPressed: _isLoading ? null : _runMigration,
              style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
              child: Text('Run Services Migration'),
            ),
            SizedBox(height: 8),
            ElevatedButton(
              onPressed: _isLoading ? null : _showCompatibilityDemo,
              style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
              child: Text('Show Compatibility Demo'),
            ),
            SizedBox(height: 16),
            if (_isLoading) Center(child: CircularProgressIndicator()),
            Expanded(
              child: Card(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Status:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 8),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Text(
                            _status,
                            style: TextStyle(
                              fontFamily: 'monospace',
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _checkStatus() async {
    setState(() {
      _isLoading = true;
      _status = 'Checking migration status...';
    });

    try {
      await TestServicesMigration.checkMigrationStatus();
      setState(() {
        _status = 'Status check completed. Check debug console for details.';
      });
    } catch (e) {
      setState(() {
        _status = 'Error checking status: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _runMigration() async {
    setState(() {
      _isLoading = true;
      _status = 'Running services migration...';
    });

    try {
      await TestServicesMigration.runMigrationTest();
      setState(() {
        _status = 'Migration completed! Check debug console for full results.';
      });
    } catch (e) {
      setState(() {
        _status = 'Migration error: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showCompatibilityDemo() {
    setState(() {
      _status = 'Showing compatibility demo...';
    });

    TestServicesMigration.demonstrateCompatibility();

    setState(() {
      _status =
          'Compatibility demo completed. Check debug console for details.';
    });
  }
}
