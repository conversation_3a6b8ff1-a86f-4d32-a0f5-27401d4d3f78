import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'package:mr_tech_mobile/services/translation_service.dart';
import 'package:mr_tech_mobile/models/request_model.dart';
import 'package:mr_tech_mobile/views/chat_screen.dart';
import 'package:mr_tech_mobile/views/dashboard_screen.dart';
import 'package:mr_tech_mobile/views/services_screen.dart';

void main() {
  group('Mobile App UI Improvements Tests', () {
    testWidgets('Chat Screen Header displays properly', (WidgetTester tester) async {
      // Create a mock request
      final mockRequest = RequestModel(
        id: 'test-123',
        customerId: 'customer-123',
        serviceId: 'service-123',
        serviceName: 'Test Service',
        serviceDescription: 'Test Description',
        customerIssue: 'Test Issue',
        technicianId: 'tech-123',
        technicianName: '<PERSON>',
        status: RequestStatus.inProgress,
        amount: 100.0,
        isPaid: true,
        chatActive: true,
        createdAt: DateTime.now(),
      );

      // Build the chat screen with providers
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider(
            create: (context) => TranslationService(),
            child: ChatScreen(request: mockRequest),
          ),
        ),
      );

      // Wait for the widget to settle
      await tester.pumpAndSettle();

      // Verify that the chat screen builds without errors
      expect(find.byType(ChatScreen), findsOneWidget);
      
      // Verify that the app bar is present
      expect(find.byType(AppBar), findsOneWidget);
      
      // Verify that technician name is displayed
      expect(find.text('John Doe'), findsOneWidget);
    });

    testWidgets('Dashboard Screen scrolls properly', (WidgetTester tester) async {
      // Build the dashboard screen with providers
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider(
            create: (context) => TranslationService(),
            child: const DashboardScreen(),
          ),
        ),
      );

      // Wait for the widget to settle
      await tester.pumpAndSettle();

      // Verify that the dashboard screen builds without errors
      expect(find.byType(DashboardScreen), findsOneWidget);
      
      // Verify that CustomScrollView is present
      expect(find.byType(CustomScrollView), findsOneWidget);
      
      // Try to scroll and verify it works
      await tester.drag(find.byType(CustomScrollView), const Offset(0, -300));
      await tester.pumpAndSettle();
      
      // If we get here without errors, scrolling works
      expect(find.byType(CustomScrollView), findsOneWidget);
    });

    testWidgets('Services Screen displays service cards properly', (WidgetTester tester) async {
      // Build the services screen with providers
      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider(
            create: (context) => TranslationService(),
            child: const ServicesScreen(),
          ),
        ),
      );

      // Wait for the widget to settle
      await tester.pumpAndSettle();

      // Verify that the services screen builds without errors
      expect(find.byType(ServicesScreen), findsOneWidget);
      
      // Verify that CustomScrollView is present
      expect(find.byType(CustomScrollView), findsOneWidget);
      
      // Try to scroll and verify it works
      await tester.drag(find.byType(CustomScrollView), const Offset(0, -300));
      await tester.pumpAndSettle();
      
      // If we get here without errors, scrolling works
      expect(find.byType(CustomScrollView), findsOneWidget);
    });

    testWidgets('Image viewer opens when image is tapped', (WidgetTester tester) async {
      // This test would require more complex setup with mock data
      // For now, we'll just verify the basic structure
      
      final mockRequest = RequestModel(
        id: 'test-123',
        customerId: 'customer-123',
        serviceId: 'service-123',
        serviceName: 'Test Service',
        serviceDescription: 'Test Description',
        customerIssue: 'Test Issue',
        status: RequestStatus.inProgress,
        amount: 100.0,
        isPaid: true,
        chatActive: true,
        createdAt: DateTime.now(),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: ChangeNotifierProvider(
            create: (context) => TranslationService(),
            child: ChatScreen(request: mockRequest),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify that the chat screen builds without errors
      expect(find.byType(ChatScreen), findsOneWidget);
    });
  });
}
