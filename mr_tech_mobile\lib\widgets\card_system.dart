import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import '../utils/spacing.dart';
import '../utils/typography.dart';

/// Comprehensive card system for consistent card styling throughout the app
/// Provides different card variants following Material 3 design principles
class CardSystem {
  // Private constructor to prevent instantiation
  CardSystem._();

  /// Standard card with basic styling
  static Widget standard(
    BuildContext context, {
    required Widget child,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    VoidCallback? onTap,
    Color? backgroundColor,
    BorderRadius? borderRadius,
  }) {
    return Card(
      margin: margin ?? Spacing.cardMargin,
      color: backgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: borderRadius ?? AppTheme.mediumRadius,
      ),
      child: _buildCardContent(
        context,
        child: child,
        padding: padding ?? Spacing.cardPadding,
        onTap: onTap,
        borderRadius: borderRadius ?? AppTheme.mediumRadius,
      ),
    );
  }

  /// Elevated card with shadow
  static Widget elevated(
    BuildContext context, {
    required Widget child,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    VoidCallback? onTap,
    Color? backgroundColor,
    BorderRadius? borderRadius,
    double elevation = 4.0,
  }) {
    return Card(
      margin: margin ?? Spacing.cardMargin,
      elevation: elevation,
      color: backgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: borderRadius ?? AppTheme.mediumRadius,
      ),
      child: _buildCardContent(
        context,
        child: child,
        padding: padding ?? Spacing.cardPadding,
        onTap: onTap,
        borderRadius: borderRadius ?? AppTheme.mediumRadius,
      ),
    );
  }

  /// Outlined card with border
  static Widget outlined(
    BuildContext context, {
    required Widget child,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    VoidCallback? onTap,
    Color? backgroundColor,
    Color? borderColor,
    BorderRadius? borderRadius,
    double borderWidth = 1.0,
  }) {
    final theme = Theme.of(context);
    final effectiveBorderColor = borderColor ?? theme.colorScheme.outline.withOpacity(0.2);

    return Container(
      margin: margin ?? Spacing.cardMargin,
      decoration: BoxDecoration(
        color: backgroundColor ?? theme.colorScheme.surface,
        borderRadius: borderRadius ?? AppTheme.mediumRadius,
        border: Border.all(
          color: effectiveBorderColor,
          width: borderWidth,
        ),
      ),
      child: _buildCardContent(
        context,
        child: child,
        padding: padding ?? Spacing.cardPadding,
        onTap: onTap,
        borderRadius: borderRadius ?? AppTheme.mediumRadius,
      ),
    );
  }

  /// Filled card with background color
  static Widget filled(
    BuildContext context, {
    required Widget child,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    VoidCallback? onTap,
    Color? backgroundColor,
    BorderRadius? borderRadius,
  }) {
    final theme = Theme.of(context);
    final effectiveBackgroundColor = backgroundColor ?? theme.colorScheme.surfaceContainer;

    return Container(
      margin: margin ?? Spacing.cardMargin,
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: borderRadius ?? AppTheme.mediumRadius,
      ),
      child: _buildCardContent(
        context,
        child: child,
        padding: padding ?? Spacing.cardPadding,
        onTap: onTap,
        borderRadius: borderRadius ?? AppTheme.mediumRadius,
      ),
    );
  }

  /// Surface card with Material 3 surface tinting
  static Widget surface(
    BuildContext context, {
    required Widget child,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    VoidCallback? onTap,
    BorderRadius? borderRadius,
    int elevation = 1,
  }) {
    final theme = Theme.of(context);
    final surfaceColor = ElevationOverlay.applySurfaceTint(
      theme.colorScheme.surface,
      theme.colorScheme.surfaceTint,
      elevation.toDouble(),
    );

    return Container(
      margin: margin ?? Spacing.cardMargin,
      decoration: BoxDecoration(
        color: surfaceColor,
        borderRadius: borderRadius ?? AppTheme.mediumRadius,
        boxShadow: AppTheme.adaptiveSubtleShadow(context),
        border: theme.brightness == Brightness.dark
          ? Border.all(
              color: theme.colorScheme.outline.withOpacity(0.2),
              width: 1,
            )
          : null,
      ),
      child: _buildCardContent(
        context,
        child: child,
        padding: padding ?? Spacing.cardPadding,
        onTap: onTap,
        borderRadius: borderRadius ?? AppTheme.mediumRadius,
      ),
    );
  }

  /// Interactive card with hover and press states
  static Widget interactive(
    BuildContext context, {
    required Widget child,
    required VoidCallback onTap,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    Color? backgroundColor,
    BorderRadius? borderRadius,
    bool showRipple = true,
  }) {
    return Card(
      margin: margin ?? Spacing.cardMargin,
      color: backgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: borderRadius ?? AppTheme.mediumRadius,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: borderRadius ?? AppTheme.mediumRadius,
        splashColor: showRipple ? null : Colors.transparent,
        highlightColor: showRipple ? null : Colors.transparent,
        child: Padding(
          padding: padding ?? Spacing.cardPadding,
          child: child,
        ),
      ),
    );
  }

  /// Status card with colored border and background
  static Widget status(
    BuildContext context, {
    required Widget child,
    required CardStatus status,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    VoidCallback? onTap,
    BorderRadius? borderRadius,
  }) {
    final theme = Theme.of(context);
    final colors = _getStatusColors(theme, status);

    return Container(
      margin: margin ?? Spacing.cardMargin,
      decoration: BoxDecoration(
        color: colors.backgroundColor,
        borderRadius: borderRadius ?? AppTheme.mediumRadius,
        border: Border.all(
          color: colors.borderColor,
          width: 2.0,
        ),
      ),
      child: _buildCardContent(
        context,
        child: child,
        padding: padding ?? Spacing.cardPadding,
        onTap: onTap,
        borderRadius: borderRadius ?? AppTheme.mediumRadius,
      ),
    );
  }

  /// Compact card with reduced padding
  static Widget compact(
    BuildContext context, {
    required Widget child,
    EdgeInsetsGeometry? margin,
    VoidCallback? onTap,
    Color? backgroundColor,
    BorderRadius? borderRadius,
  }) {
    return Card(
      margin: margin ?? Spacing.marginS,
      color: backgroundColor,
      shape: RoundedRectangleBorder(
        borderRadius: borderRadius ?? BorderRadius.circular(8),
      ),
      child: _buildCardContent(
        context,
        child: child,
        padding: Spacing.paddingS,
        onTap: onTap,
        borderRadius: borderRadius ?? BorderRadius.circular(8),
      ),
    );
  }

  // Helper method to build card content with consistent structure
  static Widget _buildCardContent(
    BuildContext context, {
    required Widget child,
    required EdgeInsetsGeometry padding,
    VoidCallback? onTap,
    BorderRadius? borderRadius,
  }) {
    final content = Padding(
      padding: padding,
      child: child,
    );

    if (onTap != null) {
      return InkWell(
        onTap: onTap,
        borderRadius: borderRadius,
        child: content,
      );
    }

    return content;
  }

  // Helper method to get status colors
  static _StatusColors _getStatusColors(ThemeData theme, CardStatus status) {
    switch (status) {
      case CardStatus.success:
        return _StatusColors(
          backgroundColor: theme.colorScheme.primaryContainer.withOpacity(0.1),
          borderColor: theme.colorScheme.primary,
        );
      case CardStatus.warning:
        return _StatusColors(
          backgroundColor: Colors.orange.withOpacity(0.1),
          borderColor: Colors.orange,
        );
      case CardStatus.error:
        return _StatusColors(
          backgroundColor: theme.colorScheme.errorContainer.withOpacity(0.1),
          borderColor: theme.colorScheme.error,
        );
      case CardStatus.info:
        return _StatusColors(
          backgroundColor: Colors.blue.withOpacity(0.1),
          borderColor: Colors.blue,
        );
      case CardStatus.neutral:
        return _StatusColors(
          backgroundColor: theme.colorScheme.surfaceContainer,
          borderColor: theme.colorScheme.outline,
        );
    }
  }
}

/// Card status enum for status cards
enum CardStatus {
  success,
  warning,
  error,
  info,
  neutral,
}

/// Helper class for status colors
class _StatusColors {
  final Color backgroundColor;
  final Color borderColor;

  _StatusColors({
    required this.backgroundColor,
    required this.borderColor,
  });
}

/// Specialized card components for common use cases
class SpecializedCards {
  // Private constructor to prevent instantiation
  SpecializedCards._();

  /// Service card for displaying service information
  static Widget service(
    BuildContext context, {
    required String title,
    String? description,
    IconData? icon,
    String? imageUrl,
    VoidCallback? onTap,
    bool isSelected = false,
  }) {
    final theme = Theme.of(context);

    return CardSystem.interactive(
      context,
      onTap: onTap ?? () {},
      margin: Spacing.cardMargin,
      padding: EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon or image
          Container(
            height: 80,
            decoration: BoxDecoration(
              color: theme.colorScheme.primaryContainer.withOpacity(0.1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Center(
              child: icon != null
                ? Icon(
                    icon,
                    size: 40,
                    color: theme.colorScheme.primary,
                  )
                : Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primary.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.category,
                      color: theme.colorScheme.primary,
                    ),
                  ),
            ),
          ),
          // Content
          Padding(
            padding: Spacing.cardPadding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Typography.titleMedium(context),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                if (description != null) ...[
                  Spacing.verticalXS,
                  Text(
                    description,
                    style: Typography.bodySmall(context).subtle(context),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Request card for displaying request information
  static Widget request(
    BuildContext context, {
    required String title,
    required String status,
    String? description,
    String? date,
    VoidCallback? onTap,
    Color? statusColor,
  }) {
    final theme = Theme.of(context);
    final effectiveStatusColor = statusColor ?? theme.colorScheme.primary;

    return CardSystem.outlined(
      context,
      onTap: onTap,
      borderColor: effectiveStatusColor.withOpacity(0.3),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and status
          Row(
            children: [
              Expanded(
                child: Text(
                  title,
                  style: Typography.titleMedium(context),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Spacing.horizontalS,
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: Spacing.s,
                  vertical: Spacing.xs,
                ),
                decoration: BoxDecoration(
                  color: effectiveStatusColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  status,
                  style: Typography.labelSmall(context).copyWith(
                    color: effectiveStatusColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          if (description != null) ...[
            Spacing.verticalS,
            Text(
              description,
              style: Typography.bodyMedium(context),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
          if (date != null) ...[
            Spacing.verticalS,
            Text(
              date,
              style: Typography.labelSmall(context).subtle(context),
            ),
          ],
        ],
      ),
    );
  }

  /// Info card for displaying information with icon
  static Widget info(
    BuildContext context, {
    required String title,
    required String content,
    IconData? icon,
    Color? iconColor,
    VoidCallback? onTap,
  }) {
    final theme = Theme.of(context);
    final effectiveIconColor = iconColor ?? theme.colorScheme.primary;

    return CardSystem.filled(
      context,
      onTap: onTap,
      child: Row(
        children: [
          if (icon != null) ...[
            Container(
              padding: Spacing.paddingS,
              decoration: BoxDecoration(
                color: effectiveIconColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: effectiveIconColor,
                size: 24,
              ),
            ),
            Spacing.horizontalM,
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Typography.titleSmall(context),
                ),
                Spacing.verticalXS,
                Text(
                  content,
                  style: Typography.bodyMedium(context),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Stat card for displaying statistics
  static Widget stat(
    BuildContext context, {
    required String label,
    required String value,
    IconData? icon,
    Color? color,
    String? subtitle,
    VoidCallback? onTap,
  }) {
    final theme = Theme.of(context);
    final effectiveColor = color ?? theme.colorScheme.primary;

    return CardSystem.surface(
      context,
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (icon != null) ...[
                Icon(
                  icon,
                  color: effectiveColor,
                  size: 20,
                ),
                Spacing.horizontalS,
              ],
              Expanded(
                child: Text(
                  label,
                  style: Typography.labelMedium(context).subtle(context),
                ),
              ),
            ],
          ),
          Spacing.verticalS,
          Text(
            value,
            style: Typography.headlineSmall(context).copyWith(
              color: effectiveColor,
              fontWeight: FontWeight.w600,
            ),
          ),
          if (subtitle != null) ...[
            Spacing.verticalXS,
            Text(
              subtitle,
              style: Typography.labelSmall(context).subtle(context),
            ),
          ],
        ],
      ),
    );
  }
}
