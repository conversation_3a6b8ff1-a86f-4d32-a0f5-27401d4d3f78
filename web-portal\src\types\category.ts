import type { Timestamp } from 'firebase/firestore';

export interface ServiceCategory {
  id: string;
  name: string | Record<string, string>; // Multilingual support
  description?: string | Record<string, string>;
  icon?: string; // Icon name or URL
  color?: string; // Hex color for UI
  is_active: boolean;
  sort_order: number;
  metadata?: Record<string, any>;
  created_at: Timestamp | Date;
  updated_at?: Timestamp | Date;
}

export interface CreateCategoryInput {
  name: string | Record<string, string>;
  description?: string | Record<string, string>;
  icon?: string;
  color?: string;
  is_active?: boolean;
  sort_order?: number;
  metadata?: Record<string, any>;
}

export interface UpdateCategoryInput {
  name?: string | Record<string, string>;
  description?: string | Record<string, string>;
  icon?: string;
  color?: string;
  is_active?: boolean;
  sort_order?: number;
  metadata?: Record<string, any>;
}

// Predefined categories that can be used as defaults
export const DEFAULT_CATEGORIES: Omit<CreateCategoryInput, 'created_at'>[] = [
  {
    name: { en: 'Phone Repair', ar: 'إصلاح الهواتف' },
    description: {
      en: 'Mobile phone and smartphone repair services',
      ar: 'خدمات إصلاح الهواتف المحمولة والذكية',
    },
    icon: 'Smartphone',
    color: '#3B82F6',
    sort_order: 1,
    is_active: true,
  },
  {
    name: { en: 'Computer Service', ar: 'خدمة الكمبيوتر' },
    description: {
      en: 'Desktop and laptop computer repair and maintenance',
      ar: 'إصلاح وصيانة أجهزة الكمبيوتر المكتبية والمحمولة',
    },
    icon: 'Monitor',
    color: '#059669',
    sort_order: 2,
    is_active: true,
  },
  {
    name: { en: 'Software Support', ar: 'دعم البرمجيات' },
    description: {
      en: 'Software installation, troubleshooting, and support',
      ar: 'تثبيت البرمجيات وإصلاح الأخطاء والدعم',
    },
    icon: 'Code',
    color: '#7C3AED',
    sort_order: 3,
    is_active: true,
  },
  {
    name: { en: 'Network Setup', ar: 'إعداد الشبكة' },
    description: {
      en: 'Network installation and configuration services',
      ar: 'خدمات تثبيت وتكوين الشبكات',
    },
    icon: 'Wifi',
    color: '#DC2626',
    sort_order: 4,
    is_active: true,
  },
  {
    name: { en: 'Data Recovery', ar: 'استعادة البيانات' },
    description: {
      en: 'Data recovery and backup services',
      ar: 'خدمات استعادة البيانات والنسخ الاحتياطي',
    },
    icon: 'HardDrive',
    color: '#EA580C',
    sort_order: 5,
    is_active: true,
  },
  {
    name: { en: 'Gaming Setup', ar: 'إعداد الألعاب' },
    description: { en: 'Gaming hardware and software setup', ar: 'إعداد أجهزة وبرمجيات الألعاب' },
    icon: 'Gamepad2',
    color: '#8B5CF6',
    sort_order: 6,
    is_active: true,
  },
];
