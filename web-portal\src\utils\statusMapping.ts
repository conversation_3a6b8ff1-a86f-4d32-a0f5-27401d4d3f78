import { RequestStatus } from '../types/request';

/**
 * Status mapping utility to handle synchronization between mobile app and web portal
 *
 * Mobile app uses: 'payment_pending', 'pending', 'approved', 'in-progress', 'completed', 'cancelled', 'refused'
 * Web portal uses: RequestStatus enum with 'in_progress' (underscore)
 */

// Mobile app status strings (as they appear in Firestore)
export type MobileAppStatus =
  | 'payment_pending'
  | 'pending'
  | 'approved'
  | 'in-progress' // Note: mobile app uses hyphen
  | 'completed'
  | 'cancelled'
  | 'refused';

/**
 * Maps mobile app status strings to web portal RequestStatus enum
 */
export const mobileToWebStatus = (mobileStatus: string): RequestStatus => {
  switch (mobileStatus) {
    case 'payment_pending':
      return RequestStatus.PAYMENT_PENDING;
    case 'pending':
      return RequestStatus.PENDING;
    case 'approved':
      return RequestStatus.APPROVED;
    case 'in-progress': // Note: mobile app uses hyphen
    case 'inProgress': // Alternative format sometimes used
    case 'in_progress': // Web portal format
      return RequestStatus.IN_PROGRESS;
    case 'completed':
      return RequestStatus.COMPLETED;
    case 'cancelled':
      return RequestStatus.CANCELLED;
    case 'refused':
      return RequestStatus.REFUSED;
    default:
      console.warn(`Unknown mobile status: ${mobileStatus}, defaulting to PENDING`);
      return RequestStatus.PENDING;
  }
};

/**
 * Maps web portal RequestStatus enum to mobile app status strings
 */
export const webToMobileStatus = (webStatus: RequestStatus): MobileAppStatus => {
  switch (webStatus) {
    case RequestStatus.PAYMENT_PENDING:
      return 'payment_pending';
    case RequestStatus.PENDING:
      return 'pending';
    case RequestStatus.APPROVED:
      return 'approved';
    case RequestStatus.IN_PROGRESS:
      // The mobile app expects 'in-progress' with a hyphen
      // But some places in the code use 'inProgress' (camelCase)
      // We'll use the hyphen version as it's more widely used
      return 'in-progress';
    case RequestStatus.COMPLETED:
      return 'completed';
    case RequestStatus.CANCELLED:
      return 'cancelled';
    case RequestStatus.REFUSED:
      return 'refused';
    default:
      console.warn(`Unknown web status: ${webStatus}, defaulting to pending`);
      return 'pending';
  }
};

/**
 * Normalizes status from any format to web portal RequestStatus
 */
export const normalizeStatus = (status: string | RequestStatus): RequestStatus => {
  if (typeof status === 'string') {
    return mobileToWebStatus(status);
  }
  return status;
};

/**
 * Validates if a status string is a valid mobile app status
 */
export const isValidMobileStatus = (status: string): status is MobileAppStatus => {
  const validStatuses: MobileAppStatus[] = [
    'payment_pending',
    'pending',
    'approved',
    'in-progress',
    'completed',
    'cancelled',
    'refused',
  ];
  return validStatuses.includes(status as MobileAppStatus);
};

/**
 * Gets all valid status transitions for a given status
 */
export const getValidStatusTransitions = (currentStatus: RequestStatus): RequestStatus[] => {
  switch (currentStatus) {
    case RequestStatus.PAYMENT_PENDING:
      return [RequestStatus.PENDING, RequestStatus.CANCELLED];
    case RequestStatus.PENDING:
      return [RequestStatus.APPROVED, RequestStatus.CANCELLED, RequestStatus.REFUSED];
    case RequestStatus.APPROVED:
      return [RequestStatus.IN_PROGRESS, RequestStatus.CANCELLED];
    case RequestStatus.IN_PROGRESS:
      return [RequestStatus.COMPLETED, RequestStatus.CANCELLED];
    case RequestStatus.COMPLETED:
      return []; // Terminal state
    case RequestStatus.CANCELLED:
      return []; // Terminal state
    case RequestStatus.REFUSED:
      return []; // Terminal state
    default:
      return [];
  }
};

/**
 * Checks if a status transition is valid
 */
export const isValidStatusTransition = (
  fromStatus: RequestStatus,
  toStatus: RequestStatus,
): boolean => {
  const validTransitions = getValidStatusTransitions(fromStatus);
  return validTransitions.includes(toStatus);
};
