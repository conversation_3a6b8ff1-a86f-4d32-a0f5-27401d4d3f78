# Notification Sounds

This directory contains sound files for different notification types.

## Sound Files

- `new-request.mp3` - Sound for new request notifications
- `update.mp3` - Sound for request update notifications
- `success.mp3` - Sound for completed request notifications
- `cancelled.mp3` - Sound for cancelled request notifications
- `message.mp3` - Sound for chat message notifications
- `payment.mp3` - Sound for payment received notifications
- `assigned.mp3` - Sound for technician assigned notifications
- `alert.mp3` - Sound for system alert notifications
- `warning.mp3` - Sound for maintenance mode notifications

## Adding Sound Files

To add custom sound files:

1. Place your audio files (MP3, WAV, or OGG format) in this directory
2. Update the `NOTIFICATION_SOUNDS` object in `src/services/realtimeNotificationService.ts`
3. Ensure file names match the notification types

## Default Behavior

If sound files are not found, the notification system will:

- Log a warning to the console
- Continue to work without sound
- Still show visual notifications (toasts, desktop notifications)

## Browser Compatibility

- Modern browsers support MP3, WAV, and OGG formats
- The Web Audio API is used for better performance
- Fallback to HTML5 Audio if Web Audio API is not available
