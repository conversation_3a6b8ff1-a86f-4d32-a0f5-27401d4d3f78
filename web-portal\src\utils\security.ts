import DOMPurify from 'dompurify';

/**
 * Security utilities for the web portal
 * Provides XSS protection, CSRF protection, and input validation
 */

// XSS Protection
export const xssProtection = {
  /**
   * Sanitize HTML content to prevent XSS attacks
   */
  sanitizeHtml: (dirty: string): string => {
    return DOMPurify.sanitize(dirty, {
      ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'a', 'p', 'br'],
      ALLOWED_ATTR: ['href', 'target'],
      ALLOW_DATA_ATTR: false,
    });
  },

  /**
   * Escape HTML entities in user input
   */
  escapeHtml: (unsafe: string): string => {
    return unsafe
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
  },

  /**
   * Sanitize user input for display
   */
  sanitizeUserInput: (input: string): string => {
    if (!input || typeof input !== 'string') return '';

    // Remove script tags and event handlers
    const sanitized = input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/on\w+\s*=\s*"[^"]*"/gi, '')
      .replace(/on\w+\s*=\s*'[^']*'/gi, '')
      .replace(/javascript:/gi, '');

    // Escape remaining HTML
    return xssProtection.escapeHtml(sanitized);
  },
};

// CSRF Protection
export const csrfProtection = {
  /**
   * Generate a CSRF token
   */
  generateToken: (): string => {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, (byte) => byte.toString(16).padStart(2, '0')).join('');
  },

  /**
   * Store CSRF token in session storage
   */
  storeToken: (token: string): void => {
    sessionStorage.setItem('csrf_token', token);
  },

  /**
   * Get CSRF token from session storage
   */
  getToken: (): string | null => {
    return sessionStorage.getItem('csrf_token');
  },

  /**
   * Validate CSRF token
   */
  validateToken: (token: string): boolean => {
    const storedToken = csrfProtection.getToken();
    return storedToken !== null && storedToken === token;
  },

  /**
   * Add CSRF token to request headers
   */
  addTokenToHeaders: (headers: Record<string, string> = {}): Record<string, string> => {
    const token = csrfProtection.getToken();
    if (token) {
      headers['X-CSRF-Token'] = token;
    }
    return headers;
  },
};

// Input Validation
export const inputValidation = {
  /**
   * Validate email format
   */
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 254;
  },

  /**
   * Validate phone number format
   */
  isValidPhone: (phone: string): boolean => {
    const phoneRegex = /^\+?[\d\s\-()]+$/;
    return phoneRegex.test(phone) && phone.replace(/\D/g, '').length >= 7;
  },

  /**
   * Validate URL format
   */
  isValidUrl: (url: string): boolean => {
    try {
      const urlObj = new URL(url);
      return ['http:', 'https:'].includes(urlObj.protocol);
    } catch {
      return false;
    }
  },

  /**
   * Validate password strength
   */
  validatePasswordStrength: (password: string): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (!/[0-9]/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    if (!/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>?]/.test(password)) {
      errors.push('Password must contain at least one special character');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  },

  /**
   * Sanitize filename for uploads
   */
  sanitizeFilename: (filename: string): string => {
    return filename
      .replace(/[^a-zA-Z0-9._-]/g, '_')
      .replace(/_{2,}/g, '_')
      .substring(0, 255);
  },

  /**
   * Validate file type
   */
  isAllowedFileType: (file: File, allowedTypes: string[]): boolean => {
    return allowedTypes.includes(file.type);
  },

  /**
   * Validate file size
   */
  isValidFileSize: (file: File, maxSizeInMB: number): boolean => {
    const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    return file.size <= maxSizeInBytes;
  },
};

// Content Security Policy helpers
export const cspHelpers = {
  /**
   * Generate nonce for inline scripts
   */
  generateNonce: (): string => {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    return btoa(String.fromCharCode(...array));
  },

  /**
   * Create CSP header value
   */
  createCspHeader: (nonce?: string): string => {
    const directives = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' https://apis.google.com",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com",
      "img-src 'self' data: https: blob:",
      "connect-src 'self' https://api.github.com https://firestore.googleapis.com",
      "frame-ancestors 'none'",
      "base-uri 'self'",
      "form-action 'self'",
    ];

    if (nonce) {
      directives[1] = `script-src 'self' 'nonce-${nonce}' https://apis.google.com`;
    }

    return directives.join('; ');
  },
};

// Rate limiting helpers
export const rateLimiting = {
  /**
   * Simple client-side rate limiting
   */
  createRateLimiter: (maxRequests: number, windowMs: number) => {
    const requests: number[] = [];

    return {
      isAllowed: (): boolean => {
        const now = Date.now();

        // Remove old requests outside the window
        while (requests.length > 0 && requests[0] <= now - windowMs) {
          requests.shift();
        }

        // Check if we're under the limit
        if (requests.length < maxRequests) {
          requests.push(now);
          return true;
        }

        return false;
      },

      getRemainingRequests: (): number => {
        const now = Date.now();

        // Remove old requests outside the window
        while (requests.length > 0 && requests[0] <= now - windowMs) {
          requests.shift();
        }

        return Math.max(0, maxRequests - requests.length);
      },
    };
  },
};

// Security headers for API requests
export const securityHeaders = {
  /**
   * Get standard security headers for API requests
   */
  getStandardHeaders: (): Record<string, string> => {
    return {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Cache-Control': 'no-store, no-cache, must-revalidate',
      Pragma: 'no-cache',
    };
  },

  /**
   * Combine security headers with custom headers
   */
  combineHeaders: (customHeaders: Record<string, string> = {}): Record<string, string> => {
    return {
      ...securityHeaders.getStandardHeaders(),
      ...customHeaders,
    };
  },
};

// Session security
export const sessionSecurity = {
  /**
   * Generate secure session ID
   */
  generateSessionId: (): string => {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, (byte) => byte.toString(16).padStart(2, '0')).join('');
  },

  /**
   * Validate session timeout
   */
  isSessionValid: (lastActivity: number, timeoutMs: number): boolean => {
    return Date.now() - lastActivity < timeoutMs;
  },

  /**
   * Update session activity
   */
  updateActivity: (): void => {
    sessionStorage.setItem('last_activity', Date.now().toString());
  },

  /**
   * Get last activity timestamp
   */
  getLastActivity: (): number => {
    const lastActivity = sessionStorage.getItem('last_activity');
    return lastActivity ? parseInt(lastActivity, 10) : 0;
  },
};
