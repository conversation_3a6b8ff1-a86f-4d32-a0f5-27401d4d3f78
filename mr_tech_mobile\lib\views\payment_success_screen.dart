import 'dart:math';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/translation_service.dart';
import '../services/request_service.dart';
import '../models/request_model.dart';
import '../utils/payment_utils.dart';
import 'main_navigation.dart';
import '../widgets/text_styles.dart';

class PaymentSuccessScreen extends StatefulWidget {
  final RequestModel request;
  
  const PaymentSuccessScreen({
    super.key,
    required this.request,
  });

  @override
  State<PaymentSuccessScreen> createState() => _PaymentSuccessScreenState();
}

class _PaymentSuccessScreenState extends State<PaymentSuccessScreen> {
  bool _isLoading = true;
  late RequestModel _request;
  String? _errorMessage;
  
  @override
  void initState() {
    super.initState();
    _request = widget.request;
    _loadRequest();
  }
  
  Future<void> _loadRequest() async {
    try {
      if (widget.request.id == 'temp_id') {
        final dbService = RequestService();
        final recentRequest = await dbService.getActiveRequest();
        
        if (recentRequest != null) {
          setState(() {
            _request = recentRequest;
            _isLoading = false;
          });
        } else {
          final userRequests = await dbService.getUserRequestsOneTime();
          if (userRequests.isNotEmpty) {
            setState(() {
              _request = userRequests.first;
              _isLoading = false;
            });
          } else {
            setState(() {
              _request = widget.request;
              _isLoading = false;
            });
          }
        }
      } else {
        setState(() {
          _request = widget.request;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading request: $e');
      setState(() {
        _request = widget.request;
        _isLoading = false;
      });
    }
  }
  
  String _translate(String key) {
    final translationService = Provider.of<TranslationService>(context, listen: false);
    return translationService.translate(key);
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading && _request.id.isNotEmpty) {
      Future.microtask(() {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      });
    }
    
    final colorScheme = Theme.of(context).colorScheme;
    
    return Scaffold(
      backgroundColor: colorScheme.surface,
      body: SafeArea(
        child: _isLoading 
          ? Center(child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
            ))
          : _errorMessage != null
            ? _buildErrorView()
            : _buildSuccessView(),
      ),
    );
  }
  
  Widget _buildErrorView() {
    final colorScheme = Theme.of(context).colorScheme;
    
    return Padding(
      padding: const EdgeInsets.all(32.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              shape: BoxShape.circle,
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Icon(
              Icons.error_outline,
              color: Colors.red.shade700,
              size: 64,
            ),
          ),
          const SizedBox(height: 32),
          Text(
            _translate('Payment Error'),
            style: AppTextStyles.headingLarge(
              context,
              color: colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            _errorMessage ?? _translate('Unknown error occurred'),
            style: AppTextStyles.bodyLarge(
              context,
              color: colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 48),
          SizedBox(
            width: double.infinity,
            height: 52,
            child: FilledButton(
              onPressed: () {
                Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(builder: (context) => const MainNavigation()),
                  (route) => false,
                );
              },
              style: FilledButton.styleFrom(
                backgroundColor: colorScheme.primary,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
              ),
              child: Text(
                _translate('Return to Home'),
                style: AppTextStyles.labelLarge(context, color: Colors.white),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildSuccessView() {
    final colorScheme = Theme.of(context).colorScheme;
    
    return Padding(
      padding: const EdgeInsets.all(32.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: Colors.green.shade700,
              shape: BoxShape.circle,
              border: Border.all(color: Colors.grey.shade300, width: 2),
            ),
            child: const Icon(
              Icons.check,
              color: Colors.white,
              size: 64,
            ),
          ),
          const SizedBox(height: 32),
          Text(
            _translate('Payment Successful'),
            style: AppTextStyles.headingLarge(
              context,
              color: colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          Text(
            _translate('Your payment has been processed successfully. Your service request is now being reviewed by our team.'),
            style: AppTextStyles.bodyLarge(
              context,
              color: colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          _buildPaymentDetailCard(),
          const SizedBox(height: 48),
          SizedBox(
            width: double.infinity,
            height: 52,
            child: FilledButton(
              onPressed: () {
                Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(builder: (context) => const MainNavigation()),
                  (route) => false,
                );
              },
              style: FilledButton.styleFrom(
                backgroundColor: colorScheme.primary,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 0,
              ),
              child: Text(
                _translate('Return to Home'),
                style: AppTextStyles.labelLarge(context, color: Colors.white),
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildPaymentDetailCard() {
    final colorScheme = Theme.of(context).colorScheme;
    
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey.shade300),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            _buildDetailRow(
              label: _translate('Service'),
              value: _request.serviceName,
              colorScheme: colorScheme,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Divider(color: Colors.grey.shade300, height: 1),
            ),
            _buildDetailRow(
              label: _translate('Request ID'),
              value: _request.id.substring(0, min(8, _request.id.length)),
              colorScheme: colorScheme,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 16),
              child: Divider(color: Colors.grey.shade300, height: 1),
            ),
            _buildDetailRow(
              label: _translate('Amount Paid'),
              value: PaymentUtils.formatPrice(_request.amount),
              valueColor: Colors.green.shade700,
              isBold: true,
              colorScheme: colorScheme,
            ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildDetailRow({
    required String label,
    required String value,
    Color? valueColor,
    bool isBold = false,
    required ColorScheme colorScheme,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: AppTextStyles.bodyMedium(
            context,
            color: colorScheme.onSurfaceVariant,
          ),
        ),
        Text(
          value,
          style: isBold 
              ? AppTextStyles.labelLarge(
                  context,
                  color: valueColor ?? colorScheme.onSurface,
                )
              : AppTextStyles.bodyMedium(
                  context,
                  color: valueColor ?? colorScheme.onSurface,
                ),
        ),
      ],
    );
  }
} 