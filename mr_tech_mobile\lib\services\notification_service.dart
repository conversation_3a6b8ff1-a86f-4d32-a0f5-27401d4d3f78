import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:mr_tech_mobile/models/notification_model.dart';
import 'package:mr_tech_mobile/models/notification_preferences_model.dart';
import 'package:mr_tech_mobile/utils/navigation_utils.dart';
import 'package:firebase_core/firebase_core.dart';
import '../firebase_options.dart';
import 'auth_service.dart';
import 'translation_service.dart';

class NotificationService {
  // Singleton instance
  static final NotificationService _instance = NotificationService._internal();

  factory NotificationService() => _instance;

  NotificationService._internal();

  final FirebaseMessaging _firebaseMessaging = FirebaseMessaging.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;

  // Global navigation key for context-less navigation
  static GlobalKey<NavigatorState>? navigatorKey;

  // User's notification preferences
  NotificationPreferencesModel _notificationPreferences =
      NotificationPreferencesModel();

  // Initialization flag
  bool _isInitialized = false;
  bool _isMinimallyInitialized = false;

  // Track processed notifications to prevent duplicates
  final Set<String> _processedNotifications = <String>{};
  static const int _maxProcessedNotifications = 100; // Limit memory usage

  // Get notification preferences
  NotificationPreferencesModel get notificationPreferences =>
      _notificationPreferences;

  // Check if service is initialized
  bool get isInitialized => _isInitialized;

  // Initialize minimal notification services - only what's needed for UI to function
  Future<void> initializeMinimal() async {
    try {
      debugPrint('Notification service - minimal initialization');

      // Initialize dependencies (minimal version)
      _initializePreferencesMinimal();

      // Set up background message handler (lightweight)
      FirebaseMessaging.onBackgroundMessage(
        _firebaseMessagingBackgroundHandler,
      );

      // Set up foreground notification handler (lightweight)
      await _setupForegroundNotificationHandling();

      _isMinimallyInitialized = true;
      debugPrint('Notification service - minimal initialization complete');

      // Move heavy operations to background
      _initializeHeavyOperationsInBackground();
    } catch (e) {
      debugPrint('Error in notification service minimal initialization: $e');
    }
  }

  // Initialize heavy operations in background
  void _initializeHeavyOperationsInBackground() {
    Future.microtask(() async {
      try {
        // Load notification preferences from shared preferences
        await _loadNotificationPreferences();

        // Request permission for user notifications
        await _requestNotificationPermission();

        debugPrint('Notification service heavy operations completed');
      } catch (e) {
        debugPrint(
          'Error in notification service background initialization: $e',
        );
      }
    });
  }

  // Initialize notification preferences with minimal settings
  void _initializePreferencesMinimal() {
    // Use default preferences until we can load from SharedPreferences
    _notificationPreferences = NotificationPreferencesModel();
  }

  // Request permission for notifications
  Future<void> _requestNotificationPermission() async {
    try {
      final settings = await _firebaseMessaging.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: true,
      );

      debugPrint(
        'Notification permission status: ${settings.authorizationStatus}',
      );
    } catch (e) {
      debugPrint('Error requesting notification permission: $e');
    }
  }

  // Set up foreground notification handling
  Future<void> _setupForegroundNotificationHandling() async {
    try {
      debugPrint('Setting up foreground notification handling');

      // Handle foreground messages
      FirebaseMessaging.onMessage.listen((RemoteMessage message) async {
        debugPrint(
          'Foreground message received: ${message.notification?.title}',
        );
        debugPrint('Message data: ${message.data}');

        try {
          String? title = message.notification?.title;
          String? body = message.notification?.body;
          String type =
              message.data['type']?.toString().toLowerCase() ??
              message.data['notificationType']?.toString().toLowerCase() ??
              'general';
          String? requestId =
              message.data['requestId'] ?? message.data['request_id'];
          String? timestamp = message.data['timestamp']?.toString();

          // Create unique identifier for this notification
          String notificationId = '${type}_${requestId}_${timestamp ?? DateTime.now().millisecondsSinceEpoch}';

          // Check if we've already processed this notification
          if (_processedNotifications.contains(notificationId)) {
            debugPrint('NotificationService: Duplicate notification detected, skipping: $notificationId');
            return;
          }

          // Add to processed set
          _processedNotifications.add(notificationId);

          // Limit memory usage by removing old entries
          if (_processedNotifications.length > _maxProcessedNotifications) {
            final oldEntries = _processedNotifications.take(_processedNotifications.length - _maxProcessedNotifications);
            _processedNotifications.removeAll(oldEntries);
          }

          // Add enhanced logging
          debugPrint(
            'NotificationService: Processing foreground message of type: $type with requestId: $requestId (ID: $notificationId)',
          );

          // Handle chat activation specially
          if (type == 'chat_activation' ||
              type == 'chat_started' ||
              message.data['type'] == 'CHAT_ACTIVATION' ||
              (message.data['notificationType'] != null &&
                  message.data['notificationType'].toString().toUpperCase() ==
                      'CHAT_ACTIVATION')) {
            debugPrint(
              'NotificationService: Processing CHAT_ACTIVATION notification',
            );
            await _handleChatActivationNotification(message, requestId ?? '');
          }

          // Create local notification to show to user
          if (_shouldShowNotification(type)) {
            await createLocalNotification(
              title: title ?? 'New Notification',
              body: body ?? '',
              type: type,
              requestId: requestId,
              data: message.data,
            );
          }
        } catch (e) {
          debugPrint('Error handling foreground message: $e');
        }
      });

      debugPrint('Foreground notification handling set up successfully');
    } catch (e) {
      debugPrint('Error setting up foreground notification handling: $e');
    }
  }

  // Complete initialization with non-critical components
  Future<void> completeInitialization() async {
    if (_isInitialized || !_isMinimallyInitialized) return;

    try {
      // Get initial message (if app was opened from a terminated state)
      FirebaseMessaging.instance.getInitialMessage().then((
        RemoteMessage? initialMessage,
      ) {
        if (initialMessage != null) {
          _handleBackgroundTap(initialMessage);
        }
      });

      // Load user notification preferences - can be delayed
      await _loadNotificationPreferences();

      // Mark as fully initialized
      _isInitialized = true;
    } catch (e) {
      debugPrint('Error completing notification initialization: $e');
    }
  }

  // Original initialize method - kept for compatibility
  Future<void> initialize(GlobalKey<NavigatorState> navKey) async {
    if (_isInitialized) return;

    // Save the navigator key
    navigatorKey = navKey;

    // Use the new two-phase initialization
    await initializeMinimal();
    await completeInitialization();
  }

  // Print FCM token for debugging purposes
  Future<void> printFcmToken() async {
    try {
      final token = await _firebaseMessaging.getToken();
      debugPrint('======================================================');
      debugPrint('CURRENT FCM TOKEN: $token');
      debugPrint('COPY THIS TOKEN FOR TESTING NOTIFICATIONS');
      debugPrint('======================================================');

      // Also log to analytics for remote debugging - with better error handling
      try {
        await _analytics.logEvent(
          name: 'fcm_token_printed',
          parameters: {
            'token_available': token != null ? 'true' : 'false',
            'token_length': token != null ? token.length.toString() : '0',
            'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
          },
        );
      } catch (analyticsError) {
        // Just log analytics errors but don't let them interrupt the flow
        debugPrint('Analytics error (non-critical): $analyticsError');
      }
    } catch (e) {
      debugPrint('Error printing FCM token: $e');
    }
  }

  /// Refresh FCM token - useful for app resume scenarios
  Future<void> refreshToken() async {
    try {
      debugPrint('Refreshing FCM token...');

      // Get the current token
      final token = await _firebaseMessaging.getToken();

      if (token != null) {
        // Update token in Firestore
        await _updateTokenInFirestore(token);

        // Update token in active service requests
        await _updateTokenInActiveRequests(token);

        debugPrint('FCM token refreshed successfully');
      } else {
        debugPrint('Failed to get FCM token during refresh');
      }
    } catch (e) {
      debugPrint('Error refreshing FCM token: $e');
    }
  }

  /// Update token in active service requests
  Future<void> _updateTokenInActiveRequests(String token) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      // Get active requests for the user
      final requestsQuery =
          await _firestore
              .collection('service_requests')
              .where('customer_id', isEqualTo: user.uid)
              .where(
                'status',
                whereIn: [
                  'pending',
                  'payment_pending',
                  'approved',
                  'inProgress',
                ],
              )
              .get();

      // Update FCM token in each active request
      final batch = _firestore.batch();
      for (final doc in requestsQuery.docs) {
        batch.update(doc.reference, {
          'customer_fcm_token': token,
          'customerFcmToken': token,
          'tokenUpdatedAt': FieldValue.serverTimestamp(),
        });
      }

      if (requestsQuery.docs.isNotEmpty) {
        await batch.commit();
        debugPrint(
          'Updated FCM token in ${requestsQuery.docs.length} active requests',
        );
      }
    } catch (e) {
      debugPrint('Error updating token in active requests: $e');
    }
  }

  // Load notification preferences
  Future<void> _loadNotificationPreferences() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      // Get user document from Firestore
      final docRef = _firestore.collection('users').doc(user.uid);
      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        final data = docSnapshot.data() as Map<String, dynamic>;

        // Get notification preferences from user document
        if (data.containsKey('notification_preferences')) {
          _notificationPreferences = NotificationPreferencesModel.fromFirestore(
            data['notification_preferences'] as Map<String, dynamic>?,
          );

          // Log analytics event for loading preferences
          await _analytics.logEvent(
            name: 'notification_preferences_loaded',
            parameters: {
              'push_enabled':
                  _notificationPreferences.pushNotifications ? '1' : '0',
              'sound_enabled':
                  _notificationPreferences.soundEnabled ? '1' : '0',
              'request_updates':
                  _notificationPreferences.requestUpdates ? '1' : '0',
              'message_notifications':
                  _notificationPreferences.messageNotifications ? '1' : '0',
              'payment_notifications':
                  _notificationPreferences.paymentNotifications ? '1' : '0',
              'system_notifications':
                  _notificationPreferences.systemNotifications ? '1' : '0',
              'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
            },
          );
        } else {
          // If no preferences saved, use defaults
          _notificationPreferences = NotificationPreferencesModel();
        }
      }
    } catch (e) {
      debugPrint('Error loading notification preferences: $e');
    }
  }

  // Save notification preferences
  Future<void> saveNotificationPreferences(
    NotificationPreferencesModel preferences,
  ) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      // Update local preference
      _notificationPreferences = preferences;

      // Save to Firestore
      await _firestore.collection('users').doc(user.uid).update({
        'notification_preferences': preferences.toMap(),
        'updated_at': FieldValue.serverTimestamp(),
      });

      // Configure Firebase Messaging based on preferences
      if (!preferences.pushNotifications) {
        // Disable push notification on the device if user opted out
        await _firebaseMessaging.setForegroundNotificationPresentationOptions(
          alert: false,
          badge: false,
          sound: false,
        );
      } else {
        // Enable push notifications
        await _firebaseMessaging.setForegroundNotificationPresentationOptions(
          alert: true,
          badge: true,
          sound: preferences.soundEnabled,
        );
      }

      // Log preference update
      await _analytics.logEvent(
        name: 'notification_preferences_updated',
        parameters: {
          'push_enabled': preferences.pushNotifications ? '1' : '0',
          'sound_enabled': preferences.soundEnabled ? '1' : '0',
          'request_updates': preferences.requestUpdates ? '1' : '0',
          'message_notifications': preferences.messageNotifications ? '1' : '0',
          'payment_notifications': preferences.paymentNotifications ? '1' : '0',
          'system_notifications': preferences.systemNotifications ? '1' : '0',
          'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
        },
      );
    } catch (e) {
      debugPrint('Error saving notification preferences: $e');
    }
  }

  // Check if notification should be shown based on preferences
  bool _shouldShowNotification(String type) {
    if (!_notificationPreferences.pushNotifications) {
      return false;
    }

    // Convert to lowercase for consistent checking
    final String lowerType = type.toLowerCase();

    switch (lowerType) {
      case 'request_accepted':
      case 'request accepted':
      case 'approved':
      case 'session_started':
      case 'session started':
      case 'in_progress':
      case 'in progress':
      case 'request_completed':
      case 'request completed':
      case 'completed':
        return _notificationPreferences.requestUpdates;

      case 'new_message':
      case 'new message':
      case 'chat_message':
      case 'chat message':
      case 'chat_activation':
      case 'chat activation':
      case 'chat_started':
      case 'chat started':
      case 'chat_active':
      case 'chat active':
        return _notificationPreferences.messageNotifications;

      case 'payment_completed':
      case 'payment completed':
      case 'payment_failed':
      case 'payment failed':
        return _notificationPreferences.paymentNotifications;

      default:
        // For unknown notification types, check if they contain specific keywords
        if (lowerType.contains('chat') || lowerType.contains('message')) {
          return _notificationPreferences.messageNotifications;
        } else if (lowerType.contains('request') ||
            lowerType.contains('service')) {
          return _notificationPreferences.requestUpdates;
        } else if (lowerType.contains('payment')) {
          return _notificationPreferences.paymentNotifications;
        }

        // Default to system notifications for anything else
        return _notificationPreferences.systemNotifications;
    }
  }

  // Update token in Firestore
  Future<void> _updateTokenInFirestore(String? token) async {
    if (token == null) return;

    try {
      final user = _auth.currentUser;
      if (user != null) {
        try {
          // Only store a minimal token record in users collection
          await _firestore.collection('users').doc(user.uid).update({
            'fcm_token': token,
            'fcmToken': token,
            'updated_at': FieldValue.serverTimestamp(),
          });
          debugPrint('Updated FCM token in user document: $token');

          // Log analytics event for token update
          await _analytics.logEvent(
            name: 'fcm_token_updated',
            parameters: {'user_id': user.uid, 'token': token},
          );
        } catch (e) {
          debugPrint('Error updating FCM token in user document: $e');
        }
      }
    } catch (e) {
      debugPrint('Error updating FCM token: $e');
    }
  }

  // Handle chat activation notifications
  Future<void> _handleChatActivationNotification(
    RemoteMessage message,
    String requestId,
  ) async {
    try {
      if (requestId.isEmpty) {
        debugPrint(
          'NotificationService: Cannot handle chat activation, requestId is empty',
        );
        return;
      }

      debugPrint(
        'NotificationService: Handling chat activation for request: $requestId',
      );

      // First check if chat is already active to prevent loops
      final requestDoc = await _firestore.collection('service_requests').doc(requestId).get();
      if (requestDoc.exists) {
        final data = requestDoc.data() as Map<String, dynamic>;
        final chatActive = data['chat_active'] == true || data['chatActive'] == true;

        if (chatActive) {
          debugPrint('NotificationService: Chat already active for request: $requestId, skipping update');
          return;
        }
      }

      // Update request document to ensure chat is marked as active - ONLY if not already active
      await _firestore.collection('service_requests').doc(requestId).update({
        'chatActive': true,
        'chat_active': true,
        'has_active_chat': true,
        'updated_at': FieldValue.serverTimestamp(),
      });

      // Update the notification in Firestore to mark as processed
      final notificationQuery =
          await _firestore
              .collection('notifications')
              .where('request_id', isEqualTo: requestId)
              .where(
                'type',
                whereIn: [
                  'CHAT_ACTIVATION',
                  'chat_activation',
                  'CHAT_STARTED',
                  'chat_started',
                ],
              )
              .orderBy('timestamp', descending: true)
              .limit(1)
              .get();

      if (notificationQuery.docs.isNotEmpty) {
        final notificationDoc = notificationQuery.docs.first;
        await notificationDoc.reference.update({
          'delivered': true,
          'processed_by_app': true,
          'read': true,
          'processed': true,
        });
        debugPrint(
          'NotificationService: Marked notification as processed in Firestore',
        );
      }

      debugPrint(
        'NotificationService: Chat activation successfully processed for request: $requestId',
      );
    } catch (e) {
      debugPrint(
        'NotificationService: Error handling chat activation notification: $e',
      );
    }
  }

  // Handle background messages
  Future<void> _handleBackgroundMessage(RemoteMessage message) async {
    debugPrint('Background message received: ${message.notification?.title}');
    debugPrint('Background message data: ${message.data}');

    try {
      // Extract notification data
      String title = message.notification?.title ?? 'Mr. Tech';
      String body = message.notification?.body ?? '';
      String type =
          message.data['type']?.toString().toLowerCase() ??
          message.data['notificationType']?.toString().toLowerCase() ??
          'general';
      String? requestId =
          message.data['requestId'] ?? message.data['request_id'];
      String? timestamp = message.data['timestamp']?.toString();

      // Create unique identifier for this notification
      String notificationId = '${type}_${requestId}_${timestamp ?? DateTime.now().millisecondsSinceEpoch}';

      // Check if we've already processed this notification
      if (_processedNotifications.contains(notificationId)) {
        debugPrint('NotificationService: Duplicate background notification detected, skipping: $notificationId');
        return;
      }

      // Add to processed set
      _processedNotifications.add(notificationId);

      // Limit memory usage
      if (_processedNotifications.length > _maxProcessedNotifications) {
        final oldEntries = _processedNotifications.take(_processedNotifications.length - _maxProcessedNotifications);
        _processedNotifications.removeAll(oldEntries);
      }

      // Enhanced logging for debugging
      debugPrint('Background message extracted type: "$type"');
      debugPrint(
        'Background message extracted requestId: "${requestId ?? 'none'}" (ID: $notificationId)',
      );

      // Special handling for chat activation to ensure it's processed
      if (type == 'chat_activation' ||
          message.data['type'] == 'CHAT_ACTIVATION' ||
          type == 'chat_started' ||
          message.data['type'] == 'CHAT_STARTED' ||
          (message.notification?.title?.contains('Chat Started') == true) ||
          (message.data['action'] == 'ACTIVATE_CHAT')) {
        debugPrint(
          'Background message identified as chat activation notification',
        );

        if (requestId?.isNotEmpty == true) {
          debugPrint('Chat activation for request $requestId');

          // Create notification record for further processing when app is opened
          final user = _auth.currentUser;
          if (user != null) {
            try {
              // Get technician name from notification
              final technicianName =
                  message.data['technicianName'] ??
                  message.data['technician_name'] ??
                  'Technician';

              // Check if chat is already active to prevent duplicate processing
              final requestDoc = await _firestore.collection('service_requests').doc(requestId).get();
              if (requestDoc.exists) {
                final data = requestDoc.data() as Map<String, dynamic>;
                final chatActive = data['chat_active'] == true || data['chatActive'] == true;

                if (chatActive) {
                  debugPrint('Chat already active for request: $requestId, skipping background processing');
                  return;
                }
              }

              // Check if there's already a recent notification for this request to prevent duplicates
              final recentNotifications = await _firestore
                  .collection('notifications')
                  .where('user_id', isEqualTo: user.uid)
                  .where('request_id', isEqualTo: requestId)
                  .where('type', whereIn: ['CHAT_ACTIVATION', 'chat_activation'])
                  .orderBy('timestamp', descending: true)
                  .limit(1)
                  .get();

              if (recentNotifications.docs.isNotEmpty) {
                final recentNotification = recentNotifications.docs.first;
                final recentTimestamp = recentNotification.data()['timestamp'];
                final now = Timestamp.now();

                // If there's a notification within the last 30 seconds, skip creating another
                if (recentTimestamp != null &&
                    (now.millisecondsSinceEpoch - recentTimestamp.millisecondsSinceEpoch) < 30000) {
                  debugPrint('Recent chat activation notification already exists for request: $requestId, skipping duplicate');
                  return;
                }
              }

              debugPrint('No duplicate found, processing chat activation for request: $requestId');

              // Also update the request's chatActive status directly
              if (requestId?.isNotEmpty == true) {
                try {
                  debugPrint('Updating request chat status directly');
                  await _firestore
                      .collection('service_requests')
                      .doc(requestId)
                      .update({
                        'chatActive': true,
                        'chat_active': true,
                        'updated_at': FieldValue.serverTimestamp(),
                        'last_activity': FieldValue.serverTimestamp(),
                      });
                  debugPrint('Updated request chat status successfully');
                } catch (e) {
                  debugPrint('Error updating request chat status: $e');
                }
              }

              // Create a local notification to show to the user
              // This will show even without notification payload in the FCM message
              await createLocalNotification(
                title: 'Chat Started',
                body:
                    '$technicianName has started a chat for your service request',
                type: 'chat_activation',
                requestId: requestId,
              );

              debugPrint(
                'Created local notification for chat activation (background processing)',
              );
            } catch (e) {
              debugPrint('Error creating notification for chat activation: $e');
            }
          }
        }
      } else {
        // Create local notification to display for non-chat activation messages
        await _createLocalNotificationFromMessage(message);
      }
    } catch (e) {
      debugPrint('Error handling background message: $e');
    }
  }

  // Handle background notification taps
  Future<void> _handleBackgroundTap(RemoteMessage message) async {
    debugPrint('Message opened app: ${message.notification?.title}');

    try {
      // Log notification tap event
      await _analytics.logEvent(
        name: 'notification_opened',
        parameters: {
          'title': message.notification?.title ?? 'No title',
          'type': message.data['type'] ?? 'unknown',
          'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
        },
      );

      // Navigate based on notification type
      if (message.data.isNotEmpty) {
        _navigateBasedOnNotificationType(message.data);
      }
    } catch (e) {
      debugPrint('Error handling background notification tap: $e');
    }
  }

  // Navigate based on notification type
  void _navigateBasedOnNotificationType(Map<String, dynamic> data) {
    // Check all possible type fields, handling both uppercase and lowercase
    final String notificationType =
        (data['notificationType'] ??
                data['type'] ??
                data['TYPE'] ??
                data['NOTIFICATION_TYPE'] ??
                '')
            .toLowerCase();

    // Also check within data field if present
    if (notificationType.isEmpty && data['data'] is Map) {
      final Map<dynamic, dynamic> nestedData = data['data'] as Map;
      final String nestedType =
          (nestedData['type'] ?? nestedData['notificationType'] ?? '')
              .toLowerCase();
      if (nestedType.isNotEmpty) {
        _handleNotificationNavigation(nestedType, data);
        return;
      }
    }

    _handleNotificationNavigation(notificationType, data);
  }

  // Handle navigation based on extracted type
  void _handleNotificationNavigation(
    String notificationType,
    Map<String, dynamic> data,
  ) {
    final String requestId =
        data['requestId'] ??
        (data['data'] is Map ? (data['data']['requestId'] ?? '') : '');

    if (navigatorKey?.currentContext == null) {
      debugPrint('Navigator key context is null, cannot navigate');
      return;
    }

    final context = navigatorKey!.currentContext!;

    try {
      // Log navigation event
      _analytics.logEvent(
        name: 'notification_navigation',
        parameters: {
          'notification_type': notificationType,
          'request_id': requestId,
          'timestamp': DateTime.now().millisecondsSinceEpoch.toString(),
        },
      );
    } catch (e) {
      debugPrint('Error logging navigation event: $e');
    }

    // Convert any upper case notification types to lowercase
    switch (notificationType.toLowerCase()) {
      case 'approved':
      case 'request_accepted':
      case 'request accepted':
      case 'request_accepted':
        // Navigate to request details page
        NavigationUtils.navigateToRequestDetails(context, requestId);
        break;
      case 'new_message':
      case 'new message':
        // Navigate to chat screen
        NavigationUtils.navigateToChatScreen(context, requestId);
        break;
      case 'chat_activation':
      case 'chat activation':
      case 'chat_started':
      case 'chat started':
        // Navigate to chat screen for chat activation notifications
        debugPrint(
          'Navigating to chat screen from chat activation notification',
        );
        NavigationUtils.navigateToChatScreen(context, requestId);
        break;
      case 'in_progress':
      case 'session_started':
      case 'in progress':
      case 'session started':
        // Navigate to request details page
        NavigationUtils.navigateToRequestDetails(context, requestId);
        break;
      case 'completed':
      case 'request_completed':
      case 'request completed':
        // Navigate to request details page
        NavigationUtils.navigateToRequestDetails(context, requestId);
        break;
      default:
        // Navigate to home screen or notification list
        break;
    }
  }

  // Subscribe to a topic
  Future<void> subscribeToTopic(String topic) async {
    await _firebaseMessaging.subscribeToTopic(topic);
  }

  // Unsubscribe from a topic
  Future<void> unsubscribeFromTopic(String topic) async {
    await _firebaseMessaging.unsubscribeFromTopic(topic);
  }

  // Subscribe to request notifications
  Future<void> subscribeToRequestNotifications(String requestId) async {
    await subscribeToTopic('request_$requestId');
  }

  // Unsubscribe from request notifications
  Future<void> unsubscribeFromRequestNotifications(String requestId) async {
    await unsubscribeFromTopic('request_$requestId');
  }

  // Create a notification for the current user
  Future<void> createLocalNotification({
    required String title,
    required String body,
    required String type,
    String? requestId,
    Map<String, dynamic>? data,
  }) async {
    // Check if the user wants to receive this type of notification
    if (!_shouldShowNotification(type)) {
      debugPrint(
        'Local notification suppressed based on user preferences: $type',
      );
      return;
    }

    try {
      final user = _auth.currentUser;
      if (user == null) return;

      // For chat activation notifications, don't create additional Firestore documents
      // The web portal already creates them and we don't want duplicates
      if (type == 'chat_activation' || type == 'CHAT_ACTIVATION') {
        debugPrint('Skipping Firestore notification creation for chat_activation - already handled by web portal');
        return;
      }

      // Add notification directly to Firestore for other types
      await _firestore.collection('notifications').add({
        'userId': user.uid,
        'user_id': user.uid, // Add snake_case for compatibility
        'title': title,
        'body': body,
        'type': type,
        'requestId': requestId,
        'request_id': requestId, // Add snake_case for compatibility
        'createdAt': FieldValue.serverTimestamp(),
        'created_at': FieldValue.serverTimestamp(), // Add snake_case for compatibility
        'read': false,
        'deleted': false,
        ...(data ?? {}),
      });
    } catch (e) {
      debugPrint('Error creating local notification: $e');
    }
  }

  // Get stream of notifications for current user
  Stream<List<NotificationModel>> getNotificationsStream() {
    final userId = _auth.currentUser?.uid;
    if (userId == null) {
      return Stream.value([]);
    }

    try {
      // Use a simpler query that works better with security rules
      // Filter out deleted notifications on the client side to avoid compound query issues
      final Query<Map<String, dynamic>> query = _firestore
          .collection('notifications')
          .where('user_id', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .limit(100); // Get more to account for deleted ones we'll filter out

      return query.snapshots().handleError((error) {
        debugPrint('Error in notifications stream: $error');
        // If permission error, try to handle it
        if (error.toString().contains('permission-denied')) {
          debugPrint('Permission denied in notifications stream, attempting to handle...');
          final authService = AuthService();
          authService.handlePermissionError();
        }
      }).map((snapshot) {
        // Process documents, filter out deleted ones, and remove duplicates
        final notifications =
            snapshot.docs
                .map((doc) {
                  return NotificationModel.fromFirestore(doc);
                })
                .where((notification) => !notification.deleted) // Filter out deleted notifications
                .toList();

        // Deduplicate notifications using a map to group by title+body+type
        final Map<String, NotificationModel> uniqueNotifications = {};

        for (final notification in notifications) {
          final key =
              '${notification.title}|${notification.body}|${notification.type}';
          // Only keep the most recent notification for each unique content
          if (!uniqueNotifications.containsKey(key) ||
              notification.createdAt.compareTo(
                    uniqueNotifications[key]!.createdAt,
                  ) >
                  0) {
            uniqueNotifications[key] = notification;
          }
        }

        // Convert back to list and sort by createdAt (newest first)
        final List<NotificationModel> result =
            uniqueNotifications.values.toList()
              ..sort((a, b) => b.createdAt.compareTo(a.createdAt));

        // Limit to 50 notifications after filtering and deduplication
        return result.take(50).toList();
      });
    } catch (e) {
      debugPrint('Error getting notifications stream: $e');
      return Stream.value([]);
    }
  }

  // Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    try {
      await _firestore.collection('notifications').doc(notificationId).update({
        'read': true,
      });
    } catch (e) {
      debugPrint('Error marking notification as read: $e');
    }
  }

  // Mark notification as deleted (without actually deleting it)
  Future<void> markAsDeleted(String notificationId) async {
    try {
      await _firestore.collection('notifications').doc(notificationId).update({
        'deleted': true,
      });
      debugPrint('Notification marked as deleted: $notificationId');
    } catch (e) {
      debugPrint('Error marking notification as deleted: $e');
    }
  }

  // Mark all notifications as read
  Future<void> markAllAsRead() async {
    try {
      final userId = _auth.currentUser?.uid;
      if (userId == null) return;

      final batch = _firestore.batch();
      final notificationsQuery =
          await _firestore
              .collection('notifications')
              .where('user_id', isEqualTo: userId)
              .where('read', isEqualTo: false)
              .get();

      for (final doc in notificationsQuery.docs) {
        batch.update(doc.reference, {'read': true});
      }

      await batch.commit();
    } catch (e) {
      debugPrint('Error marking all notifications as read: $e');
    }
  }

  // Get unread notification count
  Future<int> getUnreadCount() async {
    try {
      final userId = _auth.currentUser?.uid;
      if (userId == null) return 0;

      final notificationsQuery =
          await _firestore
              .collection('notifications')
              .where('user_id', isEqualTo: userId)
              .where('read', isEqualTo: false)
              .get();

      return notificationsQuery.docs.length;
    } catch (e) {
      debugPrint('Error getting unread count: $e');
      return 0;
    }
  }

  // Send a notification based on type
  Future<void> sendNotification({
    required String userId,
    required String notificationType,
    String? requestId,
    String? additionalInfo,
  }) async {
    try {
      final translationService = TranslationService();
      final translate = translationService.translate;

      String title = translate('Notification');
      String body = translate('You have a new notification');

      // Set title and body based on notification type
      switch (notificationType) {
        case 'request_accepted':
          title = translate('Request Accepted');
          body = additionalInfo != null
              ? '${translate('Your request has been accepted by a technician')}: $additionalInfo'
              : translate('Your request has been accepted by a technician');
          break;
        case 'session_started':
          title = translate('Session Started');
          body = translate('Your technical support session has started');
          break;
        case 'session_completed':
          title = translate('Session Completed');
          body = translate('Your support session has been completed');
          break;
        case 'new_message':
          title = translate('New Message');
          body = additionalInfo ?? translate('You have a new message');
          break;
        case 'payment_completed':
          title = translate('Payment Successful');
          body = translate('Your payment has been completed successfully');
          break;
        case 'payment_failed':
          title = translate('Payment Failed');
          body = translate('Your payment could not be processed');
          break;
        case 'new_request':
          title = translate('New Service Request');
          body = additionalInfo ?? translate('A new service request has been submitted');
          break;
        default:
          title = translate('Notification');
          body = additionalInfo ?? translate('You have a new notification');
          break;
      }

      // Create notification data
      Map<String, dynamic> notificationData = {
        'userId': userId,
        'user_id': userId, // Add snake_case for compatibility
        'title': title,
        'body': body,
        'type': notificationType,
        'requestId': requestId,
        'request_id': requestId, // Add snake_case for compatibility
        'createdAt': FieldValue.serverTimestamp(),
        'created_at': FieldValue.serverTimestamp(), // Add snake_case for compatibility
        'read': false,
        'deleted': false,
      };

      // Save notification to Firestore
      await _firestore.collection('notifications').add(notificationData);
    } catch (e) {
      debugPrint('Error sending notification: $e');
    }
  }

  // Notify all technicians about a new request
  Future<void> notifyTechniciansOfNewRequest(
    String requestId,
    String serviceName,
  ) async {
    try {
      debugPrint('Notifying technicians of new request: $requestId');

      // 1. Create a notification in 'admin_notifications' collection to be displayed on web app
      await _firestore.collection('admin_notifications').add({
        'title': 'New Service Request',
        'body': 'A customer has submitted a new request for $serviceName',
        'type': 'new_request',
        'requestId': requestId,
        'sound': true, // Indicates this notification should play a sound
        'priority': 'high',
        'createdAt': FieldValue.serverTimestamp(),
        'read': false,
      });

      // 2. Send to "technicians" topic so all technicians get notified
      // This approach requires technicians to be subscribed to "technicians" topic on the web app
      await _firestore.collection('fcm_notifications').add({
        'topic': 'technicians',
        'title': 'New Service Request',
        'body': 'A customer has submitted a new request for $serviceName',
        'data': {
          'click_action': 'FLUTTER_NOTIFICATION_CLICK',
          'type': 'new_request',
          'requestId': requestId,
          'sound': 'default',
        },
        'priority': 'high',
        'created_at': FieldValue.serverTimestamp(),
        'sent': false, // Will be processed by Cloud Functions to send FCM
      });

      debugPrint(
        'Technician notification for new request created successfully',
      );
    } catch (e) {
      debugPrint('Error notifying technicians of new request: $e');
    }
  }

  // Mark all notifications as deleted
  Future<void> markAllAsDeleted() async {
    try {
      final userId = _auth.currentUser?.uid;
      if (userId == null) return;

      final batch = _firestore.batch();
      final notificationsQuery =
          await _firestore
              .collection('notifications')
              .where('user_id', isEqualTo: userId)
              .where('deleted', isEqualTo: false)
              .get();

      for (final doc in notificationsQuery.docs) {
        batch.update(doc.reference, {'deleted': true});
      }

      await batch.commit();
      debugPrint('All notifications marked as deleted');
    } catch (e) {
      debugPrint('Error marking all notifications as deleted: $e');
    }
  }

  // Completely delete all notifications for the current user
  Future<void> deleteAllNotifications() async {
    try {
      final userId = _auth.currentUser?.uid;
      if (userId == null) return;

      // First, get all notifications for this user
      final notificationsQuery =
          await _firestore
              .collection('notifications')
              .where('user_id', isEqualTo: userId)
              .get();

      if (notificationsQuery.docs.isEmpty) {
        debugPrint('No notifications found to delete');
        return;
      }

      // Use batched writes for better performance
      // Firestore only allows 500 operations per batch
      final int batchSize = 450;
      int batchCount = 0;
      int totalDeleted = 0;

      for (int i = 0; i < notificationsQuery.docs.length; i += batchSize) {
        final batch = _firestore.batch();
        final int end =
            (i + batchSize < notificationsQuery.docs.length)
                ? i + batchSize
                : notificationsQuery.docs.length;

        for (int j = i; j < end; j++) {
          batch.delete(notificationsQuery.docs[j].reference);
        }

        await batch.commit();
        batchCount++;
        totalDeleted += (end - i);
      }

      debugPrint(
        'Completely deleted $totalDeleted notifications in $batchCount batches',
      );
    } catch (e) {
      debugPrint('Error deleting all notifications: $e');
    }
  }

  // Send notification when a technician accepts a request
  Future<void> sendRequestAcceptedNotification(
    String requestId,
    String technicianName,
  ) async {
    try {
      // Get the request data to find customer ID
      final requestDoc =
          await _firestore.collection('service_requests').doc(requestId).get();
      if (!requestDoc.exists) {
        debugPrint('Request not found for notification: $requestId');
        return;
      }

      final requestData = requestDoc.data() as Map<String, dynamic>;
      final customerId =
          requestData['customer_id'] ?? requestData['customerId'];
      if (customerId == null) {
        debugPrint('Customer ID not found for request: $requestId');
        return;
      }

      // Create notification document
      final notification = {
        'type': 'REQUEST_ACCEPTED',
        'title': 'Request Accepted',
        'body': '$technicianName has accepted your request',
        'request_id': requestId,
        'user_id': customerId,
        'technician_name': technicianName,
        'timestamp': FieldValue.serverTimestamp(),
        'read': false,
        'data': {'requestId': requestId, 'action': 'OPEN_REQUEST_DETAIL'},
      };

      // Add notification to the database
      await _firestore.collection('notifications').add(notification);

      // Create local notification for the customer
      // Save current uid
      final currentUid = _auth.currentUser?.uid;

      // Temporarily sign in as the user to create the notification
      // This is a workaround since createLocalNotification creates for current user
      try {
        // Since we can't sign in as the customer, we'll create notification directly
        await _firestore.collection('notifications').add({
          'userId': customerId,
          'user_id': customerId, // Add snake_case for compatibility
          'title': 'Request Accepted',
          'body': '$technicianName has accepted your request',
          'type': 'request_accepted',
          'requestId': requestId,
          'request_id': requestId, // Add snake_case for compatibility
          'createdAt': FieldValue.serverTimestamp(),
          'created_at': FieldValue.serverTimestamp(), // Add snake_case for compatibility
          'read': false,
          'deleted': false,
        });

        debugPrint(
          'Request accepted notification created for customer: $customerId',
        );
      } catch (e) {
        debugPrint('Error creating notification document: $e');
      }
    } catch (e) {
      debugPrint('Error sending request accepted notification: $e');
      // Don't throw to avoid breaking the main flow
    }
  }

  // Create a local notification from a RemoteMessage
  Future<void> _createLocalNotificationFromMessage(
    RemoteMessage message,
  ) async {
    try {
      // Extract notification data
      String title = message.notification?.title ?? 'Mr. Tech';
      String body = message.notification?.body ?? '';
      String? requestId =
          message.data['requestId'] ?? message.data['request_id'];
      String type =
          message.data['type']?.toLowerCase() ??
          message.data['notificationType']?.toLowerCase() ??
          'general';

      // Create and show the notification
      await createLocalNotification(
        title: title,
        body: body,
        type: type,
        requestId: requestId,
      );
    } catch (e) {
      debugPrint('Error creating local notification from message: $e');
    }
  }

  // Static handler for background messages - must be top-level function
  @pragma('vm:entry-point')
  static Future<void> _firebaseMessagingBackgroundHandler(
    RemoteMessage message,
  ) async {
    // We need to initialize Firebase here
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    // Create an instance of NotificationService and handle the message
    final notificationService = NotificationService();
    await notificationService._handleBackgroundMessage(message);
  }
}
