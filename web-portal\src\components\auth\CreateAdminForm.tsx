import React, { useState } from 'react';
import { type CreateUserInput } from '../../types/user';
import { Permission } from '../../types/permissions';

interface CreateAdminFormProps {
  onSubmit: (userData: CreateUserInput) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

const CreateAdminForm: React.FC<CreateAdminFormProps> = ({
  onSubmit,
  onCancel,
  loading = false,
}) => {
  const [formData, setFormData] = useState<CreateUserInput>({
    email: '',
    name: '',
    password: '',
    role: 'admin',
    phone_number: '',
    preferred_language: 'en',
    address: '',
    city: '',
    country: '',
    permissions: [],
    admin_level: 'standard',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Handle form field changes
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  // Handle permission checkbox changes
  const handlePermissionChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value, checked } = e.target;

    setFormData((prev) => {
      const currentPermissions = prev.permissions || [];

      if (checked) {
        // Add permission if it doesn't exist
        return { ...prev, permissions: [...currentPermissions, value] };
      } else {
        // Remove permission if unchecked
        return { ...prev, permissions: currentPermissions.filter((p) => p !== value) };
      }
    });
  };

  // Validate form before submission
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Required fields validation
    if (!formData.email) newErrors.email = 'Email is required';
    else if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = 'Email is invalid';

    if (!formData.name) newErrors.name = 'Name is required';
    // Add name format validation
    else if (!/^[a-zA-Z\s\-']{2,50}$/.test(formData.name)) {
      newErrors.name =
        'Name can only contain letters, spaces, hyphens, and apostrophes (2-50 characters)';
    }

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters';
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      newErrors.password =
        'Password must contain at least one uppercase letter, one lowercase letter, and one number';
    }

    // Admin level validation
    if (!formData.admin_level) {
      newErrors.admin_level = 'Admin level is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting form:', error);
    }
  };

  // Group permissions by category for better UI organization
  const permissionGroups = {
    Dashboard: [Permission.VIEW_DASHBOARD, Permission.VIEW_ANALYTICS],
    Requests: [
      Permission.VIEW_ALL_REQUESTS,
      Permission.VIEW_OWN_REQUESTS,
      Permission.CREATE_REQUEST,
      Permission.UPDATE_REQUEST,
      Permission.DELETE_REQUEST,
      Permission.ASSIGN_REQUEST,
    ],
    Users: [
      Permission.VIEW_USERS,
      Permission.CREATE_USER,
      Permission.UPDATE_USER,
      Permission.DELETE_USER,
      Permission.CHANGE_USER_ROLE,
    ],
    Technicians: [
      Permission.VIEW_TECHNICIANS,
      Permission.MANAGE_TECHNICIAN_AVAILABILITY,
      Permission.VIEW_TECHNICIAN_PERFORMANCE,
    ],
    Services: [
      Permission.VIEW_SERVICES,
      Permission.CREATE_SERVICE,
      Permission.UPDATE_SERVICE,
      Permission.DELETE_SERVICE,
    ],
    Payments: [Permission.VIEW_PAYMENTS, Permission.PROCESS_REFUND, Permission.EXPORT_TRANSACTIONS],
    Chat: [Permission.VIEW_ALL_CHATS, Permission.VIEW_OWN_CHATS, Permission.SEND_MESSAGE],
    Settings: [Permission.VIEW_SETTINGS, Permission.UPDATE_SETTINGS],
    Reports: [Permission.VIEW_REPORTS, Permission.GENERATE_REPORTS, Permission.EXPORT_REPORTS],
  };

  // Format permission for display
  const formatPermission = (permission: string): string => {
    return permission
      .toLowerCase()
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <form onSubmit={handleSubmit} className='space-y-4'>
      {/* Basic Information */}
      <div>
        <h3 className='text-lg font-medium mb-2'>Basic Information</h3>
        <div className='grid grid-cols-1 gap-4'>
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>
              Email <span className='text-red-500'>*</span>
            </label>
            <input
              type='email'
              name='email'
              value={formData.email}
              onChange={handleChange}
              className={`w-full px-3 py-2 border rounded-md ${
                errors.email ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder='<EMAIL>'
            />
            {errors.email && <p className='text-red-500 text-xs mt-1'>{errors.email}</p>}
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>
              Name <span className='text-red-500'>*</span>
            </label>
            <input
              type='text'
              name='name'
              value={formData.name}
              onChange={handleChange}
              className={`w-full px-3 py-2 border rounded-md ${
                errors.name ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder='Full Name'
            />
            {errors.name && <p className='text-red-500 text-xs mt-1'>{errors.name}</p>}
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>Phone Number</label>
            <input
              type='tel'
              name='phone_number'
              value={formData.phone_number || ''}
              onChange={handleChange}
              className='w-full px-3 py-2 border border-gray-300 rounded-md'
              placeholder='+****************'
            />
          </div>
        </div>
      </div>

      {/* Authentication */}
      <div>
        <h3 className='text-lg font-medium mb-2'>Authentication</h3>
        <div>
          <label className='block text-sm font-medium text-gray-700 mb-1'>
            Password <span className='text-red-500'>*</span>
          </label>
          <input
            type='password'
            name='password'
            value={formData.password || ''}
            onChange={handleChange}
            className={`w-full px-3 py-2 border rounded-md ${
              errors.password ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder='Minimum 8 characters, including one uppercase letter, one lowercase letter, and one number'
          />
          {errors.password && <p className='text-red-500 text-xs mt-1'>{errors.password}</p>}
        </div>
      </div>

      {/* Admin Settings */}
      <div>
        <h3 className='text-lg font-medium mb-2'>Admin Settings</h3>
        <div>
          <label className='block text-sm font-medium text-gray-700 mb-1'>
            Admin Level <span className='text-red-500'>*</span>
          </label>
          <select
            name='admin_level'
            value={formData.admin_level || 'standard'}
            onChange={handleChange}
            className={`w-full px-3 py-2 border rounded-md ${
              errors.admin_level ? 'border-red-500' : 'border-gray-300'
            }`}
          >
            <option value='super'>Super Admin</option>
            <option value='standard'>Standard Admin</option>
            <option value='limited'>Limited Admin</option>
          </select>
          {errors.admin_level && <p className='text-red-500 text-xs mt-1'>{errors.admin_level}</p>}

          <p className='text-xs text-gray-500 mt-1 mb-4'>
            Super admins have all permissions. Standard admins have most permissions. Limited admins
            have restricted permissions.
          </p>
        </div>
      </div>

      {/* Permissions */}
      <div>
        <h3 className='text-lg font-medium mb-2'>Permissions</h3>
        <p className='text-sm text-gray-600 mb-4'>
          Select specific permissions for this admin user. Super admins automatically have all
          permissions.
        </p>

        <div className='space-y-4'>
          {Object.entries(permissionGroups).map(([groupName, permissions]) => (
            <div key={groupName} className='border border-gray-200 rounded-md p-3'>
              <h4 className='font-medium text-gray-800 mb-2'>{groupName}</h4>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-2'>
                {permissions.map((permission) => (
                  <div key={permission} className='flex items-center'>
                    <input
                      type='checkbox'
                      id={`permission-${permission}`}
                      value={permission}
                      checked={(formData.permissions || []).includes(permission)}
                      onChange={handlePermissionChange}
                      className='h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'
                      disabled={formData.admin_level === 'super'}
                    />
                    <label
                      htmlFor={`permission-${permission}`}
                      className='ml-2 text-sm text-gray-700'
                    >
                      {formatPermission(permission)}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Location Information */}
      <div>
        <h3 className='text-lg font-medium mb-2'>Location Information (Optional)</h3>
        <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>City</label>
            <input
              type='text'
              name='city'
              value={formData.city || ''}
              onChange={handleChange}
              className='w-full px-3 py-2 border border-gray-300 rounded-md'
              placeholder='City'
            />
          </div>

          <div>
            <label className='block text-sm font-medium text-gray-700 mb-1'>Country</label>
            <input
              type='text'
              name='country'
              value={formData.country || ''}
              onChange={handleChange}
              className='w-full px-3 py-2 border border-gray-300 rounded-md'
              placeholder='Country'
            />
          </div>
        </div>
      </div>

      {/* Preferences */}
      <div>
        <h3 className='text-lg font-medium mb-2'>Preferences</h3>
        <div>
          <label className='block text-sm font-medium text-gray-700 mb-1'>Preferred Language</label>
          <select
            name='preferred_language'
            value={formData.preferred_language || 'en'}
            onChange={handleChange}
            className='w-full px-3 py-2 border border-gray-300 rounded-md'
          >
            <option value='en'>English</option>
            <option value='ar'>Arabic</option>
          </select>
        </div>
      </div>

      {/* Form Actions */}
      <div className='flex justify-end space-x-2 pt-4 border-t border-gray-200'>
        <button
          type='button'
          onClick={onCancel}
          className='px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50'
          disabled={loading}
        >
          Cancel
        </button>
        <button
          type='submit'
          className='px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50'
          disabled={loading}
        >
          {loading ? 'Creating...' : 'Create Admin'}
        </button>
      </div>
    </form>
  );
};

export default CreateAdminForm;
