import React, { useEffect, useState } from 'react';
import { Link, Outlet, useLocation, useNavigate } from 'react-router-dom';
import { Bell, HelpCircle, LogOut, Menu, Settings, User, X } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { usePermissionFilter } from '../../hooks/usePermissions';
import { getIcon, navigationItems } from '../../config/navigation';
import { ScrollArea } from '../../components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '../../components/ui/avatar';
import { Button } from '../../components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../../components/ui/dropdown-menu';
import { Separator } from '../../components/ui/separator';
import { Badge } from '../../components/ui/badge';

import NotificationBell from '../notifications/NotificationBell';

const Layout: React.FC = () => {
  const { user, logout } = useAuth();
  const location = useLocation();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const navigate = useNavigate();

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Close sidebar on Escape key
      if (e.key === 'Escape' && sidebarOpen) {
        setSidebarOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [sidebarOpen]);

  // Filter navigation items based on user permissions
  const filteredNavItems = usePermissionFilter(navigationItems);

  // Fallback navigation items for authenticated users
  const fallbackNavItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: 'LayoutDashboard',
      path: '/dashboard',
      permissions: [],
    },
    {
      id: 'profile',
      label: 'Profile',
      icon: 'User',
      path: '/profile',
      permissions: [],
    },
  ];

  // Use filtered items if available, otherwise use fallback for authenticated users
  const displayNavItems =
    filteredNavItems.length > 0 ? filteredNavItems : user ? fallbackNavItems : [];

  return (
    <div className='flex h-screen bg-background'>
      {/* Sidebar */}
      <aside
        className={`${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        } fixed inset-y-0 left-0 z-50 w-64 bg-card transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 border-r shadow-lg lg:shadow-none`}
        role='navigation'
        aria-label='Main navigation'
      >
        <div className='flex h-full flex-col'>
          {/* Logo */}
          <div className='flex h-16 items-center justify-between px-6 border-b'>
            <Link to='/' className='flex items-center space-x-2'>
              <div className='w-8 h-8 rounded-md bg-primary flex items-center justify-center'>
                <span className='text-primary-foreground font-bold'>MT</span>
              </div>
              <h1 className='text-xl font-bold'>Mr.Tech Portal</h1>
            </Link>
            <Button
              variant='ghost'
              size='icon'
              className='lg:hidden'
              onClick={() => setSidebarOpen(false)}
              aria-label='Close navigation menu'
            >
              <X className='h-5 w-5' />
            </Button>
          </div>

          {/* Navigation */}
          <ScrollArea className='flex-1 px-3 py-4'>
            <nav className='space-y-1'>
              {displayNavItems.map((item) => {
                const Icon = getIcon(item.icon);
                const isActive = location.pathname === item.path;

                return (
                  <Link
                    key={item.path}
                    to={item.path}
                    className={`flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 ${
                      isActive
                        ? 'bg-primary text-primary-foreground'
                        : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                    }`}
                    onClick={() => {
                      // Close mobile sidebar when navigation item is clicked
                      if (window.innerWidth < 1024) {
                        setSidebarOpen(false);
                      }
                    }}
                    aria-current={isActive ? 'page' : undefined}
                  >
                    {Icon && <Icon className='h-5 w-5' />}
                    {item.label}
                  </Link>
                );
              })}
            </nav>
          </ScrollArea>
        </div>
      </aside>

      {/* Main Content */}
      <div className='flex-1 flex flex-col'>
        {/* Header */}
        <header className='h-16 bg-card border-b sticky top-0 z-30 w-full'>
          <div className='flex h-full items-center justify-between px-6'>
            <div className='flex items-center'>
              <Button
                variant='ghost'
                size='icon'
                className='lg:hidden mr-2'
                onClick={() => setSidebarOpen(true)}
                aria-label='Open navigation menu'
              >
                <Menu className='h-5 w-5' />
              </Button>

              <div className='text-lg font-medium hidden sm:block'>
                {/* Display current page title based on location */}
                {filteredNavItems.find((item) => item.path === location.pathname)?.label ||
                  'Dashboard'}
              </div>

              {/* Mobile page title */}
              <div className='text-base font-medium sm:hidden'>
                {filteredNavItems.find((item) => item.path === location.pathname)?.label ||
                  'Dashboard'}
              </div>
            </div>

            {/* Header Actions */}
            <div className='flex items-center space-x-2'>
              {/* Notifications */}
              <NotificationBell />

              {/* Help */}
              <Button variant='ghost' size='icon'>
                <HelpCircle className='h-5 w-5' />
              </Button>

              {/* User Menu */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant='ghost'
                    className='relative h-10 rounded-full flex items-center gap-2 px-2'
                  >
                    <Avatar className='h-8 w-8'>
                      <AvatarImage src={user?.photo_url} alt={user?.name} />
                      <AvatarFallback className='bg-primary/10 text-primary'>
                        {user?.name?.charAt(0).toUpperCase() || 'U'}
                      </AvatarFallback>
                    </Avatar>
                    <span className='font-medium text-sm hidden sm:inline-block'>
                      {user?.name?.split(' ')[0]}
                    </span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className='w-56' align='end' forceMount>
                  <DropdownMenuLabel className='font-normal'>
                    <div className='flex flex-col space-y-1'>
                      <p className='text-sm font-medium leading-none'>{user?.name}</p>
                      <p className='text-xs leading-none text-muted-foreground'>{user?.email}</p>
                      <p className='text-xs text-muted-foreground capitalize'>{user?.role}</p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem className='cursor-pointer' onClick={() => navigate('/profile')}>
                    <User className='mr-2 h-4 w-4' />
                    <span>Profile</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    className='cursor-pointer'
                    onClick={() => navigate('/account-settings')}
                  >
                    <Settings className='mr-2 h-4 w-4' />
                    <span>Settings</span>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    className='cursor-pointer'
                    onClick={() => {
                      logout();
                      navigate('/login');
                    }}
                  >
                    <LogOut className='mr-2 h-4 w-4' />
                    <span>Log out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className='flex-1 overflow-y-auto bg-background p-6'>
          <Outlet />
        </main>
      </div>

      {/* Mobile Sidebar Overlay */}
      {sidebarOpen && (
        <div
          className='fixed inset-0 z-40 bg-black/50 lg:hidden transition-opacity duration-300'
          onClick={() => setSidebarOpen(false)}
          onKeyDown={(e) => {
            if (e.key === 'Escape') {
              setSidebarOpen(false);
            }
          }}
          role='button'
          tabIndex={0}
          aria-label='Close navigation menu'
        />
      )}
    </div>
  );
};

export default Layout;
