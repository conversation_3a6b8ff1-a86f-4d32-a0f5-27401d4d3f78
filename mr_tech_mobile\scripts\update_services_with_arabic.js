const admin = require('firebase-admin');
const serviceAccount = require('../firebase-admin-key.json');

// Initialize Firebase Admin
admin.initializeApp({
  credential: admin.credential.cert(serviceAccount)
});

const db = admin.firestore();

// Arabic translations for services
const serviceTranslations = [
  {
    englishName: 'Windows Support',
    translations: {
      name: {
        en: 'Windows Support',
        ar: 'دعم ويندوز'
      },
      description: {
        en: 'Get expert support for Windows OS issues, performance optimization, troubleshooting, and system recovery.',
        ar: 'احصل على دعم متخصص لمشاكل نظام ويندوز، وتحسين الأداء، واستكشاف الأخطاء وإصلاحها، واستعادة النظام.'
      }
    }
  },
  {
    englishName: 'Office Applications',
    translations: {
      name: {
        en: 'Office Applications',
        ar: 'تطبيقات أوفيس'
      },
      description: {
        en: 'Get help with Microsoft Office applications including Word, Excel, PowerPoint, and Outlook.',
        ar: 'احصل على مساعدة في تطبيقات مايكروسوفت أوفيس بما في ذلك وورد، إكسل، باوربوينت، وأوتلوك.'
      }
    }
  },
  {
    englishName: 'Printer Setup',
    translations: {
      name: {
        en: 'Printer Setup',
        ar: 'إعداد الطابعة'
      },
      description: {
        en: 'Professional assistance setting up and troubleshooting printers, scanners, and all-in-one devices.',
        ar: 'مساعدة احترافية في إعداد وإصلاح مشاكل الطابعات والماسحات الضوئية والأجهزة متعددة الوظائف.'
      }
    }
  },
  {
    englishName: 'Network Troubleshooting',
    translations: {
      name: {
        en: 'Network Troubleshooting',
        ar: 'استكشاف أخطاء الشبكة وإصلاحها'
      },
      description: {
        en: 'Fix Wi-Fi issues, network connectivity problems, and optimize your home or office network.',
        ar: 'إصلاح مشاكل الواي فاي، ومشاكل الاتصال بالشبكة، وتحسين شبكة المنزل أو المكتب.'
      }
    }
  },
  {
    englishName: 'Virus Removal',
    translations: {
      name: {
        en: 'Virus Removal',
        ar: 'إزالة الفيروسات'
      },
      description: {
        en: 'Remove viruses, malware, and other security threats from your computer and improve system security.',
        ar: 'إزالة الفيروسات والبرامج الضارة والتهديدات الأمنية الأخرى من جهاز الكمبيوتر الخاص بك وتحسين أمان النظام.'
      }
    }
  },
  {
    englishName: 'Data Recovery',
    translations: {
      name: {
        en: 'Data Recovery',
        ar: 'استعادة البيانات'
      },
      description: {
        en: 'Recover lost or deleted files, photos, and important documents from your computer or external drives.',
        ar: 'استعادة الملفات المفقودة أو المحذوفة والصور والمستندات المهمة من جهاز الكمبيوتر أو محركات الأقراص الخارجية.'
      }
    }
  }
];

// Function to update services with translations
async function updateServicesWithTranslations() {
  try {
    // Get all services from Firestore
    const servicesSnapshot = await db.collection('services').get();
    
    if (servicesSnapshot.empty) {
      console.log('No services found in the database.');
      return;
    }
    
    console.log(`Found ${servicesSnapshot.size} services. Updating with translations...`);
    
    // Process each service
    const updatePromises = [];
    
    servicesSnapshot.forEach(doc => {
      const serviceData = doc.data();
      const serviceName = serviceData.name;
      
      // Find matching translation
      const translation = serviceTranslations.find(t => 
        t.englishName === serviceName || 
        (typeof serviceName === 'object' && serviceName.en === t.translations.name.en)
      );
      
      if (translation) {
        console.log(`Updating service: ${typeof serviceName === 'string' ? serviceName : serviceName.en}`);
        
        // Create update object
        const updateData = {
          name: translation.translations.name,
          description: translation.translations.description,
          // Add updated_at timestamp
          updated_at: admin.firestore.FieldValue.serverTimestamp()
        };
        
        // Add to update promises
        updatePromises.push(doc.ref.update(updateData));
      } else {
        console.log(`No translation found for service: ${typeof serviceName === 'string' ? serviceName : JSON.stringify(serviceName)}`);
      }
    });
    
    // Execute all updates
    await Promise.all(updatePromises);
    console.log('Services updated successfully with translations!');
  } catch (error) {
    console.error('Error updating services:', error);
  } finally {
    // Exit the process when done
    process.exit(0);
  }
}

// Run the update function
updateServicesWithTranslations(); 