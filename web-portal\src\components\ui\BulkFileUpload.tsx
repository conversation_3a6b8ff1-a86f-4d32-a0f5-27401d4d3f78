import React, { use<PERSON><PERSON>back, useRef, useState } from 'react';
import {
  AlertCircle,
  CheckCircle,
  File,
  Image,
  Loader2,
  Plus,
  Trash2,
  Upload,
  X,
} from 'lucide-react';
import { But<PERSON> } from './button';
import { Progress } from './progress';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './card';
import { Badge } from './badge';
import { cn } from '../../lib/utils';
import fileUploadService, { type FileUploadProgress } from '../../services/fileUploadService.js';

export interface BulkFileUploadProps {
  onFilesUpload?: (
    results: Array<{ file: File; url: string; success: boolean; error?: string }>,
  ) => void;
  onFileRemove?: (index: number) => void;
  accept?: string;
  maxSize?: number;
  maxFiles?: number;
  disabled?: boolean;
  className?: string;
  requestId?: string;
  uploadOnSelect?: boolean;
  showPreview?: boolean;
}

interface FileUploadItem {
  file: File;
  progress: FileUploadProgress | null;
  isUploading: boolean;
  uploadedUrl: string | null;
  error: string | null;
  id: string;
}

const BulkFileUpload: React.FC<BulkFileUploadProps> = ({
  onFilesUpload,
  onFileRemove,
  accept = fileUploadService.getAllowedFileTypes().join(','),
  maxSize = fileUploadService.getMaxFileSize(),
  maxFiles = 10,
  disabled = false,
  className,
  requestId,
  uploadOnSelect = false,
  showPreview = true,
}) => {
  const [files, setFiles] = useState<FileUploadItem[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const addFiles = useCallback(
    (newFiles: File[]) => {
      const validFiles = newFiles.filter((file) => {
        if (file.size > maxSize) {
          console.warn(`File ${file.name} is too large`);
          return false;
        }
        return true;
      });

      if (files.length + validFiles.length > maxFiles) {
        console.warn(`Cannot add more than ${maxFiles} files`);
        validFiles.splice(maxFiles - files.length);
      }

      const fileItems: FileUploadItem[] = validFiles.map((file) => ({
        file,
        progress: null,
        isUploading: false,
        uploadedUrl: null,
        error: null,
        id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      }));

      setFiles((prev) => [...prev, ...fileItems]);

      // Auto-upload if enabled
      if (uploadOnSelect && requestId) {
        fileItems.forEach((item, index) => {
          uploadFile(files.length + index, item.file);
        });
      }
    },
    [files.length, maxFiles, maxSize, uploadOnSelect, requestId],
  );

  const uploadFile = async (index: number, file: File) => {
    if (!requestId) return;

    setFiles((prev) =>
      prev.map((item, i) => (i === index ? { ...item, isUploading: true, error: null } : item)),
    );

    try {
      const result = await fileUploadService.uploadRequestAttachment(
        file,
        requestId,
        (progress) => {
          setFiles((prev) => prev.map((item, i) => (i === index ? { ...item, progress } : item)));
        },
      );

      if (result.success && result.url) {
        setFiles((prev) =>
          prev.map((item, i) =>
            i === index
              ? {
                  ...item,
                  uploadedUrl: result.url!,
                  isUploading: false,
                  progress: null,
                }
              : item,
          ),
        );
      } else {
        setFiles((prev) =>
          prev.map((item, i) =>
            i === index
              ? {
                  ...item,
                  error: result.error || 'Upload failed',
                  isUploading: false,
                  progress: null,
                }
              : item,
          ),
        );
      }
    } catch (error) {
      setFiles((prev) =>
        prev.map((item, i) =>
          i === index
            ? {
                ...item,
                error: 'Upload failed',
                isUploading: false,
                progress: null,
              }
            : item,
        ),
      );
    }
  };

  const uploadAllFiles = async () => {
    if (!requestId) return;

    setIsUploading(true);
    const results: Array<{ file: File; url: string; success: boolean; error?: string }> = [];

    for (let i = 0; i < files.length; i++) {
      const item = files[i];
      if (item.uploadedUrl || item.isUploading) continue;

      await uploadFile(i, item.file);

      // Get the updated state
      const updatedItem = files[i];
      results.push({
        file: item.file,
        url: updatedItem.uploadedUrl || '',
        success: !!updatedItem.uploadedUrl,
        error: updatedItem.error || undefined,
      });
    }

    setIsUploading(false);
    onFilesUpload?.(results);
  };

  const removeFile = (index: number) => {
    setFiles((prev) => prev.filter((_, i) => i !== index));
    onFileRemove?.(index);
  };

  const clearAllFiles = () => {
    setFiles([]);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(e.target.files || []);
    if (selectedFiles.length > 0) {
      addFiles(selectedFiles);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    if (disabled) return;

    const droppedFiles = Array.from(e.dataTransfer.files);
    if (droppedFiles.length > 0) {
      addFiles(droppedFiles);
    }
  };

  const openFileDialog = () => {
    if (!disabled) {
      fileInputRef.current?.click();
    }
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return <Image className='h-5 w-5 text-blue-500' />;
    }
    return <File className='h-5 w-5 text-gray-500' />;
  };

  const getUploadStats = () => {
    const uploaded = files.filter((f) => f.uploadedUrl).length;
    const failed = files.filter((f) => f.error).length;
    const pending = files.filter((f) => !f.uploadedUrl && !f.error && !f.isUploading).length;
    const uploading = files.filter((f) => f.isUploading).length;

    return { uploaded, failed, pending, uploading };
  };

  const stats = getUploadStats();

  return (
    <div className={cn('space-y-4', className)}>
      {/* Drop Zone */}
      <div
        className={cn(
          'border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors',
          isDragOver ? 'border-blue-500 bg-blue-50' : 'border-gray-300',
          disabled && 'opacity-50 cursor-not-allowed',
        )}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={openFileDialog}
      >
        <Upload className='h-8 w-8 mx-auto mb-2 text-gray-400' />
        <p className='text-sm text-gray-600'>Drop files here or click to browse</p>
        <p className='text-xs text-gray-400 mt-1'>
          Max {maxFiles} files, {fileUploadService.formatFileSize(maxSize)} each
        </p>
      </div>

      {/* File List */}
      {files.length > 0 && (
        <Card>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <CardTitle className='text-lg'>
                Files ({files.length}/{maxFiles})
              </CardTitle>
              <div className='flex items-center gap-2'>
                {stats.uploaded > 0 && (
                  <Badge variant='secondary' className='bg-green-100 text-green-800'>
                    {stats.uploaded} uploaded
                  </Badge>
                )}
                {stats.failed > 0 && (
                  <Badge variant='secondary' className='bg-red-100 text-red-800'>
                    {stats.failed} failed
                  </Badge>
                )}
                {stats.pending > 0 && <Badge variant='secondary'>{stats.pending} pending</Badge>}
                <Button variant='outline' size='sm' onClick={clearAllFiles} disabled={isUploading}>
                  <Trash2 className='h-4 w-4 mr-2' />
                  Clear All
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className='space-y-3'>
            {files.map((item, index) => (
              <div key={item.id} className='flex items-center gap-3 p-3 border rounded-lg'>
                {getFileIcon(item.file.type)}

                <div className='flex-1 min-w-0'>
                  <div className='flex items-center gap-2 mb-1'>
                    <p className='text-sm font-medium truncate'>{item.file.name}</p>
                    <span className='text-xs text-gray-500'>
                      ({fileUploadService.formatFileSize(item.file.size)})
                    </span>
                  </div>

                  {/* Progress Bar */}
                  {item.progress && item.progress.status === 'uploading' && (
                    <div className='space-y-1'>
                      <Progress value={item.progress.progress} className='h-2' />
                      <p className='text-xs text-gray-500'>
                        Uploading... {Math.round(item.progress.progress)}%
                      </p>
                    </div>
                  )}

                  {/* Status */}
                  {item.uploadedUrl && (
                    <div className='flex items-center gap-1 text-green-600'>
                      <CheckCircle className='h-4 w-4' />
                      <span className='text-xs'>Upload complete</span>
                    </div>
                  )}

                  {item.error && (
                    <div className='flex items-center gap-1 text-red-600'>
                      <AlertCircle className='h-4 w-4' />
                      <span className='text-xs'>{item.error}</span>
                    </div>
                  )}
                </div>

                <div className='flex items-center gap-1'>
                  {item.isUploading && <Loader2 className='h-4 w-4 animate-spin text-blue-500' />}
                  <Button
                    variant='ghost'
                    size='sm'
                    onClick={() => removeFile(index)}
                    disabled={item.isUploading}
                    className='h-8 w-8 p-0'
                  >
                    <X className='h-4 w-4' />
                  </Button>
                </div>
              </div>
            ))}

            {/* Upload All Button */}
            {!uploadOnSelect && requestId && stats.pending > 0 && (
              <div className='pt-3 border-t'>
                <Button onClick={uploadAllFiles} disabled={isUploading} className='w-full'>
                  {isUploading ? (
                    <>
                      <Loader2 className='h-4 w-4 mr-2 animate-spin' />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload className='h-4 w-4 mr-2' />
                      Upload All Files ({stats.pending})
                    </>
                  )}
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      <input
        ref={fileInputRef}
        type='file'
        accept={accept}
        multiple
        onChange={handleInputChange}
        className='hidden'
      />
    </div>
  );
};

export default BulkFileUpload;
