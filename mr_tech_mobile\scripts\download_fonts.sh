#!/bin/bash

# <PERSON>ript to download and install required fonts for the Mr.Tech app
# This script downloads the fonts from Google Fonts and places them in the
# appropriate assets directory.

# Make sure the fonts directory exists
mkdir -p ../assets/fonts

# Define the target directory
FONTS_DIR="../assets/fonts"

# Define the font URLs (from Google Fonts)

# Playfair Display (elegant serif)
PLAYFAIR_REGULAR="https://fonts.gstatic.com/s/playfairdisplay/v30/nuFvD-vYSZviVYUb_rj3ij__anPXJzDwcbmjWBN2PKdFvXDXbtM.woff2"
PLAYFAIR_MEDIUM="https://fonts.gstatic.com/s/playfairdisplay/v30/nuFvD-vYSZviVYUb_rj3ij__anPXJzDwcbmjWBN2PKd3vXDXbtM.woff2"
PLAYFAIR_SEMIBOLD="https://fonts.gstatic.com/s/playfairdisplay/v30/nuFvD-vYSZviVYUb_rj3ij__anPXJzDwcbmjWBN2PKfFunDXbtM.woff2"
PLAYFAIR_BOLD="https://fonts.gstatic.com/s/playfairdisplay/v30/nuFvD-vYSZviVYUb_rj3ij__anPXJzDwcbmjWBN2PKeiukDXbtM.woff2"
PLAYFAIR_ITALIC="https://fonts.gstatic.com/s/playfairdisplay/v30/nuFRD-vYSZviVYUb_rj3ij__anPXDTnCjmHKM4nYO7KN_qiTXtHA-Q.woff2"

# Montserrat (modern sans-serif)
MONTSERRAT_REGULAR="https://fonts.gstatic.com/s/montserrat/v25/JTUHjIg1_i6t8kCHKm4532VJOt5-QNFgpCtr6Hw5aXo.woff2"
MONTSERRAT_MEDIUM="https://fonts.gstatic.com/s/montserrat/v25/JTUHjIg1_i6t8kCHKm4532VJOt5-QNFgpCtZ6Hw5aXo.woff2"
MONTSERRAT_SEMIBOLD="https://fonts.gstatic.com/s/montserrat/v25/JTUHjIg1_i6t8kCHKm4532VJOt5-QNFgpCu173w5aXo.woff2"
MONTSERRAT_BOLD="https://fonts.gstatic.com/s/montserrat/v25/JTUHjIg1_i6t8kCHKm4532VJOt5-QNFgpCuM73w5aXo.woff2"
MONTSERRAT_ITALIC="https://fonts.gstatic.com/s/montserrat/v25/JTUFjIg1_i6t8kCHKm459Wx7xQYXK0vOoz6jq6R9WXh0pg.woff2"

# Download Playfair Display
echo "Downloading Playfair Display fonts..."
curl -s -o "${FONTS_DIR}/PlayfairDisplay-Regular.ttf" "${PLAYFAIR_REGULAR}"
curl -s -o "${FONTS_DIR}/PlayfairDisplay-Medium.ttf" "${PLAYFAIR_MEDIUM}"
curl -s -o "${FONTS_DIR}/PlayfairDisplay-SemiBold.ttf" "${PLAYFAIR_SEMIBOLD}"
curl -s -o "${FONTS_DIR}/PlayfairDisplay-Bold.ttf" "${PLAYFAIR_BOLD}"
curl -s -o "${FONTS_DIR}/PlayfairDisplay-Italic.ttf" "${PLAYFAIR_ITALIC}"

# Download Montserrat
echo "Downloading Montserrat fonts..."
curl -s -o "${FONTS_DIR}/Montserrat-Regular.ttf" "${MONTSERRAT_REGULAR}"
curl -s -o "${FONTS_DIR}/Montserrat-Medium.ttf" "${MONTSERRAT_MEDIUM}"
curl -s -o "${FONTS_DIR}/Montserrat-SemiBold.ttf" "${MONTSERRAT_SEMIBOLD}"
curl -s -o "${FONTS_DIR}/Montserrat-Bold.ttf" "${MONTSERRAT_BOLD}"
curl -s -o "${FONTS_DIR}/Montserrat-Italic.ttf" "${MONTSERRAT_ITALIC}"

# Verify download
echo "Verifying downloaded files..."
ls -la "${FONTS_DIR}"

echo "Font download complete! The following fonts are now available:"
echo "- Playfair Display (Regular, Medium, SemiBold, Bold, Italic)"
echo "- Montserrat (Regular, Medium, SemiBold, Bold, Italic)"
echo ""
echo "Please make sure these fonts are properly declared in your pubspec.yaml file."
echo "You may need to run 'flutter pub get' and restart your app to see the changes." 