import { describe, expect, it } from 'vitest';
import {
  isApiResponse,
  isAppError,
  isArray,
  isBoolean,
  isDate,
  isFunction,
  isNull,
  isNumber,
  isObject,
  isRequest,
  isString,
  isUndefined,
  isUser,
  isValidEmail,
  isValidId,
  isValidPhone,
} from '../typeGuards';
import type { ApiResponse, AppError } from '../../types/common';
import { ErrorCode, RequestStatus, UserRole } from '../../types/enums';

describe('typeGuards', () => {
  describe('primitive type guards', () => {
    describe('isString', () => {
      it('should return true for strings', () => {
        expect(isString('hello')).toBe(true);
        expect(isString('')).toBe(true);
        expect(isString('123')).toBe(true);
      });

      it('should return false for non-strings', () => {
        expect(isString(123)).toBe(false);
        expect(isString(true)).toBe(false);
        expect(isString(null)).toBe(false);
        expect(isString(undefined)).toBe(false);
        expect(isString({})).toBe(false);
        expect(isString([])).toBe(false);
      });
    });

    describe('isNumber', () => {
      it('should return true for numbers', () => {
        expect(isNumber(123)).toBe(true);
        expect(isNumber(0)).toBe(true);
        expect(isNumber(-123)).toBe(true);
        expect(isNumber(3.14)).toBe(true);
        expect(isNumber(Infinity)).toBe(true);
      });

      it('should return false for non-numbers', () => {
        expect(isNumber('123')).toBe(false);
        expect(isNumber(true)).toBe(false);
        expect(isNumber(null)).toBe(false);
        expect(isNumber(undefined)).toBe(false);
        expect(isNumber({})).toBe(false);
        expect(isNumber(NaN)).toBe(false);
      });
    });

    describe('isBoolean', () => {
      it('should return true for booleans', () => {
        expect(isBoolean(true)).toBe(true);
        expect(isBoolean(false)).toBe(true);
      });

      it('should return false for non-booleans', () => {
        expect(isBoolean(1)).toBe(false);
        expect(isBoolean(0)).toBe(false);
        expect(isBoolean('true')).toBe(false);
        expect(isBoolean(null)).toBe(false);
        expect(isBoolean(undefined)).toBe(false);
      });
    });

    describe('isObject', () => {
      it('should return true for objects', () => {
        expect(isObject({})).toBe(true);
        expect(isObject({ key: 'value' })).toBe(true);
        expect(isObject(new Date())).toBe(true);
      });

      it('should return false for non-objects', () => {
        expect(isObject(null)).toBe(false);
        expect(isObject(undefined)).toBe(false);
        expect(isObject('string')).toBe(false);
        expect(isObject(123)).toBe(false);
        expect(isObject([])).toBe(false);
        expect(isObject(() => {})).toBe(false);
      });
    });

    describe('isArray', () => {
      it('should return true for arrays', () => {
        expect(isArray([])).toBe(true);
        expect(isArray([1, 2, 3])).toBe(true);
        expect(isArray(['a', 'b'])).toBe(true);
      });

      it('should return false for non-arrays', () => {
        expect(isArray({})).toBe(false);
        expect(isArray('string')).toBe(false);
        expect(isArray(123)).toBe(false);
        expect(isArray(null)).toBe(false);
      });
    });

    describe('isFunction', () => {
      it('should return true for functions', () => {
        expect(isFunction(() => {})).toBe(true);
        expect(isFunction(() => {})).toBe(true);
        expect(isFunction(async () => {})).toBe(true);
        expect(isFunction(Date)).toBe(true);
      });

      it('should return false for non-functions', () => {
        expect(isFunction({})).toBe(false);
        expect(isFunction('string')).toBe(false);
        expect(isFunction(123)).toBe(false);
        expect(isFunction(null)).toBe(false);
      });
    });

    describe('isUndefined', () => {
      it('should return true for undefined', () => {
        expect(isUndefined(undefined)).toBe(true);
      });

      it('should return false for defined values', () => {
        expect(isUndefined(null)).toBe(false);
        expect(isUndefined('')).toBe(false);
        expect(isUndefined(0)).toBe(false);
        expect(isUndefined(false)).toBe(false);
      });
    });

    describe('isNull', () => {
      it('should return true for null', () => {
        expect(isNull(null)).toBe(true);
      });

      it('should return false for non-null values', () => {
        expect(isNull(undefined)).toBe(false);
        expect(isNull('')).toBe(false);
        expect(isNull(0)).toBe(false);
        expect(isNull(false)).toBe(false);
      });
    });

    describe('isDate', () => {
      it('should return true for Date objects', () => {
        expect(isDate(new Date())).toBe(true);
        expect(isDate(new Date('2023-01-01'))).toBe(true);
      });

      it('should return false for non-Date objects', () => {
        expect(isDate('2023-01-01')).toBe(false);
        expect(isDate(1672531200000)).toBe(false);
        expect(isDate({})).toBe(false);
        expect(isDate(null)).toBe(false);
      });
    });
  });

  describe('complex type guards', () => {
    describe('isApiResponse', () => {
      it('should return true for valid ApiResponse', () => {
        const validResponse: ApiResponse<string> = {
          success: true,
          data: 'test data',
        };
        expect(isApiResponse(validResponse)).toBe(true);

        const validErrorResponse: ApiResponse = {
          success: false,
          error: 'Error message',
        };
        expect(isApiResponse(validErrorResponse)).toBe(true);
      });

      it('should return false for invalid ApiResponse', () => {
        expect(isApiResponse({})).toBe(false);
        expect(isApiResponse({ success: 'true' })).toBe(false);
        expect(isApiResponse({ data: 'test' })).toBe(false);
        expect(isApiResponse(null)).toBe(false);
      });

      it('should validate data with custom guard', () => {
        const response = {
          success: true,
          data: { id: '1', name: 'Test' },
        };

        const userGuard = (data: unknown): data is { id: string; name: string } => {
          return isObject(data) && 
                 isString((data as any).id) && 
                 isString((data as any).name);
        };

        expect(isApiResponse(response, userGuard)).toBe(true);

        const invalidResponse = {
          success: true,
          data: { id: 123, name: 'Test' },
        };
        expect(isApiResponse(invalidResponse, userGuard)).toBe(false);
      });
    });

    describe('isAppError', () => {
      it('should return true for valid AppError', () => {
        const validError: AppError = {
          code: ErrorCode.VALIDATION_REQUIRED_FIELD,
          message: 'Field is required',
          timestamp: new Date(),
        };
        expect(isAppError(validError)).toBe(true);

        const errorWithDetails: AppError = {
          code: ErrorCode.NETWORK_SERVER_ERROR,
          message: 'Server error',
          details: { statusCode: 500 },
          timestamp: new Date(),
          context: 'TestService',
        };
        expect(isAppError(errorWithDetails)).toBe(true);
      });

      it('should return false for invalid AppError', () => {
        expect(isAppError({})).toBe(false);
        expect(isAppError({ code: 'INVALID' })).toBe(false);
        expect(isAppError({ message: 'Error' })).toBe(false);
        expect(isAppError(null)).toBe(false);
      });
    });

    describe('isValidEmail', () => {
      it('should return true for valid emails', () => {
        const validEmails = [
          '<EMAIL>',
          '<EMAIL>',
          '<EMAIL>',
        ];

        validEmails.forEach(email => {
          expect(isValidEmail(email)).toBe(true);
        });
      });

      it('should return false for invalid emails', () => {
        const invalidEmails = [
          'invalid-email',
          '@example.com',
          'test@',
          123,
          null,
          undefined,
        ];

        invalidEmails.forEach(email => {
          expect(isValidEmail(email)).toBe(false);
        });
      });
    });

    describe('isValidPhone', () => {
      it('should return true for valid phone numbers', () => {
        const validPhones = [
          '+1234567890',
          '************',
          '(*************',
          '+44 20 7946 0958',
        ];

        validPhones.forEach(phone => {
          expect(isValidPhone(phone)).toBe(true);
        });
      });

      it('should return false for invalid phone numbers', () => {
        const invalidPhones = [
          '123',
          'abc-def-ghij',
          123456789,
          null,
          undefined,
        ];

        invalidPhones.forEach(phone => {
          expect(isValidPhone(phone)).toBe(false);
        });
      });
    });

    describe('isUser', () => {
      it('should return true for valid User', () => {
        const validUser = {
          id: 'user-123',
          email: '<EMAIL>',
          name: 'Test User',
          role: UserRole.CUSTOMER,
        };
        expect(isUser(validUser)).toBe(true);
      });

      it('should return false for invalid User', () => {
        expect(isUser({})).toBe(false);
        expect(isUser({ id: 'user-123' })).toBe(false);
        expect(isUser({ email: '<EMAIL>' })).toBe(false);
        expect(isUser(null)).toBe(false);
      });

      it('should handle optional fields', () => {
        const userWithOptionals = {
          id: 'user-123',
          email: '<EMAIL>',
          name: 'Test User',
          role: UserRole.CUSTOMER,
          phone: '+1234567890',
          avatar: 'https://example.com/avatar.jpg',
        };
        expect(isUser(userWithOptionals)).toBe(true);
      });
    });

    describe('isRequest', () => {
      it('should return true for valid Request', () => {
        const validRequest = {
          id: 'req-123',
          customerId: 'user-123',
          serviceId: 'service-123',
          status: RequestStatus.PENDING,
          createdAt: new Date(),
        };
        expect(isRequest(validRequest)).toBe(true);
      });

      it('should return false for invalid Request', () => {
        expect(isRequest({})).toBe(false);
        expect(isRequest({ id: 'req-123' })).toBe(false);
        expect(isRequest(null)).toBe(false);
      });

      it('should handle optional fields', () => {
        const requestWithOptionals = {
          id: 'req-123',
          customerId: 'user-123',
          serviceId: 'service-123',
          status: RequestStatus.PENDING,
          createdAt: new Date(),
          technicianId: 'tech-123',
        };
        expect(isRequest(requestWithOptionals)).toBe(true);
      });
    });

    describe('isValidId', () => {
      it('should return true for valid IDs', () => {
        const validIds = [
          'user-123',
          'req-456',
          'service-789',
          'abc123',
        ];

        validIds.forEach(id => {
          expect(isValidId(id)).toBe(true);
        });
      });

      it('should return false for invalid IDs', () => {
        const invalidIds = [
          '',
          '  ',
          ' leading-space',
          'trailing-space ',
          123,
          null,
          undefined,
        ];

        invalidIds.forEach(id => {
          expect(isValidId(id)).toBe(false);
        });
      });
    });
  });
});
