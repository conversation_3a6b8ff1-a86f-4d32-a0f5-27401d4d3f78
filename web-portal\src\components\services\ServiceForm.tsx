import React, { useEffect, useState } from 'react';
import type { CreateServiceInput, ServiceModel, UpdateServiceInput } from '../../types/service';
import categoryService from '../../services/categoryService';
import type { ServiceCategory } from '../../types/category';
import pricingService from '../../services/pricingService';
import type { Currency, PricingStrategy } from '../../types/pricing';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Textarea } from '../ui/textarea';
import { Switch } from '../ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs';
import { Badge } from '../ui/badge';
import { AlertCircle, CheckCircle, Globe, Loader2, Plus, Upload, X } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { formValidationHelpers, serviceFormSchema } from '../../utils/formValidation';

interface ServiceFormProps {
  service?: ServiceModel | null;
  onSubmit: (data: CreateServiceInput | UpdateServiceInput) => Promise<void>;
  onCancel: () => void;
  loading?: boolean;
}

interface Translation {
  language: string;
  text: string;
}

export const ServiceForm: React.FC<ServiceFormProps> = ({
  service,
  onSubmit,
  onCancel,
  loading = false,
}) => {
  const [formData, setFormData] = useState({
    category: '',
    base_price: 0,
    currency: 'USD' as Currency,
    image_url: '',
    is_active: true,
    estimated_duration: 60,
    pricing_strategy: 'fixed' as PricingStrategy,
    has_advanced_pricing: false,
    metadata: {},
  });

  const [nameTranslations, setNameTranslations] = useState<Translation[]>([
    { language: 'en', text: '' },
  ]);

  const [descriptionTranslations, setDescriptionTranslations] = useState<Translation[]>([
    { language: 'en', text: '' },
  ]);

  const [categories, setCategories] = useState<ServiceCategory[]>([]);
  const [categoriesLoading, setCategoriesLoading] = useState(true);

  const availableLanguages = ['en', 'ar', 'fr', 'es', 'de'];

  // Load categories
  useEffect(() => {
    const loadCategories = async () => {
      try {
        const fetchedCategories = await categoryService.getActiveCategories();
        setCategories(fetchedCategories);
      } catch (error) {
        console.error('Failed to load categories:', error);
      } finally {
        setCategoriesLoading(false);
      }
    };

    loadCategories();
  }, []);

  // Initialize form with existing service data
  useEffect(() => {
    if (service) {
      setFormData({
        category: service.category,
        base_price: service.base_price,
        currency: (service.currency as Currency) || 'USD',
        image_url: service.image_url,
        is_active: service.is_active,
        estimated_duration: service.estimated_duration,
        pricing_strategy: (service.pricing_strategy as PricingStrategy) || 'fixed',
        has_advanced_pricing: service.has_advanced_pricing || false,
        metadata: service.metadata || {},
      });

      // Handle name translations
      if (typeof service.name === 'string') {
        setNameTranslations([{ language: 'en', text: service.name }]);
      } else {
        const translations = Object.entries(service.name).map(([lang, text]) => ({
          language: lang,
          text,
        }));
        setNameTranslations(
          translations.length > 0 ? translations : [{ language: 'en', text: '' }],
        );
      }

      // Handle description translations
      if (typeof service.description === 'string') {
        setDescriptionTranslations([{ language: 'en', text: service.description }]);
      } else {
        const translations = Object.entries(service.description).map(([lang, text]) => ({
          language: lang,
          text,
        }));
        setDescriptionTranslations(
          translations.length > 0 ? translations : [{ language: 'en', text: '' }],
        );
      }
    }
  }, [service]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Convert translations to the expected format
    const nameObj: Record<string, string> = {};
    const descriptionObj: Record<string, string> = {};

    nameTranslations.forEach((t) => {
      if (t.text.trim()) {
        nameObj[t.language] = t.text.trim();
      }
    });

    descriptionTranslations.forEach((t) => {
      if (t.text.trim()) {
        descriptionObj[t.language] = t.text.trim();
      }
    });

    const submitData = {
      ...formData,
      name: Object.keys(nameObj).length === 1 && nameObj.en ? nameObj.en : nameObj,
      description:
        Object.keys(descriptionObj).length === 1 && descriptionObj.en
          ? descriptionObj.en
          : descriptionObj,
    };

    await onSubmit(submitData);
  };

  const addTranslation = (
    translations: Translation[],
    setTranslations: (translations: Translation[]) => void,
  ) => {
    const usedLanguages = translations.map((t) => t.language);
    const availableLanguage = availableLanguages.find((lang) => !usedLanguages.includes(lang));

    if (availableLanguage) {
      setTranslations([...translations, { language: availableLanguage, text: '' }]);
    }
  };

  const removeTranslation = (
    index: number,
    translations: Translation[],
    setTranslations: (translations: Translation[]) => void,
  ) => {
    if (translations.length > 1) {
      setTranslations(translations.filter((_, i) => i !== index));
    }
  };

  const updateTranslation = (
    index: number,
    field: 'language' | 'text',
    value: string,
    translations: Translation[],
    setTranslations: (translations: Translation[]) => void,
  ) => {
    const updated = [...translations];
    updated[index] = { ...updated[index], [field]: value };
    setTranslations(updated);
  };

  const renderTranslationInputs = (
    translations: Translation[],
    setTranslations: (translations: Translation[]) => void,
    label: string,
    isTextarea: boolean = false,
  ) => (
    <div className='space-y-3'>
      <div className='flex items-center justify-between'>
        <Label className='flex items-center gap-2'>
          <Globe className='w-4 h-4' />
          {label}
        </Label>
        <Button
          type='button'
          variant='outline'
          size='sm'
          onClick={() => addTranslation(translations, setTranslations)}
          disabled={translations.length >= availableLanguages.length}
        >
          <Plus className='w-4 h-4 mr-1' />
          Add Language
        </Button>
      </div>

      {translations.map((translation, index) => (
        <div key={index} className='flex gap-2 items-start'>
          <select
            value={translation.language}
            onChange={(e) =>
              updateTranslation(index, 'language', e.target.value, translations, setTranslations)
            }
            className='px-3 py-2 border border-gray-300 rounded-md text-sm min-w-20'
          >
            {availableLanguages.map((lang) => (
              <option key={lang} value={lang}>
                {lang.toUpperCase()}
              </option>
            ))}
          </select>

          {isTextarea ? (
            <Textarea
              value={translation.text}
              onChange={(e) =>
                updateTranslation(index, 'text', e.target.value, translations, setTranslations)
              }
              placeholder={`${label} in ${translation.language.toUpperCase()}`}
              className='flex-1'
              rows={3}
            />
          ) : (
            <Input
              value={translation.text}
              onChange={(e) =>
                updateTranslation(index, 'text', e.target.value, translations, setTranslations)
              }
              placeholder={`${label} in ${translation.language.toUpperCase()}`}
              className='flex-1'
            />
          )}

          {translations.length > 1 && (
            <Button
              type='button'
              variant='outline'
              size='sm'
              onClick={() => removeTranslation(index, translations, setTranslations)}
            >
              <X className='w-4 h-4' />
            </Button>
          )}
        </div>
      ))}
    </div>
  );

  return (
    <Card className='w-full max-w-4xl mx-auto'>
      <CardHeader>
        <CardTitle>{service ? 'Edit Service' : 'Create New Service'}</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className='space-y-6'>
          <Tabs defaultValue='basic' className='w-full'>
            <TabsList className='grid w-full grid-cols-3'>
              <TabsTrigger value='basic'>Basic Info</TabsTrigger>
              <TabsTrigger value='translations'>Translations</TabsTrigger>
              <TabsTrigger value='advanced'>Advanced</TabsTrigger>
            </TabsList>

            <TabsContent value='basic' className='space-y-4'>
              {/* Category */}
              <div className='space-y-2'>
                <Label htmlFor='category'>Category *</Label>
                {categoriesLoading ? (
                  <Input placeholder='Loading categories...' disabled />
                ) : (
                  <select
                    id='category'
                    value={formData.category}
                    onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                    className='flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'
                    required
                  >
                    <option value=''>Select a category</option>
                    {categories.map((category) => (
                      <option
                        key={category.id}
                        value={categoryService.getTranslatedName(category, 'en')}
                      >
                        {categoryService.getTranslatedName(category, 'en')}
                      </option>
                    ))}
                    <option value='custom'>+ Add Custom Category</option>
                  </select>
                )}
                {formData.category === 'custom' && (
                  <Input
                    placeholder='Enter custom category name'
                    onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                    className='mt-2'
                    required
                  />
                )}
              </div>

              {/* Pricing Configuration */}
              <div className='space-y-4 p-4 border rounded-lg bg-gray-50'>
                <div className='flex items-center justify-between'>
                  <h3 className='text-lg font-semibold'>Pricing Configuration</h3>
                  <div className='flex items-center space-x-2'>
                    <Switch
                      id='has_advanced_pricing'
                      checked={formData.has_advanced_pricing}
                      onCheckedChange={(checked) =>
                        setFormData({ ...formData, has_advanced_pricing: checked })
                      }
                    />
                    <Label htmlFor='has_advanced_pricing' className='text-sm'>
                      Advanced Pricing
                    </Label>
                  </div>
                </div>

                <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                  {/* Base Price */}
                  <div className='space-y-2'>
                    <Label htmlFor='base_price'>Base Price *</Label>
                    <Input
                      id='base_price'
                      type='number'
                      min='0'
                      step='0.01'
                      value={formData.base_price}
                      onChange={(e) =>
                        setFormData({ ...formData, base_price: parseFloat(e.target.value) || 0 })
                      }
                      required
                    />
                  </div>

                  {/* Currency */}
                  <div className='space-y-2'>
                    <Label htmlFor='currency'>Currency *</Label>
                    <select
                      id='currency'
                      value={formData.currency}
                      onChange={(e) =>
                        setFormData({ ...formData, currency: e.target.value as Currency })
                      }
                      className='flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'
                      required
                    >
                      {pricingService.getAvailableCurrencies().map((currency) => (
                        <option key={currency} value={currency}>
                          {currency}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Advanced Pricing Strategy */}
                {formData.has_advanced_pricing && (
                  <div className='space-y-4'>
                    <div className='space-y-2'>
                      <Label htmlFor='pricing_strategy'>Pricing Strategy</Label>
                      <select
                        id='pricing_strategy'
                        value={formData.pricing_strategy}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            pricing_strategy: e.target.value as PricingStrategy,
                          })
                        }
                        className='flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50'
                      >
                        {pricingService.getAvailableStrategies().map((strategy) => (
                          <option key={strategy.value} value={strategy.value}>
                            {strategy.label}
                          </option>
                        ))}
                      </select>
                      <p className='text-sm text-gray-600'>
                        {
                          pricingService
                            .getAvailableStrategies()
                            .find((s) => s.value === formData.pricing_strategy)?.description
                        }
                      </p>
                    </div>

                    {formData.pricing_strategy !== 'fixed' && (
                      <div className='p-3 bg-blue-50 rounded-md'>
                        <p className='text-sm text-blue-800'>
                          <strong>Note:</strong> Advanced pricing features like tiered pricing,
                          time-based pricing, and distance-based pricing will be configured after
                          service creation in the pricing management section.
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>

              {/* Estimated Duration */}
              <div className='space-y-2'>
                <Label htmlFor='estimated_duration'>Estimated Duration (minutes) *</Label>
                <Input
                  id='estimated_duration'
                  type='number'
                  min='1'
                  value={formData.estimated_duration}
                  onChange={(e) =>
                    setFormData({ ...formData, estimated_duration: parseInt(e.target.value) || 60 })
                  }
                  required
                />
              </div>

              {/* Image URL */}
              <div className='space-y-2'>
                <Label htmlFor='image_url'>Image URL</Label>
                <div className='flex gap-2'>
                  <Input
                    id='image_url'
                    value={formData.image_url}
                    onChange={(e) => setFormData({ ...formData, image_url: e.target.value })}
                    placeholder='https://example.com/image.jpg'
                  />
                  <Button type='button' variant='outline' size='sm'>
                    <Upload className='w-4 h-4' />
                  </Button>
                </div>
                {formData.image_url && (
                  <img
                    src={formData.image_url}
                    alt='Preview'
                    className='w-32 h-32 object-cover rounded-lg'
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                    }}
                  />
                )}
              </div>

              {/* Active Status */}
              <div className='flex items-center space-x-2'>
                <Switch
                  id='is_active'
                  checked={formData.is_active}
                  onCheckedChange={(checked) => setFormData({ ...formData, is_active: checked })}
                />
                <Label htmlFor='is_active'>Service is active</Label>
              </div>
            </TabsContent>

            <TabsContent value='translations' className='space-y-6'>
              {renderTranslationInputs(nameTranslations, setNameTranslations, 'Service Name')}
              {renderTranslationInputs(
                descriptionTranslations,
                setDescriptionTranslations,
                'Description',
                true,
              )}
            </TabsContent>

            <TabsContent value='advanced' className='space-y-4'>
              <div className='space-y-2'>
                <Label>Metadata (JSON)</Label>
                <Textarea
                  value={JSON.stringify(formData.metadata, null, 2)}
                  onChange={(e) => {
                    try {
                      const parsed = JSON.parse(e.target.value);
                      setFormData({ ...formData, metadata: parsed });
                    } catch {
                      // Invalid JSON, keep the current metadata
                    }
                  }}
                  placeholder='{"key": "value"}'
                  rows={6}
                  className='font-mono text-sm'
                />
              </div>
            </TabsContent>
          </Tabs>

          {/* Actions */}
          <div className='flex gap-3 pt-6'>
            <Button type='submit' disabled={loading} className='flex-1'>
              {loading ? 'Saving...' : service ? 'Update Service' : 'Create Service'}
            </Button>
            <Button type='button' variant='outline' onClick={onCancel}>
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
