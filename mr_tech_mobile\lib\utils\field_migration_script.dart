import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// Field Migration Script
/// Ensures all Firestore documents have the required snake_case fields
/// Run this during Phase 2 of the migration process
class FieldMigrationScript {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Main migration method - migrates all collections
  static Future<void> migrateAllCollections() async {
    debugPrint('Starting field migration for all collections...');

    try {
      await migrateServiceRequests();
      await migrateUsers();
      await migrateChatMessages();
      await migrateServices();
      await migrateTechnicians();
      await migrateReviews();
      await migrateNotifications();

      debugPrint('✅ Field migration completed successfully!');
    } catch (e) {
      debugPrint('❌ Field migration failed: $e');
      rethrow;
    }
  }

  /// Migrate service_requests collection
  static Future<void> migrateServiceRequests() async {
    debugPrint('Migrating service_requests collection...');

    final collection = _firestore.collection('service_requests');
    final querySnapshot = await collection.get();

    final batch = _firestore.batch();
    int updateCount = 0;

    for (final doc in querySnapshot.docs) {
      final data = doc.data();
      final updates = <String, dynamic>{};

      // Migrate customer_id
      if (data['customer_id'] == null && data['customerId'] != null) {
        updates['customer_id'] = data['customerId'];
      }

      // Migrate service_id
      if (data['service_id'] == null && data['serviceId'] != null) {
        updates['service_id'] = data['serviceId'];
      }

      // Migrate technician_id
      if (data['technician_id'] == null && data['technicianId'] != null) {
        updates['technician_id'] = data['technicianId'];
      }

      // Migrate timestamps
      if (data['created_at'] == null && data['createdAt'] != null) {
        updates['created_at'] = data['createdAt'];
      }

      if (data['updated_at'] == null && data['updatedAt'] != null) {
        updates['updated_at'] = data['updatedAt'];
      }

      // Migrate boolean fields
      if (data['is_paid'] == null && data['isPaid'] != null) {
        updates['is_paid'] = data['isPaid'];
      }

      if (data['chat_active'] == null && data['chatActive'] != null) {
        updates['chat_active'] = data['chatActive'];
      }

      if (updates.isNotEmpty) {
        batch.update(doc.reference, updates);
        updateCount++;
      }
    }

    if (updateCount > 0) {
      await batch.commit();
      debugPrint('✅ Updated $updateCount service_requests documents');
    } else {
      debugPrint('✅ No service_requests documents needed migration');
    }
  }

  /// Migrate users collection
  static Future<void> migrateUsers() async {
    debugPrint('Migrating users collection...');

    final collection = _firestore.collection('users');
    final querySnapshot = await collection.get();

    final batch = _firestore.batch();
    int updateCount = 0;

    for (final doc in querySnapshot.docs) {
      final data = doc.data();
      final updates = <String, dynamic>{};

      // Migrate display_name
      if (data['display_name'] == null && data['displayName'] != null) {
        updates['display_name'] = data['displayName'];
      }

      // Migrate phone_number
      if (data['phone_number'] == null && data['phoneNumber'] != null) {
        updates['phone_number'] = data['phoneNumber'];
      }

      // Migrate photo_url
      if (data['photo_url'] == null) {
        final photoUrl = data['photoUrl'] ?? data['photoURL'];
        if (photoUrl != null) {
          updates['photo_url'] = photoUrl;
        }
      }

      // Migrate anydesk_id
      if (data['anydesk_id'] == null && data['anydesk_id'] != null) {
        updates['anydesk_id'] = data['anydesk_id'];
      }

      // Migrate boolean fields
      if (data['is_complete'] == null && data['isComplete'] != null) {
        updates['is_complete'] = data['isComplete'];
      }

      if (data['is_onboarded'] == null && data['isOnboarded'] != null) {
        updates['is_onboarded'] = data['isOnboarded'];
      }

      if (data['email_verified'] == null && data['emailVerified'] != null) {
        updates['email_verified'] = data['emailVerified'];
      }

      // Migrate timestamps
      if (data['created_at'] == null && data['createdAt'] != null) {
        updates['created_at'] = data['createdAt'];
      }

      if (data['updated_at'] == null && data['updatedAt'] != null) {
        updates['updated_at'] = data['updatedAt'];
      }

      if (updates.isNotEmpty) {
        batch.update(doc.reference, updates);
        updateCount++;
      }
    }

    if (updateCount > 0) {
      await batch.commit();
      debugPrint('✅ Updated $updateCount users documents');
    } else {
      debugPrint('✅ No users documents needed migration');
    }
  }

  /// Migrate chat_messages collection
  static Future<void> migrateChatMessages() async {
    debugPrint('Migrating chat_messages collection...');

    final collection = _firestore.collection('chat_messages');
    final querySnapshot = await collection.get();

    final batch = _firestore.batch();
    int updateCount = 0;

    for (final doc in querySnapshot.docs) {
      final data = doc.data();
      final updates = <String, dynamic>{};

      // Migrate request_id
      if (data['request_id'] == null && data['requestId'] != null) {
        updates['request_id'] = data['requestId'];
      }

      // Migrate sender_id
      if (data['sender_id'] == null && data['senderId'] != null) {
        updates['sender_id'] = data['senderId'];
      }

      // Migrate sender_type
      if (data['sender_type'] == null && data['senderType'] != null) {
        updates['sender_type'] = data['senderType'];
      }

      // Migrate message_type
      if (data['message_type'] == null && data['messageType'] != null) {
        updates['message_type'] = data['messageType'];
      }

      // Migrate file_url
      if (data['file_url'] == null && data['fileUrl'] != null) {
        updates['file_url'] = data['fileUrl'];
      }

      // Migrate created_at
      if (data['created_at'] == null && data['createdAt'] != null) {
        updates['created_at'] = data['createdAt'];
      }

      if (updates.isNotEmpty) {
        batch.update(doc.reference, updates);
        updateCount++;
      }
    }

    if (updateCount > 0) {
      await batch.commit();
      debugPrint('✅ Updated $updateCount chat_messages documents');
    } else {
      debugPrint('✅ No chat_messages documents needed migration');
    }
  }

  /// Migrate services collection
  static Future<void> migrateServices() async {
    debugPrint('Migrating services collection...');

    final collection = _firestore.collection('services');
    final querySnapshot = await collection.get();

    final batch = _firestore.batch();
    int updateCount = 0;

    for (final doc in querySnapshot.docs) {
      final data = doc.data();
      final updates = <String, dynamic>{};

      // Migrate base_price
      if (data['base_price'] == null && data['basePrice'] != null) {
        updates['base_price'] = data['basePrice'];
      }

      // Migrate image_url
      if (data['image_url'] == null && data['imageUrl'] != null) {
        updates['image_url'] = data['imageUrl'];
      }

      // Migrate estimated_duration
      if (data['estimated_duration'] == null &&
          data['estimatedDuration'] != null) {
        updates['estimated_duration'] = data['estimatedDuration'];
      }

      // Migrate timestamps
      if (data['created_at'] == null && data['createdAt'] != null) {
        updates['created_at'] = data['createdAt'];
      }

      if (data['updated_at'] == null && data['updatedAt'] != null) {
        updates['updated_at'] = data['updatedAt'];
      }

      if (updates.isNotEmpty) {
        batch.update(doc.reference, updates);
        updateCount++;
      }
    }

    if (updateCount > 0) {
      await batch.commit();
      debugPrint('✅ Updated $updateCount services documents');
    } else {
      debugPrint('✅ No services documents needed migration');
    }
  }

  /// Migrate technicians collection
  static Future<void> migrateTechnicians() async {
    debugPrint('Migrating technicians collection...');

    final collection = _firestore.collection('technicians');
    final querySnapshot = await collection.get();

    final batch = _firestore.batch();
    int updateCount = 0;

    for (final doc in querySnapshot.docs) {
      final data = doc.data();
      final updates = <String, dynamic>{};

      // Migrate photo_url
      if (data['photo_url'] == null && data['photoUrl'] != null) {
        updates['photo_url'] = data['photoUrl'];
      }

      // Migrate phone_number
      if (data['phone_number'] == null && data['phoneNumber'] != null) {
        updates['phone_number'] = data['phoneNumber'];
      }

      // Migrate completed_requests
      if (data['completed_requests'] == null &&
          data['completedRequests'] != null) {
        updates['completed_requests'] = data['completedRequests'];
      }

      // Migrate active_requests
      if (data['active_requests'] == null && data['activeRequests'] != null) {
        updates['active_requests'] = data['activeRequests'];
      }

      // Migrate is_available
      if (data['is_available'] == null && data['isAvailable'] != null) {
        updates['is_available'] = data['isAvailable'];
      }

      // Migrate timestamps
      if (data['created_at'] == null && data['createdAt'] != null) {
        updates['created_at'] = data['createdAt'];
      }

      if (data['updated_at'] == null && data['updatedAt'] != null) {
        updates['updated_at'] = data['updatedAt'];
      }

      if (updates.isNotEmpty) {
        batch.update(doc.reference, updates);
        updateCount++;
      }
    }

    if (updateCount > 0) {
      await batch.commit();
      debugPrint('✅ Updated $updateCount technicians documents');
    } else {
      debugPrint('✅ No technicians documents needed migration');
    }
  }

  /// Migrate reviews collection
  static Future<void> migrateReviews() async {
    debugPrint('Migrating reviews collection...');

    final collection = _firestore.collection('reviews');
    final querySnapshot = await collection.get();

    final batch = _firestore.batch();
    int updateCount = 0;

    for (final doc in querySnapshot.docs) {
      final data = doc.data();
      final updates = <String, dynamic>{};

      // Migrate customer_id
      if (data['customer_id'] == null && data['customerId'] != null) {
        updates['customer_id'] = data['customerId'];
      }

      // Migrate technician_id
      if (data['technician_id'] == null && data['technicianId'] != null) {
        updates['technician_id'] = data['technicianId'];
      }

      // Migrate service_id
      if (data['service_id'] == null && data['serviceId'] != null) {
        updates['service_id'] = data['serviceId'];
      }

      // Migrate created_at
      if (data['created_at'] == null && data['createdAt'] != null) {
        updates['created_at'] = data['createdAt'];
      }

      if (updates.isNotEmpty) {
        batch.update(doc.reference, updates);
        updateCount++;
      }
    }

    if (updateCount > 0) {
      await batch.commit();
      debugPrint('✅ Updated $updateCount reviews documents');
    } else {
      debugPrint('✅ No reviews documents needed migration');
    }
  }

  /// Migrate notifications collection
  static Future<void> migrateNotifications() async {
    debugPrint('Migrating notifications collection...');

    final collection = _firestore.collection('notifications');
    final querySnapshot = await collection.get();

    final batch = _firestore.batch();
    int updateCount = 0;

    for (final doc in querySnapshot.docs) {
      final data = doc.data();
      final updates = <String, dynamic>{};

      // Migrate user_id
      if (data['user_id'] == null && data['userId'] != null) {
        updates['user_id'] = data['userId'];
      }

      // Migrate created_at
      if (data['created_at'] == null && data['createdAt'] != null) {
        updates['created_at'] = data['createdAt'];
      }

      if (updates.isNotEmpty) {
        batch.update(doc.reference, updates);
        updateCount++;
      }
    }

    if (updateCount > 0) {
      await batch.commit();
      debugPrint('✅ Updated $updateCount notifications documents');
    } else {
      debugPrint('✅ No notifications documents needed migration');
    }
  }

  /// Validate migration completion
  static Future<bool> validateMigration() async {
    debugPrint('Validating migration completion...');

    try {
      // Check service_requests
      final requests =
          await _firestore.collection('service_requests').limit(10).get();
      for (final doc in requests.docs) {
        final data = doc.data();
        if (data['customer_id'] == null || data['created_at'] == null) {
          debugPrint('❌ service_requests migration incomplete: ${doc.id}');
          return false;
        }
      }

      // Check users
      final users = await _firestore.collection('users').limit(10).get();
      for (final doc in users.docs) {
        final data = doc.data();
        if (data['created_at'] == null) {
          debugPrint('❌ users migration incomplete: ${doc.id}');
          return false;
        }
      }

      debugPrint('✅ Migration validation passed');
      return true;
    } catch (e) {
      debugPrint('❌ Migration validation failed: $e');
      return false;
    }
  }
}
