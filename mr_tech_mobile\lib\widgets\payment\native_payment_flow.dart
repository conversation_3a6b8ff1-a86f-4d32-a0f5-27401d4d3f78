import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../text_styles.dart';
import 'native_card_input.dart';
import 'payment_method_selector.dart';

/// Native payment flow with step-by-step animations
class NativePaymentFlow extends StatefulWidget {
  final Map<String, dynamic> billingData;
  final double amount;
  final String serviceName;
  final Function(Map<String, String> cardData) onPaymentConfirm;
  final VoidCallback? onCancel;

  const NativePaymentFlow({
    super.key,
    required this.billingData,
    required this.amount,
    required this.serviceName,
    required this.onPaymentConfirm,
    this.onCancel,
  });

  @override
  State<NativePaymentFlow> createState() => _NativePaymentFlowState();
}

class _NativePaymentFlowState extends State<NativePaymentFlow>
    with TickerProviderStateMixin {
  late AnimationController _stepAnimationController;
  late AnimationController _loadingAnimationController;
  late AnimationController _successAnimationController;
  
  late Animation<double> _stepFadeAnimation;
  late Animation<Offset> _stepSlideAnimation;
  late Animation<double> _loadingRotationAnimation;
  late Animation<double> _successScaleAnimation;

  int _currentStep = 0;
  bool _isProcessing = false;
  bool _isCardDataValid = false;
  Map<String, String> _cardData = {};
  String _selectedPaymentMethod = 'card';

  final PageController _pageController = PageController();

  @override
  void initState() {
    super.initState();
    
    _stepAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _loadingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _successAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _stepFadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _stepAnimationController,
      curve: Curves.easeInOut,
    ));

    _stepSlideAnimation = Tween<Offset>(
      begin: const Offset(0.3, 0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _stepAnimationController,
      curve: Curves.easeOutCubic,
    ));

    _loadingRotationAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _loadingAnimationController,
      curve: Curves.linear,
    ));

    _successScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _successAnimationController,
      curve: Curves.elasticOut,
    ));

    _stepAnimationController.forward();
  }

  @override
  void dispose() {
    _stepAnimationController.dispose();
    _loadingAnimationController.dispose();
    _successAnimationController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  void _onCardDataChanged(Map<String, String> cardData) {
    setState(() {
      _cardData = cardData;
      _isCardDataValid = cardData['isValid'] == 'true';
    });
  }

  bool _canProceedFromCurrentStep() {
    switch (_currentStep) {
      case 0: // Payment method selection
        return _selectedPaymentMethod.isNotEmpty;
      case 1: // Card input
        return _isCardDataValid;
      case 2: // Payment summary
        return _isCardDataValid;
      default:
        return false;
    }
  }

  void _nextStep() {
    if (_currentStep < 2) {  // Only allow going to step 2 (payment summary)
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      _stepAnimationController.reset();
      _stepAnimationController.forward();
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      _stepAnimationController.reset();
      _stepAnimationController.forward();
    }
  }

  void _processPayment() async {
    if (!_isCardDataValid) return;

    setState(() {
      _isProcessing = true;
      _currentStep = 3; // Move to processing step
    });

    _loadingAnimationController.repeat();

    // Simulate payment processing delay
    await Future.delayed(const Duration(seconds: 2));

    _loadingAnimationController.stop();
    _successAnimationController.forward();

    await Future.delayed(const Duration(milliseconds: 500));

    widget.onPaymentConfirm(_cardData);
  }

  Widget _buildStepIndicator() {
    final colorScheme = Theme.of(context).colorScheme;
    final stepTitles = ['Method', 'Card', 'Review', 'Process'];
    
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
      child: Column(
        children: [
          Row(
            children: List.generate(4, (index) {
              final isActive = index <= _currentStep;
              final isCompleted = index < _currentStep;
              
              return Expanded(
                child: Row(
                  children: [
                    AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: isActive ? colorScheme.primary : Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Center(
                        child: isCompleted
                            ? Icon(Icons.check, color: Colors.white, size: 18)
                            : Text(
                                '${index + 1}',
                                style: AppTextStyles.bodyMedium(
                                  context,
                                  color: isActive ? Colors.white : Colors.grey.shade600,
                                ),
                              ),
                      ),
                    ),
                    if (index < 3)
                      Expanded(
                        child: Container(
                          height: 2,
                          margin: const EdgeInsets.symmetric(horizontal: 8),
                          decoration: BoxDecoration(
                            color: isCompleted ? colorScheme.primary : Colors.grey.shade300,
                            borderRadius: BorderRadius.circular(1),
                          ),
                        ),
                      ),
                  ],
                ),
              );
            }),
          ),
          const SizedBox(height: 8),
          Row(
            children: List.generate(4, (index) {
              final isActive = index <= _currentStep;
              
              return Expanded(
                child: Text(
                  stepTitles[index],
                  textAlign: TextAlign.center,
                  style: AppTextStyles.bodySmall(
                    context,
                    color: isActive ? colorScheme.primary : Colors.grey.shade600,
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodStep() {
    return FadeTransition(
      opacity: _stepFadeAnimation,
      child: SlideTransition(
        position: _stepSlideAnimation,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Choose Payment Method',
                    style: AppTextStyles.headingMedium(context),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Select your preferred payment method',
                    style: AppTextStyles.bodyMedium(
                      context,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: PaymentMethodSelector(
                selectedMethod: _selectedPaymentMethod,
                onMethodSelected: (method) {
                  setState(() {
                    _selectedPaymentMethod = method;
                  });
                },
                enabled: !_isProcessing,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCardInputStep() {
    return FadeTransition(
      opacity: _stepFadeAnimation,
      child: SlideTransition(
        position: _stepSlideAnimation,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Enter Card Details',
                    style: AppTextStyles.headingMedium(context),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Your payment information is secure and encrypted',
                    style: AppTextStyles.bodyMedium(
                      context,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: NativeCardInput(
                onCardDataChanged: _onCardDataChanged,
                enabled: !_isProcessing,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentSummaryStep() {
    final colorScheme = Theme.of(context).colorScheme;
    
    return FadeTransition(
      opacity: _stepFadeAnimation,
      child: SlideTransition(
        position: _stepSlideAnimation,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Review Payment',
                style: AppTextStyles.headingMedium(context),
              ),
              const SizedBox(height: 8),
              Text(
                'Please review your payment details before confirming',
                style: AppTextStyles.bodyMedium(
                  context,
                  color: Colors.grey.shade600,
                ),
              ),
              const SizedBox(height: 24),

              // Payment Summary Card
              Card(
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: BorderSide(color: Colors.grey.shade300),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      _buildSummaryRow('Service', widget.serviceName),
                      const SizedBox(height: 12),
                      _buildSummaryRow('Amount', 'EGP ${widget.amount.toStringAsFixed(0)}'),
                      const SizedBox(height: 12),
                      Divider(color: Colors.grey.shade300),
                      const SizedBox(height: 12),
                      _buildSummaryRow(
                        'Total',
                        'EGP ${widget.amount.toStringAsFixed(0)}',
                        isTotal: true,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 20),

              // Card Summary
              Card(
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: BorderSide(color: Colors.grey.shade300),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      Icon(
                        Icons.credit_card,
                        color: colorScheme.primary,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '•••• •••• •••• ${_cardData['cardNumber']?.substring(_cardData['cardNumber']!.length - 4) ?? ''}',
                              style: AppTextStyles.bodyMedium(context),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _cardData['holderName'] ?? '',
                              style: AppTextStyles.bodySmall(
                                context,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: colorScheme.primary.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          (_cardData['cardType'] ?? '').toUpperCase(),
                          style: AppTextStyles.bodySmall(
                            context,
                            color: colorScheme.primary,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProcessingStep() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        AnimatedBuilder(
          animation: _loadingRotationAnimation,
          builder: (context, child) {
            return Transform.rotate(
              angle: _loadingRotationAnimation.value,
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Theme.of(context).colorScheme.primary,
                    width: 3,
                  ),
                ),
                child: Center(
                  child: Icon(
                    Icons.credit_card,
                    color: Theme.of(context).colorScheme.primary,
                    size: 32,
                  ),
                ),
              ),
            );
          },
        ),
        const SizedBox(height: 24),
        Text(
          'Processing Payment',
          style: AppTextStyles.headingMedium(context),
        ),
        const SizedBox(height: 8),
        Text(
          'Please wait while we process your payment securely',
          style: AppTextStyles.bodyMedium(
            context,
            color: Colors.grey.shade600,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 32),
        LinearProgressIndicator(
          backgroundColor: Colors.grey.shade300,
          valueColor: AlwaysStoppedAnimation<Color>(
            Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isTotal = false}) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: AppTextStyles.bodyMedium(
            context,
            color: isTotal ? colorScheme.onSurface : Colors.grey.shade600,
          ),
        ),
        Text(
          value,
          style: isTotal
              ? AppTextStyles.headingSmall(context, color: colorScheme.primary)
              : AppTextStyles.bodyMedium(context),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return Scaffold(
      appBar: AppBar(
        title: Text('Secure Payment'),
        backgroundColor: colorScheme.surface,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.close),
          onPressed: widget.onCancel,
        ),
      ),
      body: Column(
        children: [
          if (!_isProcessing) _buildStepIndicator(),
          
          Expanded(
            child: _isProcessing
                ? _buildProcessingStep()
                : PageView(
                    controller: _pageController,
                    physics: const NeverScrollableScrollPhysics(),
                    children: [
                      _buildPaymentMethodStep(),
                      _buildCardInputStep(),
                      _buildPaymentSummaryStep(),
                    ],
                  ),
          ),
          
          // Bottom Actions
          if (!_isProcessing)
            SafeArea(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    if (_currentStep > 0)
                      Expanded(
                        child: OutlinedButton(
                          onPressed: _previousStep,
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: Text('Back'),
                        ),
                      ),
                    if (_currentStep > 0) const SizedBox(width: 16),
                    Expanded(
                      flex: _currentStep == 0 ? 1 : 2,
                      child: FilledButton(
                        onPressed: _currentStep < 2
                            ? (_canProceedFromCurrentStep() ? _nextStep : null)
                            : _currentStep == 2
                            ? _processPayment
                            : null,
                        style: FilledButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          backgroundColor: colorScheme.primary,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(
                          _currentStep == 0 
                              ? 'Continue' 
                              : _currentStep == 1
                              ? 'Continue'
                              : _currentStep == 2
                              ? 'Pay Now'
                              : 'Continue',
                          style: AppTextStyles.buttonMedium(
                            context,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}