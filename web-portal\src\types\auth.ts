// User roles matching the mobile app structure
export type UserRole = 'admin' | 'technician' | 'customer';

// User model based on the mobile app structure
export interface User {
  uid: string;
  email: string;
  name: string;
  role: UserRole;
  phone?: string;
  photo_url?: string;
  created_at?: Date;
  updated_at?: Date;
  is_active?: boolean;
  last_login?: Date;
  fcm_token?: string;
  device_type?: 'web' | 'ios' | 'android';
}

// Auth state interface
export interface AuthState {
  user: User | null;
  loading: boolean;
  error: string | null;
}

// Login credentials
export interface LoginCredentials {
  email: string;
  password: string;
}

// Signup data
export interface SignupData extends LoginCredentials {
  name: string;
  phone?: string;
  role?: UserRole;
  admin_level?: 'super' | 'standard' | 'limited';
  permissions?: string[];
  specialties?: string[];
}

// Firebase Auth error codes
export const AUTH_ERROR_CODES = {
  'auth/user-not-found': 'No user found with this email address.',
  'auth/wrong-password': 'Invalid password.',
  'auth/invalid-email': 'Invalid email address.',
  'auth/email-already-in-use': 'This email is already registered.',
  'auth/weak-password': 'Password should be at least 6 characters.',
  'auth/network-request-failed': 'Network error. Please check your connection.',
  'auth/too-many-requests': 'Too many failed attempts. Please try again later.',
  'auth/user-disabled': 'This account has been disabled.',
} as const;
