#!/usr/bin/env node

/**
 * Test script to verify that the notification loop fix is working
 * This script simulates the notification flow and checks for loops
 */

const admin = require('firebase-admin');
const path = require('path');

// Initialize Firebase Admin SDK
const serviceAccountPath = path.join(__dirname, '..', 'firebase-config', 'service-account-key.json');

try {
  const serviceAccount = require(serviceAccountPath);
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    databaseURL: 'https://stopnow-be6b7-default-rtdb.firebaseio.com'
  });
  console.log('✅ Firebase Admin SDK initialized successfully');
} catch (error) {
  console.error('❌ Error initializing Firebase Admin SDK:', error.message);
  process.exit(1);
}

const db = admin.firestore();

async function testNotificationLoopFix() {
  console.log('\n🧪 Testing Notification Loop Fix...\n');

  const testRequestId = 'test-request-' + Date.now();
  const testUserId = 'test-user-' + Date.now();

  try {
    // Step 1: Create a test service request
    console.log('1️⃣ Creating test service request...');
    await db.collection('service_requests').doc(testRequestId).set({
      customer_id: testUserId,
      customer_fcm_token: 'test-fcm-token-' + Date.now(),
      customerFcmToken: 'test-fcm-token-' + Date.now(),
      status: 'pending',
      service_name: 'Test Service',
      chat_active: false,
      chatActive: false,
      created_at: admin.firestore.FieldValue.serverTimestamp(),
      updated_at: admin.firestore.FieldValue.serverTimestamp()
    });
    console.log('✅ Test service request created');

    // Step 2: Create multiple chat activation notifications rapidly
    console.log('\n2️⃣ Creating multiple chat activation notifications...');
    const notifications = [];
    const baseTimestamp = Date.now();

    for (let i = 0; i < 5; i++) {
      const notificationData = {
        type: 'chat_activation',
        title: 'Chat Started',
        body: 'Technician has started a chat for your service request',
        request_id: testRequestId,
        user_id: testUserId,
        recipient_id: testUserId,
        timestamp: baseTimestamp + (i * 1000), // 1 second apart
        delivered: false,
        processed: false
      };

      const notificationRef = await db.collection('notifications').add(notificationData);
      notifications.push(notificationRef.id);
      console.log(`📨 Created notification ${i + 1}: ${notificationRef.id}`);
    }

    // Step 3: Wait for Cloud Functions to process
    console.log('\n3️⃣ Waiting for Cloud Functions to process notifications...');
    await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds

    // Step 4: Check results
    console.log('\n4️⃣ Checking results...');
    let deliveredCount = 0;
    let skippedCount = 0;

    for (const notificationId of notifications) {
      const notificationDoc = await db.collection('notifications').doc(notificationId).get();
      if (notificationDoc.exists) {
        const data = notificationDoc.data();
        if (data.delivered) {
          deliveredCount++;
        }
        if (data.skipped_duplicate) {
          skippedCount++;
        }
        console.log(`📋 Notification ${notificationId}: delivered=${data.delivered}, skipped=${data.skipped_duplicate || false}`);
      }
    }

    // Step 5: Analyze results
    console.log('\n5️⃣ Analysis:');
    console.log(`📊 Total notifications created: ${notifications.length}`);
    console.log(`✅ Notifications delivered: ${deliveredCount}`);
    console.log(`⏭️  Notifications skipped (duplicates): ${skippedCount}`);

    if (deliveredCount === 1 && skippedCount >= 1) {
      console.log('🎉 SUCCESS: Loop prevention is working! Only 1 notification delivered, others skipped as duplicates.');
    } else if (deliveredCount > 1) {
      console.log('⚠️  WARNING: Multiple notifications were delivered. Loop prevention may not be working correctly.');
    } else {
      console.log('❓ UNCLEAR: No notifications were delivered. Check Cloud Function logs.');
    }

    // Step 6: Cleanup
    console.log('\n6️⃣ Cleaning up test data...');
    await db.collection('service_requests').doc(testRequestId).delete();
    for (const notificationId of notifications) {
      await db.collection('notifications').doc(notificationId).delete();
    }
    console.log('🧹 Cleanup completed');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testNotificationLoopFix().then(() => {
  console.log('\n✨ Test completed');
  process.exit(0);
}).catch(error => {
  console.error('💥 Test crashed:', error);
  process.exit(1);
});
