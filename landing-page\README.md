# Mr.Tech Landing Page

A world-class, award-winning landing page design for the Mr.Tech mobile app. The landing page showcases the app's features, services, and benefits with sophisticated typography and luxurious visual effects.

## Features

- Responsive design that works on all devices
- Modern, clean layout with elegant animations
- Optimized performance and accessibility
- Mobile-first approach
- Sophisticated typography using Playfair Display and Montserrat fonts
- Interactive elements (FAQ accordion, testimonial slider, etc.)
- PHP contact form backend

## File Structure

```
landing-page/
├── css/
│   └── styles.css       # Main stylesheet
├── js/
│   └── main.js          # JavaScript functionality
├── php/
│   └── contact.php      # Contact form processor
├── images/              # Image assets
├── fonts/               # Custom font files (if not using Google Fonts)
└── index.html           # Main HTML file
```

## Deployment Instructions for Shared Hosting (vilartech.com)

### Prerequisites

- FTP access to your shared hosting
- PHP support (already available on vilartech.com)
- Domain or subdomain configured

### Deployment Steps

1. **Prepare the files**:
   - Make sure all files are ready for deployment
   - Update any configuration settings like email addresses in the PHP files

2. **Connect to the server**:
   - Use an FTP client (like FileZilla, WinSCP, or CyberDuck)
   - Connect to vilartech.com using your FTP credentials
   - Navigate to the public HTML directory (typically public_html, www, or htdocs)

3. **Upload the files**:
   - Upload all files and directories from the landing-page folder
   - Maintain the same directory structure as in the project

4. **Set permissions**:
   - Set 755 (drwxr-xr-x) permissions for directories
   - Set 644 (rw-r--r--) permissions for files
   - Set 755 (rwxr-xr-x) permissions for PHP files if needed

5. **Test the deployment**:
   - Visit your website URL to ensure everything loads correctly
   - Test all interactive features (navigation, slider, FAQ, etc.)
   - Submit a test contact form to verify PHP processing

### Troubleshooting

- If images aren't loading, check file paths and permissions
- If the contact form isn't working, verify PHP is enabled and check error logs
- For styling issues, clear your browser cache and reload

## Customization

### Changing Colors

The color scheme is defined using CSS variables at the top of the `styles.css` file:

```css
:root {
    --color-primary: #3a6df0;
    --color-primary-dark: #2a5cd9;
    --color-primary-light: #6690f4;
    --color-secondary: #6c5ce7;
    /* more color variables... */
}
```

Simply update these values to change the color scheme throughout the site.

### Updating Content

The content is organized in clear sections within the `index.html` file. Each section has comments to help you identify and update the content.

### Adding New Images

1. Add your image files to the `images/` directory
2. Update the HTML to reference the new images
3. Ensure images are optimized for web (compressed, appropriate dimensions)

## Performance Optimization

The landing page is already optimized for performance, but here are some additional tips:

1. Compress all images further if needed
2. Consider lazy-loading images that are below the fold
3. Minify CSS and JavaScript files for production
4. Enable gzip compression on your server

## Support

For any questions or support needs, please contact your developer <NAME_EMAIL>.

---

Happy deployment! 🚀 