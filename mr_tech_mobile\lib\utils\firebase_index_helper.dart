import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// Helper class to manage Firebase index issues
class FirebaseIndexHelper {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Extract the index URL from an error message
  static String? extractIndexUrl(String errorMessage) {
    final RegExp urlRegex = RegExp(
      r'https://console\.firebase\.google\.com/[^\s]+',
    );
    final match = urlRegex.firstMatch(errorMessage);
    return match?.group(0);
  }

  /// Check if a notifications index exists, returns fallback query if it doesn't
  static Future<Query<Map<String, dynamic>>> getNotificationsQuery({
    required String userId,
    int limit = 20,
  }) async {
    // First try the optimal query with compound index
    final optimalQuery = _firestore
        .collection('notifications')
        .where('user_id', isEqualTo: userId)
        .orderBy('createdAt', descending: true)
        .limit(limit);

    try {
      // This will throw if the index doesn't exist
      await optimalQuery.count().get();
      debugPrint('Using optimal notifications query with index');
      return optimalQuery;
    } catch (e) {
      // If error mentions missing index, use fallback
      final errorMessage = e.toString();
      if (errorMessage.contains('index') ||
          errorMessage.contains('requires an index')) {
        debugPrint('Index missing for notifications, using fallback query');

        // Extract and log the URL for creating the index
        final indexUrl = extractIndexUrl(errorMessage);
        if (indexUrl != null) {
          debugPrint('Create the required index here: $indexUrl');
        }

        // Return a simple query without ordering
        return _firestore
            .collection('notifications')
            .where('user_id', isEqualTo: userId)
            .limit(limit);
      }

      // If it's a different error, re-throw
      debugPrint('Error getting notifications query: $e');
      rethrow;
    }
  }

  /// Get a properly configured messages query that won't fail
  static Query<Map<String, dynamic>> getMessagesQuery(String requestId) {
    return _firestore
        .collection('service_requests')
        .doc(requestId)
        .collection('messages')
        .orderBy('created_at', descending: false);
  }
}
