import {
  Timestamp,
  addDoc,
  collection,
  doc,
  getDocs,
  limit,
  orderBy,
  query,
  serverTimestamp,
  where,
} from 'firebase/firestore';
import { db } from '../config/firebase';
import type { UserActivity, UserAuditLog } from '../types/user';

// Activity types for better categorization
export enum ActivityType {
  // Authentication
  LOGIN = 'login',
  LOGOUT = 'logout',
  PASSWORD_CHANGE = 'password_change',

  // Profile Management
  PROFILE_UPDATE = 'profile_update',
  PHOTO_UPLOAD = 'photo_upload',

  // User Management (Admin actions)
  USER_CREATED = 'user_created',
  USER_UPDATED = 'user_updated',
  USER_DELETED = 'user_deleted',
  USER_SUSPENDED = 'user_suspended',
  USER_ACTIVATED = 'user_activated',
  ROLE_CHANGED = 'role_changed',

  // Service Requests
  REQUEST_CREATED = 'request_created',
  REQUEST_UPDATED = 'request_updated',
  REQUEST_ASSIGNED = 'request_assigned',
  REQUEST_COMPLETED = 'request_completed',

  // Chat Activities
  MESSAGE_SENT = 'message_sent',
  CHAT_STARTED = 'chat_started',
  CHAT_ENDED = 'chat_ended',

  // System Activities
  SETTINGS_CHANGED = 'settings_changed',
  BACKUP_CREATED = 'backup_created',
  DATA_EXPORTED = 'data_exported',

  // Security Events
  FAILED_LOGIN = 'failed_login',
  ACCOUNT_LOCKED = 'account_locked',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
}

// Audit action types
export enum AuditAction {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  SUSPEND = 'suspend',
  ACTIVATE = 'activate',
  ROLE_CHANGE = 'role_change',
  PERMISSION_CHANGE = 'permission_change',
  BULK_UPDATE = 'bulk_update',
}

interface ActivityContext {
  ip_address?: string;
  user_agent?: string;
  device_type?: 'web' | 'mobile' | 'api';
  location?: string;
  session_id?: string;
  request_id?: string;
  additional_data?: Record<string, any>;
}

class ActivityTrackingService {
  private activityCollection = 'user_activities';
  private auditCollection = 'user_audit_logs';

  // Track user activity
  async trackActivity(
    userId: string,
    activityType: ActivityType,
    details?: string,
    context?: ActivityContext,
  ): Promise<void> {
    try {
      const activityData: Omit<UserActivity, 'id'> = {
        user_id: userId,
        userId,
        action: activityType,
        details,
        ip_address: context?.ip_address,
        ipAddress: context?.ip_address,
        user_agent: context?.user_agent,
        userAgent: context?.user_agent,
        created_at: serverTimestamp(),
        createdAt: serverTimestamp(),
      };

      // Add additional context data
      if (context?.additional_data) {
        Object.assign(activityData, context.additional_data);
      }

      await addDoc(collection(db, this.activityCollection), activityData);
    } catch (error) {
      console.error('Error tracking activity:', error);
      // Don't throw error for activity tracking failures
    }
  }

  // Log audit trail for admin actions
  async logAudit(
    userId: string,
    adminId: string,
    action: AuditAction,
    oldValues?: any,
    newValues?: any,
    reason?: string,
    context?: ActivityContext,
  ): Promise<void> {
    try {
      const auditData: Omit<UserAuditLog, 'id'> = {
        user_id: userId,
        userId,
        admin_id: adminId,
        adminId,
        action,
        old_values: oldValues,
        oldValues,
        new_values: newValues,
        newValues,
        reason,
        created_at: serverTimestamp(),
        createdAt: serverTimestamp(),
      };

      // Add context information
      if (context) {
        Object.assign(auditData, {
          ip_address: context.ip_address,
          user_agent: context.user_agent,
          device_type: context.device_type,
          session_id: context.session_id,
        });
      }

      await addDoc(collection(db, this.auditCollection), auditData);
    } catch (error) {
      console.error('Error logging audit:', error);
      // Don't throw error for audit logging failures
    }
  }

  // Get user activities with pagination
  async getUserActivities(
    userId: string,
    limitCount = 50,
    activityType?: ActivityType,
  ): Promise<UserActivity[]> {
    try {
      const constraints = [
        where('user_id', '==', userId),
        orderBy('created_at', 'desc'),
        limit(limitCount),
      ];

      if (activityType) {
        constraints.splice(1, 0, where('action', '==', activityType));
      }

      const q = query(collection(db, this.activityCollection), ...constraints);
      const querySnapshot = await getDocs(q);

      return querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as UserActivity[];
    } catch (error) {
      console.error('Error getting user activities:', error);
      throw error;
    }
  }

  // Get audit logs for a user
  async getUserAuditLogs(
    userId: string,
    limitCount = 50,
    action?: AuditAction,
  ): Promise<UserAuditLog[]> {
    try {
      const constraints = [
        where('user_id', '==', userId),
        orderBy('created_at', 'desc'),
        limit(limitCount),
      ];

      if (action) {
        constraints.splice(1, 0, where('action', '==', action));
      }

      const q = query(collection(db, this.auditCollection), ...constraints);
      const querySnapshot = await getDocs(q);

      return querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as UserAuditLog[];
    } catch (error) {
      console.error('Error getting user audit logs:', error);
      throw error;
    }
  }

  // Get all audit logs (admin only)
  async getAllAuditLogs(
    limitCount = 100,
    adminId?: string,
    action?: AuditAction,
  ): Promise<UserAuditLog[]> {
    try {
      const constraints = [orderBy('created_at', 'desc'), limit(limitCount)];

      if (adminId) {
        constraints.unshift(where('admin_id', '==', adminId));
      }

      if (action) {
        constraints.unshift(where('action', '==', action));
      }

      const q = query(collection(db, this.auditCollection), ...constraints);
      const querySnapshot = await getDocs(q);

      return querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as UserAuditLog[];
    } catch (error) {
      console.error('Error getting all audit logs:', error);
      throw error;
    }
  }

  // Get recent activities across all users (admin dashboard)
  async getRecentActivities(limitCount = 50, activityType?: ActivityType): Promise<UserActivity[]> {
    try {
      const constraints = [orderBy('created_at', 'desc'), limit(limitCount)];

      if (activityType) {
        constraints.unshift(where('action', '==', activityType));
      }

      const q = query(collection(db, this.activityCollection), ...constraints);
      const querySnapshot = await getDocs(q);

      return querySnapshot.docs.map((doc) => ({
        id: doc.id,
        ...doc.data(),
      })) as UserActivity[];
    } catch (error) {
      console.error('Error getting recent activities:', error);
      throw error;
    }
  }

  // Get activity statistics
  async getActivityStats(
    userId?: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<{
    totalActivities: number;
    activitiesByType: Record<string, number>;
    activitiesByDay: Record<string, number>;
    mostActiveHours: Record<string, number>;
  }> {
    try {
      const constraints = [orderBy('created_at', 'desc')];

      if (userId) {
        constraints.unshift(where('user_id', '==', userId));
      }

      // Note: Firestore doesn't support range queries with multiple fields easily
      // For production, consider using Cloud Functions for complex analytics

      const q = query(collection(db, this.activityCollection), ...constraints);
      const querySnapshot = await getDocs(q);

      const activities = querySnapshot.docs.map((doc) => doc.data());

      // Process statistics
      const stats = {
        totalActivities: activities.length,
        activitiesByType: {} as Record<string, number>,
        activitiesByDay: {} as Record<string, number>,
        mostActiveHours: {} as Record<string, number>,
      };

      activities.forEach((activity) => {
        // Count by type
        const type = activity.action || 'unknown';
        stats.activitiesByType[type] = (stats.activitiesByType[type] || 0) + 1;

        // Count by day
        if (activity.created_at && activity.created_at.toDate) {
          const date = activity.created_at.toDate();
          const day = date.toISOString().split('T')[0];
          stats.activitiesByDay[day] = (stats.activitiesByDay[day] || 0) + 1;

          // Count by hour
          const hour = date.getHours().toString();
          stats.mostActiveHours[hour] = (stats.mostActiveHours[hour] || 0) + 1;
        }
      });

      return stats;
    } catch (error) {
      console.error('Error getting activity stats:', error);
      throw error;
    }
  }

  // Track login activity
  async trackLogin(userId: string, context?: ActivityContext): Promise<void> {
    await this.trackActivity(userId, ActivityType.LOGIN, 'User logged in', context);
  }

  // Track logout activity
  async trackLogout(userId: string, context?: ActivityContext): Promise<void> {
    await this.trackActivity(userId, ActivityType.LOGOUT, 'User logged out', context);
  }

  // Track failed login attempt
  async trackFailedLogin(email: string, reason: string, context?: ActivityContext): Promise<void> {
    await this.trackActivity(
      'anonymous',
      ActivityType.FAILED_LOGIN,
      `Failed login attempt for ${email}: ${reason}`,
      context,
    );
  }

  // Track profile update
  async trackProfileUpdate(
    userId: string,
    updatedFields: string[],
    context?: ActivityContext,
  ): Promise<void> {
    await this.trackActivity(
      userId,
      ActivityType.PROFILE_UPDATE,
      `Updated fields: ${updatedFields.join(', ')}`,
      context,
    );
  }

  // Track user management actions
  async trackUserManagement(
    adminId: string,
    targetUserId: string,
    action: ActivityType,
    details?: string,
    context?: ActivityContext,
  ): Promise<void> {
    // Track activity for both admin and target user
    await this.trackActivity(adminId, action, details, context);

    if (targetUserId !== adminId) {
      await this.trackActivity(
        targetUserId,
        action,
        `Action performed by admin: ${details}`,
        context,
      );
    }
  }

  // Get context from request (for web applications)
  getContextFromRequest(req?: any): ActivityContext {
    if (!req) return {};

    return {
      ip_address: req.ip || req.connection?.remoteAddress,
      user_agent: req.get?.('User-Agent'),
      device_type: 'web',
    };
  }

  // Get context from browser (for client-side tracking)
  getContextFromBrowser(): ActivityContext {
    if (typeof window === 'undefined') return {};

    return {
      user_agent: navigator.userAgent,
      device_type: 'web',
    };
  }
}

// Export singleton instance
export default new ActivityTrackingService();
