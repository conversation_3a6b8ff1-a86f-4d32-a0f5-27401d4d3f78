const admin = require('firebase-admin');

// Initialize Firebase Admin with explicit project
async function initializeFirebase() {
  try {
    admin.initializeApp({
      projectId: 'stopnow-be6b7'
    });
    console.log('✅ Firebase Admin initialized for project: stopnow-be6b7');
  } catch (error) {
    console.error('❌ Failed to initialize Firebase:', error.message);
    console.log('Please make sure you are logged in: firebase login');
    process.exit(1);
  }
}

async function migrateServices() {
  console.log('🚀 MIGRATING SERVICES COLLECTION ONLY');
  console.log('═'.repeat(50));
  
  await initializeFirebase();
  
  const db = admin.firestore();
  const servicesRef = db.collection('services');
  
  try {
    console.log('📋 Fetching services...');
    const snapshot = await servicesRef.get();
    
    if (snapshot.empty) {
      console.log('⚠️ No services found in database');
      return;
    }
    
    console.log(`📊 Found ${snapshot.docs.length} services`);
    
    let migrated = 0;
    let alreadyMigrated = 0;
    let errors = 0;
    
    for (const doc of snapshot.docs) {
      try {
        const data = doc.data();
        const updates = {};
        let needsUpdate = false;
        
        // Migrate basePrice -> base_price
        if (data.basePrice !== undefined && data.base_price === undefined) {
          updates.base_price = data.basePrice;
          needsUpdate = true;
        }
        
        // Migrate isActive -> is_active  
        if (data.isActive !== undefined && data.is_active === undefined) {
          updates.is_active = data.isActive;
          needsUpdate = true;
        }
        
        // Migrate estimatedDuration -> estimated_duration
        if (data.estimatedDuration !== undefined && data.estimated_duration === undefined) {
          updates.estimated_duration = data.estimatedDuration;
          needsUpdate = true;
        }
        
        // Migrate imageUrl -> image_url (if exists)
        if (data.imageUrl !== undefined && data.image_url === undefined) {
          updates.image_url = data.imageUrl;
          needsUpdate = true;
        }
        
        // Add timestamps if missing
        if (data.created_at === undefined && data.createdAt === undefined) {
          updates.created_at = admin.firestore.FieldValue.serverTimestamp();
          needsUpdate = true;
        }
        
        if (needsUpdate) {
          await doc.ref.update(updates);
          migrated++;
          
          const serviceTitle = data.title?.en || data.title || 'Unknown Service';
          const fieldList = Object.keys(updates).join(', ');
          console.log(`✅ ${serviceTitle}: Added ${fieldList}`);
        } else {
          alreadyMigrated++;
          const serviceTitle = data.title?.en || data.title || 'Unknown Service';
          console.log(`⏭️ ${serviceTitle}: Already migrated`);
        }
        
      } catch (error) {
        errors++;
        console.log(`❌ Error migrating service: ${error.message}`);
      }
    }
    
    console.log('\n📊 SERVICES MIGRATION SUMMARY:');
    console.log('═'.repeat(50));
    console.log(`   - Total services: ${snapshot.docs.length}`);
    console.log(`   - Migrated: ${migrated}`);
    console.log(`   - Already migrated: ${alreadyMigrated}`);
    console.log(`   - Errors: ${errors}`);
    
    if (errors === 0) {
      console.log('\n🎉 SERVICES MIGRATION COMPLETED SUCCESSFULLY!');
      console.log('✅ All services now have snake_case fields');
      console.log('✅ Original camelCase fields preserved');
      console.log('✅ ServiceModel will prefer snake_case with camelCase fallback');
      console.log('');
      console.log('🧪 TEST YOUR APP:');
      console.log('1. Run: flutter run');
      console.log('2. Go to Services screen');
      console.log('3. Verify all services load normally');
      console.log('4. Check debug console for field usage');
    }
    
  } catch (error) {
    console.error('💥 Migration failed:', error.message);
    
    if (error.message.includes('credentials') || error.message.includes('authentication')) {
      console.log('\n🔑 AUTHENTICATION ISSUE:');
      console.log('Run: firebase login');
      console.log('Then try again: node migrate_services_only.js');
    }
  }
}

// Run the migration
migrateServices()
  .then(() => {
    console.log('\n🏁 Script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Script failed:', error.message);
    process.exit(1);
  }); 