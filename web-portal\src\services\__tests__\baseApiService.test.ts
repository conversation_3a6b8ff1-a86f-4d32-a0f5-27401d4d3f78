import { beforeEach, describe, expect, it, vi } from 'vitest';
import { BaseApiService } from '../baseApiService';
import { ErrorCode } from '../../types/enums';
import type { ServiceResponse } from '../../types/common';

// Create a concrete implementation for testing
class TestApiService extends BaseApiService {
  constructor() {
    super({ serviceName: 'TestService' });
  }

  // Expose protected methods for testing
  public testCreateError = this.createError.bind(this);
  public testCreateResponse = this.createResponse.bind(this);
  public testLog = this.log.bind(this);
  public testExecuteWithRetry = this.executeWithRetry.bind(this);
  public testIsRetryableError = this.isRetryableError.bind(this);
  public testCalculateDelay = this.calculateDelay.bind(this);
  public testValidateRequired = this.validateRequired.bind(this);
  public testValidateTypes = this.validateTypes.bind(this);
  public testSanitizeInput = this.sanitizeInput.bind(this);
  public testHandleApiResponse = this.handleApiResponse.bind(this);
  public testExecuteWithTimeout = this.executeWithTimeout.bind(this);
}

describe('BaseApiService', () => {
  let service: TestApiService;

  beforeEach(() => {
    service = new TestApiService();
    vi.clearAllMocks();
  });

  describe('createError', () => {
    it('should create a standardized error with all required fields', () => {
      const error = service.testCreateError(
        ErrorCode.VALIDATION_REQUIRED_FIELD,
        'Test error message',
        { field: 'email' }
      );

      expect(error).toEqual({
        code: ErrorCode.VALIDATION_REQUIRED_FIELD,
        message: 'Test error message',
        details: { field: 'email' },
        timestamp: expect.any(Date),
        context: 'TestService',
      });
    });

    it('should create error without details', () => {
      const error = service.testCreateError(
        ErrorCode.NETWORK_SERVER_ERROR,
        'Server error'
      );

      expect(error).toEqual({
        code: ErrorCode.NETWORK_SERVER_ERROR,
        message: 'Server error',
        details: undefined,
        timestamp: expect.any(Date),
        context: 'TestService',
      });
    });
  });

  describe('createResponse', () => {
    it('should create successful response with data', () => {
      const data = { id: '1', name: 'Test' };
      const response = service.testCreateResponse(true, data);

      expect(response).toEqual({
        success: true,
        data,
        error: undefined,
        metadata: {
          service: 'TestService',
          timestamp: expect.any(String),
        },
      });
    });

    it('should create error response', () => {
      const error = service.testCreateError(ErrorCode.NETWORK_SERVER_ERROR, 'Test error');
      const response = service.testCreateResponse(false, undefined, error);

      expect(response).toEqual({
        success: false,
        data: undefined,
        error,
        metadata: {
          service: 'TestService',
          timestamp: expect.any(String),
        },
      });
    });
  });

  describe('executeWithRetry', () => {
    it('should execute operation successfully on first attempt', async () => {
      const operation = vi.fn().mockResolvedValue('success');
      
      const result = await service.testExecuteWithRetry(operation, 'testOperation');
      
      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(1);
    });

    it('should retry on retryable errors', async () => {
      const operation = vi.fn()
        .mockRejectedValueOnce(new Error('network timeout'))
        .mockResolvedValue('success');
      
      const result = await service.testExecuteWithRetry(operation, 'testOperation');
      
      expect(result).toBe('success');
      expect(operation).toHaveBeenCalledTimes(2);
    });

    it('should not retry on non-retryable errors', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('unauthorized'));
      
      await expect(
        service.testExecuteWithRetry(operation, 'testOperation')
      ).rejects.toThrow('unauthorized');
      
      expect(operation).toHaveBeenCalledTimes(1);
    });

    it('should fail after max retries', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('network timeout'));
      
      await expect(
        service.testExecuteWithRetry(operation, 'testOperation')
      ).rejects.toThrow('network timeout');
      
      expect(operation).toHaveBeenCalledTimes(3); // Default max retries
    });
  });

  describe('isRetryableError', () => {
    it('should identify retryable errors', () => {
      const retryableErrors = [
        new Error('network timeout'),
        new Error('connection failed'),
        new Error('server error 503'),
        new Error('rate limit exceeded'),
        new Error('temporary failure'),
      ];

      retryableErrors.forEach(error => {
        expect(service.testIsRetryableError(error)).toBe(true);
      });
    });

    it('should identify non-retryable errors', () => {
      const nonRetryableErrors = [
        new Error('unauthorized 401'),
        new Error('forbidden 403'),
        new Error('not found 404'),
        new Error('bad request 400'),
        new Error('validation failed'),
      ];

      nonRetryableErrors.forEach(error => {
        expect(service.testIsRetryableError(error)).toBe(false);
      });
    });
  });

  describe('calculateDelay', () => {
    it('should calculate exponential backoff delay', () => {
      const config = {
        maxAttempts: 3,
        baseDelay: 1000,
        maxDelay: 5000,
        exponentialBackoff: true,
      };

      const delay1 = service.testCalculateDelay(1, config);
      const delay2 = service.testCalculateDelay(2, config);
      const delay3 = service.testCalculateDelay(3, config);

      expect(delay1).toBeGreaterThanOrEqual(1000);
      expect(delay1).toBeLessThanOrEqual(1100); // With jitter
      expect(delay2).toBeGreaterThanOrEqual(2000);
      expect(delay2).toBeLessThanOrEqual(2200);
      expect(delay3).toBeGreaterThanOrEqual(4000);
      expect(delay3).toBeLessThanOrEqual(4400);
    });

    it('should use base delay when exponential backoff is disabled', () => {
      const config = {
        maxAttempts: 3,
        baseDelay: 1000,
        maxDelay: 5000,
        exponentialBackoff: false,
      };

      const delay1 = service.testCalculateDelay(1, config);
      const delay2 = service.testCalculateDelay(2, config);

      expect(delay1).toBe(1000);
      expect(delay2).toBe(1000);
    });

    it('should respect max delay', () => {
      const config = {
        maxAttempts: 5,
        baseDelay: 1000,
        maxDelay: 3000,
        exponentialBackoff: true,
      };

      const delay5 = service.testCalculateDelay(5, config);
      expect(delay5).toBeLessThanOrEqual(3000);
    });
  });

  describe('validateRequired', () => {
    it('should return null for valid parameters', () => {
      const params = { email: '<EMAIL>', name: 'Test User' };
      const result = service.testValidateRequired(params, ['email', 'name']);
      
      expect(result).toBeNull();
    });

    it('should return error for missing required fields', () => {
      const params = { email: '<EMAIL>' };
      const result = service.testValidateRequired(params, ['email', 'name']);
      
      expect(result).toEqual({
        code: ErrorCode.VALIDATION_REQUIRED_FIELD,
        message: 'Missing required fields: name',
        details: {
          missingFields: ['name'],
          providedFields: ['email'],
        },
        timestamp: expect.any(Date),
        context: 'TestService',
      });
    });

    it('should return error for empty string values', () => {
      const params = { email: '', name: 'Test User' };
      const result = service.testValidateRequired(params, ['email', 'name']);
      
      expect(result?.details?.missingFields).toContain('email');
    });
  });

  describe('validateTypes', () => {
    it('should return null for valid types', () => {
      const params = { email: '<EMAIL>', age: 25 };
      const validators = {
        email: (v: unknown): v is string => typeof v === 'string',
        age: (v: unknown): v is number => typeof v === 'number',
      };
      
      const result = service.testValidateTypes(params, validators);
      expect(result).toBeNull();
    });

    it('should return error for invalid types', () => {
      const params = { email: 123, age: '25' };
      const validators = {
        email: (v: unknown): v is string => typeof v === 'string',
        age: (v: unknown): v is number => typeof v === 'number',
      };
      
      const result = service.testValidateTypes(params, validators);
      expect(result?.details?.invalidFields).toEqual(['email', 'age']);
    });
  });

  describe('sanitizeInput', () => {
    it('should only include allowed fields', () => {
      const input = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'secret',
        maliciousField: 'hack',
      };
      
      const sanitized = service.testSanitizeInput(input, ['name', 'email']);
      
      expect(sanitized).toEqual({
        name: 'Test User',
        email: '<EMAIL>',
      });
    });

    it('should handle undefined values', () => {
      const input = { name: 'Test', email: undefined, age: 25 };
      const sanitized = service.testSanitizeInput(input, ['name', 'email', 'age']);
      
      expect(sanitized).toEqual({
        name: 'Test',
        age: 25,
      });
    });
  });

  describe('handleApiResponse', () => {
    it('should handle successful API response', () => {
      const apiResponse = {
        success: true,
        data: { id: '1', name: 'Test' },
      };
      
      const result = service.testHandleApiResponse(apiResponse, 'testOperation');
      
      expect(result.success).toBe(true);
      expect(result.data).toEqual({ id: '1', name: 'Test' });
    });

    it('should handle failed API response', () => {
      const apiResponse = {
        success: false,
        error: 'API Error',
      };
      
      const result = service.testHandleApiResponse(apiResponse, 'testOperation');
      
      expect(result.success).toBe(false);
      expect(result.error?.message).toBe('API Error');
    });
  });

  describe('executeWithTimeout', () => {
    it('should resolve when operation completes within timeout', async () => {
      const operation = Promise.resolve('success');
      
      const result = await service.testExecuteWithTimeout(operation, 1000);
      
      expect(result).toBe('success');
    });

    it('should reject when operation times out', async () => {
      const operation = new Promise(resolve => setTimeout(() => resolve('success'), 2000));
      
      await expect(
        service.testExecuteWithTimeout(operation, 1000)
      ).rejects.toThrow('Operation timed out after 1000ms');
    });
  });
});
