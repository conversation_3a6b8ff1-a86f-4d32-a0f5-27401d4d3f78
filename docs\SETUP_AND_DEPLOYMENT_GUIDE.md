# Setup and Deployment Guide - Payment Callbacks

This guide will help you install the required tools and deploy the payment callback functions.

## 📋 Prerequisites

Before you begin, you need to install the following tools:

### 1. Install Node.js and npm

1. **Download Node.js**:
   - Go to https://nodejs.org/
   - Download the **LTS version** (Long Term Support) - currently v18.x or v20.x
   - Run the installer (`node-v18.x.x-x64.msi` for Windows)
   - Follow the installation wizard (accept all defaults)

2. **Verify Installation**:
   Open a new Command Prompt (cmd) and run:
   ```cmd
   node --version
   npm --version
   ```
   You should see version numbers like:
   ```
   v18.17.0
   9.6.7
   ```

### 2. Install Firebase CLI

After Node.js is installed, run:
```cmd
npm install -g firebase-tools
```

This will install the Firebase CLI globally on your system.

### 3. Authenticate with Firebase

```cmd
firebase login
```

This will:
- Open your default web browser
- Ask you to sign in with your Google account
- Grant Firebase CLI access to your projects

## 🚀 Deployment Steps

### Step 1: Navigate to Functions Directory

Open Command Prompt and navigate to the functions directory:
```cmd
cd "C:\Users\<USER>\Desktop\mr.tech.v2\mr.tech.v2\firebase-config\functions"
```

### Step 2: Install Dependencies

```cmd
npm install
```

This will install all the required Node.js packages for the Firebase Functions.

### Step 3: Deploy Functions (Option A - Manual)

Deploy only the payment callback functions:
```cmd
firebase deploy --only functions:paymobTransactionProcessed,functions:paymobTransactionResponse
```

### Step 3: Deploy Functions (Option B - Using Script)

Alternatively, you can use the deployment script:
```cmd
deploy-payment-callbacks.bat
```

## 📋 Expected Output

After successful deployment, you should see:

```
✅ Payment callback functions deployed successfully!

📋 Your callback URLs are:
🔔 Transaction Processed Callback: https://us-central1-stopnow-be6b7.cloudfunctions.net/paymobTransactionProcessed
🔄 Transaction Response Callback: https://us-central1-stopnow-be6b7.cloudfunctions.net/paymobTransactionResponse
```

## 🔧 Configure Paymob Dashboard

After successful deployment:

1. **Login to Paymob Dashboard**:
   - Go to https://accept.paymob.com/
   - Sign in with your account

2. **Navigate to Integration Settings**:
   - Go to **Developers** → **Payment Integrations**
   - Select **Test mode** (for testing)
   - Find Integration ID **1164997**
   - Click **Edit**

3. **Update Callback URLs**:
   - **Transaction processed callback**: `https://us-central1-stopnow-be6b7.cloudfunctions.net/paymobTransactionProcessed`
   - **Transaction response callback**: `https://us-central1-stopnow-be6b7.cloudfunctions.net/paymobTransactionResponse`
   - Click **Submit**

## 🧪 Testing

### Test Payment Flow

1. **Mobile App**: Make a test payment from your mobile app
2. **Check Logs**: Monitor Firebase Functions logs:
   ```cmd
   firebase functions:log --only paymobTransactionProcessed,paymobTransactionResponse
   ```
3. **Verify Data**: Check Firestore for new payment transaction records
4. **Web Portal**: View the transaction in the web portal Payments page

### Verify Callback Processing

Look for these log messages:
- `🔔 Paymob Transaction Processed Callback received`
- `🔄 Paymob Transaction Response Callback received`
- `✅ Payment transaction saved successfully`

## 🛠️ Troubleshooting

### Common Issues

1. **"firebase: command not found"**
   - Solution: Install Firebase CLI with `npm install -g firebase-tools`
   - Make sure to restart your command prompt after installation

2. **"npm: command not found"**
   - Solution: Install Node.js from https://nodejs.org/
   - Restart your command prompt after installation

3. **Authentication Error**
   - Solution: Run `firebase login` and sign in with your Google account
   - Make sure you have access to the Firebase project

4. **Deployment Permission Error**
   - Solution: Ensure your Google account has Firebase project access
   - Contact the project owner to add you as a collaborator

5. **Function Not Found Error**
   - Solution: Make sure you're in the correct directory (`firebase-config/functions`)
   - Verify the function files exist in `src/` directory

### Check Project Configuration

Verify you're deploying to the correct project:
```cmd
firebase projects:list
firebase use stopnow-be6b7
```

### View Deployment Status

Check if functions are deployed:
```cmd
firebase functions:list
```

## 📞 Support

If you encounter issues:

1. **Check Firebase Console**: https://console.firebase.google.com/
2. **View Function Logs**: In Firebase Console → Functions → Logs
3. **Check Paymob Dashboard**: Verify callback URLs are saved correctly
4. **Test Mobile App**: Ensure payment integration is working

## 🎉 Success Checklist

- [ ] Node.js and npm installed
- [ ] Firebase CLI installed and authenticated
- [ ] Functions deployed successfully
- [ ] Callback URLs configured in Paymob dashboard
- [ ] Test payment completed successfully
- [ ] Transaction data visible in Firestore
- [ ] Transaction visible in web portal

Once all items are checked, your payment callback system is fully operational!
