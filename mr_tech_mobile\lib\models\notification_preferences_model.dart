
class NotificationPreferencesModel {
  final bool requestUpdates;       // Updates about request status (approved, in-progress, completed)
  final bool messageNotifications; // New chat messages
  final bool paymentNotifications; // Payment success/failure notifications
  final bool systemNotifications;  // System and maintenance notifications
  final bool pushNotifications;    // If false, no push notifications are sent (overrides others)
  final bool soundEnabled;         // Sound for notifications
  final bool vibrationEnabled;     // Vibration for notifications

  NotificationPreferencesModel({
    this.requestUpdates = true,
    this.messageNotifications = true,
    this.paymentNotifications = true,
    this.systemNotifications = true,
    this.pushNotifications = true,
    this.soundEnabled = true,
    this.vibrationEnabled = true,
  });
  
  factory NotificationPreferencesModel.fromFirestore(Map<String, dynamic>? data) {
    if (data == null) {
      return NotificationPreferencesModel(); // Default values (all enabled)
    }
    
    return NotificationPreferencesModel(
      requestUpdates: data['requestUpdates'] ?? true,
      messageNotifications: data['messageNotifications'] ?? true,
      paymentNotifications: data['paymentNotifications'] ?? true,
      systemNotifications: data['systemNotifications'] ?? true,
      pushNotifications: data['pushNotifications'] ?? true,
      soundEnabled: data['soundEnabled'] ?? true,
      vibrationEnabled: data['vibrationEnabled'] ?? true,
    );
  }
  
  Map<String, dynamic> toMap() {
    return {
      'requestUpdates': requestUpdates,
      'messageNotifications': messageNotifications,
      'paymentNotifications': paymentNotifications,
      'systemNotifications': systemNotifications,
      'pushNotifications': pushNotifications,
      'soundEnabled': soundEnabled,
      'vibrationEnabled': vibrationEnabled,
    };
  }
  
  // Create a copy with updated fields
  NotificationPreferencesModel copyWith({
    bool? requestUpdates,
    bool? messageNotifications,
    bool? paymentNotifications,
    bool? systemNotifications,
    bool? pushNotifications,
    bool? soundEnabled,
    bool? vibrationEnabled,
  }) {
    return NotificationPreferencesModel(
      requestUpdates: requestUpdates ?? this.requestUpdates,
      messageNotifications: messageNotifications ?? this.messageNotifications,
      paymentNotifications: paymentNotifications ?? this.paymentNotifications,
      systemNotifications: systemNotifications ?? this.systemNotifications,
      pushNotifications: pushNotifications ?? this.pushNotifications,
      soundEnabled: soundEnabled ?? this.soundEnabled,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
    );
  }
} 