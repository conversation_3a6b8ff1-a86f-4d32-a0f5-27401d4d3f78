# Mr.Tech Chat System Documentation

## Overview

The Mr.Tech chat system provides real-time messaging between customers and technicians using Firebase Realtime Database. The chat is activated only after a technician accepts a customer's service request, as per the application requirements.

## Architecture

The chat system follows this architecture:

1. **Data Storage**: Firebase Realtime Database
2. **Authentication**: Firebase Authentication
3. **Models**: ChatMessageModel class for message structure
4. **Services**: 
   - DatabaseService for core database operations
   - ChatService for higher-level chat operations

## Database Structure

```
/messages
  /{requestId}
    /{messageId}
      - content: string
      - created_at: timestamp
      - file_url: string|null
      - id: string
      - message_type: string (text|image|file|system)
      - read: boolean
      - request_id: string
      - sender_id: string
      - sender_type: string (customer|technician|system)
      - timestamp: number

/requests_meta
  /{requestId}
    - chat_active: boolean
    - customer_id: string
    - last_message: string
    - last_message_time: timestamp
    - last_sender_id: string
    - last_sender_type: string
    - technician_id: string
    - updated_at: timestamp

/user_chats
  /{userId}
    /{requestId}
      - last_updated: timestamp
      - unread_count: number
```

## Security Rules

The Firebase Realtime Database security rules ensure:

1. Only authenticated users can read/write to the database
2. Users can only access their own chat data
3. Proper indexing for efficient queries

## Usage Flow

1. **Customer creates a service request**
   - Chat is initially deactivated (chatActive = false)

2. **Technician accepts the request**
   - Backend updates request status to 'approved'
   - Chat is activated (chatActive = true)
   - System sends a welcome message

3. **Real-time messaging**
   - Both customer and technician can send/receive messages
   - Messages are delivered in real-time
   - Read status is tracked

4. **Request completion**
   - Chat history is preserved for future reference
   - Chat data can be used for quality assurance and analytics

## Implementation Details

### Models

The `ChatMessageModel` handles several types of messages:
- Text messages (standard chat)
- Image messages (image sharing)
- File messages (document sharing)
- System messages (automated notifications)

### Key Methods

1. **`sendChatMessage`**: Send a new message
2. **`streamChatMessages`**: Listen for real-time message updates
3. **`markMessagesAsRead`**: Update read status of messages
4. **`updateRequestChatStatus`**: Enable/disable chat for a request

### Chat Activation Logic

Chat is only enabled after a technician accepts a request:

```dart
// In ChatService
Future<void> initializeChat(String requestId) async {
  // Create system message
  final message = ChatMessageModel(
    requestId: requestId,
    senderType: SenderType.system,
    senderId: 'system',
    messageType: MessageType.system,
    content: 'Chat is now active. The technician will assist you shortly.',
    createdAt: DateTime.now(),
  );
  
  // Send the message
  await _databaseService.sendChatMessage(message, requestId);
      
  // Update chat status
  await _databaseService.updateRequestChatStatus(requestId, true);
}
```

## Testing

Use the `scripts/setup_chat_db.dart` script to initialize test data. This creates:
- A test request with metadata
- Sample chat messages
- Proper chat status indicators

```bash
flutter run scripts/setup_chat_db.dart
```

## Extending the Chat System

The chat system can be extended with:
- Image/file uploading via Firebase Storage
- Read receipts and typing indicators
- Message reactions
- Message search functionality
- Chat archiving

## Troubleshooting

Common issues:
- Ensure Firebase Realtime Database is properly initialized
- Verify security rules allow proper access
- Check authentication status before chat operations
- Use `.indexOn` rules for efficient queries
- Monitor Realtime Database quota and limits 