import 'dart:io';
import 'package:flutter/foundation.dart';

void main() async {
  // Directory to scan (start from lib)
  final directory = Directory('lib');

  // Count of files updated
  int filesUpdated = 0;

  // Process all dart files
  await for (final entity in directory.list(recursive: true)) {
    if (entity is File && entity.path.endsWith('.dart')) {
      final file = File(entity.path);
      String content = await file.readAsString();
      final originalContent = content;

      // Check if file uses AppTextStyles but doesn't import it
      if (content.contains('AppTextStyles') &&
          !content.contains("import '../theme/app_theme.dart'") &&
          !content.contains(
            "import 'package:mr_tech_mobile/theme/app_theme.dart'",
          )) {
        // Add the import at the top
        final lines = content.split('\n');
        int importIndex = 0;

        // Find the last import statement
        for (int i = 0; i < lines.length; i++) {
          if (lines[i].trim().startsWith('import ')) {
            importIndex = i + 1;
          } else if (lines[i].trim().isNotEmpty &&
              !lines[i].trim().startsWith('//')) {
            break;
          }
        }

        // Insert the import
        lines.insert(importIndex, "import '../theme/app_theme.dart';");

        // Write back to file
        await file.writeAsString(lines.join('\n'));
        filesUpdated++;
        debugPrint('Added AppTextStyles import to ${file.path}');
      }
    }
  }

  debugPrint('Finished adding missing imports');
  debugPrint('Run flutter analyze to check for any remaining issues');
}
