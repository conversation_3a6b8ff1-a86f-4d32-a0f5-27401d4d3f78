// Firebase Console Script - Copy and paste this into Firebase Console
// Go to: Firebase Console → Firestore Database → More options → Query
// Then switch to JavaScript tab and paste this code

// SERVICES MIGRATION SCRIPT FOR FIREBASE CONSOLE
console.log('🚀 SERVICES MIGRATION SCRIPT (Firebase Console)');
console.log('═'.repeat(50));
console.log('This script will add snake_case fields to your existing services');
console.log('Your original camelCase fields will be PRESERVED');
console.log('');

async function migrateServicesInConsole() {
  try {
    // Get services collection
    const servicesSnapshot = await firebase.firestore().collection('services').get();
    
    if (servicesSnapshot.empty) {
      console.log('❌ No services found in Firestore');
      return;
    }

    console.log('📊 BEFORE MIGRATION - Current Services:');
    console.log(`   Total Services: ${servicesSnapshot.docs.length}`);
    
    let hasSnakeCase = 0;
    let hasCamelCaseOnly = 0;
    
    // Check current status
    servicesSnapshot.docs.forEach(doc => {
      const data = doc.data();
      const hasSnakeCaseFields = data.base_price !== undefined || 
                                 data.is_active !== undefined || 
                                 data.estimated_duration !== undefined;
      
      if (hasSnakeCaseFields) {
        hasSnakeCase++;
      } else {
        hasCamelCaseOnly++;
      }
    });
    
    console.log(`   - With snake_case fields: ${hasSnakeCase}`);
    console.log(`   - CamelCase only: ${hasCamelCaseOnly}`);
    console.log(`   - Migration progress: ${servicesSnapshot.docs.length > 0 ? Math.round(hasSnakeCase / servicesSnapshot.docs.length * 100) : 0}%`);
    console.log('');
    
    console.log('🔄 STARTING MIGRATION...');
    console.log('');
    
    let migrated = 0;
    let alreadyMigrated = 0;
    let errors = 0;
    
    // Process each service
    for (const doc of servicesSnapshot.docs) {
      try {
        const data = doc.data();
        const updates = {};
        let needsUpdate = false;
        
        // Add snake_case version of basePrice
        if (data.basePrice !== undefined && data.base_price === undefined) {
          updates.base_price = data.basePrice;
          needsUpdate = true;
        }
        
        // Add snake_case version of isActive
        if (data.isActive !== undefined && data.is_active === undefined) {
          updates.is_active = data.isActive;
          needsUpdate = true;
        }
        
        // Add snake_case version of estimatedDuration
        if (data.estimatedDuration !== undefined && data.estimated_duration === undefined) {
          updates.estimated_duration = data.estimatedDuration;
          needsUpdate = true;
        }
        
        // Add snake_case version of imageUrl if it exists
        if (data.imageUrl !== undefined && data.image_url === undefined) {
          updates.image_url = data.imageUrl;
          needsUpdate = true;
        }
        
        // Add created_at if missing
        if (data.created_at === undefined && data.createdAt === undefined) {
          updates.created_at = firebase.firestore.FieldValue.serverTimestamp();
          needsUpdate = true;
        } else if (data.createdAt !== undefined && data.created_at === undefined) {
          updates.created_at = data.createdAt;
          needsUpdate = true;
        }
        
        // Add updated_at
        if (data.updated_at === undefined) {
          updates.updated_at = firebase.firestore.FieldValue.serverTimestamp();
          needsUpdate = true;
        }
        
        // Perform update if needed
        if (needsUpdate) {
          await doc.ref.update(updates);
          migrated++;
          
          // Get service name for logging
          let serviceName = 'Unknown';
          if (typeof data.name === 'string') {
            serviceName = data.name;
          } else if (data.name && typeof data.name === 'object') {
            serviceName = data.name.en || data.name.ar || Object.values(data.name)[0] || 'Unknown';
          }
          
          console.log(`   ✅ Migrated: ${serviceName}`);
          const fieldList = Object.keys(updates).join(', ');
          console.log(`      Added: ${fieldList}`);
        } else {
          alreadyMigrated++;
          console.log(`   ⏭️ Already migrated: ${doc.id}`);
        }
        
      } catch (error) {
        errors++;
        console.log(`   ❌ Error migrating ${doc.id}: ${error.message}`);
      }
    }
    
    console.log('');
    console.log('📊 MIGRATION SUMMARY:');
    console.log(`   - Total services: ${servicesSnapshot.docs.length}`);
    console.log(`   - Migrated: ${migrated}`);
    console.log(`   - Already migrated: ${alreadyMigrated}`);
    console.log(`   - Errors: ${errors}`);
    
    // Validate migration
    console.log('');
    console.log('🔍 VALIDATING MIGRATION...');
    const validationSnapshot = await firebase.firestore().collection('services').get();
    
    let valid = 0;
    let invalid = 0;
    
    validationSnapshot.docs.forEach(doc => {
      const data = doc.data();
      
      const hasBasePrice = data.base_price !== undefined;
      const hasIsActive = data.is_active !== undefined;
      const hasEstimatedDuration = data.estimated_duration !== undefined;
      const hasTimestamp = data.created_at !== undefined || data.updated_at !== undefined;
      
      if (hasBasePrice && hasIsActive && hasEstimatedDuration && hasTimestamp) {
        valid++;
      } else {
        invalid++;
        console.log(`   ❌ Service ${doc.id} missing some snake_case fields`);
      }
    });
    
    console.log(`   - Valid services: ${valid}`);
    console.log(`   - Invalid services: ${invalid}`);
    
    if (invalid === 0) {
      console.log('');
      console.log('🎉 MIGRATION COMPLETED SUCCESSFULLY!');
      console.log('═'.repeat(50));
      console.log('✅ All services now have both camelCase AND snake_case fields');
      console.log('✅ Your app will work with both field formats');
      console.log('✅ ServiceModel will prefer snake_case but fallback to camelCase');
      console.log('✅ No data was lost or removed');
      console.log('');
      console.log('🧪 NEXT STEPS:');
      console.log('1. Load your Flutter app');
      console.log('2. Check services screen loads normally');
      console.log('3. Look at debug console for field usage patterns');
      console.log('4. Verify both field formats work');
    } else {
      console.log('');
      console.log('⚠️ MIGRATION NEEDS ATTENTION');
      console.log('Some services may need manual review');
    }
    
  } catch (error) {
    console.log('');
    console.log(`❌ ERROR DURING MIGRATION: ${error.message}`);
    console.log('Your original data is safe');
  }
}

// Run the migration
migrateServicesInConsole()
  .then(() => {
    console.log('');
    console.log('🏁 Migration completed!');
    console.log('Now test your Flutter app to see that it still works perfectly!');
  })
  .catch((error) => {
    console.error('💥 Migration failed:', error);
  });

// COPY THE CODE ABOVE AND PASTE IT INTO FIREBASE CONSOLE 