import type { ErrorInfo, ReactNode } from 'react';
import React, { Component } from 'react';
import { <PERSON><PERSON>Triangle, Bug, Home, RefreshCw } from 'lucide-react';
import { Button } from '../ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { logger } from '../../utils/logger';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
  level?: 'page' | 'component' | 'critical';
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
}

class ErrorBoundary extends Component<Props, State> {
  private retryCount = 0;
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const { onError, level = 'component' } = this.props;

    // Log error with context
    logger.error('React Error Boundary caught error', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      level,
      errorId: this.state.errorId,
      retryCount: this.retryCount,
    });

    // Update state with error info
    this.setState({
      errorInfo,
    });

    // Call custom error handler if provided
    if (onError) {
      onError(error, errorInfo);
    }

    // Report to external error tracking service if needed
    this.reportError(error, errorInfo);
  }

  private reportError = (error: Error, errorInfo: ErrorInfo) => {
    // Here you could integrate with error tracking services like Sentry, Bugsnag, etc.
    if (process.env.NODE_ENV === 'production') {
      // Example: Sentry.captureException(error, { contexts: { react: errorInfo } });
      console.error('Error reported to tracking service:', {
        error: error.message,
        errorId: this.state.errorId,
      });
    }
  };

  private handleRetry = () => {
    if (this.retryCount < this.maxRetries) {
      this.retryCount++;
      logger.info('Retrying after error', {
        errorId: this.state.errorId,
        retryCount: this.retryCount,
      });

      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        errorId: null,
      });
    }
  };

  private handleGoHome = () => {
    window.location.href = '/dashboard';
  };

  private handleReload = () => {
    window.location.reload();
  };

  private renderErrorDetails = () => {
    const { error, errorInfo, errorId } = this.state;
    const { showDetails = false } = this.props;

    if (!showDetails || process.env.NODE_ENV === 'production') {
      return null;
    }

    return (
      <details className='mt-4 p-4 bg-gray-50 rounded-lg border'>
        <summary className='cursor-pointer font-medium text-gray-700 hover:text-gray-900'>
          <Bug className='inline w-4 h-4 mr-2' />
          Technical Details (Development Only)
        </summary>
        <div className='mt-3 space-y-3 text-sm'>
          <div>
            <strong>Error ID:</strong> <code className='bg-gray-200 px-1 rounded'>{errorId}</code>
          </div>
          <div>
            <strong>Error Message:</strong>
            <pre className='mt-1 p-2 bg-red-50 border border-red-200 rounded text-red-800 overflow-auto'>
              {error?.message}
            </pre>
          </div>
          <div>
            <strong>Stack Trace:</strong>
            <pre className='mt-1 p-2 bg-gray-100 border rounded text-xs overflow-auto max-h-40'>
              {error?.stack}
            </pre>
          </div>
          <div>
            <strong>Component Stack:</strong>
            <pre className='mt-1 p-2 bg-blue-50 border border-blue-200 rounded text-xs overflow-auto max-h-40'>
              {errorInfo?.componentStack}
            </pre>
          </div>
        </div>
      </details>
    );
  };

  render() {
    const { hasError, error } = this.state;
    const { children, fallback, level = 'component' } = this.props;

    if (hasError) {
      // Custom fallback UI
      if (fallback) {
        return fallback;
      }

      // Default error UI based on level
      const canRetry = this.retryCount < this.maxRetries;
      const isPageLevel = level === 'page';
      const isCritical = level === 'critical';

      return (
        <div
          className={`flex items-center justify-center ${isPageLevel ? 'min-h-screen' : 'min-h-[200px]'} p-4`}
        >
          <Card className={`w-full max-w-lg ${isCritical ? 'border-red-500' : ''}`}>
            <CardHeader className='text-center'>
              <div
                className={`mx-auto w-12 h-12 rounded-full flex items-center justify-center mb-4 ${
                  isCritical ? 'bg-red-100' : 'bg-orange-100'
                }`}
              >
                <AlertTriangle
                  className={`w-6 h-6 ${isCritical ? 'text-red-600' : 'text-orange-600'}`}
                />
              </div>
              <CardTitle className={isCritical ? 'text-red-900' : 'text-gray-900'}>
                {isCritical ? 'Critical Error' : 'Something went wrong'}
              </CardTitle>
              <CardDescription>
                {isCritical
                  ? 'A critical error occurred that requires immediate attention.'
                  : 'We encountered an unexpected error. Please try again.'}
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='text-center text-sm text-gray-600'>
                <p>
                  Error ID: <code className='bg-gray-100 px-1 rounded'>{this.state.errorId}</code>
                </p>
                <p className='mt-1'>{error?.message || 'An unexpected error occurred'}</p>
              </div>

              <div className='flex flex-col sm:flex-row gap-2 justify-center'>
                {canRetry && (
                  <Button
                    onClick={this.handleRetry}
                    variant='default'
                    size='sm'
                    className='flex items-center gap-2'
                  >
                    <RefreshCw className='w-4 h-4' />
                    Try Again ({this.maxRetries - this.retryCount} left)
                  </Button>
                )}

                {isPageLevel && (
                  <Button
                    onClick={this.handleGoHome}
                    variant='outline'
                    size='sm'
                    className='flex items-center gap-2'
                  >
                    <Home className='w-4 h-4' />
                    Go to Dashboard
                  </Button>
                )}

                <Button
                  onClick={this.handleReload}
                  variant='outline'
                  size='sm'
                  className='flex items-center gap-2'
                >
                  <RefreshCw className='w-4 h-4' />
                  Reload Page
                </Button>
              </div>

              {this.renderErrorDetails()}
            </CardContent>
          </Card>
        </div>
      );
    }

    return children;
  }
}

export default ErrorBoundary;
