# Real-Time Status Synchronization Mechanism

This document explains the bidirectional real-time status synchronization mechanism implemented between the mobile app and web portal.

## Overview

The system uses Firebase Firestore as the central data store and communication channel between the mobile app and web portal. Both platforms listen to changes in the Firestore database and update their UI accordingly. When either platform makes a status change, it writes to Firestore, which triggers updates on all listening clients.

## Key Components

### Web Portal Components

1. **Status Mapping Utilities** (`statusMapping.ts`)

   - `mobileToWebStatus`: Converts mobile app status strings to web portal `RequestStatus` enum
   - `webToMobileStatus`: Converts web portal `RequestStatus` enum to mobile app status strings
   - `normalizeStatus`: Handles status normalization from any format

2. **Real-Time Status Service** (`realtimeStatusService.ts`)

   - Sets up Firestore `onSnapshot` listeners for real-time updates
   - Provides methods to listen to specific requests, requests by status, etc.
   - Includes `updateRequestStatus` method to update statuses while ensuring proper format conversion

3. **Request Service** (`requestService.ts`)

   - Handles CRUD operations for service requests
   - Converts web portal status enums to mobile app format before saving to Firestore

4. **React Hooks** (`useRealtimeStatus.ts`)

   - Provides React components with easy access to real-time data
   - Manages listener lifecycle and state updates

5. **Status Notification Service** (`statusNotificationService.ts`)
   - Monitors status changes and generates notifications
   - Tracks status history for requests

### Mobile App Components

The mobile app implements similar listeners to receive real-time updates from Firestore.

## Synchronization Flow

### Web Portal → Mobile App

1. User updates a request status in the web portal UI
2. `realtimeStatusService.updateRequestStatus()` is called
3. This calls `requestService.updateStatus()` which:
   - Converts the status from web portal enum to mobile app string format using `webToMobileStatus`
   - Updates the document in Firestore
4. Mobile app's Firestore listeners detect the change
5. Mobile app updates its UI to reflect the new status

### Mobile App → Web Portal

1. User updates a request status in the mobile app
2. Mobile app updates the document in Firestore
3. Web portal's `onSnapshot` listeners detect the change
4. The status is normalized using `normalizeStatus` to convert from mobile app format to web portal enum
5. React components using the `useRealtimeStatus` hooks are updated with the new data
6. Web portal UI reflects the new status

## Error Handling

- Both platforms implement error handling for network failures
- The web portal logs errors during status updates
- Retry logic will be implemented in subtask 26.3

## Testing

To test the bidirectional synchronization:

1. Open the `RealtimeStatusDemo` component in the web portal
2. Select a request and update its status
3. Verify that the change is reflected in the mobile app
4. Update a status in the mobile app
5. Verify that the change is reflected in the web portal

## Conclusion

This bidirectional synchronization mechanism ensures that status updates are immediately reflected across both platforms, maintaining data consistency and providing a seamless user experience.
