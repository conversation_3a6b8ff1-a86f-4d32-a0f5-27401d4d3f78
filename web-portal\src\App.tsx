import React, { Suspense, lazy } from 'react';
import { Navigate, Route, BrowserRouter as Router, Routes } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { NotificationProvider } from './contexts/NotificationContext';
import ProtectedRoute from './components/auth/ProtectedRoute';
import Layout from './components/layout/Layout';
import { Toaster } from './components/ui/toaster';
import LoadingSpinner from './components/ui/loading-spinner';
import ErrorBoundary from './components/error/ErrorBoundary';

// Lazy load page components for better performance
const LoginPage = lazy(() => import('./pages/LoginPage'));
const DashboardPage = lazy(() => import('./pages/DashboardPage'));
const ForgotPasswordPage = lazy(() => import('./pages/ForgotPasswordPage'));
const ProfilePage = lazy(() => import('./pages/ProfilePage'));
const TechniciansPage = lazy(() => import('./pages/TechniciansPage'));
const RequestsPage = lazy(() => import('./pages/RequestsPage'));
const ChatPage = lazy(() => import('./pages/ChatPage'));
const ServicesPage = lazy(() =>
  import('./pages/ServicesPage').then((module) => ({ default: module.ServicesPage })),
);
const UserManagementPage = lazy(() => import('./pages/UserManagementPage'));
const AdminManagementPage = lazy(() => import('./pages/AdminManagementPage'));
const PaymentsPage = lazy(() => import('./pages/PaymentsPage'));
const ReportsPage = lazy(() => import('./pages/ReportsPage'));
const SettingsPage = lazy(() => import('./pages/SettingsPage'));
const NotificationsPage = lazy(() => import('./pages/NotificationsPage'));
const FileManagementPage = lazy(() => import('./pages/FileManagementPage'));

function App() {
  return (
    <ErrorBoundary level='critical' showDetails={true}>
      <Router>
        <ErrorBoundary level='page' showDetails={false}>
          <AuthProvider>
            <NotificationProvider>
              <Suspense fallback={<LoadingSpinner />}>
                <Routes>
                  <Route
                    path='/login'
                    element={
                      <ErrorBoundary level='component'>
                        <LoginPage />
                      </ErrorBoundary>
                    }
                  />
                  <Route
                    path='/forgot-password'
                    element={
                      <ErrorBoundary level='component'>
                        <ForgotPasswordPage />
                      </ErrorBoundary>
                    }
                  />

                  {/* Protected routes with layout */}
                  <Route
                    element={
                      <ProtectedRoute>
                        <Layout />
                      </ProtectedRoute>
                    }
                  >
                    <Route
                      path='/dashboard'
                      element={
                        <ErrorBoundary level='page'>
                          <DashboardPage />
                        </ErrorBoundary>
                      }
                    />
                    <Route
                      path='/requests'
                      element={
                        <ErrorBoundary level='page'>
                          <RequestsPage />
                        </ErrorBoundary>
                      }
                    />
                    <Route
                      path='/technicians'
                      element={
                        <ErrorBoundary level='page'>
                          <TechniciansPage />
                        </ErrorBoundary>
                      }
                    />
                    <Route
                      path='/users'
                      element={
                        <ErrorBoundary level='page'>
                          <UserManagementPage />
                        </ErrorBoundary>
                      }
                    />
                    <Route
                      path='/admins'
                      element={
                        <ErrorBoundary level='page'>
                          <AdminManagementPage />
                        </ErrorBoundary>
                      }
                    />
                    <Route
                      path='/services'
                      element={
                        <ErrorBoundary level='page'>
                          <ServicesPage />
                        </ErrorBoundary>
                      }
                    />
                    <Route
                      path='/payments'
                      element={
                        <ErrorBoundary level='page'>
                          <PaymentsPage />
                        </ErrorBoundary>
                      }
                    />
                    <Route
                      path='/chat'
                      element={
                        <ErrorBoundary level='page'>
                          <ChatPage />
                        </ErrorBoundary>
                      }
                    />
                    <Route
                      path='/profile'
                      element={
                        <ErrorBoundary level='page'>
                          <ProfilePage />
                        </ErrorBoundary>
                      }
                    />
                    <Route
                      path='/reports'
                      element={
                        <ErrorBoundary level='page'>
                          <ReportsPage />
                        </ErrorBoundary>
                      }
                    />
                    <Route
                      path='/settings'
                      element={
                        <ErrorBoundary level='page'>
                          <SettingsPage />
                        </ErrorBoundary>
                      }
                    />
                    <Route
                      path='/notifications'
                      element={
                        <ErrorBoundary level='page'>
                          <NotificationsPage />
                        </ErrorBoundary>
                      }
                    />
                    <Route
                      path='/files'
                      element={
                        <ErrorBoundary level='page'>
                          <FileManagementPage />
                        </ErrorBoundary>
                      }
                    />
                    {/* Add more protected routes here */}
                  </Route>

                  <Route path='/' element={<Navigate to='/dashboard' replace />} />
                  <Route path='*' element={<Navigate to='/dashboard' replace />} />
                </Routes>
              </Suspense>
              <Toaster />
            </NotificationProvider>
          </AuthProvider>
        </ErrorBoundary>
      </Router>
    </ErrorBoundary>
  );
}

export default App;
