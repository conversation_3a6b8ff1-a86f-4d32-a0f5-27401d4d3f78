import React from 'react';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../../components/ui/dialog';
import { Badge } from '../../components/ui/badge';
import { Button } from '../../components/ui/button';
import { Separator } from '../../components/ui/separator';
import {
  AlertCircle,
  Calendar,
  CheckCircle,
  Clock,
  Copy,
  DollarSign,
  Edit,
  Mail,
  MessageSquare,
  Monitor,
  Phone,
  Star,
  User,
  XCircle,
} from 'lucide-react';

import { type RequestModel, RequestStatus } from '../../types/request.js';

interface RequestDetailsDialogProps {
  request: RequestModel | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit: () => void;
  onOpenChat?: (request: RequestModel) => void;
}

const RequestDetailsDialog: React.FC<RequestDetailsDialogProps> = ({
  request,
  isOpen,
  onClose,
  onEdit,
  onOpenChat,
}) => {
  // Helper function to handle multilingual fields
  const getLocalizedText = (text: any, fallback: string = ''): string => {
    if (text == null) return fallback;
    if (typeof text === 'string') return text;
    if (typeof text === 'object' && text !== null) {
      if (typeof text.en === 'string' || typeof text.ar === 'string') {
        return text.en || text.ar || fallback;
      }
      try {
        return String(text) || fallback;
      } catch {
        return fallback;
      }
    }
    try {
      return String(text) || fallback;
    } catch {
      return fallback;
    }
  };

  const getStatusConfig = (status: RequestStatus) => {
    const configs = {
      [RequestStatus.PAYMENT_PENDING]: {
        label: 'Payment Pending',
        icon: Clock,
        color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        dotColor: 'bg-yellow-500',
      },
      [RequestStatus.PENDING]: {
        label: 'Pending',
        icon: Clock,
        color: 'bg-blue-100 text-blue-800 border-blue-200',
        dotColor: 'bg-blue-500',
      },
      [RequestStatus.APPROVED]: {
        label: 'Approved',
        icon: CheckCircle,
        color: 'bg-green-100 text-green-800 border-green-200',
        dotColor: 'bg-green-500',
      },
      [RequestStatus.IN_PROGRESS]: {
        label: 'In Progress',
        icon: AlertCircle,
        color: 'bg-orange-100 text-orange-800 border-orange-200',
        dotColor: 'bg-orange-500',
      },
      [RequestStatus.COMPLETED]: {
        label: 'Completed',
        icon: CheckCircle,
        color: 'bg-emerald-100 text-emerald-800 border-emerald-200',
        dotColor: 'bg-emerald-500',
      },
      [RequestStatus.CANCELLED]: {
        label: 'Cancelled',
        icon: XCircle,
        color: 'bg-red-100 text-red-800 border-red-200',
        dotColor: 'bg-red-500',
      },
      [RequestStatus.REFUSED]: {
        label: 'Refused',
        icon: XCircle,
        color: 'bg-red-100 text-red-800 border-red-200',
        dotColor: 'bg-red-500',
      },
    };
    return configs[status];
  };

  const formatDate = (date: Date | any) => {
    const dateObj = date instanceof Date ? date : date.toDate();
    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // TODO: Show toast notification
  };

  const handleOpenChat = () => {
    if (request && onOpenChat) {
      onOpenChat(request);
      onClose(); // Close the dialog after opening chat
    }
  };

  if (!request) return null;

  const statusConfig = getStatusConfig(request.status);
  const StatusIcon = statusConfig.icon;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-w-4xl max-h-[90vh] overflow-y-auto bg-white border border-gray-200 shadow-xl'>
        <DialogHeader>
          <div className='flex items-center justify-between'>
            <div>
              <DialogTitle className='text-2xl font-bold'>Request Details</DialogTitle>
              <DialogDescription>Request ID: #{request.id.slice(-8)}</DialogDescription>
            </div>
            <div
              className={`flex items-center gap-2 px-3 py-1 rounded-full border ${statusConfig.color}`}
            >
              <div className={`w-2 h-2 rounded-full ${statusConfig.dotColor}`}></div>
              <StatusIcon className='h-4 w-4' />
              <span className='font-medium'>{statusConfig.label}</span>
            </div>
          </div>
        </DialogHeader>

        <div className='grid grid-cols-1 lg:grid-cols-3 gap-6'>
          {/* Main Content */}
          <div className='lg:col-span-2 space-y-6'>
            {/* Service Information */}
            <div className='bg-blue-50 border border-blue-200 rounded-lg p-6'>
              <h3 className='text-lg font-semibold text-blue-900 mb-4'>Service Information</h3>
              <div className='space-y-3'>
                <div>
                  <h4 className='font-medium text-blue-800'>Service Name</h4>
                  <p className='text-blue-700'>
                    {getLocalizedText(request.service_name, 'Unknown Service')}
                  </p>
                </div>
                <div>
                  <h4 className='font-medium text-blue-800'>Description</h4>
                  <p className='text-blue-700'>
                    {getLocalizedText(request.service_description, 'No description available')}
                  </p>
                </div>
                <div>
                  <h4 className='font-medium text-blue-800'>Customer Issue</h4>
                  <p className='text-blue-700 bg-white p-3 rounded border border-blue-100'>
                    {request.customer_issue || 'No issue description provided'}
                  </p>
                </div>
              </div>
            </div>

            {/* Customer Information */}
            <div className='bg-green-50 border border-green-200 rounded-lg p-6'>
              <h3 className='text-lg font-semibold text-green-900 mb-4'>Customer Information</h3>
              <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <div className='flex items-center gap-3'>
                  <User className='h-5 w-5 text-green-600' />
                  <div>
                    <p className='font-medium text-green-800'>Name</p>
                    <p className='text-green-700'>{request.customer_name || 'Not provided'}</p>
                  </div>
                </div>
                <div className='flex items-center gap-3'>
                  <Mail className='h-5 w-5 text-green-600' />
                  <div>
                    <p className='font-medium text-green-800'>Email</p>
                    <p className='text-green-700'>{request.customer_email || 'Not provided'}</p>
                  </div>
                </div>
              </div>
            </div>

            {/* AnyDesk Information */}
            {(request.anydesk_id || request.anydesk_id) && (
              <div className='bg-purple-50 border border-purple-200 rounded-lg p-6'>
                <h3 className='text-lg font-semibold text-purple-900 mb-4'>Remote Access</h3>
                <div className='flex items-center gap-3'>
                  <Monitor className='h-5 w-5 text-purple-600' />
                  <div className='flex-1'>
                    <p className='font-medium text-purple-800'>AnyDesk ID</p>
                    <div className='flex items-center gap-2 mt-1'>
                      <code className='bg-white border border-purple-200 px-3 py-1 rounded font-mono text-purple-700'>
                        {request.anydesk_id || request.anydesk_id}
                      </code>
                      <Button
                        size='sm'
                        variant='outline'
                        onClick={() =>
                          copyToClipboard(request.anydesk_id || request.anydesk_id || '')
                        }
                        className='border-purple-200 text-purple-700 hover:bg-purple-100'
                      >
                        <Copy className='h-4 w-4 mr-1' />
                        Copy
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className='space-y-6'>
            {/* Quick Stats */}
            <div className='bg-white border rounded-lg p-6 shadow-sm'>
              <h3 className='text-lg font-semibold mb-4'>Quick Information</h3>
              <div className='space-y-4'>
                <div className='flex items-center justify-between'>
                  <div className='flex items-center gap-2'>
                    <DollarSign className='h-4 w-4 text-muted-foreground' />
                    <span className='text-sm font-medium'>Amount</span>
                  </div>
                  <div className='text-right'>
                    <p className='font-semibold'>{formatCurrency(request.amount)}</p>
                    <Badge variant={request.is_paid ? 'default' : 'secondary'} className='text-xs'>
                      {request.is_paid ? 'Paid' : 'Unpaid'}
                    </Badge>
                  </div>
                </div>

                <Separator />

                <div className='flex items-center justify-between'>
                  <div className='flex items-center gap-2'>
                    <Calendar className='h-4 w-4 text-muted-foreground' />
                    <span className='text-sm font-medium'>Created</span>
                  </div>
                  <p className='text-sm text-muted-foreground'>{formatDate(request.created_at)}</p>
                </div>

                {request.technician_name && (
                  <>
                    <Separator />
                    <div className='flex items-center justify-between'>
                      <div className='flex items-center gap-2'>
                        <User className='h-4 w-4 text-muted-foreground' />
                        <span className='text-sm font-medium'>Technician</span>
                      </div>
                      <p className='text-sm font-medium'>{request.technician_name}</p>
                    </div>
                  </>
                )}

                <Separator />

                <div className='flex items-center justify-between'>
                  <div className='flex items-center gap-2'>
                    <MessageSquare className='h-4 w-4 text-muted-foreground' />
                    <span className='text-sm font-medium'>Chat</span>
                  </div>
                  <Badge
                    variant={request.chat_active ? 'default' : 'secondary'}
                    className='text-xs'
                  >
                    {request.chat_active ? 'Active' : 'Inactive'}
                  </Badge>
                </div>

                <div className='flex items-center justify-between'>
                  <div className='flex items-center gap-2'>
                    <Monitor className='h-4 w-4 text-muted-foreground' />
                    <span className='text-sm font-medium'>Session</span>
                  </div>
                  <Badge
                    variant={request.session_active ? 'default' : 'secondary'}
                    className='text-xs'
                  >
                    {request.session_active ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className='bg-white border rounded-lg p-6 shadow-sm'>
              <h3 className='text-lg font-semibold mb-4'>Actions</h3>
              <div className='space-y-3'>
                <Button onClick={onEdit} className='w-full'>
                  <Edit className='h-4 w-4 mr-2' />
                  Update Status
                </Button>
                <Button
                  variant='outline'
                  className='w-full'
                  onClick={handleOpenChat}
                  disabled={!onOpenChat}
                >
                  <MessageSquare className='h-4 w-4 mr-2' />
                  Open Chat
                </Button>
                <Button variant='outline' className='w-full'>
                  <Phone className='h-4 w-4 mr-2' />
                  Contact Customer
                </Button>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default RequestDetailsDialog;
