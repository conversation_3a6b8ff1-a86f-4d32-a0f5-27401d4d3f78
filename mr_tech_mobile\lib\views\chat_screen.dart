import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path/path.dart' as path;
import '../models/chat_message_model.dart';
import '../models/request_model.dart';
import '../services/chat_service.dart';
import '../services/database_service.dart';
import '../services/translation_service.dart';
import '../services/technician_service.dart';
import '../models/technician_model.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../utils/network_image_handler.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../widgets/text_styles.dart';
import '../widgets/ui_kit.dart';
import 'enhanced_rating_screen.dart';

// Add this enum at the top of the file, outside any class
enum ChatStatus { active, waiting, disabled, error }

class ChatScreen extends StatefulWidget {
  final RequestModel request;

  const ChatScreen({super.key, required this.request});

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final ChatService _chatService = ChatService();
  final DatabaseService _databaseService = DatabaseService();
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final ImagePicker _imagePicker = ImagePicker();

  bool _isSending = false;
  bool _isUploading = false;
  bool _isLoading = true;
  final bool _isChatActive = false;
  List<ChatMessageModel> _messages = [];
  File? _selectedFile;
  String? _selectedFileName;
  StreamSubscription<List<ChatMessageModel>>? _messageSubscription;
  Timer? _refreshTimer;
  ChatStatus _chatStatus = ChatStatus.waiting;
  String _statusMessage = '';
  StreamSubscription<bool>? _chatStatusSubscription;
  StreamSubscription? _typingSubscription;
  StreamSubscription<RequestModel?>? _requestStatusSubscription;
  List<Map<String, dynamic>> _typingUsers = [];
  Timer? _typingTimer;
  RequestModel?
  _currentRequest; // Store current request data (may be updated from DB)
  TechnicianModel? _technicianData; // Store technician profile data

  // Get current user ID from Firebase Auth
  String? get _currentUserId => FirebaseAuth.instance.currentUser?.uid;

  @override
  void initState() {
    super.initState();
    _currentRequest = widget.request; // Initialize with widget data

    // Debug Realtime Database permissions
    WidgetsBinding.instance.addPostFrameCallback((_) {
      DatabaseService().debugRealtimeDbPermissions(widget.request.id);
    });

    _initializeChat();
  }

  Future<void> _initializeChat() async {
    try {
      setState(() => _isLoading = true);

      // Get current user ID from Firebase Auth
      final userId = FirebaseAuth.instance.currentUser?.uid;
      if (userId == null) {
        debugPrint('ChatScreen: User not authenticated');
        throw Exception('User not authenticated');
      }

      debugPrint(
        'ChatScreen: Initializing chat for request ${widget.request.id}',
      );
      debugPrint(
        'ChatScreen: Request object status: ${widget.request.status}, chatActive: ${widget.request.chatActive}',
      );

      // Refresh request data from database to get latest technician info
      try {
        final updatedRequest = await _databaseService.getRequestById(
          widget.request.id,
          forceRefresh: true,
        );
        if (updatedRequest != null) {
          debugPrint(
            'ChatScreen: Updated request data - Technician ID: ${updatedRequest.technicianId}, Name: ${updatedRequest.technicianName}',
          );
          // Update our current request data with the latest from DB
          setState(() {
            _currentRequest = updatedRequest;
          });

          if (updatedRequest.technicianName != null &&
              updatedRequest.technicianName!.isNotEmpty) {
            debugPrint(
              'ChatScreen: Found technician name in database: ${updatedRequest.technicianName}',
            );
          } else {
            debugPrint('ChatScreen: No technician name found in database');
          }
        }
      } catch (e) {
        debugPrint('ChatScreen: Error refreshing request data: $e');
        // Continue with existing data
      }

      // Fetch technician profile data if available
      if (_currentRequest?.technicianId != null) {
        await _fetchTechnicianData(_currentRequest!.technicianId!);
      }

      // Set current user ID in chat service
      _chatService.setCurrentUserId(userId);

      // First, check for any pending chat activation notifications for this request
      debugPrint('ChatScreen: Checking for chat activation notifications');
      try {
        final notificationQuery =
            await FirebaseFirestore.instance
                .collection('notifications')
                .where('user_id', isEqualTo: userId)
                .where('request_id', isEqualTo: widget.request.id)
                .where(
                  'type',
                  whereIn: [
                    'CHAT_ACTIVATION',
                    'chat_activation',
                    'CHAT_STARTED',
                    'chat_started',
                  ],
                )
                .orderBy('timestamp', descending: true)
                .limit(1)
                .get();

        if (notificationQuery.docs.isNotEmpty) {
          final notificationData = notificationQuery.docs.first.data();
          debugPrint(
            'ChatScreen: Found chat activation notification, ID: ${notificationQuery.docs.first.id}',
          );
          debugPrint(
            'ChatScreen: Notification data: ${notificationData['type']} sent at ${notificationData['timestamp']}',
          );

          // Mark notification as processed
          try {
            await notificationQuery.docs.first.reference.update({
              'delivered': true,
              'processed_by_app': true,
              'opened_in_chat': true,
              'read': true,
              'processed': true,
            });
            debugPrint('ChatScreen: Marked notification as processed');
          } catch (updateError) {
            // Log but don't let it stop initialization
            debugPrint(
              'ChatScreen: Error marking notification as processed: $updateError',
            );
            debugPrint(
              'ChatScreen: Continuing with chat initialization regardless of notification update error',
            );
          }

          // Force update request chat status to active
          try {
            await FirebaseFirestore.instance
                .collection('service_requests')
                .doc(widget.request.id)
                .update({
                  'chatActive': true,
                  'chat_active': true,
                  'updated_at': FieldValue.serverTimestamp(),
                  'last_activity': FieldValue.serverTimestamp(),
                });
            debugPrint(
              'ChatScreen: Updated request chat status to active based on notification',
            );
          } catch (e) {
            debugPrint('ChatScreen: Error updating request chat status: $e');
          }
        } else {
          debugPrint(
            'ChatScreen: No chat activation notifications found for request ${widget.request.id}',
          );
        }
      } catch (e) {
        debugPrint(
          'ChatScreen: Error checking for chat activation notifications: $e',
        );
        // Continue with normal flow
      }

      // Check if chat is active by querying DB directly
      final isActive = await _databaseService.isChatActive(widget.request.id);
      debugPrint('ChatScreen: Chat active status from DB check: $isActive');

      // Check if request is completed
      if (widget.request.status == RequestStatus.completed) {
        debugPrint('ChatScreen: Request is completed, chat may be disabled');
        final forceClosedFlag = await _databaseService.checkIfChatForceClosed(
          widget.request.id,
        );
        if (forceClosedFlag) {
          debugPrint('ChatScreen: Chat was force closed by technician');
          setState(() {
            _chatStatus = ChatStatus.disabled;
            _statusMessage = 'This chat has been closed by the technician.';
            _isLoading = false;
          });
          return;
        }
      }

      // If chat is active, load messages
      if (isActive || widget.request.chatActive == true) {
        debugPrint('ChatScreen: Chat is active, setting up chat listener');
        // Set up listener
        _chatService.setupChatListener(widget.request.id);

        // Set up typing indicator listener
        _setupTypingListener();

        // Fetch existing messages
        await _fetchMessages();

        setState(() {
          _chatStatus = ChatStatus.active;
          _isLoading = false;
        });

        // Set up request status listener to detect completion
        _setupRequestStatusListener();
      } else {
        debugPrint('ChatScreen: Chat is not active, waiting for technician');
        setState(() {
          _chatStatus = ChatStatus.waiting;
          _statusMessage = 'Waiting for the technician to start the chat.';
          _isLoading = false;
        });

        // Set up a listener to detect when chat becomes active
        _setupChatStatusListener();
      }
    } catch (e) {
      debugPrint('ChatScreen: Error initializing chat: $e');
      setState(() {
        _chatStatus = ChatStatus.error;
        _statusMessage = 'Failed to initialize chat. Please try again.';
        _isLoading = false;
      });
    }
  }

  // Set up message stream for real-time updates
  void _setupMessageStream() {
    try {
      debugPrint('ChatScreen: Setting up message stream');
      // Cancel any existing subscription first
      _messageSubscription?.cancel();

      // Cancel existing timer if any
      _refreshTimer?.cancel();

      // Listen to messages for this chat
      _chatService.setupChatListener(widget.request.id);

      // This subscription is for the UI display only
      _messageSubscription = _chatService
          .getMessagesStream(widget.request.id)
          .listen(
            (messages) {
              if (mounted) {
                // Only update state if messages are different - prevent unnecessary rebuilds
                if (_messagesAreDifferent(messages, _messages)) {
                  final bool isNewMessage = messages.length > _messages.length;
                  final bool wasAtBottom = _isScrolledToBottom();

                  setState(() {
                    _messages = messages;
                  });

                  // Scroll to bottom immediately for new messages or if user was already at bottom
                  if (isNewMessage || wasAtBottom) {
                    // Use immediate scroll for better UX - no animation delay
                    WidgetsBinding.instance.addPostFrameCallback((_) {
                      _scrollToBottomImmediate();
                    });
                  }

                  // Mark messages as read automatically
                  _markMessagesAsReadAsync();
                }
              }
            },
            onError: (error) {
              debugPrint('ChatScreen: Error in message stream: $error');
            },
          );
    } catch (e) {
      debugPrint('ChatScreen: Error setting up message stream: $e');
    }
  }

  // Compare two message lists to see if they are different
  bool _messagesAreDifferent(
    List<ChatMessageModel> newMessages,
    List<ChatMessageModel> oldMessages,
  ) {
    // Quick size comparison first
    if (newMessages.length != oldMessages.length) {
      debugPrint(
        'Message count changed: ${oldMessages.length} -> ${newMessages.length}',
      );
      return true;
    }

    // Check if message IDs and content are the same in the same order
    for (int i = 0; i < newMessages.length; i++) {
      if (newMessages[i].id != oldMessages[i].id ||
          newMessages[i].content != oldMessages[i].content ||
          newMessages[i].isRead != oldMessages[i].isRead) {
        debugPrint(
          'Message at index $i changed: ID or content or read status differs',
        );
        return true;
      }
    }

    debugPrint('No change in messages detected, avoiding rebuild');
    return false;
  }

  // Helper method to mark messages as read
  Future<void> _markMessagesAsReadAsync() async {
    try {
      // Don't need to do anything if no messages
      if (_messages.isEmpty) return;

      // Find messages from technician that aren't read
      final unreadMessages =
          _messages
              .where(
                (msg) => msg.senderType == SenderType.technician && !msg.isRead,
              )
              .toList();

      if (unreadMessages.isNotEmpty) {
        await _chatService.markMessagesAsRead(widget.request.id);
      }
    } catch (e) {
      debugPrint('ChatScreen: Error marking messages as read: $e');
    }
  }

  void _showErrorSnackbar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(message)));
    }
  }

  void _sendMessage() async {
    final message = _messageController.text.trim();
    if (message.isEmpty && _selectedFile == null) return;

    setState(() => _isSending = true);

    try {
      final userId = _currentUserId;
      if (userId == null) {
        throw Exception('User not authenticated');
      }

      if (_selectedFile != null) {
        // Upload file message
        setState(() => _isUploading = true);

        await _chatService.sendFileMessage(
          requestId: widget.request.id,
          file: _selectedFile!,
          caption: message.isNotEmpty ? message : null,
        );

        setState(() {
          _selectedFile = null;
          _selectedFileName = null;
          _isUploading = false;
        });
      } else {
        // Send text message
        await _chatService.sendMessage(
          requestId: widget.request.id,
          content: message,
        );
      }

      _messageController.clear();

      // Scroll to bottom immediately after sending
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToBottomImmediate();
      });
    } catch (e) {
      _showErrorSnackbar('Error sending message: ${e.toString()}');
    } finally {
      if (mounted) {
        setState(() => _isSending = false);
      }
    }
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: source,
        imageQuality: 80,
      );

      if (image != null) {
        setState(() {
          _selectedFile = File(image.path);
          _selectedFileName = path.basename(image.path);
        });
      }
    } catch (e) {
      _showErrorSnackbar('${translate('Error picking image')}: ${e.toString()}');
    }
  }

  Future<void> _pickFile() async {
    try {
      final XFile? file = await _imagePicker.pickMedia();

      if (file != null) {
        setState(() {
          _selectedFile = File(file.path);
          _selectedFileName = path.basename(file.path);
        });
      }
    } catch (e) {
      _showErrorSnackbar('${translate('Error picking file')}: ${e.toString()}');
    }
  }

  void _clearSelectedFile() {
    setState(() {
      _selectedFile = null;
      _selectedFileName = null;
    });
  }

  void _showAttachmentOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        final translationService = Provider.of<TranslationService>(
          context,
          listen: false,
        );
        String translate(String key) => translationService.translate(key);

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 20.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: Text(translate('Take Photo')),
                onTap: () {
                  Navigator.pop(context);
                  _pickImage(ImageSource.camera);
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: Text(translate('Photo Gallery')),
                onTap: () {
                  Navigator.pop(context);
                  _pickImage(ImageSource.gallery);
                },
              ),
              ListTile(
                leading: const Icon(Icons.attach_file),
                title: Text(translate('Document')),
                onTap: () {
                  Navigator.pop(context);
                  _pickFile();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final messageDate = DateTime(
      timestamp.year,
      timestamp.month,
      timestamp.day,
    );

    if (messageDate == today) {
      // Today, show time only
      return DateFormat.Hm().format(timestamp);
    } else if (messageDate == today.subtract(const Duration(days: 1))) {
      // Yesterday
      return 'Yesterday ${DateFormat.Hm().format(timestamp)}';
    } else {
      // Other days
      return DateFormat.yMMMd().add_Hm().format(timestamp);
    }
  }

  String _formatDateHeader(DateTime timestamp) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    final messageDate = DateTime(
      timestamp.year,
      timestamp.month,
      timestamp.day,
    );

    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );

    if (messageDate == today) {
      return translationService.translate('Today');
    } else if (messageDate == yesterday) {
      return translationService.translate('Yesterday');
    } else {
      return DateFormat.yMMMd().format(timestamp);
    }
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      // Since we're no longer using reverse: true, we scroll to the bottom (maxScrollExtent)
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    }
  }

  void _scrollToBottomImmediate() {
    if (_scrollController.hasClients) {
      _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
    }
  }

  bool _isScrolledToBottom() {
    if (!_scrollController.hasClients) return true;

    final position = _scrollController.position;
    const threshold = 100.0; // pixels from bottom to consider "at bottom"

    return (position.maxScrollExtent - position.pixels) <= threshold;
  }

  bool _isConsecutive(ChatMessageModel current, ChatMessageModel? previous) {
    if (previous == null) return false;

    return current.senderType == previous.senderType &&
        current.senderId == previous.senderId &&
        current.createdAt.difference(previous.createdAt).inMinutes < 2;
  }

  // Group messages by date
  List<Map<String, dynamic>> _groupMessagesByDate() {
    final groups = <Map<String, dynamic>>[];

    for (int i = 0; i < _messages.length; i++) {
      final message = _messages[i];
      final date = _formatDateHeader(message.createdAt);

      if (groups.isEmpty || groups.last['date'] != date) {
        groups.add({
          'date': date,
          'messages': [message],
        });
      } else {
        groups.last['messages'].add(message);
      }
    }

    return groups;
  }

  Future<void> _fetchTechnicianData(String technicianId) async {
    try {
      final technicianService = TechnicianService();
      final technician = await technicianService.getTechnicianById(
        technicianId,
      );
      if (technician != null) {
        setState(() {
          _technicianData = technician;
        });
        debugPrint(
          'ChatScreen: Fetched technician data - Name: ${technician.name}, PhotoUrl: ${technician.photoUrl}',
        );

        // Additional debug logging for profile image
        if (technician.photoUrl != null) {
          debugPrint(
            'ChatScreen: Technician has profile image URL: ${technician.photoUrl}',
          );
        } else {
          debugPrint(
            'ChatScreen: Technician does not have a profile image URL',
          );
        }
      }
    } catch (e) {
      debugPrint('ChatScreen: Error fetching technician data: $e');
    }
  }

  String _getTechnicianInitials() {
    final name =
        _currentRequest?.technicianName ?? _technicianData?.name ?? 'T';
    if (name.isEmpty) return 'T';

    final parts = name.split(' ');
    if (parts.length >= 2) {
      return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
    } else {
      return name[0].toUpperCase();
    }
  }

  Future<void> _openFile(String url) async {
    try {
      // Check if it's an image file
      if (_isImageUrl(url)) {
        // Open image in full-screen viewer
        await Navigator.of(context).push(
          MaterialPageRoute(
            builder:
                (context) => _FullScreenImageViewer(
                  imageUrl: url,
                  heroTag: 'chat_image_$url',
                ),
          ),
        );
      } else {
        // Open non-image files in external application
        final uri = Uri.parse(url);
        if (await canLaunchUrl(uri)) {
          await launchUrl(uri, mode: LaunchMode.externalApplication);
        } else {
          _showErrorSnackbar('Could not open the file');
        }
      }
    } catch (e) {
      _showErrorSnackbar('Error opening file: ${e.toString()}');
    }
  }

  bool _isImageUrl(String url) {
    final imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    final lowerUrl = url.toLowerCase();
    return imageExtensions.any((ext) => lowerUrl.contains(ext));
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _messageSubscription?.cancel();
    _refreshTimer?.cancel();
    _chatStatusSubscription?.cancel();
    _typingSubscription?.cancel();
    _requestStatusSubscription?.cancel();
    _typingTimer?.cancel();

    // Ensure chat service properly disposes resources for this request
    _chatService.disposeResources(widget.request.id);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final translationService = Provider.of<TranslationService>(context);
    final isRtl = translationService.isRtl;
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(72),
        child: Container(
          decoration: BoxDecoration(
            color: colorScheme.surface,
            border: Border(
              bottom: BorderSide(
                color: colorScheme.outline.withValues(alpha: 0.1),
                width: 1,
              ),
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  // Back button with modern styling
                  Container(
                    decoration: BoxDecoration(
                      color: colorScheme.primary.withValues(alpha: 0.08),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      icon: Icon(
                        Icons.arrow_back_ios_new,
                        color: colorScheme.primary,
                        size: 20,
                      ),
                      onPressed: () => Navigator.of(context).pop(),
                      padding: const EdgeInsets.all(8),
                      constraints: const BoxConstraints(
                        minWidth: 40,
                        minHeight: 40,
                      ),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // Technician Avatar with enhanced styling
                  if (_currentRequest?.technicianName != null)
                    GestureDetector(
                      onTap: () {
                        // Show technician profile dialog when avatar is tapped
                        _showTechnicianProfileDialog();
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: colorScheme.primary.withValues(alpha: 0.15),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Stack(
                          children: [
                            NetworkImageCircleAvatarExtension.withNetworkImage(
                              context: context,
                              imageUrl: _technicianData?.photoUrl,
                              initials: _getTechnicianInitials(),
                              radius: 22,
                              backgroundColor: colorScheme.primary.withValues(
                                alpha: 0.1,
                              ),
                              loadingBuilder:
                                  (context) => CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      colorScheme.primary,
                                    ),
                                  ),
                            ),
                            // Enhanced status indicator
                            Positioned(
                              right: 0,
                              bottom: 0,
                              child: Container(
                                width: 14,
                                height: 14,
                                decoration: BoxDecoration(
                                  color: _getChatStatusColor(),
                                  shape: BoxShape.circle,
                                  border: Border.all(
                                    color: colorScheme.surface,
                                    width: 2.5,
                                  ),
                                  boxShadow: [
                                    BoxShadow(
                                      color: _getChatStatusColor().withValues(alpha: 0.3),
                                      blurRadius: 4,
                                      offset: const Offset(0, 1),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                  const SizedBox(width: 16),

                  // Enhanced technician info section
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Technician name with improved typography
                        Text(
                          _currentRequest?.technicianName ?? _translate('Technician'),
                          style: AppTextStyles.labelLarge(
                            context,
                            color: colorScheme.onSurface,
                          ).copyWith(
                            fontWeight: FontWeight.w600,
                            letterSpacing: -0.2,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),

                        const SizedBox(height: 2),

                        // Status text only (indicator is on avatar)
                        Text(
                          _getChatStatusText(),
                          style: AppTextStyles.labelSmall(
                            context,
                            color: _getChatStatusColor(),
                          ).copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Modern action buttons
                  Container(
                    decoration: BoxDecoration(
                      color: colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: IconButton(
                      icon: Icon(
                        Icons.more_vert_rounded,
                        color: colorScheme.onSurfaceVariant,
                        size: 20,
                      ),
                      onPressed: () {
                        _showChatOptions();
                      },
                      padding: const EdgeInsets.all(8),
                      constraints: const BoxConstraints(
                        minWidth: 40,
                        minHeight: 40,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                children: [
                  // Chat status indicator
                  if (_chatStatus != ChatStatus.active) _buildStatusBanner(),

                  // Messages area
                  Expanded(
                    child:
                        _messages.isEmpty
                            ? _buildEmptyChat()
                            : _buildMessagesList(),
                  ),

                  // Typing indicator
                  if (_typingUsers.isNotEmpty) _buildTypingIndicator(),

                  // Selected file preview
                  if (_selectedFile != null) _buildSelectedFilePreview(),

                  // Input area
                  if (_chatStatus == ChatStatus.active)
                    _buildChatInput()
                  else if (_chatStatus == ChatStatus.disabled)
                    _buildDisabledChatInput(),
                ],
              ),
    );
  }

  Widget _buildStatusBanner() {
    final colorScheme = Theme.of(context).colorScheme;

    Color backgroundColor;
    IconData iconData;

    switch (_chatStatus) {
      case ChatStatus.waiting:
        backgroundColor = Colors.orange.shade50;
        iconData = Icons.hourglass_bottom;
        break;
      case ChatStatus.disabled:
        backgroundColor = Colors.red.shade50;
        iconData = Icons.block;
        break;
      case ChatStatus.error:
        backgroundColor = Colors.red.shade50;
        iconData = Icons.error_outline;
        break;
      default:
        backgroundColor = colorScheme.primary.withValues(alpha: 0.1);
        iconData = Icons.info_outline;
    }

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          Icon(
            iconData,
            size: 20,
            color:
                _chatStatus == ChatStatus.waiting
                    ? Colors.orange.shade700
                    : _chatStatus == ChatStatus.error ||
                        _chatStatus == ChatStatus.disabled
                    ? Colors.red.shade700
                    : colorScheme.primary,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _statusMessage,
              style: AppTextStyles.bodyMedium(
                context,
                color:
                    _chatStatus == ChatStatus.waiting
                        ? Colors.orange.shade700
                        : _chatStatus == ChatStatus.error ||
                            _chatStatus == ChatStatus.disabled
                        ? Colors.red.shade700
                        : colorScheme.primary,
              ),
            ),
          ),
          if (_chatStatus == ChatStatus.error)
            TextButton(
              onPressed: () {
                setState(() => _isLoading = true);
                _initializeChat();
              },
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
              ),
              child: Text(_translate('Retry')),
            ),
        ],
      ),
    );
  }

  Widget _buildEmptyChat() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: colorScheme.primary.withValues(alpha: 0.1),
                shape: BoxShape.circle,
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Icon(
                Icons.chat_bubble_outline_rounded,
                size: 48,
                color: colorScheme.primary,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              _translate('No messages yet'),
              style: AppTextStyles.headingMedium(
                context,
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _translate('Start the conversation by sending a message'),
              textAlign: TextAlign.center,
              style: AppTextStyles.bodyMedium(
                context,
                color: colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessagesList() {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(), // Dismiss keyboard on tap
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
        itemCount: _messages.length,
        reverse: false, // Display messages in chronological order (oldest to newest)
        physics: const ClampingScrollPhysics(), // Better scroll physics for chat
        itemBuilder: (context, index) {
          final message = _messages[index];
          return AnimatedSwitcher(
            key: ValueKey(message.id.isNotEmpty ? message.id : '${message.content}_${message.createdAt.millisecondsSinceEpoch}'),
            duration: const Duration(milliseconds: 150),
            child: _buildMessageBubble(message),
          );
        },
      ),
    );
  }

  Widget _buildMessageBubble(ChatMessageModel message) {
    final bool isCurrentUser = message.senderId == _currentUserId;
    final bool isSystem = message.senderType == SenderType.system;
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // For system messages, show in the center with a different style
    if (isSystem) {
      return Container(
        margin: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
        child: Center(
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 12.0,
              vertical: 8.0,
            ),
            decoration: BoxDecoration(
              color: colorScheme.surface,
              borderRadius: BorderRadius.circular(8.0),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Text(
              message.content,
              style: AppTextStyles.bodySmall(
                context,
                color: colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      );
    }

    // For user messages (current user or technician)
    return Align(
      alignment: isCurrentUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        margin: EdgeInsets.only(
          top: 4.0,
          bottom: 4.0,
          left: isCurrentUser ? 64.0 : 8.0,
          right: isCurrentUser ? 8.0 : 64.0,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 10.0),
        decoration: BoxDecoration(
          color: isCurrentUser ? colorScheme.primary : colorScheme.surface,
          borderRadius: BorderRadius.circular(12.0),
          border:
              isCurrentUser ? null : Border.all(color: Colors.grey.shade300),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Message content
            if (message.messageType == MessageType.text)
              Text(
                message.content,
                style: AppTextStyles.bodyMedium(
                  context,
                  color:
                      isCurrentUser
                          ? colorScheme.onPrimary
                          : colorScheme.onSurface,
                ),
              ),
            if (message.messageType == MessageType.image)
              _buildImageMessage(message),
            if (message.messageType == MessageType.file)
              // Check if this is actually an image file that was incorrectly categorized
              // Check both filename and URL for image detection
              () {
                final isImageByFilename = _isImageFile(message.content);
                final isImageByUrl = message.fileUrl != null && _isImageUrl(message.fileUrl!);
                final shouldShowAsImage = isImageByFilename || isImageByUrl;

                // Debug logging
                debugPrint('File message - Content: ${message.content}, FileUrl: ${message.fileUrl}');
                debugPrint('IsImageByFilename: $isImageByFilename, IsImageByUrl: $isImageByUrl, ShowAsImage: $shouldShowAsImage');

                return shouldShowAsImage
                    ? _buildImageMessage(message)
                    : _buildFileMessage(message, isCurrentUser);
              }(),

            // Timestamp
            const SizedBox(height: 6.0),
            Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment:
                  isCurrentUser
                      ? MainAxisAlignment.end
                      : MainAxisAlignment.start,
              children: [
                Text(
                  _formatTimestamp(message.createdAt),
                  style: AppTextStyles.labelSmall(
                    context,
                    color:
                        isCurrentUser
                            ? colorScheme.onPrimary.withValues(alpha: 0.8)
                            : colorScheme.onSurfaceVariant,
                  ),
                ),
                if (isCurrentUser) ...[
                  const SizedBox(width: 4.0),
                  Icon(
                    message.isRead ? Icons.done_all : Icons.done,
                    size: 12.0,
                    color: colorScheme.onPrimary.withValues(alpha: 0.8),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageMessage(ChatMessageModel message) {
    final heroTag = 'chat_image_${message.fileUrl}';
    final colorScheme = Theme.of(context).colorScheme;

    return GestureDetector(
      onTap: () {
        // Open image in full-screen viewer inside the app
        Navigator.of(context).push(
          MaterialPageRoute(
            builder:
                (context) => _FullScreenImageViewer(
                  imageUrl: message.fileUrl!,
                  heroTag: heroTag,
                ),
          ),
        );
      },
      child: Container(
        constraints: const BoxConstraints(maxWidth: 280, maxHeight: 200),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: Stack(
            alignment: Alignment.bottomCenter,
            children: [
              // Enhanced image display with better loading and error states
              Hero(
                tag: heroTag,
                child: Container(
                  width: double.infinity,
                  height: 200,
                  decoration: BoxDecoration(
                    color: colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: NetworkImageWithFallback(
                    imageUrl: message.fileUrl,
                    width: double.infinity,
                    height: 200,
                    fit: BoxFit.cover,
                    fallbackIcon: Icons.image_outlined,
                    fallbackIconSize: 32,
                    fallbackColor: colorScheme.surfaceContainerHighest,
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return Container(
                        width: double.infinity,
                        height: 200,
                        color: colorScheme.surfaceContainerHighest,
                        child: Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    colorScheme.primary,
                                  ),
                                  value: loadingProgress.expectedTotalBytes != null
                                      ? loadingProgress.cumulativeBytesLoaded /
                                          loadingProgress.expectedTotalBytes!
                                      : null,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                _translate('Loading image...'),
                                style: AppTextStyles.labelSmall(
                                  context,
                                  color: colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),

              // Enhanced expand icon with modern design
              Positioned(
                top: 12,
                right: 12,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    Icons.open_in_full_rounded,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),

              // Enhanced caption overlay with better typography
              if (message.content.isNotEmpty &&
                  message.content != path.basename(message.fileUrl!))
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withValues(alpha: 0.8),
                        ],
                      ),
                      borderRadius: const BorderRadius.only(
                        bottomLeft: Radius.circular(16),
                        bottomRight: Radius.circular(16),
                      ),
                    ),
                    padding: const EdgeInsets.fromLTRB(16, 24, 16, 12),
                    child: Text(
                      message.content,
                      style: AppTextStyles.bodySmall(
                        context,
                        color: Colors.white,
                      ).copyWith(
                        fontWeight: FontWeight.w500,
                        height: 1.3,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),

              // Image type indicator (optional enhancement)
              Positioned(
                top: 12,
                left: 12,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withValues(alpha: 0.9),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.image_rounded,
                        color: Colors.white,
                        size: 12,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        translate('IMAGE'),
                        style: AppTextStyles.labelSmall(
                          context,
                          color: Colors.white,
                        ).copyWith(
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                          letterSpacing: 0.5,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFileMessage(ChatMessageModel message, bool isCurrentUser) {
    final colorScheme = Theme.of(context).colorScheme;
    final fileName = message.content;
    final fileUrl = message.fileUrl!;

    // Check if the file is an image based on extension
    final isImageFile = _isImageFile(fileName);

    return GestureDetector(
      onTap: () => _openFile(fileUrl),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 280),
        decoration: BoxDecoration(
          color: isCurrentUser
              ? colorScheme.primary.withValues(alpha: 0.1)
              : colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isCurrentUser
                ? colorScheme.primary.withValues(alpha: 0.2)
                : colorScheme.outline.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: isImageFile
            ? _buildImageFilePreview(fileUrl, fileName, isCurrentUser, colorScheme)
            : _buildGenericFilePreview(fileName, isCurrentUser, colorScheme),
      ),
    );
  }

  // Helper method to check if file is an image
  bool _isImageFile(String fileName) {
    if (fileName.isEmpty) return false;
    final parts = fileName.toLowerCase().split('.');
    if (parts.length < 2) return false;
    final extension = parts.last;
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].contains(extension);
  }

  // Build image file preview with thumbnail
  Widget _buildImageFilePreview(String fileUrl, String fileName, bool isCurrentUser, ColorScheme colorScheme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Image thumbnail
        ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(12),
            topRight: Radius.circular(12),
          ),
          child: SizedBox(
            height: 120,
            width: double.infinity,
            child: NetworkImageWithFallback(
              imageUrl: fileUrl,
              width: double.infinity,
              height: 120,
              fit: BoxFit.cover,
              fallbackIcon: Icons.image_outlined,
              fallbackIconSize: 24,
              fallbackColor: colorScheme.surfaceContainerHighest,
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return Container(
                  color: colorScheme.surfaceContainerHighest,
                  child: Center(
                    child: SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          colorScheme.primary,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ),

        // File info section
        Padding(
          padding: const EdgeInsets.all(12),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.image_rounded,
                  color: colorScheme.primary,
                  size: 16,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      fileName,
                      style: AppTextStyles.bodySmall(
                        context,
                        color: colorScheme.onSurface,
                      ).copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    Text(
                      _translate('Tap to view'),
                      style: AppTextStyles.labelSmall(
                        context,
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.open_in_new_rounded,
                color: colorScheme.onSurfaceVariant,
                size: 16,
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Build generic file preview
  Widget _buildGenericFilePreview(String fileName, bool isCurrentUser, ColorScheme colorScheme) {
    final extension = fileName.split('.').last.toUpperCase();

    return Padding(
      padding: const EdgeInsets.all(12),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                Icon(
                  _getFileIcon(fileName),
                  color: colorScheme.primary,
                  size: 20,
                ),
                if (extension.isNotEmpty) ...[
                  const SizedBox(height: 2),
                  Text(
                    extension,
                    style: AppTextStyles.labelSmall(
                      context,
                      color: colorScheme.primary,
                    ).copyWith(
                      fontSize: 8,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ],
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  fileName,
                  style: AppTextStyles.bodyMedium(
                    context,
                    color: colorScheme.onSurface,
                  ).copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  _translate('Tap to open'),
                  style: AppTextStyles.labelSmall(
                    context,
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.download_rounded,
            color: colorScheme.onSurfaceVariant,
            size: 18,
          ),
        ],
      ),
    );
  }

  // Helper method to get appropriate file icon
  IconData _getFileIcon(String fileName) {
    final extension = fileName.toLowerCase().split('.').last;

    switch (extension) {
      case 'pdf':
        return Icons.picture_as_pdf_rounded;
      case 'doc':
      case 'docx':
        return Icons.description_rounded;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart_rounded;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow_rounded;
      case 'txt':
        return Icons.text_snippet_rounded;
      case 'zip':
      case 'rar':
      case '7z':
        return Icons.folder_zip_rounded;
      default:
        return Icons.insert_drive_file_rounded;
    }
  }

  // Scroll to bottom if user is already near bottom
  void _scrollToBottomIfNeeded() {
    if (_scrollController.hasClients) {
      final position = _scrollController.position;
      final maxScroll = position.maxScrollExtent;
      final currentScroll = position.pixels;
      const threshold = 200.0; // pixels from bottom to trigger auto-scroll

      // Check if user is near the bottom (within threshold)
      if (maxScroll - currentScroll <= threshold) {
        _scrollToBottomImmediate(); // Use immediate scroll to prevent glitch
      }
    }
  }

  // Add this method to fetch messages
  Future<void> _fetchMessages() async {
    try {
      // Set up a subscription to the messages stream
      final messagesStream = _chatService.getMessagesStream(widget.request.id);
      _messageSubscription = messagesStream.listen(
        (updatedMessages) {
          final bool isNewMessage = updatedMessages.length > _messages.length;
          setState(() {
            _messages = updatedMessages;
          });

          // Auto-scroll to bottom for new messages or when initially loading
          if (isNewMessage || _messages.isEmpty) {
            _scrollToBottom();
          } else {
            _scrollToBottomIfNeeded();
          }
        },
        onError: (e) {
          debugPrint('ChatScreen: Error in messages stream: $e');
        },
      );

      // Mark messages as read
      await _chatService.markMessagesAsRead(widget.request.id);
    } catch (e) {
      debugPrint('ChatScreen: Error fetching messages: $e');
    }
  }

  // Set up typing indicator listener
  void _setupTypingListener() {
    if (_currentUserId == null) return;

    _typingSubscription = _chatService.listenToTypingIndicators(
      widget.request.id,
      (typingUsers) {
        if (mounted) {
          setState(() {
            // Filter out current user from typing indicators
            _typingUsers =
                typingUsers
                    .where((user) => user['userId'] != _currentUserId)
                    .toList();
          });
        }
      },
    );
  }

  // Handle typing indicator
  void _handleTyping() {
    if (_currentUserId == null) return;

    // Get current user name
    final currentUser = FirebaseAuth.instance.currentUser;
    final userName = currentUser?.displayName ?? 'User';

    // Set typing indicator to true
    _chatService.setTypingIndicator(
      widget.request.id,
      _currentUserId!,
      userName,
      true,
    );

    // Cancel existing timer
    _typingTimer?.cancel();

    // Set timer to stop typing indicator after 1 second of inactivity
    _typingTimer = Timer(const Duration(seconds: 1), () {
      _chatService.setTypingIndicator(
        widget.request.id,
        _currentUserId!,
        userName,
        false,
      );
    });
  }

  // Add this method to set up a listener for chat status changes
  void _setupChatStatusListener() {
    try {
      // Listen for chat status changes in Firestore
      final chatStatusStream = _databaseService.getChatActiveStream(
        widget.request.id,
      );
      _chatStatusSubscription = chatStatusStream.listen(
        (isActive) {
          if (isActive && _chatStatus != ChatStatus.active) {
            debugPrint('ChatScreen: Chat became active, updating UI');
            setState(() {
              _chatStatus = ChatStatus.active;
            });

            // Start listening for messages
            _fetchMessages();
          }
        },
        onError: (e) {
          debugPrint('ChatScreen: Error in chat status stream: $e');
        },
      );

      // Also listen for request status changes to detect completion
      _setupRequestStatusListener();
    } catch (e) {
      debugPrint('ChatScreen: Error setting up chat status listener: $e');
    }
  }

  // Add method to listen for request status changes and trigger review
  void _setupRequestStatusListener() {
    try {
      final requestStream = _databaseService.getRequestStream(widget.request.id);
      _requestStatusSubscription = requestStream.listen(
        (updatedRequest) {
          if (updatedRequest != null && mounted) {
            setState(() {
              _currentRequest = updatedRequest;
            });

            // Check if request is completed and should show review prompt
            if (updatedRequest.status == RequestStatus.completed &&
                updatedRequest.chatActive == false &&
                updatedRequest.customerRated != true &&
                mounted) {
              debugPrint('ChatScreen: Request completed, showing review prompt');
              _showReviewPrompt();
            }

            // Update chat status if request is completed
            if (updatedRequest.status == RequestStatus.completed && mounted) {
              setState(() {
                _chatStatus = ChatStatus.disabled;
                _statusMessage = 'This chat has been completed.';
              });
            }
          }
        },
        onError: (e) {
          debugPrint('ChatScreen: Error in request status stream: $e');
        },
      );
    } catch (e) {
      debugPrint('ChatScreen: Error setting up request status listener: $e');
    }
  }

  // Show review prompt when chat is completed
  void _showReviewPrompt() {
    if (_currentRequest == null || _currentRequest!.technicianId == null) {
      return;
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          _translate('Rate Your Experience'),
          style: Theme.of(context).textTheme.titleLarge,
          textAlign: TextAlign.center,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.star_rounded,
              size: 48,
              color: Colors.amber,
            ),
            const SizedBox(height: 16),
            Text(
              _translate('Your service has been completed. Please rate your experience with the technician.'),
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Mark as reminded but not rated to avoid showing again
              _markReviewSkipped();
            },
            child: Text(_translate('Skip')),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToReviewScreen();
            },
            child: Text(_translate('Rate Now')),
          ),
        ],
      ),
    );
  }

  // Navigate to enhanced rating screen
  void _navigateToReviewScreen() {
    if (_currentRequest == null) return;

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EnhancedRatingScreen(
          request: _currentRequest!,
        ),
      ),
    ).then((reviewSubmitted) {
      // Refresh request data after review submission
      if (reviewSubmitted == true) {
        _refreshRequestData();
      }
    });
  }

  // Mark review as skipped to avoid showing prompt again
  void _markReviewSkipped() async {
    try {
      await _databaseService.updateRequestField(
        _currentRequest!.id,
        'customer_reminded_rating',
        true,
      );
    } catch (e) {
      debugPrint('ChatScreen: Error marking review as skipped: $e');
    }
  }

  // Refresh request data
  void _refreshRequestData() async {
    try {
      final updatedRequest = await _databaseService.getServiceRequest(_currentRequest!.id);
      if (updatedRequest != null && mounted) {
        setState(() {
          _currentRequest = updatedRequest;
        });
      }
    } catch (e) {
      debugPrint('ChatScreen: Error refreshing request data: $e');
    }
  }

  String _translate(String text) {
    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );
    return translationService.translate(text);
  }

  void _showRequestDetails() {
    // Navigate to request details
    Navigator.pushNamed(
      context,
      '/request-details',
      arguments: _currentRequest?.id ?? widget.request.id,
    );
  }

  Widget _buildTypingIndicator() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    String typingText;
    if (_typingUsers.length == 1) {
      typingText = '${_typingUsers[0]['userName']} ${translate('is typing...')}';
    } else {
      typingText = '${_typingUsers.length} ${translate('people are typing...')}';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          // Animated dots
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildTypingDot(0),
                const SizedBox(width: 4),
                _buildTypingDot(1),
                const SizedBox(width: 4),
                _buildTypingDot(2),
                const SizedBox(width: 8),
                Text(
                  typingText,
                  style: AppTextStyles.bodySmall(
                    context,
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTypingDot(int index) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return AnimatedContainer(
      duration: Duration(milliseconds: 600 + (index * 200)),
      curve: Curves.easeInOut,
      width: 6,
      height: 6,
      decoration: BoxDecoration(
        color: colorScheme.primary,
        shape: BoxShape.circle,
      ),
    );
  }

  Widget _buildSelectedFilePreview() {
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          Icon(Icons.attach_file, size: 16, color: colorScheme.primary),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _selectedFile != null ? path.basename(_selectedFile!.path) : '',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: AppTextStyles.bodyMedium(
                context,
                color: colorScheme.onSurface,
              ),
            ),
          ),
          IconButton(
            icon: Icon(
              Icons.close,
              size: 16,
              color: colorScheme.onSurfaceVariant,
            ),
            onPressed: () {
              setState(() {
                _selectedFile = null;
              });
            },
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(),
          ),
        ],
      ),
    );
  }

  Widget _buildChatInput() {
    final translationService = Provider.of<TranslationService>(context);
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        border: Border(top: BorderSide(color: Colors.grey.shade300)),
      ),
      padding: const EdgeInsets.all(16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // Attachment button
          Container(
            decoration: BoxDecoration(
              color: colorScheme.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: IconButton(
              icon: Icon(
                Icons.attach_file,
                color: colorScheme.primary,
                size: 20,
              ),
              onPressed: _isSending ? null : _showAttachmentOptions,
              padding: const EdgeInsets.all(8),
              constraints: const BoxConstraints(),
            ),
          ),
          const SizedBox(width: 12),
          // Text field
          Expanded(
            child: TextField(
              controller: _messageController,
              onChanged: (text) {
                // Trigger typing indicator when user types
                _handleTyping();
              },
              decoration: InputDecoration(
                hintText: translationService.translate('Type a message...'),
                hintStyle: AppTextStyles.bodyMedium(
                  context,
                  color: colorScheme.onSurfaceVariant,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: colorScheme.primary, width: 2),
                ),
                filled: true,
                fillColor: colorScheme.surface,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              style: AppTextStyles.bodyMedium(
                context,
                color: colorScheme.onSurface,
              ),
              textDirection: translationService.textDirection,
              maxLines: 4,
              minLines: 1,
              enabled: !_isSending,
              onSubmitted: (_) => _sendMessage(),
              textInputAction: TextInputAction.send,
            ),
          ),
          const SizedBox(width: 12),
          // Send button
          _isSending
              ? Container(
                width: 44,
                height: 44,
                decoration: BoxDecoration(
                  color: colorScheme.primary.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Center(
                  child: SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Colors.white,
                    ),
                  ),
                ),
              )
              : InkWell(
                onTap: _sendMessage,
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  width: 44,
                  height: 44,
                  decoration: BoxDecoration(
                    color: colorScheme.primary,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.send,
                    color: colorScheme.onPrimary,
                    size: 20,
                  ),
                ),
              ),
        ],
      ),
    );
  }

  Widget _buildDisabledChatInput() {
    final translationService = Provider.of<TranslationService>(context);
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        border: Border(top: BorderSide(color: Colors.grey.shade300)),
      ),
      padding: const EdgeInsets.all(16),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: Row(
          children: [
            Icon(
              Icons.lock_outline,
              color: Colors.grey.shade600,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                _currentRequest?.status == RequestStatus.completed
                    ? translationService.translate('This chat has been completed. You can view the history but cannot send new messages.')
                    : translationService.translate('This chat is no longer active.'),
                style: AppTextStyles.bodyMedium(
                  context,
                  color: Colors.grey.shade600,
                ),
                textDirection: translationService.textDirection,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Add this method to show chat options
  void _showChatOptions() {
    final translationService = Provider.of<TranslationService>(
      context,
      listen: false,
    );
    final colorScheme = Theme.of(context).colorScheme;

    showModalBottomSheet(
      context: context,
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: Icon(Icons.info_outline, color: colorScheme.primary),
                title: Text(_translate('Request Details')),
                onTap: () {
                  Navigator.pop(context);
                  _showRequestDetails();
                },
              ),
              if (_technicianData != null)
                ListTile(
                  leading: Icon(
                    Icons.person_outline,
                    color: colorScheme.primary,
                  ),
                  title: Text(_translate('Technician Profile')),
                  onTap: () {
                    Navigator.pop(context);
                    _showTechnicianProfileDialog();
                  },
                ),
              ListTile(
                leading: Icon(Icons.refresh, color: colorScheme.primary),
                title: Text(_translate('Refresh Chat')),
                onTap: () {
                  Navigator.pop(context);
                  _fetchMessages();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  // Show technician profile dialog
  void _showTechnicianProfileDialog() {
    if (_technicianData == null) return;

    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Container(
              padding: const EdgeInsets.all(20),
              width: double.infinity,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Profile image
                  Container(
                    width: 100,
                    height: 100,
                    margin: const EdgeInsets.only(bottom: 16),
                    child: NetworkImageCircleAvatarExtension.withNetworkImage(
                      context: context,
                      imageUrl: _technicianData?.photoUrl,
                      initials: _getTechnicianInitials(),
                      radius: 50,
                      backgroundColor: colorScheme.primary.withValues(
                        alpha: 0.1,
                      ),
                      loadingBuilder:
                          (context) => CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              colorScheme.primary,
                            ),
                          ),
                    ),
                  ),

                  // Name
                  Text(
                    _technicianData?.name ?? 'Technician',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Rating
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.star, color: Colors.amber, size: 18),
                      const SizedBox(width: 4),
                      Text(
                        '${_technicianData?.rating.toStringAsFixed(1)} (${_technicianData?.completedRequests} reviews)',
                        style: theme.textTheme.bodyMedium,
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Specialties
                  if (_technicianData?.specialties.isNotEmpty == true) ...[
                    Text(
                      'Specialties',
                      style: theme.textTheme.titleSmall,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      alignment: WrapAlignment.center,
                      spacing: 8,
                      runSpacing: 8,
                      children:
                          _technicianData!.specialties
                              .map(
                                (specialty) => Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 10,
                                    vertical: 6,
                                  ),
                                  decoration: BoxDecoration(
                                    color: colorScheme.primary.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: Text(
                                    specialty,
                                    style: TextStyle(
                                      color: colorScheme.primary,
                                      fontSize: 12,
                                    ),
                                  ),
                                ),
                              )
                              .toList(),
                    ),
                  ],

                  const SizedBox(height: 20),

                  // Close button
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colorScheme.primary,
                      foregroundColor: colorScheme.onPrimary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      minimumSize: const Size(double.infinity, 48),
                    ),
                    child: const Text('Close'),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  Color _getChatStatusColor() {
    if (_chatStatus == ChatStatus.active) {
      return Colors.green;
    } else if (_chatStatus == ChatStatus.waiting) {
      return Colors.orange;
    } else {
      return Colors.grey;
    }
  }

  String _getChatStatusText() {
    if (_chatStatus == ChatStatus.active) {
      return _translate('Online');
    } else if (_chatStatus == ChatStatus.waiting) {
      return _translate('Waiting');
    } else {
      return _translate('Offline');
    }
  }
}

class _FullScreenImageViewer extends StatefulWidget {
  final String imageUrl;
  final String heroTag;

  const _FullScreenImageViewer({required this.imageUrl, required this.heroTag});

  @override
  State<_FullScreenImageViewer> createState() => _FullScreenImageViewerState();
}

class _FullScreenImageViewerState extends State<_FullScreenImageViewer> {
  final TransformationController _transformationController =
      TransformationController();
  bool _showAppBar = true;
  Timer? _hideTimer;
  double _scale = 1.0;

  @override
  void initState() {
    super.initState();
    _startHideTimer();
  }

  @override
  void dispose() {
    _transformationController.dispose();
    _hideTimer?.cancel();
    super.dispose();
  }

  void _startHideTimer() {
    _hideTimer?.cancel();
    _hideTimer = Timer(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _showAppBar = false;
        });
      }
    });
  }

  void _toggleAppBar() {
    setState(() {
      _showAppBar = !_showAppBar;
    });
    if (_showAppBar) {
      _startHideTimer();
    }
  }

  void _resetTransformation() {
    setState(() {
      _transformationController.value = Matrix4.identity();
      _scale = 1.0;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      extendBodyBehindAppBar: true,
      appBar:
          _showAppBar
              ? AppBar(
                backgroundColor: Colors.black.withValues(alpha: 0.5),
                elevation: 0,
                leading: IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: () => Navigator.of(context).pop(),
                ),
                actions: [
                  IconButton(
                    icon: const Icon(Icons.refresh, color: Colors.white),
                    onPressed: _resetTransformation,
                  ),
                  IconButton(
                    icon: const Icon(Icons.download, color: Colors.white),
                    onPressed: () => _downloadImage(context),
                  ),
                ],
              )
              : null,
      body: GestureDetector(
        onTap: _toggleAppBar,
        child: Center(
          child: Hero(
            tag: widget.heroTag,
            child: Stack(
              alignment: Alignment.center,
              children: [
                InteractiveViewer(
                  transformationController: _transformationController,
                  minScale: 0.5,
                  maxScale: 4.0,
                  onInteractionUpdate: (details) {
                    setState(() {
                      _scale =
                          _transformationController.value.getMaxScaleOnAxis();
                    });
                  },
                  child: NetworkImageWithFallback(
                    imageUrl: widget.imageUrl,
                    width: double.infinity,
                    height: double.infinity,
                    fit: BoxFit.contain,
                    fallbackIcon: Icons.broken_image,
                    fallbackColor: Colors.grey[800],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      // Add a bottom bar with zoom controls when zoomed
      bottomNavigationBar:
          _scale > 1.05 && _showAppBar
              ? SafeArea(
                child: Container(
                  height: 60,
                  color: Colors.black.withValues(alpha: 0.5),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.zoom_out, color: Colors.white),
                        onPressed: () {
                          final currentScale =
                              _transformationController.value
                                  .getMaxScaleOnAxis();
                          if (currentScale > 0.8) {
                            final newScale = currentScale - 0.5;
                            final newMatrix =
                                Matrix4.identity()..scale(newScale);
                            setState(() {
                              _transformationController.value = newMatrix;
                              _scale = newScale;
                            });
                          }
                        },
                      ),
                      const SizedBox(width: 20),
                      IconButton(
                        icon: const Icon(Icons.zoom_in, color: Colors.white),
                        onPressed: () {
                          final currentScale =
                              _transformationController.value
                                  .getMaxScaleOnAxis();
                          if (currentScale < 4.0) {
                            final newScale = currentScale + 0.5;
                            final newMatrix =
                                Matrix4.identity()..scale(newScale);
                            setState(() {
                              _transformationController.value = newMatrix;
                              _scale = newScale;
                            });
                          }
                        },
                      ),
                      const SizedBox(width: 20),
                      IconButton(
                        icon: const Icon(Icons.refresh, color: Colors.white),
                        onPressed: _resetTransformation,
                      ),
                    ],
                  ),
                ),
              )
              : null,
    );
  }

  Future<void> _downloadImage(BuildContext context) async {
    try {
      // Show download started message
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(translate('Starting download...')),
          duration: const Duration(seconds: 2),
        ),
      );

      // Launch URL in external application to download
      final uri = Uri.parse(widget.imageUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        throw Exception('Could not launch URL');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('${translate('Failed to download')}: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
