import { type RequestModel, RequestStatus } from '../types/request';
import realtimeStatusService from './realtimeStatusService';

/**
 * Notification types for status changes
 */
export interface StatusChangeNotification {
  id: string;
  requestId: string;
  oldStatus: RequestStatus;
  newStatus: RequestStatus;
  request: RequestModel;
  timestamp: Date;
  read: boolean;
  priority: 'low' | 'medium' | 'high';
  message: string;
}

/**
 * Notification callback type
 */
export type NotificationCallback = (notification: StatusChangeNotification) => void;

/**
 * Service for handling real-time status change notifications
 */
class StatusNotificationService {
  private notifications: StatusChangeNotification[] = [];
  private callbacks: Set<NotificationCallback> = new Set();
  private requestStatusCache: Map<string, RequestStatus> = new Map();
  private activeListeners: Map<string, () => void> = new Map();
  private processingRequests: Set<string> = new Set(); // Track requests being processed to prevent duplicates
  private processingTimeouts: Map<string, NodeJS.Timeout> = new Map(); // Debounce timeouts

  /**
   * Start monitoring status changes for specific requests
   */
  startMonitoring(requestIds: string[]): void {
    requestIds.forEach((requestId) => {
      if (this.activeListeners.has(requestId)) {
        return; // Already monitoring this request
      }

      const unsubscribe = realtimeStatusService.listenToRequestStatus(requestId, (request) => {
        if (request) {
          this.handleStatusChange(request);
        }
      });

      this.activeListeners.set(requestId, unsubscribe);
    });
  }

  /**
   * Stop monitoring specific requests
   */
  stopMonitoring(requestIds: string[]): void {
    requestIds.forEach((requestId) => {
      const unsubscribe = this.activeListeners.get(requestId);
      if (unsubscribe) {
        unsubscribe();
        this.activeListeners.delete(requestId);
        this.requestStatusCache.delete(requestId);

        // Clear any pending timeout for this request
        const timeout = this.processingTimeouts.get(requestId);
        if (timeout) {
          clearTimeout(timeout);
          this.processingTimeouts.delete(requestId);
        }
      }
    });
  }

  /**
   * Start monitoring all requests for a technician
   */
  startTechnicianMonitoring(technicianId: string, statuses?: RequestStatus[]): () => void {
    const listenerId = `technician_${technicianId}`;

    // Stop existing listener if any
    if (this.activeListeners.has(listenerId)) {
      this.activeListeners.get(listenerId)!();
    }

    const unsubscribe = realtimeStatusService.listenToTechnicianRequests(
      technicianId,
      (requests) => {
        requests.forEach((request) => {
          this.handleStatusChange(request);
        });
      },
      statuses,
    );

    this.activeListeners.set(listenerId, unsubscribe);
    return unsubscribe;
  }

  /**
   * Start monitoring all requests for a customer
   */
  startCustomerMonitoring(customerId: string): () => void {
    const listenerId = `customer_${customerId}`;

    // Stop existing listener if any
    if (this.activeListeners.has(listenerId)) {
      this.activeListeners.get(listenerId)!();
    }

    const unsubscribe = realtimeStatusService.listenToCustomerRequests(customerId, (requests) => {
      requests.forEach((request) => {
        this.handleStatusChange(request);
      });
    });

    this.activeListeners.set(listenerId, unsubscribe);
    return unsubscribe;
  }

  /**
   * Handle status change and create notification with debouncing to prevent duplicates
   */
  private handleStatusChange(request: RequestModel): void {
    const requestId = request.id;
    const currentStatus = request.status;

    // Clear any existing timeout for this request
    const existingTimeout = this.processingTimeouts.get(requestId);
    if (existingTimeout) {
      clearTimeout(existingTimeout);
    }

    // Debounce the processing to prevent duplicate notifications from multiple listeners
    const timeout = setTimeout(() => {
      this.processStatusChange(request);
      this.processingTimeouts.delete(requestId);
    }, 100); // 100ms debounce

    this.processingTimeouts.set(requestId, timeout);
  }

  /**
   * Process the actual status change after debouncing
   */
  private processStatusChange(request: RequestModel): void {
    const requestId = request.id;
    const currentStatus = request.status;
    const previousStatus = this.requestStatusCache.get(requestId);

    // Only create notification if status actually changed (not first time seeing this request)
    if (previousStatus !== undefined && previousStatus !== currentStatus) {
      const notification = this.createNotification(request, previousStatus, currentStatus);

      this.addNotification(notification);
      this.notifyCallbacks(notification);
    }

    // Update cache after checking for changes
    this.requestStatusCache.set(requestId, currentStatus);
  }

  /**
   * Create a notification object
   */
  private createNotification(
    request: RequestModel,
    oldStatus: RequestStatus,
    newStatus: RequestStatus,
  ): StatusChangeNotification {
    const priority = this.getNotificationPriority(oldStatus, newStatus);
    const message = this.getNotificationMessage(request, oldStatus, newStatus);

    return {
      id: `${request.id}_${Date.now()}`,
      requestId: request.id,
      oldStatus,
      newStatus,
      request,
      timestamp: new Date(),
      read: false,
      priority,
      message,
    };
  }

  /**
   * Determine notification priority based on status change
   */
  private getNotificationPriority(
    oldStatus: RequestStatus,
    newStatus: RequestStatus,
  ): 'low' | 'medium' | 'high' {
    // High priority changes
    if (
      newStatus === RequestStatus.CANCELLED ||
      newStatus === RequestStatus.REFUSED ||
      (oldStatus === RequestStatus.IN_PROGRESS && newStatus === RequestStatus.COMPLETED)
    ) {
      return 'high';
    }

    // Medium priority changes
    if (
      newStatus === RequestStatus.IN_PROGRESS ||
      newStatus === RequestStatus.APPROVED ||
      (oldStatus === RequestStatus.PAYMENT_PENDING && newStatus === RequestStatus.PENDING)
    ) {
      return 'medium';
    }

    // Low priority changes
    return 'low';
  }

  /**
   * Generate notification message
   */
  private getNotificationMessage(
    request: RequestModel,
    oldStatus: RequestStatus,
    newStatus: RequestStatus,
  ): string {
    const serviceName =
      typeof request.service_name === 'string'
        ? request.service_name
        : request.service_name?.en || 'Service';

    const statusMessages: Record<RequestStatus, string> = {
      [RequestStatus.PAYMENT_PENDING]: 'Payment pending',
      [RequestStatus.PENDING]: 'Pending assignment',
      [RequestStatus.APPROVED]: 'Approved and assigned',
      [RequestStatus.IN_PROGRESS]: 'In progress',
      [RequestStatus.COMPLETED]: 'Completed',
      [RequestStatus.CANCELLED]: 'Cancelled',
      [RequestStatus.REFUSED]: 'Refused',
    };

    return `${serviceName} request status changed from ${statusMessages[oldStatus]} to ${statusMessages[newStatus]}`;
  }

  /**
   * Add notification to the list
   */
  private addNotification(notification: StatusChangeNotification): void {
    this.notifications.unshift(notification);

    // Keep only the last 100 notifications
    if (this.notifications.length > 100) {
      this.notifications = this.notifications.slice(0, 100);
    }
  }

  /**
   * Notify all registered callbacks
   */
  private notifyCallbacks(notification: StatusChangeNotification): void {
    this.callbacks.forEach((callback) => {
      try {
        callback(notification);
      } catch (error) {
        console.error('Error in notification callback:', error);
      }
    });
  }

  /**
   * Subscribe to notifications
   */
  subscribe(callback: NotificationCallback): () => void {
    this.callbacks.add(callback);

    // Return unsubscribe function
    return () => {
      this.callbacks.delete(callback);
    };
  }

  /**
   * Get all notifications
   */
  getNotifications(): StatusChangeNotification[] {
    return [...this.notifications];
  }

  /**
   * Get unread notifications
   */
  getUnreadNotifications(): StatusChangeNotification[] {
    return this.notifications.filter((n) => !n.read);
  }

  /**
   * Get notifications by priority
   */
  getNotificationsByPriority(priority: 'low' | 'medium' | 'high'): StatusChangeNotification[] {
    return this.notifications.filter((n) => n.priority === priority);
  }

  /**
   * Mark notification as read
   */
  markAsRead(notificationId: string): void {
    const notification = this.notifications.find((n) => n.id === notificationId);
    if (notification) {
      notification.read = true;
    }
  }

  /**
   * Mark all notifications as read
   */
  markAllAsRead(): void {
    this.notifications.forEach((n) => (n.read = true));
  }

  /**
   * Clear all notifications
   */
  clearNotifications(): void {
    this.notifications = [];
  }

  /**
   * Clear old notifications (older than specified days)
   */
  clearOldNotifications(daysOld: number = 7): void {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    this.notifications = this.notifications.filter((n) => n.timestamp > cutoffDate);
  }

  /**
   * Stop all monitoring
   */
  stopAllMonitoring(): void {
    this.activeListeners.forEach((unsubscribe) => unsubscribe());
    this.activeListeners.clear();
    this.requestStatusCache.clear();

    // Clear all pending timeouts
    this.processingTimeouts.forEach((timeout) => clearTimeout(timeout));
    this.processingTimeouts.clear();
  }

  /**
   * Get monitoring statistics
   */
  getStats(): {
    activeListeners: number;
    totalNotifications: number;
    unreadNotifications: number;
    notificationsByPriority: Record<string, number>;
  } {
    const unread = this.getUnreadNotifications().length;
    const byPriority = {
      high: this.getNotificationsByPriority('high').length,
      medium: this.getNotificationsByPriority('medium').length,
      low: this.getNotificationsByPriority('low').length,
    };

    return {
      activeListeners: this.activeListeners.size,
      totalNotifications: this.notifications.length,
      unreadNotifications: unread,
      notificationsByPriority: byPriority,
    };
  }
}

// Export singleton instance
export default new StatusNotificationService();
export { StatusNotificationService };
