/**
 * Utility for handling retries with exponential backoff
 */

export interface RetryOptions {
  maxRetries?: number; // Maximum number of retry attempts
  initialDelay?: number; // Initial delay in milliseconds
  maxDelay?: number; // Maximum delay in milliseconds
  backoffFactor?: number; // Multiplier for each subsequent retry
  retryableErrors?: Array<string | RegExp>; // Error types or patterns to retry
}

export interface RetryResult<T> {
  success: boolean;
  result?: T;
  error?: Error;
  attempts: number;
}

/**
 * Default retry options
 */
export const DEFAULT_RETRY_OPTIONS: RetryOptions = {
  maxRetries: 5,
  initialDelay: 500,
  maxDelay: 30000,
  backoffFactor: 2,
  retryableErrors: [
    'network error',
    'timeout',
    'unavailable',
    'internal',
    'resource exhausted',
    /failed to fetch/i,
    /network request failed/i,
    /operation-retry/i,
    /quota exceeded/i,
    /econnreset/i,
    /econnrefused/i,
    /etimedout/i,
  ],
};

/**
 * Checks if an error is retryable based on the provided options
 */
export function isRetryableError(
  error: Error,
  options: RetryOptions = DEFAULT_RETRY_OPTIONS,
): boolean {
  const errorMessage = error.message.toLowerCase();
  const errorName = error.name.toLowerCase();
  const errorString = `${errorName}: ${errorMessage}`;

  // Network errors are always retryable
  if (error instanceof TypeError && errorMessage.includes('network')) {
    return true;
  }

  // Check against the list of retryable errors
  return (
    options.retryableErrors?.some((pattern) => {
      if (pattern instanceof RegExp) {
        return pattern.test(errorString);
      }
      return errorString.includes(pattern.toLowerCase());
    }) ?? false
  );
}

/**
 * Calculate delay for the next retry attempt using exponential backoff
 */
export function calculateBackoffDelay(
  attempt: number,
  options: RetryOptions = DEFAULT_RETRY_OPTIONS,
): number {
  const initialDelay = options.initialDelay || DEFAULT_RETRY_OPTIONS.initialDelay!;
  const backoffFactor = options.backoffFactor || DEFAULT_RETRY_OPTIONS.backoffFactor!;
  const maxDelay = options.maxDelay || DEFAULT_RETRY_OPTIONS.maxDelay!;

  // Calculate exponential backoff with jitter
  const exponentialDelay = initialDelay * Math.pow(backoffFactor, attempt);
  const jitter = Math.random() * 0.3 * exponentialDelay; // Add up to 30% jitter
  const delay = Math.min(exponentialDelay + jitter, maxDelay);

  return delay;
}

/**
 * Retry a function with exponential backoff
 * @param fn Function to retry
 * @param options Retry options
 * @returns Promise with the result and retry metadata
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  options: RetryOptions = DEFAULT_RETRY_OPTIONS,
): Promise<RetryResult<T>> {
  const maxRetries = options.maxRetries || DEFAULT_RETRY_OPTIONS.maxRetries!;
  let attempts = 0;

  while (attempts <= maxRetries) {
    try {
      attempts++;
      const result = await fn();
      return {
        success: true,
        result,
        attempts,
      };
    } catch (error: unknown) {
      // If this was our last attempt or the error is not retryable, throw
      const errorInstance = error instanceof Error ? error : new Error(String(error));
      if (attempts > maxRetries || !isRetryableError(errorInstance, options)) {
        return {
          success: false,
          error: errorInstance,
          attempts,
        };
      }

      // Calculate delay for the next retry
      const delay = calculateBackoffDelay(attempts, options);

      // Log retry attempt
      console.warn(
        `Retry attempt ${attempts}/${maxRetries} after ${Math.round(delay)}ms due to error: ${error.message}`,
      );

      // Wait before the next retry
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }

  // This should never happen due to the return in the catch block
  return {
    success: false,
    error: new Error('Maximum retry attempts exceeded'),
    attempts,
  };
}

/**
 * Decorator function to add retry capability to any async method
 * @param options Retry options
 * @returns Decorated function with retry logic
 */
export function withRetryDecorator<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  options: RetryOptions = DEFAULT_RETRY_OPTIONS,
): (...args: T) => Promise<R> {
  return async (...args: T): Promise<R> => {
    const retryResult = await withRetry(() => fn(...args), options);

    if (!retryResult.success) {
      throw retryResult.error;
    }

    return retryResult.result!;
  };
}
