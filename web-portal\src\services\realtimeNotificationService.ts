import { toast } from '../components/ui/use-toast';
import { statusNotificationService } from './statusNotificationService';
import { realtimeStatusService } from './realtimeStatusService';
import { RequestModel, RequestStatus } from '../types/request';

// Notification types
export type NotificationType =
  | 'request_created'
  | 'request_updated'
  | 'request_completed'
  | 'request_cancelled'
  | 'chat_message'
  | 'payment_received'
  | 'technician_assigned'
  | 'system_alert'
  | 'maintenance_mode';

export type NotificationPriority = 'low' | 'medium' | 'high' | 'urgent';

export interface RealtimeNotification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  priority: NotificationPriority;
  timestamp: Date;
  read: boolean;
  data?: Record<string, any>;
  userId?: string;
  requestId?: string;
  sound?: boolean;
  persistent?: boolean;
}

export interface NotificationSettings {
  enableToasts: boolean;
  enableSounds: boolean;
  enableDesktopNotifications: boolean;
  soundVolume: number;
  toastDuration: number;
  enabledTypes: NotificationType[];
  quietHours: {
    enabled: boolean;
    start: string; // HH:MM format
    end: string; // HH:MM format
  };
}

// Sound files for different notification types
const NOTIFICATION_SOUNDS = {
  request_created: '/sounds/new-request.mp3',
  request_updated: '/sounds/update.mp3',
  request_completed: '/sounds/success.mp3',
  request_cancelled: '/sounds/cancelled.mp3',
  chat_message: '/sounds/message.mp3',
  payment_received: '/sounds/payment.mp3',
  technician_assigned: '/sounds/assigned.mp3',
  system_alert: '/sounds/alert.mp3',
  maintenance_mode: '/sounds/warning.mp3',
} as const;

class RealtimeNotificationService {
  private notifications: RealtimeNotification[] = [];
  private callbacks: Set<(notification: RealtimeNotification) => void> = new Set();
  private settings: NotificationSettings;
  private audioContext: AudioContext | null = null;
  private soundBuffers: Map<string, AudioBuffer> = new Map();
  private unsubscribeFunctions: (() => void)[] = [];
  private isInitialized = false;
  private notificationPermission: NotificationPermission = 'default';

  constructor() {
    this.settings = this.loadSettings();
    this.initializeAudioContext();
    this.requestNotificationPermission();
  }

  /**
   * Initialize the notification service
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Subscribe to status notifications
      const statusUnsubscribe = statusNotificationService.subscribe((statusNotification) => {
        this.handleStatusNotification(statusNotification);
      });
      this.unsubscribeFunctions.push(statusUnsubscribe);

      // Load sound files
      await this.preloadSounds();

      // Listen for admin notifications (for technicians/admins)
      this.listenToAdminNotifications();

      // Listen for user-specific notifications
      this.listenToUserNotifications();

      this.isInitialized = true;
      console.warn('RealtimeNotificationService initialized successfully');
    } catch (error) {
      console.error('Failed to initialize RealtimeNotificationService:', error);
    }
  }

  /**
   * Handle status change notifications from the existing service
   */
  private handleStatusNotification(statusNotification: any): void {
    const notification: RealtimeNotification = {
      id: statusNotification.id,
      type: this.mapStatusToNotificationType(statusNotification.type),
      title: 'Request Status Update',
      message: statusNotification.message,
      priority: statusNotification.priority,
      timestamp: statusNotification.timestamp,
      read: false,
      requestId: statusNotification.requestId,
      sound: true,
      data: statusNotification,
    };

    this.addNotification(notification);
  }

  /**
   * Map status notification types to our notification types
   */
  private mapStatusToNotificationType(statusType: string): NotificationType {
    switch (statusType) {
      case 'status_change':
        return 'request_updated';
      case 'request_completed':
        return 'request_completed';
      case 'request_cancelled':
        return 'request_cancelled';
      default:
        return 'request_updated';
    }
  }

  /**
   * Listen to admin notifications from Firestore
   */
  private listenToAdminNotifications(): void {
    // This would connect to Firestore admin_notifications collection
    // For now, we'll simulate this functionality
    console.warn('Listening to admin notifications...');
  }

  /**
   * Listen to user-specific notifications
   */
  private listenToUserNotifications(): void {
    // This would connect to user-specific notifications
    console.warn('Listening to user notifications...');
  }

  /**
   * Add a new notification
   */
  addNotification(notification: RealtimeNotification): void {
    // Check if notification type is enabled
    if (!this.settings.enabledTypes.includes(notification.type)) {
      return;
    }

    // Check quiet hours
    if (this.isQuietHours()) {
      notification.sound = false;
    }

    // Add to notifications list
    this.notifications.unshift(notification);

    // Keep only last 100 notifications
    if (this.notifications.length > 100) {
      this.notifications = this.notifications.slice(0, 100);
    }

    // Show toast notification
    if (this.settings.enableToasts) {
      this.showToastNotification(notification);
    }

    // Play sound
    if (this.settings.enableSounds && notification.sound) {
      this.playNotificationSound(notification.type);
    }

    // Show desktop notification
    if (this.settings.enableDesktopNotifications && this.notificationPermission === 'granted') {
      this.showDesktopNotification(notification);
    }

    // Notify callbacks
    this.notifyCallbacks(notification);
  }

  /**
   * Show toast notification
   */
  private showToastNotification(notification: RealtimeNotification): void {
    const variant = this.getToastVariant(notification.priority);

    toast({
      title: notification.title,
      description: notification.message,
      variant,
      duration: notification.persistent ? undefined : this.settings.toastDuration,
    });
  }

  /**
   * Get toast variant based on priority
   */
  private getToastVariant(priority: NotificationPriority): 'default' | 'destructive' {
    switch (priority) {
      case 'urgent':
      case 'high':
        return 'destructive';
      default:
        return 'default';
    }
  }

  /**
   * Show desktop notification
   */
  private showDesktopNotification(notification: RealtimeNotification): void {
    if (!('Notification' in window) || Notification.permission !== 'granted') {
      return;
    }

    const desktopNotification = new Notification(notification.title, {
      body: notification.message,
      icon: '/favicon.ico',
      badge: '/favicon.ico',
      tag: notification.id,
      requireInteraction: notification.priority === 'urgent',
    });

    // Auto-close after 5 seconds unless it's urgent
    if (notification.priority !== 'urgent') {
      setTimeout(() => {
        desktopNotification.close();
      }, 5000);
    }

    desktopNotification.onclick = () => {
      window.focus();
      desktopNotification.close();

      // Navigate to relevant page if requestId is provided
      if (notification.requestId) {
        window.location.href = `/requests/${notification.requestId}`;
      }
    };
  }

  /**
   * Play notification sound
   */
  private async playNotificationSound(type: NotificationType): Promise<void> {
    if (!this.audioContext || !this.settings.enableSounds) return;

    try {
      const soundBuffer = this.soundBuffers.get(type);
      if (!soundBuffer) return;

      const source = this.audioContext.createBufferSource();
      const gainNode = this.audioContext.createGain();

      source.buffer = soundBuffer;
      gainNode.gain.value = this.settings.soundVolume;

      source.connect(gainNode);
      gainNode.connect(this.audioContext.destination);

      source.start();
    } catch (error) {
      console.warn('Failed to play notification sound:', error);
    }
  }

  /**
   * Initialize audio context
   */
  private initializeAudioContext(): void {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    } catch (error) {
      console.warn('Audio context not supported:', error);
    }
  }

  /**
   * Preload sound files
   */
  private async preloadSounds(): Promise<void> {
    if (!this.audioContext) return;

    // Create a simple beep sound as fallback
    const createBeepBuffer = (frequency: number = 800, duration: number = 0.2): AudioBuffer => {
      const sampleRate = this.audioContext!.sampleRate;
      const frameCount = sampleRate * duration;
      const buffer = this.audioContext!.createBuffer(1, frameCount, sampleRate);
      const channelData = buffer.getChannelData(0);

      for (let i = 0; i < frameCount; i++) {
        channelData[i] = Math.sin((2 * Math.PI * frequency * i) / sampleRate) * 0.1;
      }

      return buffer;
    };

    // Create different beep sounds for different notification types
    const beepSounds = {
      request_created: createBeepBuffer(800, 0.3),
      request_updated: createBeepBuffer(600, 0.2),
      request_completed: createBeepBuffer(1000, 0.4),
      request_cancelled: createBeepBuffer(400, 0.3),
      chat_message: createBeepBuffer(900, 0.15),
      payment_received: createBeepBuffer(1200, 0.5),
      technician_assigned: createBeepBuffer(700, 0.25),
      system_alert: createBeepBuffer(500, 0.6),
      maintenance_mode: createBeepBuffer(300, 0.8),
    };

    // Try to load actual sound files, fallback to beep sounds
    const soundPromises = Object.entries(NOTIFICATION_SOUNDS).map(async ([type, url]) => {
      try {
        const response = await fetch(url);
        if (response.ok) {
          const arrayBuffer = await response.arrayBuffer();
          const audioBuffer = await this.audioContext!.decodeAudioData(arrayBuffer);
          this.soundBuffers.set(type, audioBuffer);
        } else {
          // Use beep sound as fallback
          this.soundBuffers.set(type, beepSounds[type as keyof typeof beepSounds]);
        }
      } catch (error) {
        console.warn(`Failed to load sound for ${type}, using beep fallback:`, error);
        // Use beep sound as fallback
        this.soundBuffers.set(type, beepSounds[type as keyof typeof beepSounds]);
      }
    });

    await Promise.allSettled(soundPromises);
  }

  /**
   * Request notification permission
   */
  private async requestNotificationPermission(): Promise<void> {
    if (!('Notification' in window)) {
      console.warn('Desktop notifications not supported');
      return;
    }

    if (Notification.permission === 'default') {
      this.notificationPermission = await Notification.requestPermission();
    } else {
      this.notificationPermission = Notification.permission;
    }
  }

  /**
   * Check if current time is within quiet hours
   */
  private isQuietHours(): boolean {
    if (!this.settings.quietHours.enabled) return false;

    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;

    const { start, end } = this.settings.quietHours;

    if (start <= end) {
      return currentTime >= start && currentTime <= end;
    } else {
      // Quiet hours span midnight
      return currentTime >= start || currentTime <= end;
    }
  }

  /**
   * Notify all callbacks
   */
  private notifyCallbacks(notification: RealtimeNotification): void {
    this.callbacks.forEach((callback) => {
      try {
        callback(notification);
      } catch (error) {
        console.error('Error in notification callback:', error);
      }
    });
  }

  /**
   * Subscribe to notifications
   */
  subscribe(callback: (notification: RealtimeNotification) => void): () => void {
    this.callbacks.add(callback);

    return () => {
      this.callbacks.delete(callback);
    };
  }

  /**
   * Get all notifications
   */
  getNotifications(): RealtimeNotification[] {
    return [...this.notifications];
  }

  /**
   * Get unread notifications
   */
  getUnreadNotifications(): RealtimeNotification[] {
    return this.notifications.filter((n) => !n.read);
  }

  /**
   * Mark notification as read
   */
  markAsRead(notificationId: string): void {
    const notification = this.notifications.find((n) => n.id === notificationId);
    if (notification) {
      notification.read = true;
    }
  }

  /**
   * Mark all notifications as read
   */
  markAllAsRead(): void {
    this.notifications.forEach((n) => (n.read = true));
  }

  /**
   * Clear all notifications
   */
  clearAllNotifications(): void {
    this.notifications = [];
  }

  /**
   * Update settings
   */
  updateSettings(newSettings: Partial<NotificationSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    this.saveSettings();
  }

  /**
   * Get current settings
   */
  getSettings(): NotificationSettings {
    return { ...this.settings };
  }

  /**
   * Load settings from localStorage
   */
  private loadSettings(): NotificationSettings {
    try {
      const saved = localStorage.getItem('notification-settings');
      if (saved) {
        return { ...this.getDefaultSettings(), ...JSON.parse(saved) };
      }
    } catch (error) {
      console.warn('Failed to load notification settings:', error);
    }

    return this.getDefaultSettings();
  }

  /**
   * Save settings to localStorage
   */
  private saveSettings(): void {
    try {
      localStorage.setItem('notification-settings', JSON.stringify(this.settings));
    } catch (error) {
      console.warn('Failed to save notification settings:', error);
    }
  }

  /**
   * Get default settings
   */
  private getDefaultSettings(): NotificationSettings {
    return {
      enableToasts: true,
      enableSounds: true,
      enableDesktopNotifications: true,
      soundVolume: 0.5,
      toastDuration: 5000,
      enabledTypes: [
        'request_created',
        'request_updated',
        'request_completed',
        'request_cancelled',
        'chat_message',
        'payment_received',
        'technician_assigned',
        'system_alert',
        'maintenance_mode',
      ],
      quietHours: {
        enabled: false,
        start: '22:00',
        end: '08:00',
      },
    };
  }

  /**
   * Test notification (for settings page)
   */
  testNotification(type: NotificationType = 'system_alert'): void {
    const testNotification: RealtimeNotification = {
      id: `test-${Date.now()}`,
      type,
      title: 'Test Notification',
      message: 'This is a test notification to verify your settings.',
      priority: 'medium',
      timestamp: new Date(),
      read: false,
      sound: true,
    };

    this.addNotification(testNotification);
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.unsubscribeFunctions.forEach((unsubscribe) => unsubscribe());
    this.unsubscribeFunctions = [];
    this.callbacks.clear();
    this.notifications = [];

    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    this.isInitialized = false;
  }
}

// Create singleton instance
export const realtimeNotificationService = new RealtimeNotificationService();
