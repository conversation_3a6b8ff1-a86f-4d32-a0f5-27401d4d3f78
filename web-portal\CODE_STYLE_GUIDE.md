# Code Style Guide

## Overview

This document outlines the coding standards and style conventions for the Mr. Tech Web Portal project. Following these guidelines ensures consistency, maintainability, and readability across the codebase.

## General Principles

1. **Consistency**: Follow established patterns throughout the codebase
2. **Readability**: Write code that is easy to understand and maintain
3. **Type Safety**: Leverage TypeScript's type system for better code quality
4. **Performance**: Consider performance implications of coding decisions
5. **Accessibility**: Ensure components are accessible by default

## File Naming Conventions

### Components

- **React Components**: PascalCase (e.g., `UserProfile.tsx`, `RequestDetailsDialog.tsx`)
- **UI Components**: PascalCase (e.g., `Button.tsx`, `Dialog.tsx`)
- **Page Components**: PascalCase with "Page" suffix (e.g., `DashboardPage.tsx`)

### Services and Utilities

- **Services**: camelCase with "Service" suffix (e.g., `authService.ts`, `requestService.ts`)
- **Utilities**: camelCase (e.g., `validation.ts`, `logger.ts`)
- **Hooks**: camelCase with "use" prefix (e.g., `useAuth.ts`, `usePermissions.ts`)

### Types and Interfaces

- **Type Files**: camelCase (e.g., `auth.ts`, `request.ts`)
- **Test Files**: Same as source file with `.test.ts` or `.spec.ts` suffix

## Import/Export Conventions

### Import Order

1. React and React-related imports
2. Third-party libraries
3. Internal imports (services, utils, types)
4. Relative imports (components, assets)

```typescript
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

import { Button } from '@radix-ui/react-button';
import { format } from 'date-fns';

import { authService } from '../services/authService';
import type { User } from '../types/auth';

import { Dialog } from './ui/dialog';
import './Component.css';
```

### Import Paths

- Use relative imports for local files: `'../../lib/utils'`
- Avoid mixing `@/` alias with relative imports in the same file
- Group imports with blank lines between categories

### Export Patterns

- **Default exports**: For main component/service of the file
- **Named exports**: For utilities, types, and secondary components
- **Consistent pattern**: Use either default or named exports consistently within similar file types

## TypeScript Conventions

### Interface Naming

- Use descriptive names without "I" prefix
- Add appropriate suffixes for clarity:
  - `Props` for component props
  - `State` for component state
  - `Config` for configuration objects
  - `Options` for option objects

```typescript
// Good
interface UserProfileProps {
  user: User;
  onEdit: () => void;
}

interface DatabaseConfig {
  host: string;
  port: number;
}

// Avoid
interface IUserProfile {
  user: User;
}
```

### Type Definitions

- Use `interface` for object shapes
- Use `type` for unions, primitives, and computed types
- Use `enum` for constants with meaningful names

```typescript
// Interfaces for object shapes
interface User {
  id: string;
  name: string;
  role: UserRole;
}

// Types for unions and computed types
type UserRole = 'admin' | 'technician' | 'customer';
type UserWithPermissions = User & { permissions: Permission[] };

// Enums for constants
enum RequestStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
}
```

### Function Declarations

- Use arrow functions for inline functions and callbacks
- Use function declarations for main component functions
- Use consistent parameter and return type annotations

```typescript
// Component functions
const UserProfile: React.FC<UserProfileProps> = ({ user, onEdit }) => {
  // Component logic
};

// Service functions
export const getUserById = async (id: string): Promise<User | null> => {
  // Service logic
};

// Event handlers
const handleSubmit = (event: React.FormEvent) => {
  event.preventDefault();
  // Handle submit
};
```

## React Component Conventions

### Component Structure

```typescript
import React, { useState, useEffect } from 'react';

import type { ComponentProps } from './types';

interface ComponentNameProps {
  // Props definition
}

const ComponentName: React.FC<ComponentNameProps> = ({
  prop1,
  prop2,
  ...props
}) => {
  // Hooks
  const [state, setState] = useState();

  // Effects
  useEffect(() => {
    // Effect logic
  }, []);

  // Event handlers
  const handleEvent = () => {
    // Handler logic
  };

  // Render
  return (
    <div>
      {/* JSX */}
    </div>
  );
};

export default ComponentName;
```

### JSX Conventions

- Use single quotes for JSX attributes
- Use meaningful prop names
- Prefer explicit boolean values over implicit ones
- Use fragments instead of unnecessary divs

```typescript
// Good
<Button
  variant='primary'
  disabled={false}
  onClick={handleClick}
>
  Submit
</Button>

// Avoid
<Button variant="primary" disabled onClick={handleClick}>
  Submit
</Button>
```

## Styling Conventions

### CSS Classes

- Use Tailwind CSS utility classes
- Group related classes together
- Use the `cn()` utility for conditional classes

```typescript
import { cn } from '../../lib/utils';

const className = cn(
  // Base styles
  'flex items-center justify-center',
  // Spacing
  'px-4 py-2 m-2',
  // Colors and states
  'bg-primary text-white hover:bg-primary/90',
  // Conditional styles
  isActive && 'ring-2 ring-primary',
  disabled && 'opacity-50 cursor-not-allowed',
);
```

## Error Handling

### Consistent Error Patterns

- Use try-catch blocks for async operations
- Provide meaningful error messages
- Log errors appropriately
- Handle errors at the appropriate level

```typescript
try {
  const result = await apiCall();
  return result;
} catch (error) {
  console.error('Failed to fetch data:', error);
  throw new Error('Unable to load user data. Please try again.');
}
```

## Performance Considerations

### Optimization Patterns

- Use `React.memo()` for expensive components
- Implement proper dependency arrays in hooks
- Avoid inline object/function creation in render
- Use lazy loading for large components

```typescript
// Memoized component
const ExpensiveComponent = React.memo<Props>(({ data }) => {
  return <div>{/* Expensive rendering */}</div>;
});

// Proper hook dependencies
useEffect(() => {
  fetchData();
}, [userId, filters]); // Include all dependencies
```

## Documentation

### Code Comments

- Use JSDoc for public APIs
- Add inline comments for complex logic
- Keep comments up-to-date with code changes

```typescript
/**
 * Validates user input and returns formatted data
 * @param input - Raw user input
 * @param options - Validation options
 * @returns Formatted and validated data
 */
export const validateInput = (input: string, options: ValidationOptions): ValidatedData => {
  // Complex validation logic here
  return validatedData;
};
```

## Testing Conventions

### Test File Structure

- Place tests in `__tests__` directories or alongside source files
- Use descriptive test names
- Group related tests with `describe` blocks
- Follow AAA pattern (Arrange, Act, Assert)

```typescript
describe('UserService', () => {
  describe('getUserById', () => {
    it('should return user when valid ID is provided', async () => {
      // Arrange
      const userId = 'valid-id';

      // Act
      const result = await getUserById(userId);

      // Assert
      expect(result).toBeDefined();
      expect(result.id).toBe(userId);
    });
  });
});
```

## Tools and Automation

### ESLint Configuration

- Follow the project's ESLint rules
- Run `npm run lint` before committing
- Use `npm run lint:fix` to auto-fix issues

### Prettier Configuration

- Code formatting is handled automatically
- Run `npm run format` to format all files
- Configure your editor to format on save

### Pre-commit Hooks

- Linting and formatting run automatically on commit
- Tests should pass before committing
- Type checking is enforced

## Migration and Refactoring

When updating existing code to follow these standards:

1. **Gradual Migration**: Update files as you work on them
2. **Consistency First**: Prioritize consistency within a file/module
3. **Test Coverage**: Ensure tests pass after style changes
4. **Documentation**: Update documentation when changing APIs

## Enforcement

These standards are enforced through:

- ESLint rules and configuration
- Prettier formatting
- Code review process
- Automated CI/CD checks
- Team code review guidelines

For questions or clarifications about these standards, please refer to the team lead or create an issue in the project repository.
