import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'dart:math' as math;

/// WCAG (Web Content Accessibility Guidelines) compliance utilities
/// Provides tools to ensure the app meets WCAG 2.1 AA standards
class WCAGCompliance {
  // Private constructor to prevent instantiation
  WCAGCompliance._();

  /// WCAG contrast ratio requirements
  static const double contrastRatioAA = 4.5;
  static const double contrastRatioAAA = 7.0;
  static const double contrastRatioLargeTextAA = 3.0;
  static const double contrastRatioLargeTextAAA = 4.5;

  /// Minimum touch target size (44x44 dp for WCAG AA)
  static const double minTouchTargetSize = 44.0;

  /// Maximum line length for readability
  static const int maxLineLength = 80;

  /// Validate color contrast ratio
  static ContrastValidationResult validateContrast({
    required Color foreground,
    required Color background,
    required WCAGLevel level,
    bool isLargeText = false,
  }) {
    final contrastRatio = calculateContrastRatio(foreground, background);
    final requiredRatio = _getRequiredContrastRatio(level, isLargeText);
    
    return ContrastValidationResult(
      contrastRatio: contrastRatio,
      requiredRatio: requiredRatio,
      passes: contrastRatio >= requiredRatio,
      level: level,
      isLargeText: isLargeText,
    );
  }

  /// Calculate contrast ratio between two colors
  static double calculateContrastRatio(Color color1, Color color2) {
    final luminance1 = calculateRelativeLuminance(color1);
    final luminance2 = calculateRelativeLuminance(color2);
    
    final lighter = math.max(luminance1, luminance2);
    final darker = math.min(luminance1, luminance2);
    
    return (lighter + 0.05) / (darker + 0.05);
  }

  /// Calculate relative luminance of a color
  static double calculateRelativeLuminance(Color color) {
    final r = _linearizeColorComponent(color.red / 255.0);
    final g = _linearizeColorComponent(color.green / 255.0);
    final b = _linearizeColorComponent(color.blue / 255.0);
    
    return 0.2126 * r + 0.7152 * g + 0.0722 * b;
  }

  /// Linearize color component for luminance calculation
  static double _linearizeColorComponent(double component) {
    if (component <= 0.03928) {
      return component / 12.92;
    } else {
      return math.pow((component + 0.055) / 1.055, 2.4).toDouble();
    }
  }

  /// Get required contrast ratio for WCAG level
  static double _getRequiredContrastRatio(WCAGLevel level, bool isLargeText) {
    switch (level) {
      case WCAGLevel.AA:
        return isLargeText ? contrastRatioLargeTextAA : contrastRatioAA;
      case WCAGLevel.AAA:
        return isLargeText ? contrastRatioLargeTextAAA : contrastRatioAAA;
    }
  }

  /// Validate touch target size
  static TouchTargetValidationResult validateTouchTarget({
    required double width,
    required double height,
    WCAGLevel level = WCAGLevel.AA,
  }) {
    final minSize = level == WCAGLevel.AAA ? 48.0 : minTouchTargetSize;
    final passes = width >= minSize && height >= minSize;
    
    return TouchTargetValidationResult(
      width: width,
      height: height,
      minSize: minSize,
      passes: passes,
      level: level,
    );
  }

  /// Validate text readability
  static TextReadabilityResult validateTextReadability({
    required String text,
    required double fontSize,
    required double lineHeight,
    required double lineLength,
  }) {
    final issues = <String>[];
    
    // Check font size
    if (fontSize < 12.0) {
      issues.add('Font size too small (minimum 12sp recommended)');
    }
    
    // Check line height
    final recommendedLineHeight = fontSize * 1.5;
    if (lineHeight < recommendedLineHeight) {
      issues.add('Line height too small (minimum 1.5x font size recommended)');
    }
    
    // Check line length
    if (lineLength > maxLineLength) {
      issues.add('Line length too long (maximum $maxLineLength characters recommended)');
    }
    
    // Check for proper sentence structure
    if (text.isNotEmpty && !text.trim().endsWith('.') && 
        !text.trim().endsWith('!') && !text.trim().endsWith('?')) {
      issues.add('Text should end with proper punctuation for screen readers');
    }
    
    return TextReadabilityResult(
      text: text,
      fontSize: fontSize,
      lineHeight: lineHeight,
      lineLength: lineLength,
      issues: issues,
      passes: issues.isEmpty,
    );
  }

  /// Create WCAG compliant color scheme
  static ColorScheme createCompliantColorScheme({
    required Brightness brightness,
    Color? primaryColor,
    WCAGLevel level = WCAGLevel.AA,
  }) {
    final baseScheme = brightness == Brightness.light
        ? const ColorScheme.light()
        : const ColorScheme.dark();
    
    // Use provided primary color or default
    final primary = primaryColor ?? baseScheme.primary;
    
    // Generate compliant colors
    final surface = brightness == Brightness.light ? Colors.white : const Color(0xFF121212);
    final onSurface = brightness == Brightness.light ? Colors.black : Colors.white;
    
    // Ensure primary color meets contrast requirements
    final primaryContrast = validateContrast(
      foreground: Colors.white,
      background: primary,
      level: level,
    );
    
    final adjustedPrimary = primaryContrast.passes
        ? primary
        : _adjustColorForContrast(primary, Colors.white, level, false);
    
    return baseScheme.copyWith(
      primary: adjustedPrimary,
      surface: surface,
      onSurface: onSurface,
    );
  }

  /// Adjust color to meet WCAG contrast requirements
  static Color _adjustColorForContrast(
    Color color,
    Color background,
    WCAGLevel level,
    bool isLargeText,
  ) {
    final requiredRatio = _getRequiredContrastRatio(level, isLargeText);
    final currentRatio = calculateContrastRatio(color, background);
    
    if (currentRatio >= requiredRatio) {
      return color;
    }
    
    // Determine if we need to lighten or darken
    final backgroundLuminance = calculateRelativeLuminance(background);
    final shouldDarken = backgroundLuminance > 0.5;
    
    Color adjustedColor = color;
    double testRatio = currentRatio;
    
    // Iteratively adjust the color
    for (int i = 0; i < 20 && testRatio < requiredRatio; i++) {
      if (shouldDarken) {
        adjustedColor = _darkenColor(adjustedColor, 0.1);
      } else {
        adjustedColor = _lightenColor(adjustedColor, 0.1);
      }
      testRatio = calculateContrastRatio(adjustedColor, background);
    }
    
    return adjustedColor;
  }

  /// Darken a color
  static Color _darkenColor(Color color, double amount) {
    final hsl = HSLColor.fromColor(color);
    final darkened = hsl.withLightness(
      (hsl.lightness - amount).clamp(0.0, 1.0),
    );
    return darkened.toColor();
  }

  /// Lighten a color
  static Color _lightenColor(Color color, double amount) {
    final hsl = HSLColor.fromColor(color);
    final lightened = hsl.withLightness(
      (hsl.lightness + amount).clamp(0.0, 1.0),
    );
    return lightened.toColor();
  }

  /// Validate semantic structure
  static SemanticValidationResult validateSemanticStructure({
    required List<SemanticElement> elements,
  }) {
    final issues = <String>[];
    
    // Check for proper heading hierarchy
    int lastHeadingLevel = 0;
    for (final element in elements) {
      if (element.type == SemanticElementType.heading) {
        if (element.level != null) {
          if (element.level! > lastHeadingLevel + 1) {
            issues.add('Heading level ${element.level} skips levels (previous was $lastHeadingLevel)');
          }
          lastHeadingLevel = element.level!;
        }
      }
    }
    
    // Check for alt text on images
    final images = elements.where((e) => e.type == SemanticElementType.image);
    for (final image in images) {
      if (image.altText == null || image.altText!.isEmpty) {
        issues.add('Image missing alt text: ${image.identifier}');
      }
    }
    
    // Check for form labels
    final formElements = elements.where((e) => e.type == SemanticElementType.formField);
    for (final formElement in formElements) {
      if (formElement.label == null || formElement.label!.isEmpty) {
        issues.add('Form field missing label: ${formElement.identifier}');
      }
    }
    
    return SemanticValidationResult(
      elements: elements,
      issues: issues,
      passes: issues.isEmpty,
    );
  }

  /// Create accessible focus indicator
  static BoxDecoration createFocusIndicator(BuildContext context) {
    final theme = Theme.of(context);
    return BoxDecoration(
      border: Border.all(
        color: theme.colorScheme.primary,
        width: 2.0,
      ),
      borderRadius: BorderRadius.circular(4.0),
    );
  }

  /// Get WCAG compliant text style
  static TextStyle getCompliantTextStyle({
    required BuildContext context,
    required double fontSize,
    FontWeight? fontWeight,
    Color? color,
    WCAGLevel level = WCAGLevel.AA,
  }) {
    final theme = Theme.of(context);
    final backgroundColor = theme.scaffoldBackgroundColor;
    final textColor = color ?? theme.textTheme.bodyLarge?.color ?? theme.colorScheme.onSurface;
    
    // Validate contrast
    final contrastResult = validateContrast(
      foreground: textColor,
      background: backgroundColor,
      level: level,
      isLargeText: fontSize >= 18.0,
    );
    
    final adjustedColor = contrastResult.passes
        ? textColor
        : _adjustColorForContrast(textColor, backgroundColor, level, fontSize >= 18.0);
    
    return TextStyle(
      fontSize: math.max(fontSize, 12.0), // Minimum font size
      fontWeight: fontWeight,
      color: adjustedColor,
      height: 1.5, // WCAG recommended line height
    );
  }
}

/// WCAG compliance levels
enum WCAGLevel { AA, AAA }

/// Semantic element types
enum SemanticElementType {
  heading,
  text,
  button,
  link,
  image,
  formField,
  list,
  listItem,
}

/// Contrast validation result
class ContrastValidationResult {
  final double contrastRatio;
  final double requiredRatio;
  final bool passes;
  final WCAGLevel level;
  final bool isLargeText;

  const ContrastValidationResult({
    required this.contrastRatio,
    required this.requiredRatio,
    required this.passes,
    required this.level,
    required this.isLargeText,
  });

  @override
  String toString() {
    return 'Contrast: ${contrastRatio.toStringAsFixed(2)}:1 '
           '(required: ${requiredRatio.toStringAsFixed(2)}:1) '
           '${passes ? 'PASS' : 'FAIL'}';
  }
}

/// Touch target validation result
class TouchTargetValidationResult {
  final double width;
  final double height;
  final double minSize;
  final bool passes;
  final WCAGLevel level;

  const TouchTargetValidationResult({
    required this.width,
    required this.height,
    required this.minSize,
    required this.passes,
    required this.level,
  });

  @override
  String toString() {
    return 'Touch target: ${width}x$height '
           '(minimum: ${minSize}x$minSize) '
           '${passes ? 'PASS' : 'FAIL'}';
  }
}

/// Text readability validation result
class TextReadabilityResult {
  final String text;
  final double fontSize;
  final double lineHeight;
  final double lineLength;
  final List<String> issues;
  final bool passes;

  const TextReadabilityResult({
    required this.text,
    required this.fontSize,
    required this.lineHeight,
    required this.lineLength,
    required this.issues,
    required this.passes,
  });
}

/// Semantic element for validation
class SemanticElement {
  final SemanticElementType type;
  final String identifier;
  final String? label;
  final String? altText;
  final int? level; // For headings
  final String? hint;

  const SemanticElement({
    required this.type,
    required this.identifier,
    this.label,
    this.altText,
    this.level,
    this.hint,
  });
}

/// Semantic validation result
class SemanticValidationResult {
  final List<SemanticElement> elements;
  final List<String> issues;
  final bool passes;

  const SemanticValidationResult({
    required this.elements,
    required this.issues,
    required this.passes,
  });
}
