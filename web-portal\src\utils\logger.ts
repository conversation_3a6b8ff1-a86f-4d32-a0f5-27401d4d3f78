/**
 * Centralized logging utility for the web portal
 * Provides different log levels and can be configured for production
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4,
}

class Logger {
  private currentLevel: LogLevel = LogLevel.DEBUG;
  private isDevelopment: boolean = import.meta.env.DEV;

  constructor() {
    // Set log level based on environment
    if (!this.isDevelopment) {
      this.currentLevel = LogLevel.WARN; // Only show warnings and errors in production
    }
  }

  setLevel(level: LogLevel): void {
    this.currentLevel = level;
  }

  private shouldLog(level: LogLevel): boolean {
    return level >= this.currentLevel;
  }

  private formatMessage(level: LogLevel, message: string, data?: any, source?: string): string {
    const timestamp = new Date().toISOString();
    const levelName = LogLevel[level];
    const sourcePrefix = source ? `[${source}]` : '';

    let formattedMessage = `${timestamp} ${levelName} ${sourcePrefix} ${message}`;

    if (data !== undefined) {
      formattedMessage += ` | Data: ${JSON.stringify(data, null, 2)}`;
    }

    return formattedMessage;
  }

  private log(level: LogLevel, message: string, data?: any, source?: string): void {
    if (!this.shouldLog(level)) {
      return;
    }

    const formattedMessage = this.formatMessage(level, message, data, source);

    switch (level) {
      case LogLevel.DEBUG:
        if (this.isDevelopment) {
          console.debug(formattedMessage);
        }
        break;
      case LogLevel.INFO:
        console.info(formattedMessage);
        break;
      case LogLevel.WARN:
        console.warn(formattedMessage);
        break;
      case LogLevel.ERROR:
        console.error(formattedMessage);
        break;
    }
  }

  debug(message: string, data?: any, source?: string): void {
    this.log(LogLevel.DEBUG, message, data, source);
  }

  info(message: string, data?: any, source?: string): void {
    this.log(LogLevel.INFO, message, data, source);
  }

  warn(message: string, data?: any, source?: string): void {
    this.log(LogLevel.WARN, message, data, source);
  }

  error(message: string, data?: any, source?: string): void {
    this.log(LogLevel.ERROR, message, data, source);
  }

  // Convenience methods for common use cases
  apiCall(method: string, url: string, data?: any): void {
    this.debug(`API ${method} ${url}`, data, 'API');
  }

  apiResponse(method: string, url: string, status: number, data?: any): void {
    if (status >= 400) {
      this.error(`API ${method} ${url} failed with status ${status}`, data, 'API');
    } else {
      this.debug(`API ${method} ${url} succeeded with status ${status}`, data, 'API');
    }
  }

  userAction(action: string, data?: any): void {
    this.info(`User action: ${action}`, data, 'USER');
  }

  authEvent(event: string, data?: any): void {
    this.info(`Auth event: ${event}`, data, 'AUTH');
  }

  performanceMetric(metric: string, value: number, unit: string = 'ms'): void {
    this.debug(`Performance: ${metric} = ${value}${unit}`, undefined, 'PERF');
  }

  // Error handling with stack trace
  exception(error: Error, context?: string, data?: any): void {
    const message = context ? `${context}: ${error.message}` : error.message;
    this.error(
      message,
      {
        stack: error.stack,
        name: error.name,
        ...data,
      },
      'EXCEPTION',
    );
  }
}

// Export singleton instance
export const logger = new Logger();

// Export default for convenience
export default logger;

// Development helper - can be removed in production builds
if (import.meta.env.DEV) {
  // Make logger available globally for debugging
  (window as any).logger = logger;
}
