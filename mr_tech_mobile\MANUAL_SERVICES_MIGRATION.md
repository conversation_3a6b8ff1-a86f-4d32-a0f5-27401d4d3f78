# Manual Services Migration Guide

Since automated migration has authentication issues, here's how to manually migrate your services collection through Firebase Console:

## 🎯 Goal
Add snake_case fields to your existing services while keeping the camelCase fields for safety.

## 📋 Step-by-Step Process

### 1. Open Firebase Console
1. Go to [Firebase Console](https://console.firebase.google.com)
2. Select your project: **stopnow-be6b7**
3. Go to **Firestore Database**
4. Click on **Data** tab
5. Find the **services** collection

### 2. For Each Service Document
You should see about 6 services. For each service:

1. **Click on the service document**
2. **Click "Add field" button**
3. **Add these new fields** (copy the values from existing camelCase fields):

#### Field Mappings:
- **basePrice** → **base_price** (copy the number value)
- **isActive** → **is_active** (copy the boolean value) 
- **estimatedDuration** → **estimated_duration** (copy the number value)
- **imageUrl** → **image_url** (copy the string value, if it exists)

#### Add Timestamp (if missing):
- **created_at** → Set to "timestamp" with current date/time

### 3. Example Migration
If you have a service with:
```
basePrice: 50
isActive: true  
estimatedDuration: 120
imageUrl: "https://example.com/image.jpg"
```

Add these new fields:
```
base_price: 50
is_active: true
estimated_duration: 120
image_url: "https://example.com/image.jpg"
created_at: [timestamp - current date/time]
```

### 4. Keep Original Fields
**IMPORTANT:** Do NOT delete the original camelCase fields (basePrice, isActive, etc.). 
The app will read from snake_case first, but fallback to camelCase if needed.

## 🧪 Testing After Migration

1. **Save all changes in Firebase Console**
2. **Run your Flutter app:**
   ```bash
   cd mr_tech_mobile
   flutter run
   ```
3. **Go to Services screen**
4. **Verify all services load correctly**
5. **Check debug console** - you should see messages like:
   ```
   ServiceModel.fromMap: Using snake_case fields for service [id]
   ```

## ✅ Expected Results

After migration:
- ✅ All services display normally in the app
- ✅ No crashes or errors
- ✅ App uses snake_case fields (base_price, is_active, etc.)
- ✅ Original camelCase fields preserved as fallback
- ✅ New services created by app will use ONLY snake_case
- ✅ Smaller document sizes (after eventual cleanup)

## 🔧 What Changed in the App

The ServiceModel has been updated to:
1. **Read:** Prefer snake_case, fallback to camelCase
2. **Write:** Use ONLY snake_case (no more dual-write)

This means:
- **Existing services:** Will work with both field formats
- **New services:** Will be created with snake_case only
- **Consistent naming:** All future data follows snake_case standard

## 🚨 If Something Goes Wrong

If the app crashes or services don't load:
1. Check the debug console for error messages
2. Verify field names are exactly: `base_price`, `is_active`, `estimated_duration`
3. Make sure you copied the values correctly (numbers as numbers, booleans as booleans)
4. The original camelCase fields should still be there as backup

## 📊 Benefits After Migration

- **Consistency:** All fields follow snake_case convention
- **Smaller docs:** 30-50% reduction in document size (after cleanup)
- **Better performance:** Less data transfer
- **Easier maintenance:** Standardized field naming
- **Future-proof:** Ready for web portal integration

---

**Time needed:** About 5-10 minutes for 6 services
**Risk level:** Very low (original fields preserved)
**Rollback:** Not needed (backward compatible) 