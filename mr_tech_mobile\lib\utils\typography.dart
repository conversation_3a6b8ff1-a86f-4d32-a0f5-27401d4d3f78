import 'package:flutter/material.dart';
import '../widgets/text_styles.dart';

/// Typography utility class for consistent text styling throughout the app
/// Provides easy access to Material 3 typography scale and common text styles
class Typography {
  // Private constructor to prevent instantiation
  Typography._();

  // Material 3 Typography Scale - Display
  static TextStyle displayLarge(BuildContext context, {Color? color}) => 
      AppTextStyles.displayLarge(context, color: color);
  
  static TextStyle displayMedium(BuildContext context, {Color? color}) => 
      AppTextStyles.displayMedium(context, color: color);
  
  static TextStyle displaySmall(BuildContext context, {Color? color}) => 
      AppTextStyles.displaySmall(context, color: color);

  // Material 3 Typography Scale - Headline
  static TextStyle headlineLarge(BuildContext context, {Color? color}) => 
      AppTextStyles.headingLarge(context, color: color);
  
  static TextStyle headlineMedium(BuildContext context, {Color? color}) => 
      AppTextStyles.headingMedium(context, color: color);
  
  static TextStyle headlineSmall(BuildContext context, {Color? color}) => 
      AppTextStyles.headingSmall(context, color: color);

  // Material 3 Typography Scale - Title
  static TextStyle titleLarge(BuildContext context, {Color? color}) => 
      AppTextStyles.titleLarge(context, color: color);
  
  static TextStyle titleMedium(BuildContext context, {Color? color}) => 
      AppTextStyles.titleMedium(context, color: color);
  
  static TextStyle titleSmall(BuildContext context, {Color? color}) => 
      AppTextStyles.titleSmall(context, color: color);

  // Material 3 Typography Scale - Body
  static TextStyle bodyLarge(BuildContext context, {Color? color}) => 
      AppTextStyles.bodyLarge(context, color: color);
  
  static TextStyle bodyMedium(BuildContext context, {Color? color}) => 
      AppTextStyles.bodyMedium(context, color: color);
  
  static TextStyle bodySmall(BuildContext context, {Color? color}) => 
      AppTextStyles.bodySmall(context, color: color);

  // Material 3 Typography Scale - Label
  static TextStyle labelLarge(BuildContext context, {Color? color}) => 
      AppTextStyles.labelLarge(context, color: color);
  
  static TextStyle labelMedium(BuildContext context, {Color? color}) => 
      AppTextStyles.labelMedium(context, color: color);
  
  static TextStyle labelSmall(BuildContext context, {Color? color}) => 
      AppTextStyles.labelSmall(context, color: color);

  // Semantic text styles for specific use cases
  
  /// App bar title style
  static TextStyle appBarTitle(BuildContext context, {Color? color}) => 
      AppTextStyles.appBarTitle(context, color: color);
  
  /// Card title style
  static TextStyle cardTitle(BuildContext context, {Color? color}) => 
      AppTextStyles.cardTitle(context, color: color);
  
  /// Card subtitle style
  static TextStyle cardSubtitle(BuildContext context, {Color? color}) => 
      AppTextStyles.cardSubtitle(context, color: color);
  
  /// Button text style
  static TextStyle buttonText(BuildContext context, {Color? color}) => 
      AppTextStyles.buttonText(context, color: color);
  
  /// Caption text style
  static TextStyle caption(BuildContext context, {Color? color}) => 
      AppTextStyles.caption(context, color: color);
  
  /// Error text style
  static TextStyle error(BuildContext context) => 
      AppTextStyles.errorText(context);
  
  /// Success text style
  static TextStyle success(BuildContext context) => 
      AppTextStyles.successText(context);
  
  /// Hint text style
  static TextStyle hint(BuildContext context) => 
      AppTextStyles.hintText(context);

  // Text widgets with predefined styles for common use cases
  
  /// Creates a Text widget with display large style
  static Widget displayLargeText(
    String text, 
    BuildContext context, {
    Color? color,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
  }) => Text(
    text,
    style: displayLarge(context, color: color),
    textAlign: textAlign,
    maxLines: maxLines,
    overflow: overflow,
  );

  /// Creates a Text widget with headline large style
  static Widget headlineLargeText(
    String text, 
    BuildContext context, {
    Color? color,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
  }) => Text(
    text,
    style: headlineLarge(context, color: color),
    textAlign: textAlign,
    maxLines: maxLines,
    overflow: overflow,
  );

  /// Creates a Text widget with title large style
  static Widget titleLargeText(
    String text, 
    BuildContext context, {
    Color? color,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
  }) => Text(
    text,
    style: titleLarge(context, color: color),
    textAlign: textAlign,
    maxLines: maxLines,
    overflow: overflow,
  );

  /// Creates a Text widget with body large style
  static Widget bodyLargeText(
    String text, 
    BuildContext context, {
    Color? color,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
  }) => Text(
    text,
    style: bodyLarge(context, color: color),
    textAlign: textAlign,
    maxLines: maxLines,
    overflow: overflow,
  );

  /// Creates a Text widget with body medium style
  static Widget bodyMediumText(
    String text, 
    BuildContext context, {
    Color? color,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
  }) => Text(
    text,
    style: bodyMedium(context, color: color),
    textAlign: textAlign,
    maxLines: maxLines,
    overflow: overflow,
  );

  /// Creates a Text widget with caption style
  static Widget captionText(
    String text, 
    BuildContext context, {
    Color? color,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
  }) => Text(
    text,
    style: caption(context, color: color),
    textAlign: textAlign,
    maxLines: maxLines,
    overflow: overflow,
  );

  /// Creates a Text widget with error style
  static Widget errorText(
    String text, 
    BuildContext context, {
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
  }) => Text(
    text,
    style: error(context),
    textAlign: textAlign,
    maxLines: maxLines,
    overflow: overflow,
  );

  /// Creates a Text widget with success style
  static Widget successText(
    String text, 
    BuildContext context, {
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow? overflow,
  }) => Text(
    text,
    style: success(context),
    textAlign: textAlign,
    maxLines: maxLines,
    overflow: overflow,
  );

  // Text style validation and utilities
  
  /// Validates if a text style follows Material 3 typography guidelines
  static bool isValidMaterial3Style(TextStyle style) {
    // Check if font size follows Material 3 scale
    final validSizes = [11, 12, 14, 16, 22, 24, 28, 32, 36, 45, 57];
    return validSizes.contains(style.fontSize?.round());
  }

  /// Gets the appropriate text style based on context and importance
  static TextStyle getContextualStyle(
    BuildContext context, {
    required TextStyleContext textContext,
    TextStyleImportance importance = TextStyleImportance.medium,
    Color? color,
  }) {
    switch (textContext) {
      case TextStyleContext.appBar:
        return appBarTitle(context, color: color);
      case TextStyleContext.cardTitle:
        return cardTitle(context, color: color);
      case TextStyleContext.cardContent:
        return bodyMedium(context, color: color);
      case TextStyleContext.button:
        return buttonText(context, color: color);
      case TextStyleContext.caption:
        return caption(context, color: color);
      case TextStyleContext.body:
        switch (importance) {
          case TextStyleImportance.high:
            return bodyLarge(context, color: color);
          case TextStyleImportance.medium:
            return bodyMedium(context, color: color);
          case TextStyleImportance.low:
            return bodySmall(context, color: color);
        }
      case TextStyleContext.heading:
        switch (importance) {
          case TextStyleImportance.high:
            return headlineLarge(context, color: color);
          case TextStyleImportance.medium:
            return headlineMedium(context, color: color);
          case TextStyleImportance.low:
            return headlineSmall(context, color: color);
        }
    }
  }
}

/// Enum for different text style contexts
enum TextStyleContext {
  appBar,
  cardTitle,
  cardContent,
  button,
  caption,
  body,
  heading,
}

/// Enum for text style importance levels
enum TextStyleImportance {
  high,
  medium,
  low,
}

/// Extension on TextStyle for additional utilities
extension TextStyleExtension on TextStyle {
  /// Creates a copy of this text style with adaptive color based on theme
  TextStyle adaptiveColor(BuildContext context) {
    final theme = Theme.of(context);
    return copyWith(color: color ?? theme.colorScheme.onSurface);
  }

  /// Creates a copy of this text style with emphasis (bold)
  TextStyle get emphasized => copyWith(fontWeight: FontWeight.w600);

  /// Creates a copy of this text style with subtle appearance
  TextStyle subtle(BuildContext context) {
    final theme = Theme.of(context);
    return copyWith(
      color: theme.colorScheme.onSurface.withOpacity(0.7),
    );
  }

  /// Creates a copy of this text style with disabled appearance
  TextStyle disabled(BuildContext context) {
    final theme = Theme.of(context);
    return copyWith(
      color: theme.colorScheme.onSurface.withOpacity(0.38),
    );
  }
}
