import type { Timestamp } from 'firebase/firestore';
import {
  collection,
  doc,
  limit,
  onSnapshot,
  orderBy,
  query,
  updateDoc,
  where,
} from 'firebase/firestore';
import { db } from '../config/firebase';

export interface AdminNotification {
  id: string;
  title: string;
  body: string;
  type: string;
  requestId?: string;
  sound?: boolean;
  priority: 'low' | 'medium' | 'high';
  createdAt: Date | Timestamp;
  read: boolean;
}

export type AdminNotificationCallback = (notification: AdminNotification) => void;

/**
 * Service for managing admin notifications from mobile app
 */
export class AdminNotificationService {
  private callbacks = new Set<AdminNotificationCallback>();
  private notifications: AdminNotification[] = [];
  private unsubscribe: (() => void) | null = null;

  /**
   * Start listening to admin notifications
   */
  startListening(): () => void {
    if (this.unsubscribe) {
      this.unsubscribe();
    }

    console.warn('Starting to listen for admin notifications...');

    const notificationsRef = collection(db, 'admin_notifications');
    const notificationsQuery = query(notificationsRef, orderBy('createdAt', 'desc'), limit(50));

    this.unsubscribe = onSnapshot(
      notificationsQuery,
      (snapshot) => {
        snapshot.docChanges().forEach((change) => {
          if (change.type === 'added') {
            const data = change.doc.data();
            const notification: AdminNotification = {
              id: change.doc.id,
              title: data.title || 'New Notification',
              body: data.body || '',
              type: data.type || 'general',
              requestId: data.requestId,
              sound: data.sound || false,
              priority: data.priority || 'medium',
              createdAt: data.createdAt || new Date(),
              read: data.read || false,
            };

            // Add to notifications list
            this.notifications.unshift(notification);

            // Keep only last 50 notifications
            if (this.notifications.length > 50) {
              this.notifications = this.notifications.slice(0, 50);
            }

            // Notify callbacks
            this.notifyCallbacks(notification);

            console.warn('New admin notification received:', notification);
          }
        });
      },
      (error) => {
        console.error('Error listening to admin notifications:', error);
      },
    );

    return () => {
      if (this.unsubscribe) {
        this.unsubscribe();
        this.unsubscribe = null;
      }
    };
  }

  /**
   * Stop listening to notifications
   */
  stopListening(): void {
    if (this.unsubscribe) {
      this.unsubscribe();
      this.unsubscribe = null;
    }
  }

  /**
   * Subscribe to notification updates
   */
  subscribe(callback: AdminNotificationCallback): () => void {
    this.callbacks.add(callback);

    return () => {
      this.callbacks.delete(callback);
    };
  }

  /**
   * Mark notification as read
   */
  async markAsRead(notificationId: string): Promise<void> {
    try {
      await updateDoc(doc(db, 'admin_notifications', notificationId), {
        read: true,
      });

      // Update local notification
      const notification = this.notifications.find((n) => n.id === notificationId);
      if (notification) {
        notification.read = true;
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  }

  /**
   * Mark all notifications as read
   */
  async markAllAsRead(): Promise<void> {
    try {
      const promises = this.notifications.filter((n) => !n.read).map((n) => this.markAsRead(n.id));

      await Promise.all(promises);
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
    }
  }

  /**
   * Get all notifications
   */
  getNotifications(): AdminNotification[] {
    return [...this.notifications];
  }

  /**
   * Get unread notifications
   */
  getUnreadNotifications(): AdminNotification[] {
    return this.notifications.filter((n) => !n.read);
  }

  /**
   * Get unread count
   */
  getUnreadCount(): number {
    return this.notifications.filter((n) => !n.read).length;
  }

  /**
   * Clear all notifications
   */
  clearNotifications(): void {
    this.notifications = [];
  }

  /**
   * Notify all callbacks
   */
  private notifyCallbacks(notification: AdminNotification): void {
    this.callbacks.forEach((callback) => {
      try {
        callback(notification);
      } catch (error) {
        console.error('Error in admin notification callback:', error);
      }
    });
  }

  /**
   * Play notification sound if enabled
   */
  private playNotificationSound(notification: AdminNotification): void {
    if (notification.sound && 'Audio' in window) {
      try {
        // You can add a notification sound file to public/sounds/
        const audio = new Audio('/sounds/notification.mp3');
        audio.volume = 0.5;
        audio.play().catch((e) => {
          console.warn('Could not play notification sound:', e);
        });
      } catch (error) {
        console.warn('Error playing notification sound:', error);
      }
    }
  }
}

// Export singleton instance
export const adminNotificationService = new AdminNotificationService();
