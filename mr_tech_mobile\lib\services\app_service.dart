import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import '../utils/env_config.dart';
import '../utils/token_manager.dart';
import '../models/request_model.dart';
import 'database_service.dart';
import 'notification_service.dart';
import 'auth_service.dart';
import 'theme_service.dart';
import 'translation_service.dart';
import 'payment_service.dart';
import 'dart:async';
import 'chat_service.dart';
import 'request_service.dart';
import 'offline_service.dart';

class AppService {
  // Singleton instance
  static final AppService _instance = AppService._internal();

  factory AppService() => _instance;

  // Service instances - lazy initialization
  DatabaseService? _databaseService;
  AuthService? _authService;
  NotificationService? _notificationService;
  final ThemeService _themeService = ThemeService();
  final TranslationService _translationService = TranslationService();
  PaymentService? _paymentService;
  SharedPreferences? _prefs;
  FirebaseAnalytics? _analytics;
  final EnvConfig _envConfig = EnvConfig.instance;

  // App state flags
  bool _isInitialized = false;
  bool _isInitializing = false;
  bool _hasCompletedOnboarding = false;
  bool _isFirstLaunch = true;
  bool _isFullyInitialized = false;
  bool _isBackgroundInitializationStarted = false;

  // Global navigator key for context-less navigation
  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  // Initialization completer for preventing multiple initializations
  Completer<void>? _initializationCompleter;

  AppService._internal();

  // Lazy getters for services
  DatabaseService get databaseService => _databaseService ??= DatabaseService();
  AuthService get authService => _authService ??= AuthService();
  NotificationService get notificationService =>
      _notificationService ??= NotificationService();
  ThemeService get themeService => _themeService;
  TranslationService get translationService => _translationService;
  PaymentService get paymentService => _paymentService ??= PaymentService();
  FirebaseAnalytics get analytics => _analytics ??= FirebaseAnalytics.instance;

  // Getter for app state
  bool get isInitialized => _isInitialized;
  bool get hasCompletedOnboarding => _hasCompletedOnboarding;
  bool get isFirstLaunch => _isFirstLaunch;
  bool get isFullyInitialized => _isFullyInitialized;

  /// Ensure environment config is loaded - useful for payment operations
  Future<void> ensureEnvironmentConfigLoaded() async {
    if (!_envConfig.isInitialized) {
      debugPrint(
        'Loading environment config synchronously for payment operation...',
      );
      await _envConfig.initialize();
      debugPrint('Environment config loaded successfully');
    }
  }

  // Fast initialization - only critical components
  Future<void> initialize() async {
    // Prevent multiple initializations
    if (_isInitialized || _isInitializing) {
      return _initializationCompleter?.future ?? Future.value();
    }

    _isInitializing = true;
    _initializationCompleter = Completer<void>();

    try {
      debugPrint('Initializing app services...');

      // Initialize only critical services synchronously
      await _initializeCriticalServices();

      // Start background initialization immediately but don't wait
      _startBackgroundInitialization();

      _isInitialized = true;
      _isInitializing = false;
      _initializationCompleter!.complete();

      debugPrint('App services initialized successfully');
    } catch (e) {
      debugPrint('Error initializing app services: $e');
      _isInitializing = false;
      _initializationCompleter!.completeError(e);
      rethrow;
    }
  }

  // Initialize only critical services needed for UI
  Future<void> _initializeCriticalServices() async {
    try {
      // Load shared preferences first (needed for onboarding status)
      _prefs = await SharedPreferences.getInstance();

      // Load onboarding status
      _hasCompletedOnboarding =
          _prefs!.getBool('has_completed_onboarding') ?? false;

      // Load first launch status
      _isFirstLaunch = _prefs!.getBool('is_first_launch') ?? true;
      if (_isFirstLaunch) {
        await _prefs!.setBool('is_first_launch', false);
      }

      // Initialize Firebase Analytics (lightweight)
      _analytics = FirebaseAnalytics.instance;

      // Configure Firestore with minimal settings
      await _configureFirestoreMinimal();

      // Initialize offline service (needs SharedPreferences)
      final offlineService = OfflineService();
      await offlineService.initialize();

      // Initialize notification service minimally
      await notificationService.initializeMinimal();

      // Setup auth state listener (lightweight)
      authService.authStateChanges.listen(_onUserStateChanged);

      debugPrint('Critical services initialized successfully');
    } catch (e) {
      debugPrint('Error initializing critical services: $e');
      rethrow;
    }
  }

  // Start background initialization without blocking UI
  void _startBackgroundInitialization() {
    if (_isBackgroundInitializationStarted) return;
    _isBackgroundInitializationStarted = true;

    // Use microtask to ensure UI thread is not blocked
    Future.microtask(() async {
      try {
        // Small delay to let UI render first
        await Future.delayed(const Duration(milliseconds: 100));

        await _initializeBackgroundServices();

        _isFullyInitialized = true;
        debugPrint('Background initialization completed');
      } catch (e) {
        debugPrint('Error in background initialization: $e');
      }
    });
  }

  // Initialize non-critical services in background
  Future<void> _initializeBackgroundServices() async {
    try {
      // Initialize environment config
      if (!_envConfig.isInitialized) {
        await _envConfig.initialize();
      }

      // Complete notification service initialization
      await notificationService.completeInitialization();

      // Initialize payment service
      await paymentService.completeInitialization();

      // Set up chat activation listener with delay and error handling
      await Future.delayed(const Duration(milliseconds: 500));
      final ChatService chatService = ChatService();
      debugPrint(
        'Setting up chat activation listener in background initialization',
      );
      try {
        await chatService.setupChatActivationListener();
      } catch (e) {
        debugPrint('Error setting up chat activation listener: $e');
        // If permission error, try to handle it
        if (e.toString().contains('permission-denied')) {
          debugPrint('Permission denied error detected, attempting to handle...');
          await authService.handlePermissionError();
          // Retry after handling permission error
          try {
            await Future.delayed(const Duration(seconds: 1));
            await chatService.setupChatActivationListener();
          } catch (retryError) {
            debugPrint('Retry failed: $retryError');
          }
        }
      }

      // Run data migrations with further delay
      await Future.delayed(const Duration(seconds: 1));
      await _runDataMigrations();

      // Refresh FCM token with even more delay
      await Future.delayed(const Duration(seconds: 2));
      _refreshFCMTokenInBackground();

      debugPrint('Background services initialized successfully');
    } catch (e) {
      debugPrint('Error initializing background services: $e');
    }
  }

  // Minimal Firestore configuration for faster startup
  Future<void> _configureFirestoreMinimal() async {
    try {
      // Use smaller cache and disable persistence initially for faster startup
      FirebaseFirestore.instance.settings = const Settings(
        persistenceEnabled: false, // Disable initially for faster startup
        cacheSizeBytes: 1048576, // 1MB cache for minimal memory usage
      );

      debugPrint('Firestore configured with minimal settings for fast startup');
    } catch (e) {
      debugPrint('Error configuring Firestore settings: $e');
    }
  }

  // Configure Firestore for optimal performance after startup
  Future<void> _configureFirestoreForOptimalPerformance() async {
    try {
      // Re-enable persistence and increase cache after startup
      FirebaseFirestore.instance.settings = const Settings(
        persistenceEnabled: true,
        cacheSizeBytes: 10485760, // 10MB cache
      );

      debugPrint('Firestore reconfigured for optimal performance');
    } catch (e) {
      debugPrint('Error reconfiguring Firestore settings: $e');
    }
  }

  // Run data migrations in background with throttling
  Future<void> _runDataMigrations() async {
    try {
      debugPrint('Running data migrations...');

      // Check if migrations were recently run to avoid redundant operations
      final lastMigrationTime = _prefs?.getInt('last_migration_time') ?? 0;
      final currentTime = DateTime.now().millisecondsSinceEpoch;
      const migrationCooldown = 24 * 60 * 60 * 1000; // 24 hours in milliseconds

      if (currentTime - lastMigrationTime < migrationCooldown) {
        debugPrint('Migrations recently run, skipping...');
        return;
      }

      // Run FCM token migration for service requests
      await _runFCMTokenMigration();

      // Update last migration time
      await _prefs?.setInt('last_migration_time', currentTime);

      debugPrint('Data migrations completed');
    } catch (e) {
      debugPrint('Error running data migrations: $e');
    }
  }

  // FCM token migration with better error handling and throttling
  Future<void> _runFCMTokenMigration() async {
    try {
      debugPrint('Running FCM token migration for service requests...');

      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        debugPrint('No user logged in, skipping FCM token migration');
        return;
      }

      // Get current FCM token
      final token = await FirebaseMessaging.instance.getToken();
      if (token == null || token.isEmpty) {
        debugPrint('No FCM token available, skipping migration');
        return;
      }

      // Get user's service requests with limit to avoid large queries
      final requestsQuery =
          await FirebaseFirestore.instance
              .collection('service_requests')
              .where('customer_id', isEqualTo: user.uid)
              .where('status', whereIn: ['pending', 'approved', 'inProgress'])
              .limit(10) // Limit to avoid large operations
              .get();

      debugPrint(
        'Found ${requestsQuery.docs.length} service requests to update with FCM token',
      );

      int updatedCount = 0;
      for (final doc in requestsQuery.docs) {
        try {
          final data = doc.data();
          final currentToken = data['customer_fcm_token'] as String? ?? data['customerFcmToken'] as String?;

          // Only update if token is different
          if (currentToken != token) {
            await doc.reference.update({
              'customer_fcm_token': token,
              'customerFcmToken': token,
            });
            updatedCount++;
            debugPrint('Updated FCM token for request ${doc.id}');
          }
        } catch (e) {
          debugPrint('Error updating FCM token for request ${doc.id}: $e');
        }
      }

      debugPrint(
        'Migration complete. Updated $updatedCount service requests with FCM token',
      );
    } catch (e) {
      debugPrint('Error in FCM token migration: $e');
    }
  }

  // Enable/disable Firestore network connection based on app state
  Future<void> optimizeFirestoreNetworkUsage(bool enableNetwork) async {
    try {
      if (enableNetwork) {
        debugPrint('Enabling Firestore network connection');
        await FirebaseFirestore.instance.enableNetwork();
        // Reconfigure for optimal performance when coming to foreground
        await _configureFirestoreForOptimalPerformance();
      } else {
        debugPrint('Disabling Firestore network connection');
        await FirebaseFirestore.instance.disableNetwork();
      }
    } catch (e) {
      debugPrint('Error managing Firestore network connection: $e');
    }
  }

  // Call this when app goes to background
  Future<void> onAppBackground() async {
    try {
      // Disable Firestore network to save bandwidth and reduce costs
      await optimizeFirestoreNetworkUsage(false);
    } catch (e) {
      debugPrint('Error handling app background state: $e');
    }
  }

  // Call this when app comes to foreground
  Future<void> onAppForeground() async {
    try {
      debugPrint('App coming to foreground - performing resume operations');

      // Re-enable Firestore network when app comes to foreground
      await optimizeFirestoreNetworkUsage(true);

      // Refresh authentication state
      await _refreshAuthenticationState();

      // Refresh FCM token if needed (with throttling)
      await _refreshFCMTokenOnResume();

      debugPrint('App foreground operations completed successfully');
    } catch (e) {
      debugPrint('Error handling app foreground state: $e');
      await _attemptForegroundRecovery();
    }
  }

  /// Refresh FCM token on app resume with throttling
  Future<void> _refreshFCMTokenOnResume() async {
    try {
      // Check if notification service is initialized
      if (!notificationService.isInitialized) {
        await notificationService.initializeMinimal();
      }

      // Use token manager to prevent excessive refreshes
      final tokenManager = TokenManager();
      if (!await tokenManager.canRefreshToken()) {
        debugPrint('Token refresh throttled, skipping...');
        return;
      }

      // Refresh the FCM token
      final token = await FirebaseMessaging.instance.getToken();
      if (token != null) {
        debugPrint('FCM token refreshed on resume');
      }
    } catch (e) {
      debugPrint('Error refreshing FCM token on resume: $e');
    }
  }

  /// Refresh authentication state on resume
  Future<void> _refreshAuthenticationState() async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser != null) {
        // Reload user to get latest state
        await currentUser.reload();
      }
    } catch (e) {
      debugPrint('Error refreshing authentication state: $e');
    }
  }

  /// Attempt recovery from foreground errors
  Future<void> _attemptForegroundRecovery() async {
    try {
      debugPrint('Attempting foreground recovery...');

      // Re-enable network connection
      await FirebaseFirestore.instance.enableNetwork();

      debugPrint('Foreground recovery completed');
    } catch (e) {
      debugPrint('Error in foreground recovery: $e');
    }
  }

  /// Refresh critical data on resume
  Future<void> _refreshCriticalData() async {
    try {
      // Only refresh if fully initialized
      if (!_isFullyInitialized) return;

      // Refresh notification service
      if (notificationService.isInitialized) {
        await notificationService.refreshToken();
      }
    } catch (e) {
      debugPrint('Error refreshing critical data: $e');
    }
  }

  // Handle user sign in with optimized operations
  Future<void> _onUserSignedIn(User user) async {
    try {
      debugPrint('User signed in: ${user.uid}');

      // Check onboarding status in background
      Future.microtask(() => _checkOnboardingStatus(user));

      // Set up user-specific services in background
      Future.microtask(() => _setupUserServices(user));
    } catch (e) {
      debugPrint('Error in onUserSignedIn: $e');
    }
  }

  // Set up user-specific services
  Future<void> _setupUserServices(User user) async {
    try {
      // Subscribe to user-specific FCM topic
      await FirebaseMessaging.instance.subscribeToTopic('user_${user.uid}');

      // Subscribe to general topics
      await FirebaseMessaging.instance.subscribeToTopic('app_announcements');
      await FirebaseMessaging.instance.subscribeToTopic('all_users');
    } catch (e) {
      debugPrint('Error setting up user services: $e');
    }
  }

  // Move FCM token refresh to background with better throttling
  Future<void> _refreshFCMTokenInBackground() async {
    try {
      // Use token manager to prevent refresh loops
      final tokenManager = TokenManager();

      // Check if refresh is allowed
      if (!await tokenManager.canRefreshToken()) {
        debugPrint('Token refresh not allowed at this time, skipping...');
        return;
      }

      // Get token without forcing refresh first
      String? token = await FirebaseMessaging.instance.getToken();
      if (token?.isEmpty != false) {
        debugPrint('No FCM token available');
        return;
      }

      // Update token in service requests for logged-in users only
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        await _updateActiveRequestsWithToken(user.uid, token!);
      }

      // Set up token refresh listener - only do this once!
      _setupTokenRefreshListener();

      // Request permission with improved handling
      await _requestNotificationPermission();
    } catch (e) {
      debugPrint('Error refreshing FCM token: $e');
    }
  }

  // Update active requests with FCM token
  Future<void> _updateActiveRequestsWithToken(
    String userId,
    String token,
  ) async {
    try {
      final requestService = RequestService();
      final activeRequests = await requestService.getUserRequestsOneTime();

      int updateCount = 0;
      for (final request in activeRequests) {
        if (request.status == RequestStatus.pending ||
            request.status == RequestStatus.payment_pending ||
            request.status == RequestStatus.approved ||
            request.status == RequestStatus.inProgress) {
          await requestService.updateRequestFCMToken(request.id, token);
          updateCount++;
        }
      }

      if (updateCount > 0) {
        debugPrint('Updated $updateCount requests with FCM token');
      }
    } catch (e) {
      debugPrint('Error updating requests with FCM token: $e');
    }
  }

  // Track if token refresh listener is set up
  bool _listenerSetup = false;

  // Set up token refresh listener (only once)
  void _setupTokenRefreshListener() {
    if (_listenerSetup) return;

    _listenerSetup = true;
    FirebaseMessaging.instance.onTokenRefresh.listen((newToken) {
      debugPrint('FCM token refreshed automatically');

      // Update token in service requests when refreshed
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser != null && newToken.isNotEmpty) {
        Future.microtask(
          () => _updateActiveRequestsWithToken(currentUser.uid, newToken),
        );
      }
    });
  }

  // Request notification permission
  Future<void> _requestNotificationPermission() async {
    try {
      final settings = await FirebaseMessaging.instance.requestPermission(
        alert: true,
        badge: true,
        sound: true,
        provisional: true,
      );
      debugPrint('FCM Permission status: ${settings.authorizationStatus}');
    } catch (e) {
      debugPrint('Error requesting FCM permission: $e');
    }
  }

  // Mark onboarding as completed
  Future<void> completeOnboarding() async {
    _hasCompletedOnboarding = true;
    await _prefs?.setBool('has_completed_onboarding', true);

    // Update user if signed in
    final user = FirebaseAuth.instance.currentUser;
    if (user != null) {
      try {
        final userModel = await databaseService.getUserById(user.uid);
        if (userModel != null) {
          final updatedUser = userModel.copyWith(isOnboarded: true);
          await databaseService.createOrUpdateUser(updatedUser);
        }
      } catch (e) {
        debugPrint('Error updating user onboarding status: $e');
      }
    }
  }

  // Reset onboarding (for testing)
  Future<void> resetOnboarding() async {
    _hasCompletedOnboarding = false;
    await _prefs?.setBool('has_completed_onboarding', false);
  }

  // Handle user sign out
  void _onUserSignedOut() {
    // Minimal processing for sign out
    debugPrint('User signed out');
  }

  // Auth state listener
  void _onUserStateChanged(User? user) {
    if (user != null) {
      _onUserSignedIn(user);
    } else {
      _onUserSignedOut();
    }
  }

  // Check onboarding status from Firestore with retry logic
  Future<void> _checkOnboardingStatus(User user) async {
    try {
      // Try to get user document with retry logic for network issues
      DocumentSnapshot? userDoc;

      for (int attempt = 0; attempt < 3; attempt++) {
        try {
          userDoc = await FirebaseFirestore.instance
              .collection('users')
              .doc(user.uid)
              .get();
          break; // Success, exit retry loop
        } catch (e) {
          final isLastAttempt = attempt == 2;
          final isRetryableError = e.toString().toLowerCase().contains('unavailable') ||
                                  e.toString().toLowerCase().contains('timeout') ||
                                  e.toString().toLowerCase().contains('network');

          if (isLastAttempt || !isRetryableError) {
            debugPrint('Failed to check onboarding status after ${attempt + 1} attempts: $e');
            return; // Give up and use local state
          }

          // Wait before retry with exponential backoff
          await Future.delayed(Duration(seconds: 1 << attempt));
        }
      }

      if (userDoc?.exists == true) {
        final userData = userDoc!.data() as Map<String, dynamic>;
        final bool isOnboarded = userData['is_onboarded'] ?? userData['isOnboarded'] ?? false;

        // Update local flag if needed
        if (isOnboarded && !_hasCompletedOnboarding) {
          _hasCompletedOnboarding = true;
          await _prefs?.setBool('has_completed_onboarding', true);
          debugPrint(
            'User already completed onboarding according to Firestore. Updating local flag.',
          );
        }
      } else {
        // User document doesn't exist, create it
        debugPrint('User document missing for ${user.uid}, creating...');
        try {
          await FirebaseFirestore.instance.collection('users').doc(user.uid).set({
            'firebase_uid': user.uid,
            'email': user.email ?? '',
            'full_name': user.displayName ?? 'User',
            'display_name': user.displayName ?? 'User',
            'photo_url': user.photoURL,
            'phone_number': '',
            'anydesk_id': null,
            'is_onboarded': false,
            'email_verified': user.emailVerified,
            'created_at': FieldValue.serverTimestamp(),
            'updated_at': FieldValue.serverTimestamp(),
            'preferred_language': 'en',
            'security': {
              'account_locked': false,
              'failed_login_attempts': 0,
              'password_changed_at': FieldValue.serverTimestamp(),
              'last_login_at': FieldValue.serverTimestamp(),
            },
          });
          debugPrint('User document created successfully for ${user.uid}');
        } catch (e) {
          debugPrint('Error creating user document: $e');
        }
      }
    } catch (e) {
      debugPrint('Error checking onboarding status: $e');
      // Continue with local state if Firestore is unavailable
    }
  }

  // Legacy method for compatibility
  Future<void> initializeApp() async {
    return initialize();
  }

  // Legacy method for compatibility
  Future<void> completeAsyncInitialization() async {
    // Ensure background initialization is started
    if (!_isBackgroundInitializationStarted) {
      _startBackgroundInitialization();
    }

    // Wait for full initialization if needed
    while (!_isFullyInitialized) {
      await Future.delayed(const Duration(milliseconds: 100));
    }
  }
}
