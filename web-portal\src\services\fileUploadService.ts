import {
  deleteObject,
  getDownloadURL,
  getMetadata,
  ref as storageRef,
  uploadBytesResumable,
} from 'firebase/storage';
import { storage } from '../config/firebase';

export interface FileUploadProgress {
  fileName: string;
  progress: number;
  status: 'uploading' | 'completed' | 'error';
  url?: string;
  error?: string;
}

export interface UploadResult {
  success: boolean;
  url?: string;
  fileName?: string;
  size?: number;
  error?: string;
}

class FileUploadService {
  private readonly MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
  private readonly ALLOWED_TYPES = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'application/pdf',
    'text/plain',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  ];

  /**
   * Upload a file to Firebase Storage for chat
   */
  async uploadChatFile(
    file: File,
    requestId: string,
    onProgress?: (progress: FileUploadProgress) => void,
  ): Promise<UploadResult> {
    try {
      // Validate file
      const validation = this.validateFile(file);
      if (!validation.valid) {
        return { success: false, error: validation.error };
      }

      // Create storage reference
      const fileName = `${Date.now()}_${file.name}`;
      const fileRef = storageRef(storage, `chat_files/${requestId}/${fileName}`);

      // Create upload task
      const uploadTask = uploadBytesResumable(fileRef, file);

      return new Promise((resolve) => {
        uploadTask.on(
          'state_changed',
          (snapshot) => {
            // Progress callback
            const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
            onProgress?.({
              fileName: file.name,
              progress,
              status: 'uploading',
            });
          },
          (error) => {
            // Error callback
            console.error('Upload error:', error);
            onProgress?.({
              fileName: file.name,
              progress: 0,
              status: 'error',
              error: error.message,
            });
            resolve({ success: false, error: error.message });
          },
          async () => {
            // Success callback
            try {
              const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
              const metadata = await getMetadata(uploadTask.snapshot.ref);

              onProgress?.({
                fileName: file.name,
                progress: 100,
                status: 'completed',
                url: downloadURL,
              });

              resolve({
                success: true,
                url: downloadURL,
                fileName: file.name,
                size: metadata.size,
              });
            } catch (error) {
              console.error('Error getting download URL:', error);
              resolve({ success: false, error: 'Failed to get download URL' });
            }
          },
        );
      });
    } catch (error) {
      console.error('Upload initialization error:', error);
      return { success: false, error: 'Failed to initialize upload' };
    }
  }

  /**
   * Upload an image file (with additional image-specific handling)
   */
  async uploadChatImage(
    file: File,
    requestId: string,
    onProgress?: (progress: FileUploadProgress) => void,
  ): Promise<UploadResult> {
    // Validate it's an image
    if (!file.type.startsWith('image/')) {
      return { success: false, error: 'File must be an image' };
    }

    return this.uploadChatFile(file, requestId, onProgress);
  }

  /**
   * Delete a file from storage
   */
  async deleteFile(fileUrl: string): Promise<boolean> {
    try {
      const fileRef = storageRef(storage, fileUrl);
      await deleteObject(fileRef);
      return true;
    } catch (error) {
      console.error('Error deleting file:', error);
      return false;
    }
  }

  /**
   * Validate file before upload
   */
  private validateFile(file: File): { valid: boolean; error?: string } {
    // Check file size
    if (file.size > this.MAX_FILE_SIZE) {
      return {
        valid: false,
        error: `File size must be less than ${this.MAX_FILE_SIZE / (1024 * 1024)}MB`,
      };
    }

    // Check file type
    if (!this.ALLOWED_TYPES.includes(file.type)) {
      return {
        valid: false,
        error: 'File type not allowed',
      };
    }

    return { valid: true };
  }

  /**
   * Get file type category for display
   */
  getFileTypeCategory(mimeType: string): 'image' | 'document' | 'other' {
    if (mimeType.startsWith('image/')) {
      return 'image';
    } else if (
      mimeType.includes('pdf') ||
      mimeType.includes('document') ||
      mimeType.includes('text') ||
      mimeType.includes('sheet')
    ) {
      return 'document';
    } else {
      return 'other';
    }
  }

  /**
   * Format file size for display
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  }

  /**
   * Extract file name from URL
   */
  getFileNameFromUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split('/');
      const fileName = pathParts[pathParts.length - 1];

      // Remove Firebase Storage tokens and decode
      const decodedFileName = decodeURIComponent(fileName.split('?')[0]);

      // Remove timestamp prefix if present
      const timestampRegex = /^\d+_/;
      return decodedFileName.replace(timestampRegex, '');
    } catch (error) {
      console.error('Error extracting filename from URL:', error);
      return 'Unknown file';
    }
  }

  /**
   * Check if URL is an image
   */
  isImageUrl(url: string): boolean {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg'];
    const fileName = this.getFileNameFromUrl(url).toLowerCase();
    return imageExtensions.some((ext) => fileName.endsWith(ext));
  }

  /**
   * Generate thumbnail URL for images (if using Firebase Extensions or similar)
   */
  getThumbnailUrl(originalUrl: string, size: 'small' | 'medium' = 'small'): string {
    // This would depend on your Firebase Extensions setup
    // For now, return the original URL
    return originalUrl;
  }

  /**
   * Get allowed file types for display
   */
  getAllowedFileTypes(): string[] {
    return [...this.ALLOWED_TYPES];
  }

  /**
   * Get max file size
   */
  getMaxFileSize(): number {
    return this.MAX_FILE_SIZE;
  }

  /**
   * Upload a file for request attachments
   */
  async uploadRequestAttachment(
    file: File,
    requestId: string,
    onProgress?: (progress: FileUploadProgress) => void,
  ): Promise<UploadResult> {
    try {
      // Validate file
      const validation = this.validateFile(file);
      if (!validation.valid) {
        return { success: false, error: validation.error };
      }

      // Create storage reference for request attachments
      const fileName = `${Date.now()}_${file.name}`;
      const fileRef = storageRef(storage, `request_attachments/${requestId}/${fileName}`);

      // Create upload task
      const uploadTask = uploadBytesResumable(fileRef, file);

      return new Promise((resolve) => {
        uploadTask.on(
          'state_changed',
          (snapshot) => {
            // Progress callback
            const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
            onProgress?.({
              fileName: file.name,
              progress,
              status: 'uploading',
            });
          },
          (error) => {
            // Error callback
            console.error('Upload error:', error);
            onProgress?.({
              fileName: file.name,
              progress: 0,
              status: 'error',
              error: error.message,
            });
            resolve({ success: false, error: error.message });
          },
          async () => {
            // Success callback
            try {
              const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
              const metadata = await getMetadata(uploadTask.snapshot.ref);

              onProgress?.({
                fileName: file.name,
                progress: 100,
                status: 'completed',
                url: downloadURL,
              });

              resolve({
                success: true,
                url: downloadURL,
                fileName: file.name,
                size: metadata.size,
              });
            } catch (error) {
              console.error('Error getting download URL:', error);
              resolve({ success: false, error: 'Failed to get download URL' });
            }
          },
        );
      });
    } catch (error) {
      console.error('Error uploading request attachment:', error);
      return { success: false, error: 'Upload failed' };
    }
  }

  /**
   * Upload profile image
   */
  async uploadProfileImage(
    file: File,
    userId: string,
    onProgress?: (progress: FileUploadProgress) => void,
  ): Promise<UploadResult> {
    try {
      // Validate it's an image
      if (!file.type.startsWith('image/')) {
        return { success: false, error: 'File must be an image' };
      }

      // Validate file
      const validation = this.validateFile(file);
      if (!validation.valid) {
        return { success: false, error: validation.error };
      }

      // Create storage reference for profile images
      const fileName = `${Date.now()}_${file.name}`;
      const fileRef = storageRef(storage, `profile_images/${userId}/${fileName}`);

      // Create upload task
      const uploadTask = uploadBytesResumable(fileRef, file);

      return new Promise((resolve) => {
        uploadTask.on(
          'state_changed',
          (snapshot) => {
            // Progress callback
            const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
            onProgress?.({
              fileName: file.name,
              progress,
              status: 'uploading',
            });
          },
          (error) => {
            // Error callback
            console.error('Upload error:', error);
            onProgress?.({
              fileName: file.name,
              progress: 0,
              status: 'error',
              error: error.message,
            });
            resolve({ success: false, error: error.message });
          },
          async () => {
            // Success callback
            try {
              const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
              const metadata = await getMetadata(uploadTask.snapshot.ref);

              onProgress?.({
                fileName: file.name,
                progress: 100,
                status: 'completed',
                url: downloadURL,
              });

              resolve({
                success: true,
                url: downloadURL,
                fileName: file.name,
                size: metadata.size,
              });
            } catch (error) {
              console.error('Error getting download URL:', error);
              resolve({ success: false, error: 'Failed to get download URL' });
            }
          },
        );
      });
    } catch (error) {
      console.error('Error uploading profile image:', error);
      return { success: false, error: 'Upload failed' };
    }
  }

  /**
   * Get file extension from filename
   */
  getFileExtension(fileName: string): string {
    return fileName.split('.').pop()?.toLowerCase() || '';
  }

  /**
   * Check if file type is allowed
   */
  isFileTypeAllowed(fileType: string): boolean {
    return this.ALLOWED_TYPES.includes(fileType);
  }

  /**
   * Get human-readable file type name
   */
  getFileTypeName(mimeType: string): string {
    const typeMap: Record<string, string> = {
      'image/jpeg': 'JPEG Image',
      'image/png': 'PNG Image',
      'image/gif': 'GIF Image',
      'image/webp': 'WebP Image',
      'application/pdf': 'PDF Document',
      'text/plain': 'Text File',
      'application/msword': 'Word Document',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word Document',
      'application/vnd.ms-excel': 'Excel Spreadsheet',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel Spreadsheet',
    };

    return typeMap[mimeType] || 'Unknown File Type';
  }
}

export default new FileUploadService();
