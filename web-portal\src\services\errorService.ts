import { logger } from '../utils/logger';
import type { AppError, ServiceResponse } from '../types/common';
import { ErrorCode } from '../types/enums';
import { BaseApiService } from './baseApiService';

export interface ErrorContext {
  userId?: string;
  component?: string;
  action?: string;
  requestId?: string;
  timestamp?: Date;
  userAgent?: string;
  url?: string;
  additionalData?: Record<string, any>;
}

export interface ProcessedError {
  id: string;
  message: string;
  userMessage: string;
  type: ErrorType;
  severity: ErrorSeverity;
  isRetryable: boolean;
  retryAfter?: number;
  context?: ErrorContext;
  originalError: Error;
}

export type ErrorType =
  | 'network'
  | 'authentication'
  | 'authorization'
  | 'validation'
  | 'not-found'
  | 'server'
  | 'client'
  | 'timeout'
  | 'rate-limit'
  | 'unknown';

export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

class ErrorService extends BaseApiService {
  private errorQueue: ProcessedError[] = [];
  private maxQueueSize = 100;

  constructor() {
    super({
      serviceName: 'ErrorService',
      enableLogging: true,
      enableRetry: false, // Error service shouldn't retry
    });
  }

  /**
   * Process and categorize an error
   */
  processError(error: Error | string, context?: ErrorContext): ProcessedError {
    const originalError = typeof error === 'string' ? new Error(error) : error;
    const errorMessage = originalError.message.toLowerCase();

    const processedError: ProcessedError = {
      id: this.generateErrorId(),
      message: originalError.message,
      userMessage: this.getUserFriendlyMessage(originalError),
      type: this.categorizeError(originalError),
      severity: this.determineSeverity(originalError),
      isRetryable: this.isRetryableError(originalError),
      retryAfter: this.getRetryDelay(originalError),
      context: {
        ...context,
        timestamp: new Date(),
        userAgent: navigator.userAgent,
        url: window.location.href,
      },
      originalError,
    };

    // Add to queue for tracking
    this.addToQueue(processedError);

    // Log the error
    this.logError(processedError);

    return processedError;
  }

  /**
   * Categorize error type based on error message and properties
   */
  private categorizeError(error: Error): ErrorType {
    const message = error.message.toLowerCase();
    const name = error.name.toLowerCase();

    // Network errors
    if (
      message.includes('network') ||
      message.includes('fetch') ||
      message.includes('connection') ||
      name.includes('networkerror')
    ) {
      return 'network';
    }

    // Authentication errors
    if (
      message.includes('unauthorized') ||
      message.includes('auth') ||
      message.includes('login') ||
      message.includes('token')
    ) {
      return 'authentication';
    }

    // Authorization errors
    if (
      message.includes('forbidden') ||
      message.includes('permission') ||
      message.includes('access denied')
    ) {
      return 'authorization';
    }

    // Validation errors
    if (
      message.includes('validation') ||
      message.includes('invalid') ||
      message.includes('required') ||
      message.includes('format')
    ) {
      return 'validation';
    }

    // Not found errors
    if (
      message.includes('not found') ||
      message.includes('404') ||
      message.includes('does not exist')
    ) {
      return 'not-found';
    }

    // Timeout errors
    if (message.includes('timeout') || message.includes('deadline')) {
      return 'timeout';
    }

    // Rate limit errors
    if (
      message.includes('rate limit') ||
      message.includes('too many requests') ||
      message.includes('429')
    ) {
      return 'rate-limit';
    }

    // Server errors
    if (
      message.includes('server') ||
      message.includes('500') ||
      message.includes('internal') ||
      message.includes('service unavailable')
    ) {
      return 'server';
    }

    // Client errors
    if (message.includes('400') || message.includes('bad request')) {
      return 'client';
    }

    return 'unknown';
  }

  /**
   * Determine error severity
   */
  private determineSeverity(error: Error): ErrorSeverity {
    const message = error.message.toLowerCase();
    const type = this.categorizeError(error);

    // Critical errors
    if (type === 'server' && message.includes('500')) return 'critical';
    if (message.includes('critical') || message.includes('fatal')) return 'critical';

    // High severity errors
    if (type === 'authentication' || type === 'authorization') return 'high';
    if (message.includes('security') || message.includes('breach')) return 'high';

    // Medium severity errors
    if (type === 'network' || type === 'timeout') return 'medium';
    if (type === 'not-found' || type === 'server') return 'medium';

    // Low severity errors
    if (type === 'validation' || type === 'client') return 'low';

    return 'medium';
  }

  /**
   * Check if error is retryable
   */
  private isRetryableError(error: Error): boolean {
    const message = error.message.toLowerCase();
    const type = this.categorizeError(error);

    // Always retryable
    if (type === 'network' || type === 'timeout') return true;
    if (message.includes('temporary') || message.includes('retry')) return true;

    // Sometimes retryable
    if (type === 'server' && !message.includes('500')) return true;
    if (type === 'rate-limit') return true;

    // Never retryable
    if (type === 'authentication' || type === 'authorization') return false;
    if (type === 'validation' || type === 'not-found') return false;

    return false;
  }

  /**
   * Get retry delay in milliseconds
   */
  private getRetryDelay(error: Error): number | undefined {
    const type = this.categorizeError(error);
    const message = error.message.toLowerCase();

    if (type === 'rate-limit') {
      // Extract retry-after if available
      const retryMatch = message.match(/retry.?after.?(\d+)/i);
      if (retryMatch) {
        return parseInt(retryMatch[1]) * 1000;
      }
      return 60000; // Default 1 minute for rate limits
    }

    if (type === 'network' || type === 'timeout') {
      return 2000; // 2 seconds for network issues
    }

    if (type === 'server') {
      return 5000; // 5 seconds for server errors
    }

    return undefined;
  }

  /**
   * Get user-friendly error message
   */
  private getUserFriendlyMessage(error: Error): string {
    const type = this.categorizeError(error);
    const message = error.message.toLowerCase();

    const friendlyMessages: Record<ErrorType, string> = {
      network: 'Connection problem. Please check your internet connection and try again.',
      authentication: 'Please log in again to continue.',
      authorization: "You don't have permission to perform this action.",
      validation: 'Please check your input and try again.',
      'not-found': 'The requested item could not be found.',
      server: 'A server error occurred. Please try again later.',
      client: 'Invalid request. Please check your input.',
      timeout: 'The request timed out. Please try again.',
      'rate-limit': 'Too many requests. Please wait a moment and try again.',
      unknown: 'An unexpected error occurred. Please try again.',
    };

    // Check for specific error patterns
    if (message.includes('firebase') && message.includes('permission')) {
      return "You don't have permission to access this data.";
    }

    if (message.includes('offline') || message.includes('no internet')) {
      return 'You appear to be offline. Please check your connection.';
    }

    return friendlyMessages[type];
  }

  /**
   * Generate unique error ID
   */
  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Add error to tracking queue
   */
  private addToQueue(error: ProcessedError): void {
    this.errorQueue.unshift(error);

    // Maintain queue size
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue = this.errorQueue.slice(0, this.maxQueueSize);
    }
  }

  /**
   * Log error with appropriate level
   */
  private logError(error: ProcessedError): void {
    const logData = {
      errorId: error.id,
      type: error.type,
      severity: error.severity,
      message: error.message,
      userMessage: error.userMessage,
      isRetryable: error.isRetryable,
      context: error.context,
    };

    switch (error.severity) {
      case 'critical':
        logger.error('Critical error occurred', logData);
        break;
      case 'high':
        logger.error('High severity error', logData);
        break;
      case 'medium':
        logger.warn('Medium severity error', logData);
        break;
      case 'low':
        logger.info('Low severity error', logData);
        break;
    }
  }

  /**
   * Get recent errors for debugging
   */
  getRecentErrors(limit = 10): ProcessedError[] {
    return this.errorQueue.slice(0, limit);
  }

  /**
   * Clear error queue
   */
  clearErrorQueue(): void {
    this.errorQueue = [];
  }

  /**
   * Get error statistics
   */
  getErrorStats(): {
    total: number;
    byType: Record<ErrorType, number>;
    bySeverity: Record<ErrorSeverity, number>;
  } {
    const stats = {
      total: this.errorQueue.length,
      byType: {} as Record<ErrorType, number>,
      bySeverity: {} as Record<ErrorSeverity, number>,
    };

    this.errorQueue.forEach((error) => {
      stats.byType[error.type] = (stats.byType[error.type] || 0) + 1;
      stats.bySeverity[error.severity] = (stats.bySeverity[error.severity] || 0) + 1;
    });

    return stats;
  }
}

// Create singleton instance
export const errorService = new ErrorService();

// Export types
export type { ProcessedError, ErrorContext };
