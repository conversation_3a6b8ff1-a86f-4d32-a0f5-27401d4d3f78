import {
  createUserWithEmailAndPassword,
  deleteUser as deleteFirebaseUser,
  sendEmailVerification,
  updatePassword,
  updateProfile,
} from 'firebase/auth';
import {
  type QueryConstraint,
  collection,
  deleteDoc,
  doc,
  getDoc,
  getDocs,
  limit,
  orderBy,
  query,
  serverTimestamp,
  setDoc,
  updateDoc,
  where,
  writeBatch,
} from 'firebase/firestore';
import { auth, db } from '../config/firebase';
import { FirebaseService } from './firebaseService';
import { BaseApiService } from './baseApiService';
import {
  type AdminUser,
  type CreateUserInput,
  type CustomerUser,
  type TechnicianUser,
  type UpdateUserInput,
  type User,
  type UserActivity,
  type UserAuditLog,
  type UserQueryOptions,
  type UserRole,
  type UserStats,
  UserStatus,
} from '../types/user';
import type { ServiceResponse, AppError } from '../types/common';
import { ErrorCode, UserRole as UserRoleEnum } from '../types/enums';
import { isString, isObject } from '../utils/typeGuards';
import validationService from '../utils/validation';
import sanitizationService from '../utils/sanitization';
import activityTrackingService, { ActivityType, AuditAction } from './activityTrackingService';

/**
 * User Service
 *
 * This service handles all user-related operations including CRUD operations,
 * authentication, and user management.
 *
 * IMPORTANT: Admin users are stored ONLY in the 'admins' collection, not in the 'users' collection.
 * Other user types (customers, technicians) are stored in both their role-specific collections
 * and in the main 'users' collection for backwards compatibility.
 */
class UserService extends FirebaseService<User> {
  private apiService: BaseApiService;

  constructor() {
    super('users', {
      enableLogging: true,
      enableRetry: true,
      maxRetries: 3,
      retryDelay: 1000,
    });

    this.apiService = new (class extends BaseApiService {
      constructor() {
        super({ serviceName: 'UserService' });
      }
    })();
  }

  // Helper method to normalize user data with dual field naming
  private normalizeUserData(userData: any): any {
    const normalized = { ...userData };

    // Ensure both snake_case and camelCase fields exist
    const dualFields = [
      ['display_name', 'displayName'],
      ['phone_number', 'phoneNumber'],
      ['photo_url', 'photoUrl'],
      ['is_active', 'isActive'],
      ['is_verified', 'isVerified'],
      ['email_verified', 'emailVerified'],
      ['is_onboarded', 'isOnboarded'],
      ['is_complete', 'isComplete'],
      ['preferred_language', 'preferredLanguage'],
      ['created_at', 'createdAt'],
      ['updated_at', 'updatedAt'],
      ['last_login', 'lastLogin'],
      ['device_type', 'deviceType'],
      ['fcm_token', 'fcmToken'],
    ];

    dualFields.forEach(([snakeCase, camelCase]) => {
      if (normalized[snakeCase] !== undefined && normalized[camelCase] === undefined) {
        normalized[camelCase] = normalized[snakeCase];
      } else if (normalized[camelCase] !== undefined && normalized[snakeCase] === undefined) {
        normalized[snakeCase] = normalized[camelCase];
      }
    });

    // Handle nested objects
    if (normalized.security) {
      const security = normalized.security;
      if (security.account_locked !== undefined && security.accountLocked === undefined) {
        security.accountLocked = security.account_locked;
      }
      if (
        security.failed_login_attempts !== undefined &&
        security.failedLoginAttempts === undefined
      ) {
        security.failedLoginAttempts = security.failed_login_attempts;
      }
    }

    if (normalized.notification_preferences && !normalized.notificationPreferences) {
      normalized.notificationPreferences = {
        chatMessages: normalized.notification_preferences.chat_messages,
        requestUpdates: normalized.notification_preferences.request_updates,
        systemNotifications: normalized.notification_preferences.system_notifications,
        emailNotifications: normalized.notification_preferences.email_notifications,
        pushNotifications: normalized.notification_preferences.push_notifications,
      };
    }

    return normalized;
  }

  // Get user by ID with proper type casting
  async getUserById(id: string): Promise<User | null> {
    try {
      // Try to find the user in role-specific collections first
      for (const role of ['admin', 'technician', 'customer']) {
        const collection = this.getCollectionForRole(role as UserRole);
        const docRef = doc(db, collection, id);
        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
          const userData = { id: docSnap.id, ...docSnap.data() };
          return this.normalizeUserData(userData) as User;
        }
      }

      // If not found in role-specific collections, try the main users collection
      const docRef = doc(db, this.collectionName, id);
      const docSnap = await getDoc(docRef);

      if (docSnap.exists()) {
        const userData = { id: docSnap.id, ...docSnap.data() };
        return this.normalizeUserData(userData) as User;
      }

      return null;
    } catch (error) {
      console.error('Error getting user by ID:', error);
      throw error;
    }
  }

  // Get user by email
  async getUserByEmail(email: string): Promise<User | null> {
    try {
      const q = query(collection(db, this.collectionName), where('email', '==', email), limit(1));
      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        const doc = querySnapshot.docs[0];
        const userData = { id: doc.id, ...doc.data() };
        return this.normalizeUserData(userData) as User;
      }
      return null;
    } catch (error) {
      console.error('Error getting user by email:', error);
      throw error;
    }
  }

  /**
   * Create a new user with Firebase Auth and Firestore profile
   */
  async createUser(userData: CreateUserInput, createAuthAccount = true): Promise<ServiceResponse<User>> {
    // Validate required fields
    const requiredValidation = this.apiService['validateRequired'](userData, [
      'email', 'role', 'display_name'
    ]);
    if (requiredValidation) {
      return this.createResponse(false, undefined, requiredValidation);
    }

    // Validate field types
    const typeValidation = this.apiService['validateTypes'](userData, {
      email: isString,
      role: isString,
      display_name: isString,
      phone_number: (v) => v === undefined || isString(v),
    });
    if (typeValidation) {
      return this.createResponse(false, undefined, typeValidation);
    }

    try {
      const result = await this.executeWithRetry(async () => {
        // Sanitize input data
        const sanitizedData = sanitizationService.sanitizeCreateUserInput(userData);

        // Validate input data
        const validationResult = validationService.validateCreateUserInput(sanitizedData);
        if (!validationResult.isValid) {
          const errorMessages = validationResult.errors.map((e) => e.message).join(', ');
          throw new Error(`Validation failed: ${errorMessages}`);
        }

        return sanitizedData;
      }, 'validateUserData');

      // Continue with user creation logic...
      return await this.executeUserCreation(result, createAuthAccount);
    } catch (error) {
      const appError = this.createError(
        ErrorCode.VALIDATION_INVALID_FORMAT,
        `Failed to create user: ${error instanceof Error ? error.message : String(error)}`,
        { userData: { ...userData, password: '[REDACTED]' }, originalError: error }
      );
      this.log('error', 'Failed to create user', { error: appError });
      return this.createResponse(false, undefined, appError);
    }
  }

  /**
   * Execute the actual user creation process
   */
  private async executeUserCreation(sanitizedData: CreateUserInput, createAuthAccount: boolean): Promise<ServiceResponse<User>> {

      let userId: string;
      let firebaseUser = null;

      // Create Firebase Auth account if requested
      if (createAuthAccount && sanitizedData.password) {
        const userCredential = await createUserWithEmailAndPassword(
          auth,
          sanitizedData.email,
          sanitizedData.password,
        );
        firebaseUser = userCredential.user;
        userId = firebaseUser.uid;

        // Update Firebase Auth profile
        if (sanitizedData.name) {
          await updateProfile(firebaseUser, {
            displayName: sanitizedData.name,
            photoURL: sanitizedData.photo_url,
          });
        }

        // Send email verification
        await sendEmailVerification(firebaseUser);
      } else {
        // Generate a custom ID if not creating auth account
        const targetCollection = this.getCollectionForRole(sanitizedData.role);
        userId = doc(collection(db, targetCollection)).id;
      }

      // Prepare user data with dual field naming
      const userDoc = {
        email: sanitizedData.email,
        name: sanitizedData.name,
        display_name: sanitizedData.name,
        displayName: sanitizedData.name,
        role: sanitizedData.role,
        status: UserStatus.ACTIVE,
        is_active: true,
        isActive: true,
        is_verified: false,
        isVerified: false,
        email_verified: firebaseUser?.emailVerified || false,
        emailVerified: firebaseUser?.emailVerified || false,
        is_onboarded: false,
        isOnboarded: false,
        is_complete: false,
        isComplete: false,
        phone_number: sanitizedData.phone_number || null,
        phoneNumber: sanitizedData.phone_number || null,
        photo_url: sanitizedData.photo_url || null,
        photoUrl: sanitizedData.photo_url || null,
        address: sanitizedData.address || null,
        city: sanitizedData.city || null,
        country: sanitizedData.country || null,
        preferred_language: sanitizedData.preferred_language || 'en',
        preferredLanguage: sanitizedData.preferred_language || 'en',
        created_at: serverTimestamp(),
        createdAt: serverTimestamp(),
        updated_at: serverTimestamp(),
        updatedAt: serverTimestamp(),
        device_type: 'web',
        deviceType: 'web',
        security: {
          account_locked: false,
          accountLocked: false,
          failed_login_attempts: 0,
          failedLoginAttempts: 0,
          password_changed_at: serverTimestamp(),
          passwordChangedAt: serverTimestamp(),
        },
        notification_preferences: {
          chat_messages: true,
          request_updates: true,
          system_notifications: true,
          email_notifications: true,
          push_notifications: true,
        },
        notificationPreferences: {
          chatMessages: true,
          requestUpdates: true,
          systemNotifications: true,
          emailNotifications: true,
          pushNotifications: true,
        },
      };

      // Add role-specific fields
      if (sanitizedData.role === 'technician') {
        Object.assign(userDoc, {
          specialties: sanitizedData.specialties || [],
          rating: 0,
          completed_requests: 0,
          completedRequests: 0,
          active_requests: 0,
          activeRequests: 0,
          is_available: true,
          isAvailable: true,
          technician_status: 'active',
          technicianStatus: 'active',
        });
      } else if (sanitizedData.role === 'admin') {
        Object.assign(userDoc, {
          permissions: sanitizedData.permissions || [],
          admin_level: sanitizedData.admin_level || 'standard',
          adminLevel: sanitizedData.admin_level || 'standard',
        });
      } else if (sanitizedData.role === 'customer') {
        Object.assign(userDoc, {
          anydesk_id: sanitizedData.anydesk_id || null,
          total_requests: 0,
          totalRequests: 0,
          completed_requests: 0,
          completedRequests: 0,
        });
      }

      // Save to appropriate collection based on role
      const targetCollection = this.getCollectionForRole(sanitizedData.role);
      await setDoc(doc(db, targetCollection, userId), userDoc);

      // Only save to main users collection if it's not an admin
      if (sanitizedData.role !== 'admin') {
        await setDoc(doc(db, 'users', userId), userDoc);
      }

      // Log the creation activity
      await activityTrackingService.trackActivity(
        userId,
        ActivityType.USER_CREATED,
        `User created with role: ${sanitizedData.role}`,
        activityTrackingService.getContextFromBrowser(),
      );

      // Return the created user
      const createdUser = await this.getUserById(userId);
      if (!createdUser) {
        throw new Error('Failed to retrieve created user');
      }

      return createdUser;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  // Get collection name based on user role
  private getCollectionForRole(role: UserRole): string {
    switch (role) {
      case 'technician':
        return 'technicians';
      case 'admin':
        return 'admins';
      case 'customer':
      default:
        return 'users';
    }
  }

  // Update user profile
  async updateUser(userId: string, updateData: UpdateUserInput, adminId?: string): Promise<User> {
    try {
      const existingUser = await this.getUserById(userId);
      if (!existingUser) {
        throw new Error('User not found');
      }

      // Sanitize input data
      const sanitizedData = sanitizationService.sanitizeUpdateUserInput(updateData);

      // Validate input data
      const validationResult = validationService.validateUpdateUserInput(sanitizedData);
      if (!validationResult.isValid) {
        const errorMessages = validationResult.errors.map((e) => e.message).join(', ');
        throw new Error(`Validation failed: ${errorMessages}`);
      }

      // Prepare update data with dual field naming
      const updateDoc: any = {
        updated_at: serverTimestamp(),
        updatedAt: serverTimestamp(),
      };

      // Handle basic fields
      if (sanitizedData.name !== undefined) {
        updateDoc.name = sanitizedData.name;
        updateDoc.display_name = sanitizedData.name;
        updateDoc.displayName = sanitizedData.name;
      }

      const basicFields = [
        'phone_number',
        'photo_url',
        'address',
        'city',
        'country',
        'preferred_language',
        'status',
        'is_active',
      ];

      basicFields.forEach((field) => {
        if (sanitizedData[field as keyof UpdateUserInput] !== undefined) {
          updateDoc[field] = sanitizedData[field as keyof UpdateUserInput];
          // Add camelCase version
          const camelField = this.toCamelCase(field);
          updateDoc[camelField] = sanitizedData[field as keyof UpdateUserInput];
        }
      });

      // Handle role-specific updates
      if (existingUser.role === 'technician' && sanitizedData.specialties) {
        updateDoc.specialties = sanitizedData.specialties;
      }
      if (existingUser.role === 'admin' && sanitizedData.permissions) {
        updateDoc.permissions = sanitizedData.permissions;
      }
      if (existingUser.role === 'admin' && sanitizedData.admin_level) {
        updateDoc.admin_level = sanitizedData.admin_level;
        updateDoc.adminLevel = sanitizedData.admin_level;
      }

      // Update in appropriate collections
      const targetCollection = this.getCollectionForRole(existingUser.role);
      await updateDoc(doc(db, targetCollection, userId), updateDoc);

      // Only update in users collection if it's not an admin
      if (existingUser.role !== 'admin') {
        await updateDoc(doc(db, 'users', userId), updateDoc);
      }

      // Log the update activity
      await activityTrackingService.trackActivity(
        userId,
        ActivityType.USER_UPDATED,
        'User profile updated',
        activityTrackingService.getContextFromBrowser(),
      );

      // Log audit trail if admin made the change
      if (adminId) {
        await activityTrackingService.logAudit(
          userId,
          adminId,
          AuditAction.UPDATE,
          existingUser,
          sanitizedData,
          'User profile updated by admin',
          activityTrackingService.getContextFromBrowser(),
        );
      }

      // Return updated user
      const updatedUser = await this.getUserById(userId);
      if (!updatedUser) {
        throw new Error('Failed to retrieve updated user');
      }

      return updatedUser;
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  // Helper method to convert snake_case to camelCase
  private toCamelCase(str: string): string {
    return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
  }

  // Get user activity logs (delegated to activity tracking service)
  async getUserActivity(userId: string, limit = 50): Promise<UserActivity[]> {
    return activityTrackingService.getUserActivities(userId, limit);
  }

  // Get user audit logs (delegated to activity tracking service)
  async getUserAuditLogs(userId: string, limit = 50): Promise<UserAuditLog[]> {
    return activityTrackingService.getUserAuditLogs(userId, limit);
  }

  // Query users with filters
  async queryUsers(options: UserQueryOptions = {}): Promise<User[]> {
    try {
      const constraints: QueryConstraint[] = [];

      // Determine which collection to query based on role
      let targetCollection = this.collectionName;
      if (options.role) {
        targetCollection = this.getCollectionForRole(options.role);

        // If we're querying a specific role collection, we don't need the role filter
        // except for the 'users' collection which contains multiple roles
        if (targetCollection !== 'users') {
          // Remove role from options to avoid redundant filtering
          const { role, ...restOptions } = options;
          options = restOptions;
        }
      }

      // Add role filter if still needed and we're querying the main users collection
      // BUT skip this for customer role since our users don't have a role field
      if (options.role && targetCollection === 'users' && options.role !== 'customer') {
        constraints.push(where('role', '==', options.role));
      }

      // Add status filter
      if (options.status) {
        constraints.push(where('status', '==', options.status));
      }

      // Add active filter
      if (options.is_active !== undefined) {
        constraints.push(where('is_active', '==', options.is_active));
      }

      // Add location filters
      if (options.city) {
        constraints.push(where('city', '==', options.city));
      }
      if (options.country) {
        constraints.push(where('country', '==', options.country));
      }

      // Add ordering
      if (options.orderBy) {
        constraints.push(orderBy(options.orderBy.field, options.orderBy.direction));
      } else {
        constraints.push(orderBy('created_at', 'desc'));
      }

      // Add limit
      if (options.limit) {
        constraints.push(limit(options.limit));
      }

      const q = query(collection(db, targetCollection), ...constraints);
      const querySnapshot = await getDocs(q);

      const users = querySnapshot.docs.map((doc) => {
        const userData: any = { id: doc.id, ...doc.data() };

        // Ensure all users have a role field, default to 'customer' if missing
        if (!userData.role) {
          userData.role =
            targetCollection === 'admins'
              ? 'admin'
              : targetCollection === 'technicians'
                ? 'technician'
                : 'customer';
        }

        return this.normalizeUserData(userData) as User;
      });

      // Apply search filter if provided (client-side filtering)
      if (options.search) {
        const searchTerm = options.search.toLowerCase();
        return users.filter(
          (user) =>
            (user.name && user.name.toLowerCase().includes(searchTerm)) ||
            (user.email && user.email.toLowerCase().includes(searchTerm)) ||
            (user.phone_number && user.phone_number.includes(searchTerm)),
        );
      }

      return users;
    } catch (error) {
      console.error('Error querying users:', error);
      throw error;
    }
  }

  // Get users by role
  async getUsersByRole(role: UserRole): Promise<User[]> {
    return this.queryUsers({ role });
  }

  // Get active users
  async getActiveUsers(): Promise<User[]> {
    return this.queryUsers({ is_active: true });
  }

  // Search users
  async searchUsers(searchTerm: string, role?: UserRole): Promise<User[]> {
    return this.queryUsers({ search: searchTerm, role });
  }

  // Get user statistics
  async getUserStats(): Promise<UserStats> {
    try {
      // Query all users and filter for customers in memory
      // This ensures we catch users without a role field
      const allUsers = await this.queryUsers();
      const customerUsers = allUsers.filter((user) => !user.role || user.role === 'customer');

      const stats: UserStats = {
        total: customerUsers.length,
        active: 0,
        inactive: 0,
        suspended: 0,
        pending: 0,
        byRole: {
          customers: customerUsers.length,
          technicians: 0,
          admins: 0,
        },
        byLocation: {},
        recentSignups: 0,
        totalRequests: 0,
        customerStats: {
          withRequests: 0,
          withAnydesk: 0,
          verified: 0,
          onboarded: 0,
        },
      };

      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      let totalRequests = 0;

      customerUsers.forEach((user) => {
        // Count by status
        if (user.status) {
          switch (user.status) {
            case UserStatus.ACTIVE:
              stats.active++;
              break;
            case UserStatus.INACTIVE:
              stats.inactive++;
              break;
            case UserStatus.SUSPENDED:
              stats.suspended++;
              break;
            case UserStatus.PENDING:
              stats.pending++;
              break;
          }
        } else {
          // Default to active if no status
          stats.active++;
        }

        // Count by location
        if (user.country) {
          stats.byLocation[user.country] = (stats.byLocation[user.country] || 0) + 1;
        }

        // Count recent signups
        const createdAt =
          user.created_at instanceof Date ? user.created_at : user.created_at?.toDate();
        if (createdAt && createdAt > thirtyDaysAgo) {
          stats.recentSignups++;
        }

        // Count customer-specific stats
        if (user.is_verified) {
          stats.customerStats.verified++;
        }

        // Check for AnyDesk ID
        if ((user as any).anydesk_id) {
          stats.customerStats.withAnydesk++;
        }

        // Check for onboarding status
        if ((user as any).is_onboarded) {
          stats.customerStats.onboarded++;
        }

        // Count requests
        const requestCount = (user as any).total_requests || (user as any).totalRequests || 0;
        totalRequests += requestCount;
        if (requestCount > 0) {
          stats.customerStats.withRequests++;
        }
      });

      // Add total requests to stats
      stats.totalRequests = totalRequests;

      return stats;
    } catch (error) {
      console.error('Error getting user stats:', error);
      throw error;
    }
  }

  // Suspend user
  async suspendUser(userId: string, adminId: string, reason?: string): Promise<User> {
    const updateData: UpdateUserInput = {
      status: UserStatus.SUSPENDED,
      is_active: false,
    };

    const updatedUser = await this.updateUser(userId, updateData, adminId);

    // Track activities
    await activityTrackingService.trackUserManagement(
      adminId,
      userId,
      ActivityType.USER_SUSPENDED,
      reason || 'User suspended by admin',
      activityTrackingService.getContextFromBrowser(),
    );

    await activityTrackingService.logAudit(
      userId,
      adminId,
      AuditAction.SUSPEND,
      null,
      updateData,
      reason,
      activityTrackingService.getContextFromBrowser(),
    );

    return updatedUser;
  }

  // Activate user
  async activateUser(userId: string, adminId: string): Promise<User> {
    const updateData: UpdateUserInput = {
      status: UserStatus.ACTIVE,
      is_active: true,
    };

    const updatedUser = await this.updateUser(userId, updateData, adminId);

    // Track activities
    await activityTrackingService.trackUserManagement(
      adminId,
      userId,
      ActivityType.USER_ACTIVATED,
      'User activated by admin',
      activityTrackingService.getContextFromBrowser(),
    );

    await activityTrackingService.logAudit(
      userId,
      adminId,
      AuditAction.ACTIVATE,
      null,
      updateData,
      'User activated by admin',
      activityTrackingService.getContextFromBrowser(),
    );

    return updatedUser;
  }

  // Delete user (soft delete by default)
  async deleteUser(userId: string, adminId: string, hardDelete = false): Promise<void> {
    try {
      const user = await this.getUserById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      if (hardDelete) {
        // Hard delete - remove from the appropriate collection
        const roleCollection = this.getCollectionForRole(user.role);
        await deleteDoc(doc(db, roleCollection, userId));

        // Also delete from main users collection if not an admin
        if (user.role !== 'admin') {
          await deleteDoc(doc(db, 'users', userId));
        }

        // TODO: Also delete Firebase Auth account if needed
        // This would require admin SDK or cloud function
      } else {
        // Soft delete - mark as inactive and suspended
        await this.suspendUser(userId, adminId, 'Account deleted');
      }

      // Track activities
      await activityTrackingService.trackUserManagement(
        adminId,
        userId,
        ActivityType.USER_DELETED,
        hardDelete ? 'Hard delete' : 'Soft delete',
        activityTrackingService.getContextFromBrowser(),
      );

      await activityTrackingService.logAudit(
        userId,
        adminId,
        AuditAction.DELETE,
        user,
        null,
        hardDelete ? 'Hard delete' : 'Soft delete',
        activityTrackingService.getContextFromBrowser(),
      );
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }

  // Change user role
  async changeUserRole(userId: string, newRole: UserRole, adminId: string): Promise<User> {
    try {
      const user = await this.getUserById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      const oldRole = user.role;
      if (oldRole === newRole) {
        return user; // No change needed
      }

      // Remove from old role collection
      const oldCollection = this.getCollectionForRole(oldRole);
      await deleteDoc(doc(db, oldCollection, userId));

      // Also remove from main users collection if moving to admin role
      if (newRole === 'admin' && oldCollection !== 'admins') {
        await deleteDoc(doc(db, 'users', userId));
      }

      // Prepare data for new role
      const userData = { ...user, role: newRole };

      // Add role-specific fields
      if (newRole === 'technician') {
        Object.assign(userData, {
          specialties: [],
          rating: 0,
          completed_requests: 0,
          active_requests: 0,
          is_available: true,
          technician_status: 'active',
        });
      } else if (newRole === 'admin') {
        Object.assign(userData, {
          permissions: [],
          admin_level: 'standard',
        });
      }

      // Add to new role collection
      const newCollection = this.getCollectionForRole(newRole);
      await setDoc(doc(db, newCollection, userId), userData);

      // Add to main users collection if moving from admin to another role
      if (oldRole === 'admin' && newRole !== 'admin') {
        await setDoc(doc(db, 'users', userId), userData);
      }

      // Track activities
      await activityTrackingService.trackUserManagement(
        adminId,
        userId,
        ActivityType.ROLE_CHANGED,
        `Role changed from ${oldRole} to ${newRole}`,
        activityTrackingService.getContextFromBrowser(),
      );

      await activityTrackingService.logAudit(
        userId,
        adminId,
        AuditAction.ROLE_CHANGE,
        { role: oldRole },
        { role: newRole },
        `Role changed from ${oldRole} to ${newRole}`,
        activityTrackingService.getContextFromBrowser(),
      );

      return (await this.getUserById(userId)) as User;
    } catch (error) {
      console.error('Error changing user role:', error);
      throw error;
    }
  }

  // Bulk operations
  async bulkUpdateUsers(
    userIds: string[],
    updateData: UpdateUserInput,
    adminId: string,
  ): Promise<void> {
    try {
      const batch = writeBatch(db);
      const timestamp = serverTimestamp();

      for (const userId of userIds) {
        const user = await this.getUserById(userId);
        if (user) {
          const roleCollection = this.getCollectionForRole(user.role);

          // Prepare update with dual naming
          const updateDoc: any = {
            ...updateData,
            updated_at: timestamp,
            updatedAt: timestamp,
          };

          // Add camelCase versions
          Object.keys(updateData).forEach((key) => {
            const camelKey = this.toCamelCase(key);
            if (camelKey !== key) {
              updateDoc[camelKey] = updateData[key as keyof UpdateUserInput];
            }
          });

          batch.update(doc(db, roleCollection, userId), updateDoc);

          // Only update in users collection if not an admin
          if (user.role !== 'admin') {
            batch.update(doc(db, 'users', userId), updateDoc);
          }
        }
      }

      await batch.commit();

      // Log bulk operation
      for (const userId of userIds) {
        await activityTrackingService.trackUserManagement(
          adminId,
          userId,
          ActivityType.USER_UPDATED,
          'Bulk update operation',
          activityTrackingService.getContextFromBrowser(),
        );

        await activityTrackingService.logAudit(
          userId,
          adminId,
          AuditAction.BULK_UPDATE,
          null,
          updateData,
          'Bulk update operation',
          activityTrackingService.getContextFromBrowser(),
        );
      }
    } catch (error) {
      console.error('Error bulk updating users:', error);
      throw error;
    }
  }
}

// Export singleton instance
export default new UserService();
