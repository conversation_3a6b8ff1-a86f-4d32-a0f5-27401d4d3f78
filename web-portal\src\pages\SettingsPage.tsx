import React, { useEffect, useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import {
  AlertTriangle,
  Bell,
  CheckCircle,
  Database,
  Eye,
  EyeOff,
  Globe,
  Info,
  Lock,
  Mail,
  Palette,
  Phone,
  RefreshCw,
  Save,
  Settings,
  Shield,
  User,
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Switch } from '../components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Separator } from '../components/ui/separator';
import { Badge } from '../components/ui/badge';
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from '../components/ui/alert';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useAsyncOperation } from '../hooks/useErrorHandler';
import { useNotifications } from '../contexts/NotificationContext';
import { toast } from '../components/ui/use-toast';
import ErrorDisplay from '../components/error/ErrorDisplay';
import { LoadingButton, StatusIndicator } from '../components/ui/loading-states';
import { SkeletonCard } from '../components/ui/skeleton';

// Settings interfaces
interface UserSettings {
  email: string;
  name: string;
  phone?: string;
  avatar?: string;
  language: string;
  timezone: string;
  theme: 'light' | 'dark' | 'system';
}

interface NotificationSettings {
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  requestUpdates: boolean;
  systemAlerts: boolean;
  marketingEmails: boolean;
  weeklyReports: boolean;
}

interface SecuritySettings {
  twoFactorEnabled: boolean;
  sessionTimeout: number;
  passwordLastChanged: Date;
  loginAlerts: boolean;
  deviceTracking: boolean;
}

interface SystemSettings {
  maintenanceMode: boolean;
  debugMode: boolean;
  logLevel: 'error' | 'warn' | 'info' | 'debug';
  maxFileSize: number;
  sessionDuration: number;
  autoBackup: boolean;
  backupFrequency: 'daily' | 'weekly' | 'monthly';
}

// Validation schemas
const userSettingsSchema = z.object({
  email: z.string().email('Invalid email address'),
  name: z.string().min(2, 'Name must be at least 2 characters'),
  phone: z.string().optional(),
  language: z.string(),
  timezone: z.string(),
  theme: z.enum(['light', 'dark', 'system']),
});

const passwordChangeSchema = z
  .object({
    currentPassword: z.string().min(1, 'Current password is required'),
    newPassword: z.string().min(8, 'Password must be at least 8 characters'),
    confirmPassword: z.string(),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

const SettingsPage: React.FC = () => {
  const { user } = useAuth();
  const { settings: notificationSettings, updateSettings, testNotification } = useNotifications();
  const [activeTab, setActiveTab] = useState('profile');
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Settings state
  const [userSettings, setUserSettings] = useState<UserSettings>({
    email: user?.email || '',
    name: user?.name || '',
    phone: user?.phone || '',
    language: 'en',
    timezone: 'UTC',
    theme: 'system',
  });

  const [securitySettings, setSecuritySettings] = useState<SecuritySettings>({
    twoFactorEnabled: false,
    sessionTimeout: 30,
    passwordLastChanged: new Date(),
    loginAlerts: true,
    deviceTracking: true,
  });

  const [systemSettings, setSystemSettings] = useState<SystemSettings>({
    maintenanceMode: false,
    debugMode: false,
    logLevel: 'info',
    maxFileSize: 10,
    sessionDuration: 60,
    autoBackup: true,
    backupFrequency: 'daily',
  });

  // Form handling
  const userForm = useForm<UserSettings>({
    resolver: zodResolver(userSettingsSchema),
    defaultValues: userSettings,
  });

  const passwordForm = useForm({
    resolver: zodResolver(passwordChangeSchema),
  });

  // Async operations
  const saveUserSettingsOperation = useAsyncOperation();
  const changePasswordOperation = useAsyncOperation();

  const saveSecuritySettingsOperation = useAsyncOperation();
  const saveSystemSettingsOperation = useAsyncOperation();

  // Load settings on mount
  useEffect(() => {
    // In a real app, load settings from API
    userForm.reset(userSettings);
  }, [userSettings, userForm]);

  // Save user settings
  const handleSaveUserSettings = async (data: UserSettings) => {
    await saveUserSettingsOperation.execute(async () => {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
      setUserSettings(data);
      return true;
    });
  };

  // Change password
  const handleChangePassword = async (data: any) => {
    await changePasswordOperation.execute(async () => {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500));
      passwordForm.reset();
      return true;
    });
  };

  // Save security settings
  const handleSaveSecuritySettings = async () => {
    await saveSecuritySettingsOperation.execute(async () => {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 800));
      return true;
    });
  };

  // Save system settings (admin only)
  const handleSaveSystemSettings = async () => {
    await saveSystemSettingsOperation.execute(async () => {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 800));
      return true;
    });
  };

  return (
    <div className='space-y-6 p-6'>
      {/* Header */}
      <div>
        <h1 className='text-3xl font-bold tracking-tight'>Settings</h1>
        <p className='text-muted-foreground mt-2'>Manage your account settings and preferences</p>
      </div>

      {/* Settings Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className='space-y-4'>
        <TabsList className='grid w-full grid-cols-2 lg:grid-cols-5'>
          <TabsTrigger value='profile' className='flex items-center gap-2'>
            <User className='h-4 w-4' />
            Profile
          </TabsTrigger>
          <TabsTrigger value='notifications' className='flex items-center gap-2'>
            <Bell className='h-4 w-4' />
            Notifications
          </TabsTrigger>
          <TabsTrigger value='security' className='flex items-center gap-2'>
            <Shield className='h-4 w-4' />
            Security
          </TabsTrigger>
          <TabsTrigger value='appearance' className='flex items-center gap-2'>
            <Palette className='h-4 w-4' />
            Appearance
          </TabsTrigger>
          {user?.role === 'admin' && (
            <TabsTrigger value='system' className='flex items-center gap-2'>
              <Database className='h-4 w-4' />
              System
            </TabsTrigger>
          )}
        </TabsList>

        {/* Profile Settings */}
        <TabsContent value='profile' className='space-y-6'>
          <Card>
            <CardHeader>
              <CardTitle>Profile Information</CardTitle>
              <CardDescription>
                Update your personal information and contact details
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={userForm.handleSubmit(handleSaveUserSettings)} className='space-y-4'>
                <div className='grid gap-4 md:grid-cols-2'>
                  <div>
                    <Label htmlFor='name'>Full Name</Label>
                    <Input
                      id='name'
                      {...userForm.register('name')}
                      placeholder='Enter your full name'
                    />
                    {userForm.formState.errors.name && (
                      <p className='text-sm text-destructive mt-1'>
                        {userForm.formState.errors.name.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor='email'>Email Address</Label>
                    <Input
                      id='email'
                      type='email'
                      {...userForm.register('email')}
                      placeholder='Enter your email'
                    />
                    {userForm.formState.errors.email && (
                      <p className='text-sm text-destructive mt-1'>
                        {userForm.formState.errors.email.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor='phone'>Phone Number</Label>
                    <Input
                      id='phone'
                      {...userForm.register('phone')}
                      placeholder='Enter your phone number'
                    />
                  </div>

                  <div>
                    <Label htmlFor='language'>Language</Label>
                    <Select
                      value={userForm.watch('language')}
                      onValueChange={(value) => userForm.setValue('language', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='en'>English</SelectItem>
                        <SelectItem value='ar'>العربية</SelectItem>
                        <SelectItem value='fr'>Français</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor='timezone'>Timezone</Label>
                    <Select
                      value={userForm.watch('timezone')}
                      onValueChange={(value) => userForm.setValue('timezone', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='UTC'>UTC</SelectItem>
                        <SelectItem value='America/New_York'>Eastern Time</SelectItem>
                        <SelectItem value='America/Los_Angeles'>Pacific Time</SelectItem>
                        <SelectItem value='Europe/London'>London</SelectItem>
                        <SelectItem value='Asia/Dubai'>Dubai</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className='flex justify-end'>
                  <LoadingButton
                    type='submit'
                    isLoading={saveUserSettingsOperation.isLoading}
                    loadingText='Saving...'
                  >
                    <Save className='h-4 w-4 mr-2' />
                    Save Changes
                  </LoadingButton>
                </div>
              </form>

              {saveUserSettingsOperation.data && (
                <Alert className='mt-4'>
                  <CheckCircle className='h-4 w-4' />
                  <AlertTitle>Success</AlertTitle>
                  <AlertDescription>
                    Your profile settings have been updated successfully.
                  </AlertDescription>
                </Alert>
              )}

              {saveUserSettingsOperation.isError && saveUserSettingsOperation.error && (
                <div className='mt-4'>
                  <ErrorDisplay
                    error={saveUserSettingsOperation.error}
                    title='Failed to Save Settings'
                    description='Please try again or contact support if the problem persists.'
                    showRetry={true}
                    onRetry={() => userForm.handleSubmit(handleSaveUserSettings)()}
                    type='server'
                    severity='medium'
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Change Password */}
          <Card>
            <CardHeader>
              <CardTitle>Change Password</CardTitle>
              <CardDescription>Update your password to keep your account secure</CardDescription>
            </CardHeader>
            <CardContent>
              <form
                onSubmit={passwordForm.handleSubmit(handleChangePassword)}
                className='space-y-4'
              >
                <div>
                  <Label htmlFor='currentPassword'>Current Password</Label>
                  <div className='relative'>
                    <Input
                      id='currentPassword'
                      type={showCurrentPassword ? 'text' : 'password'}
                      {...passwordForm.register('currentPassword')}
                      placeholder='Enter current password'
                    />
                    <Button
                      type='button'
                      variant='ghost'
                      size='icon'
                      className='absolute right-2 top-1/2 -translate-y-1/2'
                      onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                    >
                      {showCurrentPassword ? (
                        <EyeOff className='h-4 w-4' />
                      ) : (
                        <Eye className='h-4 w-4' />
                      )}
                    </Button>
                  </div>
                  {passwordForm.formState.errors.currentPassword && (
                    <p className='text-sm text-destructive mt-1'>
                      {passwordForm.formState.errors.currentPassword.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor='newPassword'>New Password</Label>
                  <div className='relative'>
                    <Input
                      id='newPassword'
                      type={showNewPassword ? 'text' : 'password'}
                      {...passwordForm.register('newPassword')}
                      placeholder='Enter new password'
                    />
                    <Button
                      type='button'
                      variant='ghost'
                      size='icon'
                      className='absolute right-2 top-1/2 -translate-y-1/2'
                      onClick={() => setShowNewPassword(!showNewPassword)}
                    >
                      {showNewPassword ? (
                        <EyeOff className='h-4 w-4' />
                      ) : (
                        <Eye className='h-4 w-4' />
                      )}
                    </Button>
                  </div>
                  {passwordForm.formState.errors.newPassword && (
                    <p className='text-sm text-destructive mt-1'>
                      {passwordForm.formState.errors.newPassword.message}
                    </p>
                  )}
                </div>

                <div>
                  <Label htmlFor='confirmPassword'>Confirm New Password</Label>
                  <div className='relative'>
                    <Input
                      id='confirmPassword'
                      type={showConfirmPassword ? 'text' : 'password'}
                      {...passwordForm.register('confirmPassword')}
                      placeholder='Confirm new password'
                    />
                    <Button
                      type='button'
                      variant='ghost'
                      size='icon'
                      className='absolute right-2 top-1/2 -translate-y-1/2'
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    >
                      {showConfirmPassword ? (
                        <EyeOff className='h-4 w-4' />
                      ) : (
                        <Eye className='h-4 w-4' />
                      )}
                    </Button>
                  </div>
                  {passwordForm.formState.errors.confirmPassword && (
                    <p className='text-sm text-destructive mt-1'>
                      {passwordForm.formState.errors.confirmPassword.message}
                    </p>
                  )}
                </div>

                <div className='flex justify-end'>
                  <LoadingButton
                    type='submit'
                    isLoading={changePasswordOperation.isLoading}
                    loadingText='Changing...'
                  >
                    <Lock className='h-4 w-4 mr-2' />
                    Change Password
                  </LoadingButton>
                </div>
              </form>

              {changePasswordOperation.data && (
                <Alert className='mt-4'>
                  <CheckCircle className='h-4 w-4' />
                  <AlertTitle>Password Changed</AlertTitle>
                  <AlertDescription>Your password has been changed successfully.</AlertDescription>
                </Alert>
              )}

              {changePasswordOperation.isError && changePasswordOperation.error && (
                <div className='mt-4'>
                  <ErrorDisplay
                    error={changePasswordOperation.error}
                    title='Failed to Change Password'
                    description='Please check your current password and try again.'
                    showRetry={true}
                    onRetry={() => passwordForm.handleSubmit(handleChangePassword)()}
                    type='server'
                    severity='medium'
                  />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Notification Settings */}
        <TabsContent value='notifications' className='space-y-6'>
          <Card>
            <CardHeader>
              <CardTitle>Real-time Notification Settings</CardTitle>
              <CardDescription>
                Configure how you receive real-time notifications and alerts
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='space-y-4'>
                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Toast Notifications</Label>
                    <p className='text-sm text-muted-foreground'>
                      Show popup notifications in the browser
                    </p>
                  </div>
                  <Switch
                    checked={notificationSettings.enableToasts}
                    onCheckedChange={(checked) => updateSettings({ enableToasts: checked })}
                  />
                </div>

                <Separator />

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Sound Notifications</Label>
                    <p className='text-sm text-muted-foreground'>
                      Play sounds when notifications arrive
                    </p>
                  </div>
                  <Switch
                    checked={notificationSettings.enableSounds}
                    onCheckedChange={(checked) => updateSettings({ enableSounds: checked })}
                  />
                </div>

                <Separator />

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Desktop Notifications</Label>
                    <p className='text-sm text-muted-foreground'>
                      Show system notifications even when browser is minimized
                    </p>
                  </div>
                  <Switch
                    checked={notificationSettings.enableDesktopNotifications}
                    onCheckedChange={(checked) =>
                      updateSettings({ enableDesktopNotifications: checked })
                    }
                  />
                </div>

                <Separator />

                <div className='space-y-2'>
                  <Label>Sound Volume</Label>
                  <div className='flex items-center space-x-4'>
                    <input
                      type='range'
                      min='0'
                      max='1'
                      step='0.1'
                      value={notificationSettings.soundVolume}
                      onChange={(e) => updateSettings({ soundVolume: parseFloat(e.target.value) })}
                      className='flex-1'
                    />
                    <span className='text-sm text-muted-foreground w-12'>
                      {Math.round(notificationSettings.soundVolume * 100)}%
                    </span>
                  </div>
                </div>

                <Separator />

                <div className='space-y-2'>
                  <Label>Toast Duration (seconds)</Label>
                  <div className='flex items-center space-x-4'>
                    <input
                      type='range'
                      min='1000'
                      max='10000'
                      step='1000'
                      value={notificationSettings.toastDuration}
                      onChange={(e) => updateSettings({ toastDuration: parseInt(e.target.value) })}
                      className='flex-1'
                    />
                    <span className='text-sm text-muted-foreground w-12'>
                      {notificationSettings.toastDuration / 1000}s
                    </span>
                  </div>
                </div>

                <Separator />

                <div className='space-y-3'>
                  <Label>Quiet Hours</Label>
                  <div className='space-y-3'>
                    <div className='flex items-center justify-between'>
                      <span className='text-sm'>Enable quiet hours</span>
                      <Switch
                        checked={notificationSettings.quietHours.enabled}
                        onCheckedChange={(checked) =>
                          updateSettings({
                            quietHours: { ...notificationSettings.quietHours, enabled: checked },
                          })
                        }
                      />
                    </div>

                    {notificationSettings.quietHours.enabled && (
                      <div className='grid grid-cols-2 gap-4'>
                        <div className='space-y-1'>
                          <Label className='text-xs'>Start Time</Label>
                          <Input
                            type='time'
                            value={notificationSettings.quietHours.start}
                            onChange={(e) =>
                              updateSettings({
                                quietHours: {
                                  ...notificationSettings.quietHours,
                                  start: e.target.value,
                                },
                              })
                            }
                          />
                        </div>
                        <div className='space-y-1'>
                          <Label className='text-xs'>End Time</Label>
                          <Input
                            type='time'
                            value={notificationSettings.quietHours.end}
                            onChange={(e) =>
                              updateSettings({
                                quietHours: {
                                  ...notificationSettings.quietHours,
                                  end: e.target.value,
                                },
                              })
                            }
                          />
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              <div className='flex justify-between'>
                <Button
                  variant='outline'
                  onClick={() => testNotification()}
                  className='flex items-center gap-2'
                >
                  <Bell className='h-4 w-4' />
                  Test Notification
                </Button>

                <Button
                  onClick={() => {
                    // Settings are automatically saved when changed
                    toast({
                      title: 'Settings Saved',
                      description: 'Your notification preferences have been updated.',
                    });
                  }}
                  className='flex items-center gap-2'
                >
                  <Save className='h-4 w-4' />
                  Settings Auto-Saved
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Settings */}
        <TabsContent value='security' className='space-y-6'>
          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
              <CardDescription>Manage your account security and privacy settings</CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='space-y-4'>
                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Two-Factor Authentication</Label>
                    <p className='text-sm text-muted-foreground'>
                      Add an extra layer of security to your account
                    </p>
                  </div>
                  <div className='flex items-center gap-2'>
                    <Badge variant={securitySettings.twoFactorEnabled ? 'default' : 'secondary'}>
                      {securitySettings.twoFactorEnabled ? 'Enabled' : 'Disabled'}
                    </Badge>
                    <Switch
                      checked={securitySettings.twoFactorEnabled}
                      onCheckedChange={(checked) =>
                        setSecuritySettings((prev) => ({ ...prev, twoFactorEnabled: checked }))
                      }
                    />
                  </div>
                </div>

                <Separator />

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Login Alerts</Label>
                    <p className='text-sm text-muted-foreground'>
                      Get notified of new login attempts
                    </p>
                  </div>
                  <Switch
                    checked={securitySettings.loginAlerts}
                    onCheckedChange={(checked) =>
                      setSecuritySettings((prev) => ({ ...prev, loginAlerts: checked }))
                    }
                  />
                </div>

                <Separator />

                <div className='flex items-center justify-between'>
                  <div className='space-y-0.5'>
                    <Label>Device Tracking</Label>
                    <p className='text-sm text-muted-foreground'>
                      Track devices that access your account
                    </p>
                  </div>
                  <Switch
                    checked={securitySettings.deviceTracking}
                    onCheckedChange={(checked) =>
                      setSecuritySettings((prev) => ({ ...prev, deviceTracking: checked }))
                    }
                  />
                </div>

                <Separator />

                <div className='space-y-2'>
                  <Label>Session Timeout (minutes)</Label>
                  <Select
                    value={securitySettings.sessionTimeout.toString()}
                    onValueChange={(value) =>
                      setSecuritySettings((prev) => ({ ...prev, sessionTimeout: parseInt(value) }))
                    }
                  >
                    <SelectTrigger className='w-48'>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='15'>15 minutes</SelectItem>
                      <SelectItem value='30'>30 minutes</SelectItem>
                      <SelectItem value='60'>1 hour</SelectItem>
                      <SelectItem value='120'>2 hours</SelectItem>
                      <SelectItem value='480'>8 hours</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className='flex justify-end'>
                <LoadingButton
                  onClick={handleSaveSecuritySettings}
                  isLoading={saveSecuritySettingsOperation.isLoading}
                  loadingText='Saving...'
                >
                  <Save className='h-4 w-4 mr-2' />
                  Save Security Settings
                </LoadingButton>
              </div>

              {saveSecuritySettingsOperation.data && (
                <Alert>
                  <CheckCircle className='h-4 w-4' />
                  <AlertTitle>Security Settings Updated</AlertTitle>
                  <AlertDescription>
                    Your security preferences have been saved successfully.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Appearance Settings */}
        <TabsContent value='appearance' className='space-y-6'>
          <Card>
            <CardHeader>
              <CardTitle>Appearance</CardTitle>
              <CardDescription>Customize the look and feel of the application</CardDescription>
            </CardHeader>
            <CardContent className='space-y-6'>
              <div className='space-y-4'>
                <div>
                  <Label>Theme</Label>
                  <p className='text-sm text-muted-foreground mb-3'>Choose your preferred theme</p>
                  <Select
                    value={userSettings.theme}
                    onValueChange={(value: 'light' | 'dark' | 'system') =>
                      setUserSettings((prev) => ({ ...prev, theme: value }))
                    }
                  >
                    <SelectTrigger className='w-48'>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='light'>Light</SelectItem>
                      <SelectItem value='dark'>Dark</SelectItem>
                      <SelectItem value='system'>System</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <Alert>
                <Info className='h-4 w-4' />
                <AlertTitle>Theme Settings</AlertTitle>
                <AlertDescription>
                  Theme changes will be applied immediately. System theme follows your device's
                  preference.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </TabsContent>

        {/* System Settings (Admin Only) */}
        {user?.role === 'admin' && (
          <TabsContent value='system' className='space-y-6'>
            <Alert>
              <AlertTriangle className='h-4 w-4' />
              <AlertTitle>Administrator Settings</AlertTitle>
              <AlertDescription>
                These settings affect the entire system. Changes should be made carefully.
              </AlertDescription>
            </Alert>

            <Card>
              <CardHeader>
                <CardTitle>System Configuration</CardTitle>
                <CardDescription>Manage system-wide settings and configurations</CardDescription>
              </CardHeader>
              <CardContent className='space-y-6'>
                <div className='space-y-4'>
                  <div className='flex items-center justify-between'>
                    <div className='space-y-0.5'>
                      <Label>Maintenance Mode</Label>
                      <p className='text-sm text-muted-foreground'>
                        Enable maintenance mode to restrict access
                      </p>
                    </div>
                    <Switch
                      checked={systemSettings.maintenanceMode}
                      onCheckedChange={(checked) =>
                        setSystemSettings((prev) => ({ ...prev, maintenanceMode: checked }))
                      }
                    />
                  </div>

                  <Separator />

                  <div className='flex items-center justify-between'>
                    <div className='space-y-0.5'>
                      <Label>Debug Mode</Label>
                      <p className='text-sm text-muted-foreground'>
                        Enable debug mode for development
                      </p>
                    </div>
                    <Switch
                      checked={systemSettings.debugMode}
                      onCheckedChange={(checked) =>
                        setSystemSettings((prev) => ({ ...prev, debugMode: checked }))
                      }
                    />
                  </div>

                  <Separator />

                  <div className='space-y-2'>
                    <Label>Log Level</Label>
                    <Select
                      value={systemSettings.logLevel}
                      onValueChange={(value: 'error' | 'warn' | 'info' | 'debug') =>
                        setSystemSettings((prev) => ({ ...prev, logLevel: value }))
                      }
                    >
                      <SelectTrigger className='w-48'>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='error'>Error</SelectItem>
                        <SelectItem value='warn'>Warning</SelectItem>
                        <SelectItem value='info'>Info</SelectItem>
                        <SelectItem value='debug'>Debug</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <Separator />

                  <div className='space-y-2'>
                    <Label>Max File Size (MB)</Label>
                    <Input
                      type='number'
                      value={systemSettings.maxFileSize}
                      onChange={(e) =>
                        setSystemSettings((prev) => ({
                          ...prev,
                          maxFileSize: parseInt(e.target.value) || 10,
                        }))
                      }
                      className='w-48'
                    />
                  </div>

                  <Separator />

                  <div className='flex items-center justify-between'>
                    <div className='space-y-0.5'>
                      <Label>Auto Backup</Label>
                      <p className='text-sm text-muted-foreground'>
                        Automatically backup system data
                      </p>
                    </div>
                    <Switch
                      checked={systemSettings.autoBackup}
                      onCheckedChange={(checked) =>
                        setSystemSettings((prev) => ({ ...prev, autoBackup: checked }))
                      }
                    />
                  </div>

                  {systemSettings.autoBackup && (
                    <div className='space-y-2'>
                      <Label>Backup Frequency</Label>
                      <Select
                        value={systemSettings.backupFrequency}
                        onValueChange={(value: 'daily' | 'weekly' | 'monthly') =>
                          setSystemSettings((prev) => ({ ...prev, backupFrequency: value }))
                        }
                      >
                        <SelectTrigger className='w-48'>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value='daily'>Daily</SelectItem>
                          <SelectItem value='weekly'>Weekly</SelectItem>
                          <SelectItem value='monthly'>Monthly</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}
                </div>

                <div className='flex justify-end'>
                  <LoadingButton
                    onClick={handleSaveSystemSettings}
                    isLoading={saveSystemSettingsOperation.isLoading}
                    loadingText='Saving...'
                  >
                    <Save className='h-4 w-4 mr-2' />
                    Save System Settings
                  </LoadingButton>
                </div>

                {saveSystemSettingsOperation.data && (
                  <Alert>
                    <CheckCircle className='h-4 w-4' />
                    <AlertTitle>System Settings Updated</AlertTitle>
                    <AlertDescription>
                      System configuration has been saved successfully.
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
};

export default SettingsPage;
