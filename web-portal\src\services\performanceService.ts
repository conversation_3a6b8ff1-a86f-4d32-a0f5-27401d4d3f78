import { type QueryConstraint, Timestamp, orderBy, where } from 'firebase/firestore';
import { FirebaseService, type QueryOptions } from './firebaseService';
import requestService from './requestService';
import { type RequestModel, RequestStatus } from '../types/request';
import { type TechnicianModel } from '../types/technician';
import { differenceInMilliseconds, endOfDay, startOfDay, subDays } from 'date-fns';

export interface TechnicianPerformanceMetrics {
  technicianId: string;
  technicianName: string;
  totalCompleted: number;
  avgAcceptTimeMs: number;
  avgCompletionTimeMs: number;
  reopenRate: number;
  cancellationRate: number;
  avgResponseTimeMs: number;
  periodStart: Date;
  periodEnd: Date;
  lastUpdated: Date;
}

export interface PerformanceStats {
  totalRequests: number;
  completedRequests: number;
  avgAcceptTime: string;
  avgCompletionTime: string;
  cancellationRate: number;
  reopenRate: number;
}

export interface PerformanceChartData {
  date: string;
  completed: number;
  avgResponseTime: number;
  cancellationRate: number;
}

class PerformanceService {
  /**
   * Calculate performance metrics for a specific technician
   */
  async getTechnicianPerformance(
    technicianId: string,
    startDate: Date = subDays(new Date(), 30),
    endDate: Date = new Date(),
  ): Promise<TechnicianPerformanceMetrics | null> {
    try {
      // Get all requests for this technician in the date range
      const requests = await requestService.getByTechnicianId(technicianId);

      const filteredRequests = requests.filter((request) => {
        const createdAt =
          request.created_at instanceof Timestamp
            ? request.created_at.toDate()
            : new Date(request.created_at);
        return createdAt >= startDate && createdAt <= endDate;
      });

      if (filteredRequests.length === 0) {
        return null;
      }

      const completedRequests = filteredRequests.filter(
        (r) => r.status === RequestStatus.COMPLETED,
      );
      const cancelledRequests = filteredRequests.filter(
        (r) => r.status === RequestStatus.CANCELLED,
      );

      // Calculate metrics
      const totalCompleted = completedRequests.length;
      const totalRequests = filteredRequests.length;

      // Average acceptance time (from creation to technician assignment)
      const acceptTimes = filteredRequests
        .filter((r) => r.technician_id && r.created_at)
        .map((r) => {
          const createdAt =
            r.created_at instanceof Timestamp ? r.created_at.toDate() : new Date(r.created_at);
          const updatedAt =
            r.updated_at instanceof Timestamp
              ? r.updated_at.toDate()
              : new Date(String(r.updated_at || r.created_at));
          return differenceInMilliseconds(updatedAt, createdAt);
        });

      const avgAcceptTimeMs =
        acceptTimes.length > 0
          ? acceptTimes.reduce((sum, time) => sum + time, 0) / acceptTimes.length
          : 0;

      // Average completion time (from acceptance to completion)
      const completionTimes = completedRequests
        .filter((r) => r.session_start_time && r.session_end_time)
        .map((r) => {
          const startTime =
            r.session_start_time instanceof Timestamp
              ? r.session_start_time.toDate()
              : new Date(r.session_start_time!);
          const endTime =
            r.session_end_time instanceof Timestamp
              ? r.session_end_time.toDate()
              : new Date(r.session_end_time!);
          return differenceInMilliseconds(endTime, startTime);
        });

      const avgCompletionTimeMs =
        completionTimes.length > 0
          ? completionTimes.reduce((sum, time) => sum + time, 0) / completionTimes.length
          : 0;

      // Cancellation rate
      const cancellationRate =
        totalRequests > 0 ? (cancelledRequests.length / totalRequests) * 100 : 0;

      // Reopen rate (simplified - requests that were completed then reopened)
      const reopenRate = 0; // This would require tracking status history

      // Average response time (simplified - using session duration as proxy)
      const responseTimes = completedRequests
        .filter((r) => r.session_duration)
        .map((r) => (r.session_duration || 0) * 60 * 1000); // Convert minutes to milliseconds

      const avgResponseTimeMs =
        responseTimes.length > 0
          ? responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length
          : 0;

      return {
        technicianId,
        technicianName: filteredRequests[0]?.technician_name || 'Unknown',
        totalCompleted,
        avgAcceptTimeMs,
        avgCompletionTimeMs,
        reopenRate,
        cancellationRate,
        avgResponseTimeMs,
        periodStart: startDate,
        periodEnd: endDate,
        lastUpdated: new Date(),
      };
    } catch (error) {
      console.error('Error calculating technician performance:', error);
      return null;
    }
  }

  /**
   * Get overall performance statistics
   */
  async getOverallPerformanceStats(
    startDate: Date = subDays(new Date(), 30),
    endDate: Date = new Date(),
  ): Promise<PerformanceStats> {
    try {
      const allRequests = await requestService.getAll();

      const filteredRequests = allRequests.filter((request) => {
        const createdAt =
          request.created_at instanceof Timestamp
            ? request.created_at.toDate()
            : new Date(request.created_at);
        return createdAt >= startDate && createdAt <= endDate;
      });

      const completedRequests = filteredRequests.filter(
        (r) => r.status === RequestStatus.COMPLETED,
      );
      const cancelledRequests = filteredRequests.filter(
        (r) => r.status === RequestStatus.CANCELLED,
      );

      const totalRequests = filteredRequests.length;
      const completedCount = completedRequests.length;

      // Calculate average times
      const acceptTimes = filteredRequests
        .filter((r) => r.technician_id && r.created_at && r.updated_at)
        .map((r) => {
          const createdAt =
            r.created_at instanceof Timestamp ? r.created_at.toDate() : new Date(r.created_at);
          const updatedAt =
            r.updated_at instanceof Timestamp
              ? r.updated_at.toDate()
              : new Date(String(r.updated_at || r.created_at));
          return differenceInMilliseconds(updatedAt, createdAt);
        });

      const avgAcceptTimeMs =
        acceptTimes.length > 0
          ? acceptTimes.reduce((sum, time) => sum + time, 0) / acceptTimes.length
          : 0;

      const completionTimes = completedRequests
        .filter((r) => r.session_duration)
        .map((r) => (r.session_duration || 0) * 60 * 1000);

      const avgCompletionTimeMs =
        completionTimes.length > 0
          ? completionTimes.reduce((sum, time) => sum + time, 0) / completionTimes.length
          : 0;

      return {
        totalRequests,
        completedRequests: completedCount,
        avgAcceptTime: this.formatDuration(avgAcceptTimeMs),
        avgCompletionTime: this.formatDuration(avgCompletionTimeMs),
        cancellationRate: totalRequests > 0 ? (cancelledRequests.length / totalRequests) * 100 : 0,
        reopenRate: 0, // Simplified for now
      };
    } catch (error) {
      console.error('Error getting overall performance stats:', error);
      return {
        totalRequests: 0,
        completedRequests: 0,
        avgAcceptTime: '0 min',
        avgCompletionTime: '0 min',
        cancellationRate: 0,
        reopenRate: 0,
      };
    }
  }

  /**
   * Get performance chart data for the last N days
   */
  async getPerformanceChartData(
    days: number = 7,
    technicianId?: string,
  ): Promise<PerformanceChartData[]> {
    try {
      const endDate = new Date();
      const startDate = subDays(endDate, days);

      let requests: RequestModel[];
      if (technicianId) {
        requests = await requestService.getByTechnicianId(technicianId);
      } else {
        requests = await requestService.getAll();
      }

      const filteredRequests = requests.filter((request) => {
        const createdAt =
          request.created_at instanceof Timestamp
            ? request.created_at.toDate()
            : new Date(request.created_at);
        return createdAt >= startDate && createdAt <= endDate;
      });

      // Group by day
      const chartData: PerformanceChartData[] = [];

      for (let i = 0; i < days; i++) {
        const date = subDays(endDate, i);
        const dayStart = startOfDay(date);
        const dayEnd = endOfDay(date);

        const dayRequests = filteredRequests.filter((request) => {
          const createdAt =
            request.created_at instanceof Timestamp
              ? request.created_at.toDate()
              : new Date(request.created_at);
          return createdAt >= dayStart && createdAt <= dayEnd;
        });

        const completed = dayRequests.filter((r) => r.status === RequestStatus.COMPLETED).length;
        const cancelled = dayRequests.filter((r) => r.status === RequestStatus.CANCELLED).length;
        const total = dayRequests.length;

        const avgResponseTime =
          dayRequests
            .filter((r) => r.session_duration)
            .reduce((sum, r) => sum + (r.session_duration || 0), 0) /
          Math.max(dayRequests.length, 1);

        chartData.unshift({
          date: date.toISOString().split('T')[0],
          completed,
          avgResponseTime,
          cancellationRate: total > 0 ? (cancelled / total) * 100 : 0,
        });
      }

      return chartData;
    } catch (error) {
      console.error('Error getting performance chart data:', error);
      return [];
    }
  }

  /**
   * Format duration from milliseconds to human readable string
   */
  private formatDuration(milliseconds: number): string {
    if (milliseconds === 0) return '0 min';

    const minutes = Math.round(milliseconds / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;

    if (hours > 0) {
      return `${hours}h ${remainingMinutes}m`;
    }
    return `${minutes} min`;
  }

  /**
   * Get top performing technicians
   */
  async getTopPerformers(
    limit: number = 5,
    startDate: Date = subDays(new Date(), 30),
    endDate: Date = new Date(),
  ): Promise<TechnicianPerformanceMetrics[]> {
    try {
      // This would ideally be done with a proper aggregation query
      // For now, we'll get all technicians and calculate their performance
      const allRequests = await requestService.getAll();

      // Group requests by technician
      const technicianRequests = new Map<string, RequestModel[]>();

      allRequests.forEach((request) => {
        if (request.technician_id) {
          if (!technicianRequests.has(request.technician_id)) {
            technicianRequests.set(request.technician_id, []);
          }
          technicianRequests.get(request.technician_id)!.push(request);
        }
      });

      const performances: TechnicianPerformanceMetrics[] = [];

      for (const [technicianId] of technicianRequests) {
        const performance = await this.getTechnicianPerformance(technicianId, startDate, endDate);
        if (performance) {
          performances.push(performance);
        }
      }

      // Sort by total completed requests (descending)
      return performances.sort((a, b) => b.totalCompleted - a.totalCompleted).slice(0, limit);
    } catch (error) {
      console.error('Error getting top performers:', error);
      return [];
    }
  }
}

const performanceService = new PerformanceService();
export default performanceService;
