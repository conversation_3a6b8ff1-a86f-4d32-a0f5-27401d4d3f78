# Updating Google Sign-In Configuration

Your Google Sign-In is failing because of a SHA-1 certificate fingerprint mismatch. Follow these steps to fix it:

## Current SHA-1 Fingerprints

From your debug keystore:
```
SHA1: 5B:70:3F:B3:E8:8F:A1:2D:B6:AE:6F:C7:49:F8:A4:10:9F:FB:B5:B2
```

From your release keystore:
```
SHA1: A1:1E:6C:44:20:7A:4A:58:16:92:3B:CA:CA:49:4D:A9:80:47:FB:43
```

From your current google-services.json:
```
certificate_hash: "e191e586238eb930a5e0cbf46e809c71ee922503"
```

## Steps to Fix

1. Go to the [Firebase Console](https://console.firebase.google.com/) and select your project.
2. Click on the gear icon (⚙️) next to "Project Overview" and select "Project settings".
3. Scroll down to "Your apps" section and find your Android app.
4. Click "Add fingerprint" under the SHA certificate fingerprints section.
5. Add both SHA-1 fingerprints (without colons):
   - Debug: `5B703FB3E88FA12DB6AE6FC749F8A4109FFBB5B2`
   - Release: `A11E6C44207A4A5816923BCACA494DA98047FB43`
6. Click "Save".
7. Download the updated google-services.json file.
8. Replace the file in your project at: `mr_tech_mobile/android/app/google-services.json`
9. Clean and rebuild the project:
   ```
   cd mr_tech_mobile
   flutter clean
   flutter pub get
   flutter run
   ```

## Troubleshooting

If Google Sign-In still fails:
1. Check the logs for any specific error messages.
2. Make sure your app is using the correct Firebase project.
3. Verify your package name matches exactly in Firebase console and your app.
4. Try uninstalling and reinstalling the app on your device.
5. Ensure you have internet connectivity when testing.

## Additional Information

For Web client ID configuration in your app, use:
```dart
GoogleSignIn(
  scopes: ['email', 'profile'],
  // clientId is only needed for web platforms, removed for Android
)
``` 