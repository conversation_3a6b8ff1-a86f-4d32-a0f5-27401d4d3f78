/**
 * Payment System Types
 * Complete payment implementation for Mr.Tech application
 * Includes Paymob integration and transaction management
 */

// Core Payment Enums
export type PaymentStatus = 'pending' | 'completed' | 'failed' | 'refunded' | 'cancelled';

export type PaymentMethod = 'paymob' | 'test' | 'dummy';

// Payment Transaction Interface
export interface PaymentTransaction {
  id: string;
  paymobTransactionId: string;
  requestId?: string;
  customerId?: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  paymentMethod: PaymentMethod;
  merchantOrderId?: string;
  integrationId?: string;
  profileId?: string;
  owner?: number;
  isLive: boolean;
  errorOccurred?: boolean;
  errorCode?: string;
  txnResponseCode?: string;
  dataMessage?: string;
  sourceDataType?: string;
  sourceDataSubType?: string;
  sourceDataPan?: string;
  is3dSecure?: boolean;
  isSettled?: boolean;
  isRefunded?: boolean;
  refundedAmountCents?: number;
  paidAmountCents?: number;
  merchantCommission?: number;
  createdAt: Date;
  processedAt: Date;
  completedAt?: Date;
  callbackData?: any;
}

// Payment Filters Interface
export interface PaymentFilters {
  status?: PaymentStatus[];
  paymentMethod?: PaymentMethod[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  amountRange?: {
    min: number;
    max: number;
  };
  requestId?: string;
  customerId?: string;
  transactionId?: string;
  isLive?: boolean;
}

// Payment Response Interface
export interface PaymentResponse {
  transactionId: string;
  success: boolean;
  errorOccurred?: boolean;
  txnResponseCode?: string;
  dataMessage?: string;
  paymentStatus: PaymentStatus;
  merchantOrderId?: string;
  amountCents?: number;
  currency?: string;
  integrationId?: string;
  responseParams: any;
  createdAt: Date;
}

// Payment Summary Interface
export interface PaymentSummary {
  totalTransactions: number;
  totalAmount: number;
  completedTransactions: number;
  completedAmount: number;
  failedTransactions: number;
  failedAmount: number;
  pendingTransactions: number;
  pendingAmount: number;
  refundedTransactions: number;
  refundedAmount: number;
}

// Payment Export Options
export interface PaymentExportOptions {
  format: 'csv' | 'excel' | 'pdf';
  filters?: PaymentFilters;
  includeFields?: (keyof PaymentTransaction)[];
  dateRange: {
    start: Date;
    end: Date;
  };
}

// Refund Interfaces
export interface RefundRequest {
  transactionId: string;
  amount?: number;
  reason: string;
  requestedBy: string;
  notes?: string;
}

export interface RefundResponse {
  success: boolean;
  refundId?: string;
  message: string;
  refundAmount?: number;
  refundStatus?: 'pending' | 'completed' | 'failed';
}

// Paymob Integration Types
export interface PaymobCallbackData {
  type: string;
  obj: {
    id: number;
    success: boolean;
    amount_cents: number;
    currency: string;
    order: {
      id: number;
      merchant_order_id: string;
      amount_cents: number;
      currency: string;
      items: any[];
    };
    created_at: string;
    txn_response_code: string;
    data_message: string;
    integration_id: number;
    profile_id: number;
    source_data_type: string;
    source_data_sub_type: string;
    source_data_pan: string;
    merchant_commission: number;
    is_3d_secure: boolean;
    is_settled: boolean;
    is_refunded: boolean;
    error_occured: boolean;
    is_live: boolean;
    refunded_amount_cents: number;
    paid_amount_cents: number;
    extra: any;
    owner: number;
  };
}

export interface PaymobResponseParams {
  success?: string | boolean;
  error_occured?: string | boolean;
  txn_response_code?: string;
  'data.message'?: string;
  order?: string;
  id?: string;
  integration_id?: string;
  amount_cents?: string;
  currency?: string;
  merchant_order_id?: string;
  hmac?: string;
}

// Dashboard Statistics
export interface PaymentStats {
  today: PaymentSummary;
  thisWeek: PaymentSummary;
  thisMonth: PaymentSummary;
  thisYear: PaymentSummary;
  recentTransactions: PaymentTransaction[];
  topPaymentMethods: {
    method: PaymentMethod;
    count: number;
    amount: number;
  }[];
  statusDistribution: {
    status: PaymentStatus;
    count: number;
    percentage: number;
  }[];
}

// API Response Types
export interface PaymentTransactionResponse {
  transactions: PaymentTransaction[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// Form Types for UI
export interface PaymentSearchForm {
  query?: string;
  status?: PaymentStatus;
  paymentMethod?: PaymentMethod;
  dateFrom?: string;
  dateTo?: string;
  amountMin?: number;
  amountMax?: number;
}

export interface RefundForm {
  amount?: number;
  reason: string;
  notes?: string;
}

// Utility Types
export type PaymentTableColumn =
  | 'id'
  | 'paymobTransactionId'
  | 'requestId'
  | 'amount'
  | 'currency'
  | 'status'
  | 'paymentMethod'
  | 'createdAt'
  | 'processedAt'
  | 'isLive'
  | 'actions';

export interface PaymentTableConfig {
  columns: PaymentTableColumn[];
  sortBy?: keyof PaymentTransaction;
  sortOrder?: 'asc' | 'desc';
  pageSize: number;
}
