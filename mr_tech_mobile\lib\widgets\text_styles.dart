import 'package:flutter/material.dart';
import '../utils/font_manager.dart';

class AppTextStyles {
  // Private constructor to prevent instantiation
  AppTextStyles._();
  
  // Get standard text style with appropriate font family based on locale
  static TextStyle getBaseStyle(BuildContext context, {
    double? fontSize, 
    FontWeight? fontWeight, 
    Color? color,
    double? letterSpacing,
    double? height,
    FontStyle? fontStyle,
    bool isPrimary = false,
  }) {
    final theme = Theme.of(context);
    
    // Use primary font (Playfair Display/Tajawal) or secondary font (Montserrat/Tajawal)
    final fontFamily = isPrimary 
        ? FontManager.getPrimaryFontFamily(context) 
        : FontManager.getSecondaryFontFamily(context);
    
    return TextStyle(
      fontFamily: fontFamily,
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color ?? theme.colorScheme.onSurface,
      letterSpacing: letterSpacing,
      height: height,
      fontStyle: fontStyle,
    );
  }
  
  // Common text styles
  
  // Display styles (for large headlines, splash screens) - Material 3 scale
  static TextStyle displayLarge(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 57,
    fontWeight: FontWeight.w400,
    color: color,
    letterSpacing: -0.25,
    height: 1.12,
    isPrimary: true,
  );

  static TextStyle displayMedium(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 45,
    fontWeight: FontWeight.w400,
    color: color,
    letterSpacing: 0,
    height: 1.16,
    isPrimary: true,
  );

  static TextStyle displaySmall(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 36,
    fontWeight: FontWeight.w400,
    color: color,
    letterSpacing: 0,
    height: 1.22,
    isPrimary: true,
  );
  
  // Heading styles (for section headers) - Material 3 scale
  static TextStyle headingLarge(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 32,
    fontWeight: FontWeight.w400,
    color: color,
    letterSpacing: 0,
    height: 1.25,
    isPrimary: true,
  );

  static TextStyle headingMedium(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 28,
    fontWeight: FontWeight.w400,
    color: color,
    letterSpacing: 0,
    height: 1.29,
    isPrimary: true,
  );

  static TextStyle headingSmall(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 24,
    fontWeight: FontWeight.w400,
    color: color,
    letterSpacing: 0,
    height: 1.33,
    isPrimary: true,
  );
  
  // Title styles (for titles and subtitles) - Material 3 scale
  static TextStyle titleLarge(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 22,
    fontWeight: FontWeight.w400,
    color: color,
    letterSpacing: 0,
    height: 1.27,
    isPrimary: true,
  );

  static TextStyle titleMedium(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: color,
    letterSpacing: 0.15,
    height: 1.50,
  );

  static TextStyle titleSmall(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: color,
    letterSpacing: 0.1,
    height: 1.43,
  );

  // Body styles (for main content) - Material 3 scale
  static TextStyle bodyLarge(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 16,
    fontWeight: FontWeight.w400,
    color: color,
    letterSpacing: 0.5,
    height: 1.50,
  );

  static TextStyle bodyMedium(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 14,
    fontWeight: FontWeight.w400,
    color: color,
    letterSpacing: 0.25,
    height: 1.43,
  );

  static TextStyle bodySmall(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 12,
    fontWeight: FontWeight.w400,
    color: color,
    letterSpacing: 0.4,
    height: 1.33,
  );
  
  // Label styles (for form fields, captions) - Material 3 scale
  static TextStyle labelLarge(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    color: color,
    letterSpacing: 0.1,
    height: 1.43,
  );

  static TextStyle labelMedium(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 12,
    fontWeight: FontWeight.w500,
    color: color,
    letterSpacing: 0.5,
    height: 1.33,
  );

  static TextStyle labelSmall(BuildContext context, {Color? color}) => getBaseStyle(
    context,
    fontSize: 11,
    fontWeight: FontWeight.w500,
    color: color,
    letterSpacing: 0.5,
    height: 1.45,
  );

  // Convenience methods for common use cases

  // App bar title
  static TextStyle appBarTitle(BuildContext context, {Color? color}) => titleLarge(context, color: color);

  // Card title
  static TextStyle cardTitle(BuildContext context, {Color? color}) => titleMedium(context, color: color);

  // Card subtitle
  static TextStyle cardSubtitle(BuildContext context, {Color? color}) => bodyMedium(context, color: color);

  // Button text
  static TextStyle buttonText(BuildContext context, {Color? color}) => labelLarge(context, color: color);

  // Caption text
  static TextStyle caption(BuildContext context, {Color? color}) => labelSmall(context, color: color);

  // Error text
  static TextStyle errorText(BuildContext context) => labelMedium(
    context,
    color: Theme.of(context).colorScheme.error,
  );

  // Success text
  static TextStyle successText(BuildContext context) => labelMedium(
    context,
    color: Theme.of(context).colorScheme.primary,
  );

  // Hint text
  static TextStyle hintText(BuildContext context) => bodyMedium(
    context,
    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
  );
} 