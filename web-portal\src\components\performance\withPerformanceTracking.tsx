import type { ComponentType } from 'react';
import React, { useEffect, useRef } from 'react';
import { performanceMonitor } from '../../utils/performanceMonitor';

/**
 * Higher-order component for tracking component performance
 */
export function withPerformanceTracking<P extends object>(
  WrappedComponent: ComponentType<P>,
  componentName?: string,
) {
  const displayName =
    componentName || WrappedComponent.displayName || WrappedComponent.name || 'Component';

  const PerformanceTrackedComponent: React.FC<P> = (props) => {
    const loadStartTime = useRef(performance.now());
    const renderStartTime = useRef<number>(0);
    const hasTracked = useRef(false);

    // Track render start
    renderStartTime.current = performance.now();

    useEffect(() => {
      if (!hasTracked.current) {
        const loadTime = renderStartTime.current - loadStartTime.current;
        const renderTime = performance.now() - renderStartTime.current;

        performanceMonitor.trackComponentLoad(displayName, loadTime, renderTime, props);
        hasTracked.current = true;
      }
    }, [props]);

    return <WrappedComponent {...props} />;
  };

  PerformanceTrackedComponent.displayName = `withPerformanceTracking(${displayName})`;

  return PerformanceTrackedComponent;
}

/**
 * Hook for tracking custom performance metrics within components
 */
export function usePerformanceTracking(componentName: string) {
  const startTime = useRef(performance.now());

  const trackOperation = (operationName: string, operation: () => void | Promise<void>) => {
    const opStartTime = performance.now();

    const result = operation();

    if (result instanceof Promise) {
      return result.then((res) => {
        const duration = performance.now() - opStartTime;
        performanceMonitor.trackComponentLoad(`${componentName}.${operationName}`, 0, duration);
        return res;
      });
    } else {
      const duration = performance.now() - opStartTime;
      performanceMonitor.trackComponentLoad(`${componentName}.${operationName}`, 0, duration);
      return result;
    }
  };

  const markMilestone = (milestoneName: string) => {
    const duration = performance.now() - startTime.current;
    performanceMonitor.trackComponentLoad(`${componentName}.${milestoneName}`, duration, 0);
  };

  return {
    trackOperation,
    markMilestone,
  };
}
