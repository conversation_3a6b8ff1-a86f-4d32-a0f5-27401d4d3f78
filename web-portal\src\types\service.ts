import type { Timestamp } from 'firebase/firestore';

export interface ServiceModel {
  id: string;
  name: string | Record<string, string>; // Can be a string or translations map
  description: string | Record<string, string>; // Can be a string or translations map
  category: string;
  base_price: number;
  currency?: string; // Currency for base_price
  image_url: string;
  is_active: boolean;
  metadata?: Record<string, any>;
  estimated_duration: number; // in minutes
  pricing_strategy?: 'fixed' | 'tiered' | 'time_based' | 'distance_based' | 'dynamic';
  has_advanced_pricing?: boolean; // Whether this service uses the advanced pricing system
  created_at: Timestamp | Date;
  updated_at?: Timestamp | Date;
}

export interface CreateServiceInput {
  name: string | Record<string, string>;
  description: string | Record<string, string>;
  category: string;
  base_price: number;
  currency?: string;
  image_url: string;
  is_active?: boolean;
  metadata?: Record<string, any>;
  estimated_duration: number;
  pricing_strategy?: 'fixed' | 'tiered' | 'time_based' | 'distance_based' | 'dynamic';
  has_advanced_pricing?: boolean;
}

export interface UpdateServiceInput {
  name?: string | Record<string, string>;
  description?: string | Record<string, string>;
  category?: string;
  base_price?: number;
  currency?: string;
  image_url?: string;
  is_active?: boolean;
  metadata?: Record<string, any>;
  estimated_duration?: number;
  pricing_strategy?: 'fixed' | 'tiered' | 'time_based' | 'distance_based' | 'dynamic';
  has_advanced_pricing?: boolean;
}
