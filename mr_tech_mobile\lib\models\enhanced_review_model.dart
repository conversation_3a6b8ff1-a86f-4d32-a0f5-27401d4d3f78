import 'package:cloud_firestore/cloud_firestore.dart';

/// Enhanced review model with detailed rating categories and sentiment analysis
class EnhancedReviewModel {
  final String id;
  final String requestId;
  final String customerId;
  final String customerName;
  final String technicianId;
  final String serviceId;
  final String serviceName;

  // Overall rating (1-5)
  final double overallRating;

  // Detailed category ratings (1-5 each)
  final Map<String, double> categoryRatings;

  // Text feedback
  final String comment;
  final List<String> tags; // Positive/negative aspects

  // Sentiment analysis (if available)
  final String? sentiment; // 'positive', 'neutral', 'negative'
  final double? sentimentScore; // -1.0 to 1.0

  // Metadata
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isVerified; // Verified purchase/service
  final bool isPublic; // Show in public reviews
  final int helpfulCount; // How many found this helpful
  final List<String> reportedBy; // Users who reported this review

  // Response from technician/admin
  final String? technicianResponse;
  final DateTime? responseDate;
  final String? adminNotes; // Internal notes (not shown to users)

  EnhancedReviewModel({
    this.id = '',
    required this.requestId,
    required this.customerId,
    required this.customerName,
    required this.technicianId,
    required this.serviceId,
    required this.serviceName,
    required this.overallRating,
    this.categoryRatings = const {},
    required this.comment,
    this.tags = const [],
    this.sentiment,
    this.sentimentScore,
    required this.createdAt,
    this.updatedAt,
    this.isVerified = false,
    this.isPublic = true,
    this.helpfulCount = 0,
    this.reportedBy = const [],
    this.technicianResponse,
    this.responseDate,
    this.adminNotes,
  });

  // Factory constructor from Firestore document
  factory EnhancedReviewModel.fromFirestore(
    DocumentSnapshot<Map<String, dynamic>> doc,
  ) {
    final data = doc.data() ?? {};

    return EnhancedReviewModel(
      id: doc.id,
      requestId: data['request_id'] ?? '',
      customerId: data['customer_id'] ?? '',
      customerName: data['customer_name'] ?? '',
      technicianId: data['technician_id'] ?? '',
      serviceId: data['service_id'] ?? '',
      serviceName: data['service_name'] ?? '',
      overallRating: (data['overall_rating'] ?? 0.0).toDouble(),
      categoryRatings: Map<String, double>.from(data['category_ratings'] ?? {}),
      comment: data['comment'] ?? '',
      tags: List<String>.from(data['tags'] ?? []),
      sentiment: data['sentiment'],
      sentimentScore: data['sentiment_score']?.toDouble(),
      createdAt: (data['created_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updated_at'] as Timestamp?)?.toDate(),
      isVerified: data['is_verified'] ?? false,
      isPublic: data['is_public'] ?? true,
      helpfulCount: data['helpful_count'] ?? 0,
      reportedBy: List<String>.from(data['reported_by'] ?? []),
      technicianResponse: data['technician_response'],
      responseDate: (data['response_date'] as Timestamp?)?.toDate(),
      adminNotes: data['admin_notes'],
    );
  }

  // Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'request_id': requestId,
      'customer_id': customerId,
      'customer_name': customerName,
      'technician_id': technicianId,
      'service_id': serviceId,
      'service_name': serviceName,
      'overall_rating': overallRating,
      'category_ratings': categoryRatings,
      'comment': comment,
      'tags': tags,
      'sentiment': sentiment,
      'sentiment_score': sentimentScore,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'is_verified': isVerified,
      'is_public': isPublic,
      'helpful_count': helpfulCount,
      'reported_by': reportedBy,
      'technician_response': technicianResponse,
      'response_date': responseDate,
      'admin_notes': adminNotes,
    };
  }

  // Create a copy with updated fields
  EnhancedReviewModel copyWith({
    String? id,
    String? requestId,
    String? customerId,
    String? customerName,
    String? technicianId,
    String? serviceId,
    String? serviceName,
    double? overallRating,
    Map<String, double>? categoryRatings,
    String? comment,
    List<String>? tags,
    String? sentiment,
    double? sentimentScore,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isVerified,
    bool? isPublic,
    int? helpfulCount,
    List<String>? reportedBy,
    String? technicianResponse,
    DateTime? responseDate,
    String? adminNotes,
  }) {
    return EnhancedReviewModel(
      id: id ?? this.id,
      requestId: requestId ?? this.requestId,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      technicianId: technicianId ?? this.technicianId,
      serviceId: serviceId ?? this.serviceId,
      serviceName: serviceName ?? this.serviceName,
      overallRating: overallRating ?? this.overallRating,
      categoryRatings: categoryRatings ?? this.categoryRatings,
      comment: comment ?? this.comment,
      tags: tags ?? this.tags,
      sentiment: sentiment ?? this.sentiment,
      sentimentScore: sentimentScore ?? this.sentimentScore,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isVerified: isVerified ?? this.isVerified,
      isPublic: isPublic ?? this.isPublic,
      helpfulCount: helpfulCount ?? this.helpfulCount,
      reportedBy: reportedBy ?? this.reportedBy,
      technicianResponse: technicianResponse ?? this.technicianResponse,
      responseDate: responseDate ?? this.responseDate,
      adminNotes: adminNotes ?? this.adminNotes,
    );
  }

  // Get average category rating
  double get averageCategoryRating {
    if (categoryRatings.isEmpty) return overallRating;
    return categoryRatings.values.reduce((a, b) => a + b) /
        categoryRatings.length;
  }

  // Check if review is positive (>= 4 stars)
  bool get isPositive => overallRating >= 4.0;

  // Check if review needs attention (low rating or negative sentiment)
  bool get needsAttention {
    return overallRating <= 2.0 ||
        sentiment == 'negative' ||
        reportedBy.isNotEmpty;
  }

  // Get formatted date string
  String get formattedDate {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    }
  }

  // Convert to legacy ReviewModel for backward compatibility
  ReviewModel toLegacyReview() {
    return ReviewModel(
      id: id,
      requestId: requestId,
      customerId: customerId,
      customerName: customerName,
      technicianId: technicianId,
      serviceId: serviceId,
      rating: overallRating,
      comment: comment,
      createdAt: createdAt,
    );
  }

  // Create from legacy ReviewModel
  factory EnhancedReviewModel.fromLegacyReview(
    ReviewModel legacy, {
    String serviceName = '',
    Map<String, double> categoryRatings = const {},
  }) {
    return EnhancedReviewModel(
      id: legacy.id,
      requestId: legacy.requestId,
      customerId: legacy.customerId,
      customerName: legacy.customerName,
      technicianId: legacy.technicianId,
      serviceId: legacy.serviceId,
      serviceName: serviceName,
      overallRating: legacy.rating,
      categoryRatings: categoryRatings,
      comment: legacy.comment,
      createdAt: legacy.createdAt,
      isVerified: true, // Assume legacy reviews are verified
    );
  }
}

/// Review model for backward compatibility
class ReviewModel {
  final String id;
  final String requestId;
  final String customerId;
  final String customerName;
  final String technicianId;
  final String serviceId;
  final double rating;
  final String comment;
  final DateTime createdAt;

  ReviewModel({
    this.id = '',
    required this.requestId,
    required this.customerId,
    required this.customerName,
    required this.technicianId,
    required this.serviceId,
    required this.rating,
    required this.comment,
    required this.createdAt,
  });

  factory ReviewModel.fromFirestore(
    DocumentSnapshot<Map<String, dynamic>> doc,
  ) {
    final data = doc.data() ?? {};

    return ReviewModel(
      id: doc.id,
      requestId: data['request_id'] ?? '',
      customerId: data['customer_id'] ?? '',
      customerName: data['customer_name'] ?? '',
      technicianId: data['technician_id'] ?? '',
      serviceId: data['service_id'] ?? '',
      rating: (data['rating'] ?? 0.0).toDouble(),
      comment: data['comment'] ?? '',
      createdAt: (data['created_at'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'request_id': requestId,
      'customer_id': customerId,
      'customer_name': customerName,
      'technician_id': technicianId,
      'service_id': serviceId,
      'rating': rating,
      'comment': comment,
      'created_at': createdAt,
    };
  }

  ReviewModel copyWith({
    String? id,
    String? requestId,
    String? customerId,
    String? customerName,
    String? technicianId,
    String? serviceId,
    double? rating,
    String? comment,
    DateTime? createdAt,
  }) {
    return ReviewModel(
      id: id ?? this.id,
      requestId: requestId ?? this.requestId,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      technicianId: technicianId ?? this.technicianId,
      serviceId: serviceId ?? this.serviceId,
      rating: rating ?? this.rating,
      comment: comment ?? this.comment,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
