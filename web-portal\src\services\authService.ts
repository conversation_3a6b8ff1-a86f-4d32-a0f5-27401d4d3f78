import {
  type User as FirebaseUser,
  createUserWithEmailAndPassword,
  onAuthStateChanged,
  sendPasswordResetEmail,
  signInWithEmailAndPassword,
  updateProfile,
} from 'firebase/auth';
import { doc, getDoc, serverTimestamp, setDoc, updateDoc } from 'firebase/firestore';
import { auth, db } from '../config/firebase';
import {
  AUTH_ERROR_CODES,
  type LoginCredentials,
  type SignupData,
  type User,
  type UserRole,
} from '../types/auth';
import type { AppError, ServiceResponse } from '../types/common';
import { ErrorCode } from '../types/enums';
import { BaseApiService } from './baseApiService';
import { isString } from '../utils/typeGuards';
import sessionService from './sessionService';
import logger from '../utils/logger';

class AuthService extends BaseApiService {
  constructor() {
    super({
      serviceName: 'AuthService',
      enableLogging: true,
      enableRetry: true,
      retryConfig: {
        maxAttempts: 2, // Auth operations should have fewer retries
        baseDelay: 500,
        maxDelay: 2000,
        exponentialBackoff: true,
      },
    });
  }
  // Convert Firebase user to our User type
  private async convertFirebaseUser(firebaseUser: FirebaseUser): Promise<User | null> {
    try {
      logger.debug('Converting Firebase user', { uid: firebaseUser.uid }, 'AUTH');

      // Check role-specific collections first (admins, technicians)
      // First check admins collection
      let userDoc = await getDoc(doc(db, 'admins', firebaseUser.uid));
      logger.debug('Checking admins collection', { exists: userDoc.exists() }, 'AUTH');

      // If not found in admins, check technicians collection
      if (!userDoc.exists()) {
        userDoc = await getDoc(doc(db, 'technicians', firebaseUser.uid));
        logger.debug('Checking technicians collection', { exists: userDoc.exists() }, 'AUTH');
      }

      // If still not found, check users collection as fallback
      if (!userDoc.exists()) {
        userDoc = await getDoc(doc(db, 'users', firebaseUser.uid));
        logger.debug('Checking users collection', { exists: userDoc.exists() }, 'AUTH');
      }

      if (!userDoc.exists()) {
        console.error('User document not found in any collection for:', firebaseUser.uid);
        return null;
      }

      const userData = userDoc.data();
      logger.debug('User data retrieved', { role: userData.role }, 'AUTH');

      return {
        uid: firebaseUser.uid,
        email: firebaseUser.email || '',
        name: userData.name || userData.display_name || firebaseUser.displayName || '',
        role: userData.role || 'customer',
        phone: userData.phone,
        photo_url: userData.photo_url || firebaseUser.photoURL,
        created_at: userData.created_at?.toDate(),
        updated_at: userData.updated_at?.toDate(),
        is_active: userData.is_active !== false,
        last_login: userData.last_login?.toDate(),
        fcm_token: userData.fcm_token,
        device_type: 'web',
      };
    } catch (error) {
      console.error('Error converting Firebase user:', error);
      return null;
    }
  }

  /**
   * Login with email and password using standardized error handling
   */
  async login({
    email,
    password,
    rememberMe = false,
  }: LoginCredentials & { rememberMe?: boolean }): Promise<ServiceResponse<User>> {
    // Validate required fields
    const requiredValidation = this.validateRequired({ email, password }, ['email', 'password']);
    if (requiredValidation) {
      return { success: false, error: requiredValidation };
    }

    // Validate field types
    const typeValidation = this.validateTypes({ email, password, rememberMe }, {
      email: isString,
      password: isString,
      rememberMe: (v) => v === undefined || typeof v === 'boolean',
    });
    if (typeValidation) {
      return { success: false, error: typeValidation };
    }

    try {
      const result = await this.executeWithRetry(async () => {
        this.log('info', 'Attempting login', { email });

        // Set persistence before login
        await sessionService.setPersistence(rememberMe);

        const userCredential = await signInWithEmailAndPassword(auth, email, password);
        this.log('info', 'Firebase auth successful', { uid: userCredential.user.uid });

        // Get user data to determine the correct collection
        const userDoc = await this.convertFirebaseUser(userCredential.user);
        if (!userDoc) {
          throw new Error('Failed to load user data');
        }

        this.log('info', 'User role determined', { role: userDoc.role });
        return userDoc;
      }, 'login');

      return this.createResponse(true, result);
    } catch (error) {
      const appError = this.createAuthError(error, 'login');
      this.log('error', 'Login failed', { email, error: appError });
      return { success: false, error: appError };
    }
  }

  /**
   * Create standardized auth error
   */
  private createAuthError(error: unknown, operation: string): AppError {
    const errorMessage = error instanceof Error ? error.message : String(error);

    // Map Firebase auth errors to our error codes
    if (errorMessage.includes('user-not-found') || errorMessage.includes('wrong-password')) {
      return this.createError(
        ErrorCode.AUTH_INVALID_CREDENTIALS,
        'Invalid email or password',
        { operation, originalError: errorMessage }
      );
    }

    if (errorMessage.includes('too-many-requests')) {
      return this.createError(
        ErrorCode.NETWORK_SERVER_ERROR,
        'Too many failed attempts. Please try again later.',
        { operation, originalError: errorMessage }
      );
    }

    if (errorMessage.includes('user-disabled')) {
      return this.createError(
        ErrorCode.AUTH_ACCOUNT_DISABLED,
        'This account has been disabled',
        { operation, originalError: errorMessage }
      );
    }

    return this.createError(
      ErrorCode.UNKNOWN_ERROR,
      `Authentication failed: ${errorMessage}`,
      { operation, originalError: errorMessage }
    );
  }

  // Create new user account (admin only)
  async signup(data: SignupData): Promise<User> {
    try {
      // Only admins can create new accounts
      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error('You must be logged in as an admin to create accounts');
      }

      const currentUserData = await this.getCurrentUser();
      if (!currentUserData || currentUserData.role !== 'admin') {
        throw new Error('Only administrators can create new accounts');
      }

      // Validate that we're only creating admin or technician accounts
      if (data.role !== 'admin' && data.role !== 'technician') {
        throw new Error(
          'Web portal accounts can only be created for administrators or technicians',
        );
      }

      logger.info('Creating new user account', { email: data.email, role: data.role }, 'AUTH');

      // Create the user account
      const userCredential = await createUserWithEmailAndPassword(auth, data.email, data.password);

      // Update display name
      await updateProfile(userCredential.user, {
        displayName: data.name,
      });

      // Determine the collection based on role
      const collectionName = data.role === 'admin' ? 'admins' : 'technicians';

      // Create user document in the appropriate Firestore collection
      const userData = {
        email: data.email,
        name: data.name,
        display_name: data.name,
        displayName: data.name,
        role: data.role,
        phone_number: data.phone || '',
        phoneNumber: data.phone || '',
        created_at: serverTimestamp(),
        createdAt: serverTimestamp(),
        updated_at: serverTimestamp(),
        updatedAt: serverTimestamp(),
        is_active: true,
        isActive: true,
        is_verified: false,
        isVerified: false,
        device_type: 'web',
        deviceType: 'web',
        status: 'active',
      };

      // Add role-specific fields
      if (data.role === 'admin') {
        Object.assign(userData, {
          admin_level: data.admin_level || 'standard',
          adminLevel: data.admin_level || 'standard',
          permissions: data.permissions || [],
        });
      } else if (data.role === 'technician') {
        Object.assign(userData, {
          specialties: data.specialties || [],
          rating: 0,
          completed_requests: 0,
          completedRequests: 0,
          active_requests: 0,
          activeRequests: 0,
          is_available: true,
          isAvailable: true,
          technician_status: 'active',
          technicianStatus: 'active',
        });
      }

      logger.debug(
        'Creating document in collection',
        { collection: collectionName, userData },
        'AUTH',
      );

      // Use a different approach to create the document
      try {
        // Use the Firestore API directly
        const docRef = doc(db, collectionName, userCredential.user.uid);
        await setDoc(docRef, userData);
        logger.info('Document created', { collection: collectionName }, 'AUTH');

        // Also save to users collection if it's a technician (for backwards compatibility)
        if (data.role === 'technician') {
          const userDocRef = doc(db, 'users', userCredential.user.uid);
          await setDoc(userDocRef, userData);
          logger.debug(
            'Document also created in users collection for backwards compatibility',
            undefined,
            'AUTH',
          );
        }
      } catch (docError) {
        console.error('Error creating user document:', docError);
        throw new Error('Failed to create user document in database');
      }

      // Refresh the user data
      const user = await this.convertFirebaseUser(userCredential.user);

      if (!user) {
        throw new Error('Failed to create user account');
      }

      return user;
    } catch (error: unknown) {
      console.error('Signup error:', error);
      const firebaseError = error as { code?: string; message?: string };
      const errorMessage =
        AUTH_ERROR_CODES[firebaseError.code as keyof typeof AUTH_ERROR_CODES] ||
        firebaseError.message ||
        'An unknown error occurred';
      throw new Error(errorMessage);
    }
  }

  // Logout
  async logout(): Promise<void> {
    try {
      await sessionService.endSession('logout');
    } catch (error) {
      console.error('Logout error:', error);
      throw new Error('Failed to logout');
    }
  }

  // Logout from all devices
  async logoutAllDevices(): Promise<void> {
    try {
      const user = auth.currentUser;
      if (user) {
        await sessionService.endAllSessions(user.uid);
      }
    } catch (error) {
      console.error('Logout all devices error:', error);
      throw new Error('Failed to logout from all devices');
    }
  }

  // Send password reset email
  async resetPassword(email: string): Promise<void> {
    try {
      await sendPasswordResetEmail(auth, email);
    } catch (error: unknown) {
      const firebaseError = error as { code?: string; message?: string };
      const errorMessage =
        AUTH_ERROR_CODES[firebaseError.code as keyof typeof AUTH_ERROR_CODES] ||
        firebaseError.message ||
        'Failed to send password reset email';
      throw new Error(errorMessage);
    }
  }

  // Get current user
  async getCurrentUser(): Promise<User | null> {
    const firebaseUser = auth.currentUser;

    if (!firebaseUser) {
      return null;
    }

    return this.convertFirebaseUser(firebaseUser);
  }

  // Subscribe to auth state changes
  onAuthStateChange(callback: (user: User | null) => void): () => void {
    return onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        const user = await this.convertFirebaseUser(firebaseUser);
        callback(user);
      } else {
        callback(null);
      }
    });
  }

  // Check if user has specific role
  hasRole(user: User | null, roles: UserRole[]): boolean {
    if (!user) return false;
    return roles.includes(user.role);
  }

  // Update user profile
  async updateUserProfile(uid: string, data: Partial<User>): Promise<void> {
    try {
      // First get the current user data to determine the correct collection
      const currentUser = await this.getCurrentUser();
      if (!currentUser) {
        throw new Error('User not found');
      }

      logger.debug('Updating profile for user', { role: currentUser.role }, 'AUTH');

      // Determine which collection to update based on user role
      let collectionName = 'users';
      if (currentUser.role === 'admin') {
        collectionName = 'admins';
        logger.debug('Using admins collection for profile update', undefined, 'AUTH');
      } else if (currentUser.role === 'technician') {
        collectionName = 'technicians';
        logger.debug('Using technicians collection for profile update', undefined, 'AUTH');
      } else {
        logger.debug('Using users collection for profile update', undefined, 'AUTH');
      }

      // Create a copy of the data without photo_url (handled separately)
      const updateData = {
        ...data,
        updated_at: serverTimestamp(),
        updatedAt: serverTimestamp(),
      };

      // Remove fields that shouldn't be updated directly
      delete updateData.uid;
      delete updateData.email;
      delete updateData.role; // Role changes should be done by admin only
      delete updateData.photo_url; // Photo URL is handled by profileService.updateProfilePicture

      // Update in the appropriate collection
      await updateDoc(doc(db, collectionName, uid), updateData);
      logger.info('Updated profile', { collection: collectionName }, 'AUTH');

      // Also update in users collection if technician (for backwards compatibility)
      if (currentUser.role === 'technician' && collectionName !== 'users') {
        await updateDoc(doc(db, 'users', uid), updateData);
        logger.debug(
          'Also updated profile in users collection for backwards compatibility',
          undefined,
          'AUTH',
        );
      }

      // Update Firebase Auth profile if name changed
      if (auth.currentUser && auth.currentUser.uid === uid) {
        const updates: { displayName?: string } = {};
        if (data.name) updates.displayName = data.name;
        // Photo URL is handled by profileService.updateProfilePicture

        if (Object.keys(updates).length > 0) {
          await updateProfile(auth.currentUser, updates);
          logger.debug('Updated Firebase Auth profile', undefined, 'AUTH');
        }
      }
    } catch (error) {
      console.error('Update profile error:', error);
      throw new Error('Failed to update profile');
    }
  }
}

export default new AuthService();
