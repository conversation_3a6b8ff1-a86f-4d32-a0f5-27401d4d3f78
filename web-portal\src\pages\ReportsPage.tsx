import React, { useC<PERSON>back, useEffect, useMemo, useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import {
  Activity,
  BarChart3,
  Calendar,
  Clock,
  DollarSign,
  Download,
  Eye,
  EyeOff,
  FileText,
  Filter,
  <PERSON><PERSON>hart,
  RefreshCw,
  Star,
  TrendingUp,
  Users,
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../components/ui/select';
import { DatePickerWithRange } from '../components/ui/date-range-picker';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Badge } from '../components/ui/badge';
import { Separator } from '../components/ui/separator';
import { useAsyncOperation } from '../hooks/useErrorHandler';
import ErrorDisplay from '../components/error/ErrorDisplay';
import { Skeleton<PERSON>ard, Skeleton<PERSON>hart, SkeletonStats } from '../components/ui/skeleton';
import { LoadingButton, StatusIndicator } from '../components/ui/loading-states';

// Import services and types
import requestService from '../services/requestService';
import technicianService from '../services/technicianService';
import { type RequestModel, RequestStatus } from '../types/request';
import { type TechnicianModel } from '../types/technician';
import { addDays, endOfDay, format, startOfDay, subDays } from 'date-fns';

// Report interfaces
interface ReportFilters {
  dateRange: {
    from: Date;
    to: Date;
  };
  status?: RequestStatus[];
  technicianId?: string;
  serviceId?: string;
}

interface ReportMetrics {
  totalRequests: number;
  completedRequests: number;
  cancelledRequests: number;
  pendingRequests: number;
  totalRevenue: number;
  averageRating: number;
  averageResponseTime: number;
  averageSessionDuration: number;
  customerSatisfactionRate: number;
  technicianUtilization: number;
}

interface ChartData {
  requestsByStatus: Array<{ name: string; value: number; color: string }>;
  requestsByDate: Array<{ date: string; requests: number; revenue: number }>;
  technicianPerformance: Array<{
    name: string;
    completed: number;
    rating: number;
    revenue: number;
  }>;
  servicePopularity: Array<{ name: string; requests: number; revenue: number }>;
  hourlyDistribution: Array<{ hour: string; requests: number }>;
}

const ReportsPage: React.FC = () => {
  const { user } = useAuth();
  const [filters, setFilters] = useState<ReportFilters>({
    dateRange: {
      from: subDays(new Date(), 30),
      to: new Date(),
    },
  });
  const [technicians, setTechnicians] = useState<TechnicianModel[]>([]);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [exportFormat, setExportFormat] = useState<'csv' | 'pdf' | 'excel'>('csv');

  // Async operations
  const reportsDataOperation = useAsyncOperation<{
    metrics: ReportMetrics;
    chartData: ChartData;
    requests: RequestModel[];
  }>();

  const exportOperation = useAsyncOperation();

  // Load technicians for filter dropdown
  useEffect(() => {
    const loadTechnicians = async () => {
      try {
        const techniciansList = await technicianService.getAll();
        setTechnicians(techniciansList);
      } catch (error) {
        console.error('Failed to load technicians:', error);
      }
    };

    loadTechnicians();
  }, []);

  // Generate report data
  const generateReportData = useCallback(async () => {
    return await reportsDataOperation.execute(async () => {
      // Fetch requests based on filters
      const requests = await requestService.getAll();

      // Filter requests based on date range and other filters
      const filteredRequests = requests.filter((request) => {
        const requestDate =
          request.created_at instanceof Date ? request.created_at : request.created_at.toDate();

        const isInDateRange =
          requestDate >= startOfDay(filters.dateRange.from) &&
          requestDate <= endOfDay(filters.dateRange.to);

        const matchesStatus =
          !filters.status || filters.status.length === 0 || filters.status.includes(request.status);

        const matchesTechnician =
          !filters.technicianId || request.technician_id === filters.technicianId;

        return isInDateRange && matchesStatus && matchesTechnician;
      });

      // Calculate metrics
      const metrics: ReportMetrics = {
        totalRequests: filteredRequests.length,
        completedRequests: filteredRequests.filter((r) => r.status === RequestStatus.COMPLETED)
          .length,
        cancelledRequests: filteredRequests.filter((r) => r.status === RequestStatus.CANCELLED)
          .length,
        pendingRequests: filteredRequests.filter((r) =>
          [RequestStatus.PENDING, RequestStatus.PAYMENT_PENDING].includes(r.status),
        ).length,
        totalRevenue: filteredRequests
          .filter((r) => r.is_paid)
          .reduce((sum, r) => sum + r.amount, 0),
        averageRating: filteredRequests
          .filter((r) => r.rating)
          .reduce((sum, r, _, arr) => sum + (r.rating || 0) / arr.length, 0),
        averageResponseTime: 0, // Would need additional data tracking
        averageSessionDuration: filteredRequests
          .filter((r) => r.session_duration)
          .reduce((sum, r, _, arr) => sum + (r.session_duration || 0) / arr.length, 0),
        customerSatisfactionRate:
          (filteredRequests.filter((r) => (r.rating || 0) >= 4).length /
            filteredRequests.filter((r) => r.rating).length) *
            100 || 0,
        technicianUtilization: 0, // Would need additional calculation
      };

      // Generate chart data
      const chartData: ChartData = {
        requestsByStatus: [
          {
            name: 'Completed',
            value: metrics.completedRequests,
            color: '#10b981',
          },
          {
            name: 'Pending',
            value: metrics.pendingRequests,
            color: '#f59e0b',
          },
          {
            name: 'Cancelled',
            value: metrics.cancelledRequests,
            color: '#ef4444',
          },
        ],
        requestsByDate: [], // Would implement date grouping
        technicianPerformance: [], // Would implement technician grouping
        servicePopularity: [], // Would implement service grouping
        hourlyDistribution: [], // Would implement hourly grouping
      };

      return {
        metrics,
        chartData,
        requests: filteredRequests,
      };
    });
  }, [filters, reportsDataOperation]);

  // Load report data on mount and filter changes
  useEffect(() => {
    generateReportData();
  }, [generateReportData]);

  // Export functionality
  const handleExport = async (format: 'csv' | 'pdf' | 'excel') => {
    await exportOperation.execute(async () => {
      const data = reportsDataOperation.data;
      if (!data) throw new Error('No data to export');

      // Generate export data
      const exportData = data.requests.map((request) => ({
        'Request ID': request.id,
        'Customer ID': request.customer_id,
        Service:
          typeof request.service_name === 'string'
            ? request.service_name
            : request.service_name?.en || 'Unknown',
        Technician: request.technician_name || 'Unassigned',
        Status: request.status,
        Amount: request.amount,
        Paid: request.is_paid ? 'Yes' : 'No',
        Rating: request.rating || 'N/A',
        Created:
          request.created_at instanceof Date
            ? format(request.created_at, 'yyyy-MM-dd HH:mm')
            : format(request.created_at.toDate(), 'yyyy-MM-dd HH:mm'),
        'Duration (min)': request.session_duration || 'N/A',
      }));

      // Create and download file
      if (format === 'csv') {
        const csv = [
          Object.keys(exportData[0] || {}).join(','),
          ...exportData.map((row) => Object.values(row).join(',')),
        ].join('\n');

        const blob = new Blob([csv], { type: 'text/csv' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `reports-${format(new Date(), 'yyyy-MM-dd')}.csv`;
        a.click();
        URL.revokeObjectURL(url);
      }

      // PDF and Excel export would require additional libraries
      // For now, we'll just download as CSV

      return true;
    });
  };

  // Render loading state
  if (reportsDataOperation.isLoading) {
    return (
      <div className='space-y-6 p-6'>
        <div className='flex justify-between items-center'>
          <div>
            <h1 className='text-3xl font-bold tracking-tight'>Reports & Analytics</h1>
            <p className='text-muted-foreground mt-2'>Loading report data...</p>
          </div>
        </div>

        <SkeletonStats cards={4} />

        <div className='grid gap-6 md:grid-cols-2'>
          <SkeletonChart height='h-80' />
          <SkeletonChart height='h-80' />
        </div>

        <SkeletonCard className='h-96' />
      </div>
    );
  }

  // Render error state
  if (reportsDataOperation.isError && reportsDataOperation.error) {
    return (
      <div className='space-y-6 p-6'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>Reports & Analytics</h1>
          <p className='text-muted-foreground mt-2'>Generate comprehensive reports and analytics</p>
        </div>

        <ErrorDisplay
          error={reportsDataOperation.error}
          title='Failed to Load Reports'
          description="We couldn't load the report data. Please try again."
          showRetry={true}
          onRetry={generateReportData}
          type='server'
          severity='medium'
        />
      </div>
    );
  }

  const data = reportsDataOperation.data;
  if (!data) return null;

  return (
    <div className='space-y-6 p-6'>
      {/* Header */}
      <div className='flex justify-between items-start'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>Reports & Analytics</h1>
          <p className='text-muted-foreground mt-2'>
            Generate comprehensive reports and analytics for your service requests
          </p>
        </div>

        <div className='flex gap-2'>
          <Button
            variant='outline'
            onClick={() => generateReportData()}
            disabled={reportsDataOperation.isLoading}
          >
            <RefreshCw className='h-4 w-4 mr-2' />
            Refresh
          </Button>

          <Select value={exportFormat} onValueChange={(value: any) => setExportFormat(value)}>
            <SelectTrigger className='w-32'>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='csv'>CSV</SelectItem>
              <SelectItem value='pdf'>PDF</SelectItem>
              <SelectItem value='excel'>Excel</SelectItem>
            </SelectContent>
          </Select>

          <LoadingButton
            onClick={() => handleExport(exportFormat)}
            isLoading={exportOperation.isLoading}
            loadingText='Exporting...'
          >
            <Download className='h-4 w-4 mr-2' />
            Export
          </LoadingButton>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <CardTitle className='flex items-center gap-2'>
              <Filter className='h-5 w-5' />
              Filters
            </CardTitle>
            <Button
              variant='ghost'
              size='sm'
              onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
            >
              {showAdvancedFilters ? <EyeOff className='h-4 w-4' /> : <Eye className='h-4 w-4' />}
              {showAdvancedFilters ? 'Hide' : 'Show'} Advanced
            </Button>
          </div>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
            <div>
              <label className='text-sm font-medium mb-2 block'>Date Range</label>
              <DatePickerWithRange
                date={filters.dateRange}
                onDateChange={(range) =>
                  setFilters((prev) => ({
                    ...prev,
                    dateRange: range || { from: subDays(new Date(), 30), to: new Date() },
                  }))
                }
              />
            </div>

            {showAdvancedFilters && (
              <>
                <div>
                  <label className='text-sm font-medium mb-2 block'>Technician</label>
                  <Select
                    value={filters.technicianId || ''}
                    onValueChange={(value) =>
                      setFilters((prev) => ({ ...prev, technicianId: value || undefined }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='All Technicians' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value=''>All Technicians</SelectItem>
                      {technicians.map((tech) => (
                        <SelectItem key={tech.id} value={tech.id}>
                          {tech.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className='text-sm font-medium mb-2 block'>Status</label>
                  <Select
                    value={filters.status?.[0] || ''}
                    onValueChange={(value) =>
                      setFilters((prev) => ({
                        ...prev,
                        status: value ? [value as RequestStatus] : undefined,
                      }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder='All Statuses' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value=''>All Statuses</SelectItem>
                      <SelectItem value={RequestStatus.PENDING}>Pending</SelectItem>
                      <SelectItem value={RequestStatus.IN_PROGRESS}>In Progress</SelectItem>
                      <SelectItem value={RequestStatus.COMPLETED}>Completed</SelectItem>
                      <SelectItem value={RequestStatus.CANCELLED}>Cancelled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Metrics Overview */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Requests</CardTitle>
            <FileText className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{data.metrics.totalRequests}</div>
            <p className='text-xs text-muted-foreground'>
              {format(filters.dateRange.from, 'MMM d')} - {format(filters.dateRange.to, 'MMM d')}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Revenue</CardTitle>
            <DollarSign className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>${data.metrics.totalRevenue.toFixed(2)}</div>
            <p className='text-xs text-muted-foreground'>
              From {data.metrics.completedRequests} completed requests
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Avg Rating</CardTitle>
            <Star className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{data.metrics.averageRating.toFixed(1)}</div>
            <p className='text-xs text-muted-foreground'>
              {data.metrics.customerSatisfactionRate.toFixed(1)}% satisfaction
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Avg Duration</CardTitle>
            <Clock className='h-4 w-4 text-muted-foreground' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {Math.round(data.metrics.averageSessionDuration)}m
            </div>
            <p className='text-xs text-muted-foreground'>Average session time</p>
          </CardContent>
        </Card>
      </div>

      {/* Charts and Analytics */}
      <Tabs defaultValue='overview' className='space-y-4'>
        <TabsList>
          <TabsTrigger value='overview'>Overview</TabsTrigger>
          <TabsTrigger value='performance'>Performance</TabsTrigger>
          <TabsTrigger value='trends'>Trends</TabsTrigger>
        </TabsList>

        <TabsContent value='overview' className='space-y-4'>
          <div className='grid gap-6 md:grid-cols-2'>
            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <PieChart className='h-5 w-5' />
                  Requests by Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className='space-y-3'>
                  {data.chartData.requestsByStatus.map((item, index) => (
                    <div key={index} className='flex items-center justify-between'>
                      <div className='flex items-center gap-2'>
                        <div
                          className='w-3 h-3 rounded-full'
                          style={{ backgroundColor: item.color }}
                        />
                        <span className='text-sm'>{item.name}</span>
                      </div>
                      <div className='flex items-center gap-2'>
                        <span className='text-sm font-medium'>{item.value}</span>
                        <Badge variant='secondary'>
                          {((item.value / data.metrics.totalRequests) * 100).toFixed(1)}%
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className='flex items-center gap-2'>
                  <BarChart3 className='h-5 w-5' />
                  Key Metrics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className='space-y-4'>
                  <div className='flex justify-between items-center'>
                    <span className='text-sm'>Completion Rate</span>
                    <span className='font-medium'>
                      {(
                        (data.metrics.completedRequests / data.metrics.totalRequests) *
                        100
                      ).toFixed(1)}
                      %
                    </span>
                  </div>
                  <div className='flex justify-between items-center'>
                    <span className='text-sm'>Cancellation Rate</span>
                    <span className='font-medium'>
                      {(
                        (data.metrics.cancelledRequests / data.metrics.totalRequests) *
                        100
                      ).toFixed(1)}
                      %
                    </span>
                  </div>
                  <div className='flex justify-between items-center'>
                    <span className='text-sm'>Revenue per Request</span>
                    <span className='font-medium'>
                      $
                      {(data.metrics.totalRevenue / data.metrics.completedRequests || 0).toFixed(2)}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value='performance'>
          <Card>
            <CardHeader>
              <CardTitle>Performance Analytics</CardTitle>
              <CardDescription>
                Detailed performance metrics and technician analytics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='text-center py-8 text-muted-foreground'>
                <Activity className='h-12 w-12 mx-auto mb-4 opacity-50' />
                <p>Performance analytics coming soon...</p>
                <p className='text-sm'>
                  This will include technician performance, response times, and efficiency metrics.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='trends'>
          <Card>
            <CardHeader>
              <CardTitle>Trend Analysis</CardTitle>
              <CardDescription>Historical trends and forecasting</CardDescription>
            </CardHeader>
            <CardContent>
              <div className='text-center py-8 text-muted-foreground'>
                <TrendingUp className='h-12 w-12 mx-auto mb-4 opacity-50' />
                <p>Trend analysis coming soon...</p>
                <p className='text-sm'>
                  This will include time-series charts, seasonal patterns, and growth projections.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ReportsPage;
