import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'text_styles.dart';

/// A luxury-styled card widget with customizable content, styling, and interactions.
/// 
/// This widget provides a consistent, premium look for cards throughout the app
/// with customizable borders, shadows, and content layouts.
class LuxuryCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? elevation;
  final BorderRadius? borderRadius;
  final Color? backgroundColor;
  final Color? borderColor;
  final double borderWidth;
  final bool useGradient;
  final Gradient? customGradient;
  final VoidCallback? onTap;
  final bool hasShadow;
  final double? height;
  final double? width;
  final BoxConstraints? constraints;
  final Alignment alignment;
  final bool hasShimmer;
  final bool isLoading;

  const LuxuryCard({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.elevation,
    this.borderRadius,
    this.backgroundColor,
    this.borderColor,
    this.borderWidth = 1.0,
    this.useGradient = false,
    this.customGradient,
    this.onTap,
    this.hasShadow = true,
    this.height,
    this.width,
    this.constraints,
    this.alignment = Alignment.center,
    this.hasShimmer = false,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    // Determine background
    Color bgColor = backgroundColor ?? theme.cardTheme.color ?? Colors.white;
    List<BoxShadow>? shadow;
    
    // Determine shadow level
    if (hasShadow) {
      if (elevation == null) {
        shadow = AppTheme.subtleShadow;
      } else if (elevation! <= 2) {
        shadow = AppTheme.subtleShadow;
      } else if (elevation! <= 6) {
        shadow = AppTheme.mediumShadow;
      } else {
        shadow = AppTheme.strongShadow;
      }
    }
    
    // Create the main container decoration
    BoxDecoration decoration = BoxDecoration(
      color: useGradient ? null : bgColor,
      gradient: useGradient ? (customGradient ?? AppTheme.primaryGradient) : null,
      borderRadius: borderRadius ?? AppTheme.mediumRadius,
      boxShadow: hasShadow ? shadow : null,
      border: Border.all(
        color: borderColor ?? Colors.transparent,
        width: borderWidth,
      ),
    );
    
    // Base widget
    Widget cardWidget = Container(
      height: height,
      width: width,
      constraints: constraints,
      margin: margin ?? const EdgeInsets.all(8.0),
      decoration: decoration,
      child: ClipRRect(
        borderRadius: borderRadius ?? AppTheme.mediumRadius,
        child: Padding(
          padding: padding ?? const EdgeInsets.all(16.0),
          child: Align(
            alignment: alignment,
            child: child,
          ),
        ),
      ),
    );
    
    // Add tap functionality if provided
    if (onTap != null) {
      cardWidget = InkWell(
        onTap: onTap,
        borderRadius: borderRadius ?? AppTheme.mediumRadius,
        child: cardWidget,
      );
    }
    
    // Add loading shimmer effect if needed
    if (hasShimmer && isLoading) {
      // Note: This requires 'shimmer' package in your pubspec.yaml
      cardWidget = _applyShimmerEffect(cardWidget, context);
    }
    
    return cardWidget;
  }
  
  // Shimmer loading effect for cards
  Widget _applyShimmerEffect(Widget child, BuildContext context) {
    // Access shimmer package if you have it imported
    // Note: If you don't have shimmer package, replace with a simple container with animation
    try {
      // This is a fallback animation if shimmer isn't available
      return Stack(
        children: [
          child,
          Positioned.fill(
            child: ClipRRect(
              borderRadius: borderRadius ?? AppTheme.mediumRadius,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment(-1.0, -0.5),
                    end: Alignment(1.0, 0.5),
                    colors: [
                      Colors.white.withOpacity(0.0),
                      Colors.white.withOpacity(0.1),
                      Colors.white.withOpacity(0.2),
                      Colors.white.withOpacity(0.1),
                      Colors.white.withOpacity(0.0),
                    ],
                    stops: const [0.0, 0.35, 0.5, 0.65, 1.0],
                  ),
                ),
              ),
            ),
          ),
        ],
      );
    } catch (e) {
      // If there's any error, just return the original child
      return child;
    }
  }
}

/// A premium styled card specifically designed for service listings
class ServiceCard extends StatelessWidget {
  final String title;
  final String? description;
  final IconData? icon;
  final String? imageUrl;
  final VoidCallback? onTap;
  final Color? accentColor;
  final bool isActive;

  const ServiceCard({
    super.key,
    required this.title,
    this.description,
    this.icon,
    this.imageUrl,
    this.onTap,
    this.accentColor,
    this.isActive = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final color = accentColor ?? theme.colorScheme.primary;
    
    return LuxuryCard(
      onTap: onTap,
      borderRadius: AppTheme.mediumRadius,
      hasShadow: true,
      elevation: 3,
      borderColor: isActive ? color.withOpacity(0.5) : Colors.transparent,
      borderWidth: isActive ? 2.0 : 0,
      padding: EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon or image
          Container(
            height: 80,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Center(
              child: imageUrl != null
                  ? _buildImage()
                  : Icon(
                      icon ?? Icons.category,
                      size: 40,
                      color: color,
                    ),
            ),
          ),
          
          // Content
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: AppTextStyles.labelLarge(context),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (description != null) ...[
                  const SizedBox(height: 8),
                  Text(
                    description!,
                    style: AppTextStyles.bodySmall(context),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildImage() {
    try {
      return ClipRRect(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
        child: Image.network(
          imageUrl!,
          fit: BoxFit.cover,
          width: double.infinity,
          height: double.infinity,
          errorBuilder: (context, error, stackTrace) {
            return Icon(
              Icons.image_not_supported_outlined,
              size: 40,
              color: accentColor ?? Theme.of(context).colorScheme.primary,
            );
          },
        ),
      );
    } catch (e) {
      return Icon(
        Icons.image_not_supported_outlined,
        size: 40,
        color: accentColor ?? Colors.grey,
      );
    }
  }
}

/// A premium styled card specifically designed for request listings
class RequestCard extends StatelessWidget {
  final String title;
  final String status;
  final DateTime date;
  final String? technicianName;
  final VoidCallback? onTap;
  final Color? accentColor;

  const RequestCard({
    super.key,
    required this.title,
    required this.status,
    required this.date,
    this.technicianName,
    this.onTap,
    this.accentColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    // Determine status color
    Color statusColor;
    switch (status.toLowerCase()) {
      case 'pending':
        statusColor = Colors.orange;
        break;
      case 'in-progress':
      case 'in_progress':
      case 'inprogress':
        statusColor = Colors.blue;
        break;
      case 'completed':
      case 'done':
        statusColor = Colors.green;
        break;
      case 'cancelled':
      case 'canceled':
        statusColor = Colors.red;
        break;
      default:
        statusColor = Colors.grey;
    }
    
    return LuxuryCard(
      onTap: onTap,
      hasShadow: true,
      borderColor: statusColor.withOpacity(0.3),
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and status
          Row(
            children: [
              Expanded(
                child: Text(
                  title,
                  style: AppTextStyles.labelLarge(context),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                decoration: BoxDecoration(
                  color: statusColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: statusColor.withOpacity(0.5),
                    width: 1,
                  ),
                ),
                child: Text(
                  status.toUpperCase(),
                  style: AppTextStyles.labelSmall(context, color: statusColor),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Date and technician info
          Row(
            children: [
              Icon(
                Icons.calendar_today_outlined,
                size: 16,
                color: AppTextStyles.bodyMedium(context).color?.withOpacity(0.7),
              ),
              const SizedBox(width: 4),
              Text(
                _formatDate(date),
                style: AppTextStyles.bodySmall(context),
              ),
              
              if (technicianName != null) ...[
                const SizedBox(width: 16),
                Icon(
                  Icons.person_outline,
                  size: 16,
                  color: AppTextStyles.bodyMedium(context).color?.withOpacity(0.7),
                ),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    technicianName!,
                    style: AppTextStyles.bodySmall(context),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ],
          ),
          
          // Button or next steps
          if (status.toLowerCase() == 'pending') ...[
            const SizedBox(height: 12),
            Align(
              alignment: Alignment.centerRight,
              child: OutlinedButton(
                onPressed: onTap,
                style: OutlinedButton.styleFrom(
                  minimumSize: Size(120, 36),
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 0),
                ),
                child: Text('View Details'),
              ),
            ),
          ],
        ],
      ),
    );
  }
  
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Today, ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      return 'Yesterday, ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
    }
  }
} 