import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../services/log_service.dart';

enum LegalDocType {
  privacyPolicy,
  termsAndConditions,
  refundPolicy,
  aboutUs,
  shippingPolicy,
  contactUs,
}

/// Utility class for loading legal documents from either local assets
/// or remote URLs if configured
class LegalDocsUtils {
  static final LogService _logger = LogService('LegalDocsUtils');

  // Remote URLs for documents - configured to fetch from vilartech.com
  static const String _remotePrivacyPolicyUrl =
      'https://vilartech.com/mrtech/privacy_policy.html';
  static const String _remoteTermsUrl =
      'https://vilartech.com/mrtech/terms_and_conditions.html';
  static const String _remoteRefundPolicyUrl =
      'https://vilartech.com/mrtech/refund_policy.html';
  static const String _remoteAboutUsUrl =
      'https://vilartech.com/mrtech/about_us.html';
  static const String _remoteShippingPolicyUrl =
      'https://vilartech.com/mrtech/shipping_policy.html';
  static const String _remoteContactUsUrl =
      'https://vilartech.com/mrtech/contact_us.html';

  // Local asset paths as fallback
  static const String _localPrivacyPolicyPath =
      'assets/html/privacy_policy.html';
  static const String _localTermsPath = 'assets/html/terms_and_conditions.html';
  static const String _localRefundPolicyPath = 'assets/html/refund_policy.html';
  static const String _localAboutUsPath = 'assets/html/about_us.html';
  static const String _localShippingPolicyPath =
      'assets/html/shipping_policy.html';
  static const String _localContactUsPath = 'assets/html/contact_us.html';

  /// Get a document from remote URL or local asset
  static Future<String> getDocument(LegalDocType type) async {
    String remoteUrl;
    String localPath;

    // Determine which document to fetch
    switch (type) {
      case LegalDocType.privacyPolicy:
        remoteUrl = _remotePrivacyPolicyUrl;
        localPath = _localPrivacyPolicyPath;
        break;
      case LegalDocType.termsAndConditions:
        remoteUrl = _remoteTermsUrl;
        localPath = _localTermsPath;
        break;
      case LegalDocType.refundPolicy:
        remoteUrl = _remoteRefundPolicyUrl;
        localPath = _localRefundPolicyPath;
        break;
      case LegalDocType.aboutUs:
        remoteUrl = _remoteAboutUsUrl;
        localPath = _localAboutUsPath;
        break;
      case LegalDocType.shippingPolicy:
        remoteUrl = _remoteShippingPolicyUrl;
        localPath = _localShippingPolicyPath;
        break;
      case LegalDocType.contactUs:
        remoteUrl = _remoteContactUsUrl;
        localPath = _localContactUsPath;
        break;
    }

    // Try remote URL first
    try {
      final response = await http.get(Uri.parse(remoteUrl));
      if (response.statusCode == 200) {
        _logger.info('Successfully loaded remote document from: $remoteUrl');
        return utf8.decode(response.bodyBytes);
      } else {
        _logger.error('Failed to load remote document: ${response.statusCode}');
      }
    } catch (e) {
      _logger.error('Error fetching remote document: $e');
    }

    // Fallback to local asset
    try {
      _logger.info('Using local document fallback');
      return await rootBundle.loadString(localPath);
    } catch (e) {
      _logger.error('Error loading local document asset: $e');
      return '<html><body><h1>Error</h1><p>Unable to load document. Please try again later.</p></body></html>';
    }
  }

  /// Get the document's title based on type
  static String getDocumentTitle(BuildContext context, LegalDocType type) {
    switch (type) {
      case LegalDocType.privacyPolicy:
        return 'Privacy Policy';
      case LegalDocType.termsAndConditions:
        return 'Terms & Conditions';
      case LegalDocType.refundPolicy:
        return 'Refund Policy';
      case LegalDocType.aboutUs:
        return 'About Us';
      case LegalDocType.shippingPolicy:
        return 'Service Delivery Policy';
      case LegalDocType.contactUs:
        return 'Contact Us';
    }
  }
}
