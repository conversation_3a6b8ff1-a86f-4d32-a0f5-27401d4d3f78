import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../theme/app_theme.dart';
import '../utils/font_manager.dart';

class ThemeService extends ChangeNotifier {
  // Theme mode
  ThemeMode _themeMode = ThemeMode.system;

  // System brightness tracking
  Brightness? _systemBrightness;

  // Get theme mode
  ThemeMode get themeMode => _themeMode;

  // Check if dark mode (considering system theme)
  bool get isDarkMode {
    switch (_themeMode) {
      case ThemeMode.dark:
        return true;
      case ThemeMode.light:
        return false;
      case ThemeMode.system:
        return _systemBrightness == Brightness.dark;
    }
  }

  // Get effective brightness
  Brightness get effectiveBrightness {
    switch (_themeMode) {
      case ThemeMode.dark:
        return Brightness.dark;
      case ThemeMode.light:
        return Brightness.light;
      case ThemeMode.system:
        return _systemBrightness ?? Brightness.light;
    }
  }

  // Constructor
  ThemeService({bool isDarkMode = false}) {
    _themeMode = isDarkMode ? ThemeMode.dark : ThemeMode.light;
    _loadThemePreference();
    _initializeSystemBrightness();
  }
  
  // Initialize system brightness tracking
  void _initializeSystemBrightness() {
    _systemBrightness = WidgetsBinding.instance.platformDispatcher.platformBrightness;
    WidgetsBinding.instance.platformDispatcher.onPlatformBrightnessChanged = () {
      _systemBrightness = WidgetsBinding.instance.platformDispatcher.platformBrightness;
      if (_themeMode == ThemeMode.system) {
        _updateSystemUI();
        notifyListeners();
      }
    };
  }

  // Load theme preference from SharedPreferences
  Future<void> _loadThemePreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load theme mode (enhanced to support system theme)
      final themeModeString = prefs.getString('theme_mode');
      if (themeModeString != null) {
        switch (themeModeString) {
          case 'dark':
            _themeMode = ThemeMode.dark;
            break;
          case 'light':
            _themeMode = ThemeMode.light;
            break;
          case 'system':
            _themeMode = ThemeMode.system;
            break;
          default:
            // Fallback to legacy boolean check
            final isDark = prefs.getBool('is_dark_mode') ?? prefs.getBool('darkMode') ?? false;
            _themeMode = isDark ? ThemeMode.dark : ThemeMode.light;
        }
      } else {
        // Fallback to legacy boolean check
        final isDark = prefs.getBool('is_dark_mode') ?? prefs.getBool('darkMode') ?? false;
        _themeMode = isDark ? ThemeMode.dark : ThemeMode.light;
      }

      _updateSystemUI();
      notifyListeners();
    } catch (e) {
      debugPrint('Error loading theme preference: $e');
    }
  }
  
  // Update system UI overlay style based on current theme
  void _updateSystemUI() {
    final isDark = isDarkMode;
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
        statusBarBrightness: isDark ? Brightness.dark : Brightness.light,
        systemNavigationBarColor: isDark ? const Color(0xFF121212) : Colors.white,
        systemNavigationBarDividerColor: Colors.transparent,
        systemNavigationBarIconBrightness: isDark ? Brightness.light : Brightness.dark,
      ),
    );
  }

  // Set theme mode (enhanced to support all three modes)
  Future<void> setThemeMode(ThemeMode mode) async {
    _themeMode = mode;

    // Save preference to SharedPreferences
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('theme_mode', mode.toString().split('.').last);

      // Keep legacy keys for backward compatibility
      final isDark = isDarkMode;
      await prefs.setBool('is_dark_mode', isDark);
      await prefs.setBool('darkMode', isDark);
    } catch (e) {
      debugPrint('Error saving theme preference: $e');
    }

    _updateSystemUI();
    notifyListeners();
  }

  // Toggle theme (legacy method for backward compatibility)
  Future<void> toggleTheme(bool isDark) async {
    await setThemeMode(isDark ? ThemeMode.dark : ThemeMode.light);
  }

  // Cycle through theme modes (light -> dark -> system -> light)
  Future<void> cycleThemeMode() async {
    switch (_themeMode) {
      case ThemeMode.light:
        await setThemeMode(ThemeMode.dark);
        break;
      case ThemeMode.dark:
        await setThemeMode(ThemeMode.system);
        break;
      case ThemeMode.system:
        await setThemeMode(ThemeMode.light);
        break;
    }
  }
  
  // Light theme - Use BuildContext when available
  ThemeData get lightTheme {
    // Create a default ThemeData as fallback when no context is available
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF4335A7),
        brightness: Brightness.light,
      ),
      fontFamily: FontManager.defaultFontFamily,
    );
  }
  
  // Use this when you have a BuildContext
  ThemeData getLightTheme(BuildContext context) {
    return AppTheme.lightTheme(context);
  }
  
  // Dark theme - Use BuildContext when available
  ThemeData get darkTheme {
    // Create a default ThemeData as fallback when no context is available
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: const Color(0xFF4335A7),
        brightness: Brightness.dark,
      ),
      fontFamily: FontManager.defaultFontFamily,
    );
  }
  
  // Use this when you have a BuildContext
  ThemeData getDarkTheme(BuildContext context) {
    return AppTheme.darkTheme(context);
  }

  // Get theme mode display name for UI
  String get themeModeDisplayName {
    switch (_themeMode) {
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
      case ThemeMode.system:
        return 'System';
    }
  }

  // Get theme mode icon for UI
  IconData get themeModeIcon {
    switch (_themeMode) {
      case ThemeMode.light:
        return Icons.light_mode_rounded;
      case ThemeMode.dark:
        return Icons.dark_mode_rounded;
      case ThemeMode.system:
        return Icons.brightness_auto_rounded;
    }
  }

  // Check if current theme is high contrast (for accessibility)
  bool get isHighContrast {
    // This could be enhanced to check system accessibility settings
    return isDarkMode; // Dark mode generally provides better contrast
  }

  // Get adaptive color based on current theme
  Color getAdaptiveColor(Color lightColor, Color darkColor) {
    return isDarkMode ? darkColor : lightColor;
  }

  // Get adaptive text color with proper contrast
  Color getAdaptiveTextColor(BuildContext context, {bool isOnSurface = true}) {
    final theme = Theme.of(context);
    if (isOnSurface) {
      return theme.colorScheme.onSurface;
    }
    return theme.colorScheme.onBackground;
  }

  // Get adaptive surface color
  Color getAdaptiveSurfaceColor(BuildContext context, {int elevation = 0}) {
    final theme = Theme.of(context);
    if (elevation == 0) {
      return theme.colorScheme.surface;
    }
    // Apply elevation tint for Material 3
    return ElevationOverlay.applySurfaceTint(
      theme.colorScheme.surface,
      theme.colorScheme.surfaceTint,
      elevation.toDouble(),
    );
  }
}