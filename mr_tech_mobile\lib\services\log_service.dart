import 'dart:developer' as developer;

/// A simple logging service for the application
class LogService {
  final String _tag;
  
  /// Creates a new LogService instance with the given tag
  LogService(this._tag);
  
  /// Log an info message
  void info(String message) {
    developer.log('[$_tag] INFO: $message');
    // In a production app, you might want to send logs to a remote service
  }
  
  /// Log a debug message
  void debug(String message) {
    developer.log('[$_tag] DEBUG: $message');
  }
  
  /// Log a warning message
  void warning(String message) {
    developer.log('[$_tag] WARNING: $message', level: 900);
  }
  
  /// Log an error message
  void error(String message) {
    developer.log('[$_tag] ERROR: $message', level: 1000);
    // In a production app, you might want to send errors to a crash reporting service
  }
} 