import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/translation_service.dart';
import '../widgets/text_styles.dart';
import 'login_screen.dart';
import 'signup_screen.dart';

class WelcomeScreen extends StatefulWidget {
  final Function()? onAuthenticationComplete;
  final Function(Locale)? onLocaleChanged;
  
  const WelcomeScreen({
    super.key,
    this.onAuthenticationComplete,
    this.onLocaleChanged,
  });

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen> {
  @override
  Widget build(BuildContext context) {
    final translationService = Provider.of<TranslationService>(context, listen: false);
    final translate = translationService.translate;
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          // Language selector with simplified design
          Container(
            margin: const EdgeInsets.only(right: 16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: TextButton.icon(
              onPressed: () {
                final newLocale = translationService.currentLocale.languageCode == 'en'
                    ? const Locale('ar')
                    : const Locale('en');
                
                if (widget.onLocaleChanged != null) {
                  widget.onLocaleChanged!(newLocale);
                } else {
                  translationService.currentLocale = newLocale;
                }
              },
              icon: Icon(
                Icons.language_rounded,
                size: 18,
                color: colorScheme.primary,
              ),
              label: Text(
                translationService.currentLocale.languageCode == 'en'
                    ? 'العربية'
                    : 'English',
                style: AppTextStyles.bodyMedium(context, color: colorScheme.primary),
              ),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Spacer(flex: 1),
              
              // Logo and app info section
              Center(
                child: Column(
                  children: [
                    // Simplified logo without shadow
                    Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.grey.shade300, width: 2),
                        color: colorScheme.surface,
                      ),
                      child: ClipOval(
                        child: Image.asset(
                          'assets/images/logo.png',
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Icon(
                              Icons.engineering,
                              size: 50,
                              color: colorScheme.primary,
                            );
                          },
                        ),
                      ),
                    ),
                    
                    const SizedBox(height: 24),
                    
                    // App name
                    Text(
                      'Mr. Tech',
                      style: AppTextStyles.displayLarge(context, color: colorScheme.primary),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    // Tagline - using the same as HomeScreen
                    Text(
                      translate('Tech support, simplified'),
                      textAlign: TextAlign.center,
                      style: AppTextStyles.bodyLarge(
                        context,
                        color: colorScheme.onSurface.withOpacity(0.7),
                      ),
                    ),
                    
                    const SizedBox(height: 32),
                    
                    // Features card with light border
                    Card(
                      margin: EdgeInsets.zero,
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(color: Colors.grey.shade300),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          children: [
                            _buildFeatureRow(
                              Icons.computer_outlined,
                              translate('Remote Support'),
                              translate('Get help from anywhere'),
                              colorScheme,
                            ),
                            const SizedBox(height: 16),
                            _buildFeatureRow(
                              Icons.support_agent_outlined,
                              translate('Expert Technicians'),
                              translate('Certified professionals'),
                              colorScheme,
                            ),
                            const SizedBox(height: 16),
                            _buildFeatureRow(
                              Icons.schedule_outlined,
                              translate('24/7 Available'),
                              translate('Round the clock service'),
                              colorScheme,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              const Spacer(flex: 2),
              
              // Action buttons section
              Column(
                children: [
                  // Login button
                  SizedBox(
                    width: double.infinity,
                    height: 52,
                    child: FilledButton(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => LoginScreen(
                              onLoginSuccess: () {
                                Future.delayed(const Duration(milliseconds: 300), () {
                                  if (widget.onAuthenticationComplete != null) {
                                    widget.onAuthenticationComplete!();
                                  }
                                });
                              },
                            ),
                          ),
                        );
                      },
                      style: FilledButton.styleFrom(
                        backgroundColor: colorScheme.primary,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 0,
                      ),
                      child: Text(
                        translate('Login'),
                        style: AppTextStyles.buttonMedium(context, color: Colors.white),
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Create account button
                  SizedBox(
                    width: double.infinity,
                    height: 52,
                    child: OutlinedButton(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => SignupScreen(
                              onSignupSuccess: () {
                                Future.delayed(const Duration(milliseconds: 300), () {
                                  if (widget.onAuthenticationComplete != null) {
                                    widget.onAuthenticationComplete!();
                                  }
                                });
                              },
                            ),
                          ),
                        );
                      },
                      style: OutlinedButton.styleFrom(
                        foregroundColor: colorScheme.primary,
                        side: BorderSide(color: Colors.grey.shade300),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        translate('Create Account'),
                        style: AppTextStyles.buttonMedium(context, color: colorScheme.primary),
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }
  
  // Helper method to build feature rows
  Widget _buildFeatureRow(
    IconData icon,
    String title,
    String subtitle,
    ColorScheme colorScheme,
  ) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: colorScheme.primary,
            size: 20,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextStyles.labelMedium(context, color: colorScheme.onSurface),
              ),
              const SizedBox(height: 2),
              Text(
                subtitle,
                style: AppTextStyles.bodySmall(
                  context,
                  color: colorScheme.onSurface.withOpacity(0.7),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
} 