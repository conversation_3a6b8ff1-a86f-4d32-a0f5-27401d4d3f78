import { csrfProtection, rateLimiting, securityHeaders } from '../utils/security';
import { logger } from '../utils/logger';

/**
 * Secure API service wrapper that provides:
 * - CSRF protection
 * - Security headers
 * - Rate limiting
 * - Request/response logging
 * - Error handling
 */

interface ApiRequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
  timeout?: number;
  retries?: number;
  rateLimitKey?: string;
}

interface ApiResponse<T = any> {
  data: T;
  status: number;
  headers: Headers;
  success: boolean;
}

class SecureApiService {
  private baseUrl: string;
  private defaultTimeout: number = 30000; // 30 seconds
  private rateLimiters: Map<string, ReturnType<typeof rateLimiting.createRateLimiter>> = new Map();

  constructor(baseUrl: string = '') {
    this.baseUrl = baseUrl;
    this.initializeCsrfToken();
  }

  /**
   * Initialize CSRF token if not present
   */
  private initializeCsrfToken(): void {
    if (!csrfProtection.getToken()) {
      const token = csrfProtection.generateToken();
      csrfProtection.storeToken(token);
    }
  }

  /**
   * Get or create rate limiter for a specific key
   */
  private getRateLimiter(key: string): ReturnType<typeof rateLimiting.createRateLimiter> {
    if (!this.rateLimiters.has(key)) {
      // Default: 100 requests per minute
      this.rateLimiters.set(key, rateLimiting.createRateLimiter(100, 60000));
    }
    return this.rateLimiters.get(key)!;
  }

  /**
   * Check rate limiting
   */
  private checkRateLimit(key: string): boolean {
    const limiter = this.getRateLimiter(key);
    return limiter.isAllowed();
  }

  /**
   * Prepare request headers with security headers and CSRF token
   */
  private prepareHeaders(customHeaders: Record<string, string> = {}): Record<string, string> {
    const headers = securityHeaders.combineHeaders({
      'Content-Type': 'application/json',
      ...customHeaders,
    });

    // Add CSRF token for state-changing requests
    return csrfProtection.addTokenToHeaders(headers);
  }

  /**
   * Handle request timeout
   */
  private createTimeoutPromise(timeout: number): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Request timeout after ${timeout}ms`));
      }, timeout);
    });
  }

  /**
   * Retry logic for failed requests
   */
  private async retryRequest<T>(
    requestFn: () => Promise<Response>,
    retries: number,
    delay: number = 1000,
  ): Promise<Response> {
    try {
      return await requestFn();
    } catch (error) {
      if (retries > 0) {
        logger.warn(`Request failed, retrying in ${delay}ms. Retries left: ${retries}`, { error });
        await new Promise((resolve) => setTimeout(resolve, delay));
        return this.retryRequest(requestFn, retries - 1, delay * 2);
      }
      throw error;
    }
  }

  /**
   * Make a secure API request
   */
  async request<T = any>(
    endpoint: string,
    options: ApiRequestOptions = {},
  ): Promise<ApiResponse<T>> {
    const {
      method = 'GET',
      headers = {},
      body,
      timeout = this.defaultTimeout,
      retries = 0,
      rateLimitKey = 'default',
    } = options;

    // Check rate limiting
    if (!this.checkRateLimit(rateLimitKey)) {
      const error = new Error('Rate limit exceeded. Please try again later.');
      logger.warn('Rate limit exceeded', { endpoint, rateLimitKey });
      throw error;
    }

    const url = `${this.baseUrl}${endpoint}`;
    const requestHeaders = this.prepareHeaders(headers);

    logger.apiCall(method, url, body);

    const requestFn = async (): Promise<Response> => {
      const fetchPromise = fetch(url, {
        method,
        headers: requestHeaders,
        body: body ? JSON.stringify(body) : undefined,
        credentials: 'same-origin', // Include cookies for same-origin requests
        mode: 'cors',
        cache: 'no-cache',
      });

      return Promise.race([fetchPromise, this.createTimeoutPromise(timeout)]);
    };

    try {
      const response = await (retries > 0 ? this.retryRequest(requestFn, retries) : requestFn());

      const responseData = await this.parseResponse<T>(response);

      logger.info('API request successful', {
        method,
        url,
        status: response.status,
        responseSize: JSON.stringify(responseData).length,
      });

      return {
        data: responseData,
        status: response.status,
        headers: response.headers,
        success: response.ok,
      };
    } catch (error) {
      logger.error('API request failed', {
        method,
        url,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Parse response based on content type
   */
  private async parseResponse<T>(response: Response): Promise<T> {
    const contentType = response.headers.get('content-type');

    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

      try {
        if (contentType?.includes('application/json')) {
          const errorData = await response.json();
          errorMessage = errorData.message || errorData.error || errorMessage;
        } else {
          errorMessage = (await response.text()) || errorMessage;
        }
      } catch {
        // Use default error message if parsing fails
      }

      throw new Error(errorMessage);
    }

    if (contentType?.includes('application/json')) {
      return response.json();
    }

    if (contentType?.includes('text/')) {
      return response.text() as unknown as T;
    }

    return response.blob() as unknown as T;
  }

  /**
   * Convenience methods for common HTTP methods
   */
  async get<T = any>(
    endpoint: string,
    options: Omit<ApiRequestOptions, 'method'> = {},
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'GET' });
  }

  async post<T = any>(
    endpoint: string,
    body?: any,
    options: Omit<ApiRequestOptions, 'method' | 'body'> = {},
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'POST', body });
  }

  async put<T = any>(
    endpoint: string,
    body?: any,
    options: Omit<ApiRequestOptions, 'method' | 'body'> = {},
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'PUT', body });
  }

  async patch<T = any>(
    endpoint: string,
    body?: any,
    options: Omit<ApiRequestOptions, 'method' | 'body'> = {},
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'PATCH', body });
  }

  async delete<T = any>(
    endpoint: string,
    options: Omit<ApiRequestOptions, 'method'> = {},
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' });
  }

  /**
   * Upload file with security checks
   */
  async uploadFile(
    endpoint: string,
    file: File,
    additionalData: Record<string, any> = {},
    options: Omit<ApiRequestOptions, 'method' | 'body'> = {},
  ): Promise<ApiResponse> {
    // Security checks would be performed here
    const formData = new FormData();
    formData.append('file', file);

    Object.entries(additionalData).forEach(([key, value]) => {
      formData.append(key, typeof value === 'string' ? value : JSON.stringify(value));
    });

    const headers = this.prepareHeaders(options.headers);
    delete headers['Content-Type']; // Let browser set multipart boundary

    logger.info('Uploading file', {
      endpoint,
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
    });

    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      method: 'POST',
      headers,
      body: formData,
      credentials: 'same-origin',
    });

    const responseData = await this.parseResponse(response);

    return {
      data: responseData,
      status: response.status,
      headers: response.headers,
      success: response.ok,
    };
  }

  /**
   * Set custom rate limiter for specific operations
   */
  setRateLimiter(key: string, maxRequests: number, windowMs: number): void {
    this.rateLimiters.set(key, rateLimiting.createRateLimiter(maxRequests, windowMs));
  }

  /**
   * Clear all rate limiters
   */
  clearRateLimiters(): void {
    this.rateLimiters.clear();
  }
}

// Create default instance
export const secureApiService = new SecureApiService();

// Export class for custom instances
export { SecureApiService };
