import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'dart:async';
import 'dart:convert';
import '../models/service_model.dart';
import '../models/request_model.dart';
import '../models/user_model.dart';
import '../models/chat_message_model.dart';
import 'database_service.dart';
import 'secure_storage_service.dart';

/// Comprehensive offline service for Mr.Tech app
/// Handles data caching, sync, and offline functionality
class OfflineService {
  static final OfflineService _instance = OfflineService._internal();
  factory OfflineService() => _instance;
  OfflineService._internal();

  // Services
  late SharedPreferences _prefs;
  final SecureStorageService _secureStorage = SecureStorageService();
  final Connectivity _connectivity = Connectivity();

  // State management
  bool _isInitialized = false;
  bool _isOnline = true;
  final StreamController<bool> _connectivityController =
      StreamController<bool>.broadcast();
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  // Cache keys
  static const String _servicesKey = 'offline_services';
  static const String _userRequestsKey = 'offline_user_requests';
  static const String _userProfileKey = 'offline_user_profile';
  static const String _chatMessagesKey = 'offline_chat_messages';
  static const String _pendingActionsKey = 'offline_pending_actions';
  static const String _lastSyncKey = 'offline_last_sync';

  // Cache expiry times
  static const Duration _servicesExpiry = Duration(hours: 6);
  static const Duration _requestsExpiry = Duration(hours: 1);
  static const Duration _profileExpiry = Duration(hours: 2);
  static const Duration _messagesExpiry = Duration(minutes: 30);

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isOnline => _isOnline;
  Stream<bool> get connectivityStream => _connectivityController.stream;

  /// Convert data to JSON-safe format by handling DateTime and Timestamp objects
  Map<String, dynamic> _convertToJsonSafe(Map<String, dynamic> data) {
    final result = <String, dynamic>{};

    for (final entry in data.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value is DateTime) {
        result[key] = value.millisecondsSinceEpoch;
      } else if (value is Timestamp) {
        result[key] = value.millisecondsSinceEpoch;
      } else if (value is Map<String, dynamic>) {
        result[key] = _convertToJsonSafe(value);
      } else if (value is List) {
        result[key] =
            value.map((item) {
              if (item is DateTime) {
                return item.millisecondsSinceEpoch;
              } else if (item is Timestamp) {
                return item.millisecondsSinceEpoch;
              } else if (item is Map<String, dynamic>) {
                return _convertToJsonSafe(item);
              }
              return item;
            }).toList();
      } else {
        result[key] = value;
      }
    }

    return result;
  }

  /// Initialize the offline service with fast startup
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _prefs = await SharedPreferences.getInstance();

      // Quick connectivity check without heavy operations
      _isOnline = true; // Assume online initially for faster startup

      // Set up connectivity monitoring in background
      _setupConnectivityMonitoringInBackground();

      _isInitialized = true;
      debugPrint('OfflineService initialized - Online: $_isOnline');
    } catch (e) {
      debugPrint('Error initializing OfflineService: $e');
      rethrow;
    }
  }

  /// Set up connectivity monitoring in background
  void _setupConnectivityMonitoringInBackground() {
    Future.microtask(() async {
      try {
        // Check actual connectivity status
        final connectivityResults = await _connectivity.checkConnectivity();
        _isOnline = !connectivityResults.contains(ConnectivityResult.none);

        // Listen to connectivity changes
        _connectivitySubscription = _connectivity.onConnectivityChanged.listen((
          List<ConnectivityResult> results,
        ) {
          final wasOnline = _isOnline;
          _isOnline = !results.contains(ConnectivityResult.none);

          if (wasOnline != _isOnline) {
            _connectivityController.add(_isOnline);

            if (_isOnline) {
              _onConnectivityRestored();
            } else {
              _onConnectivityLost();
            }
          }
        });

        debugPrint(
          'OfflineService connectivity monitoring set up - Online: $_isOnline',
        );
      } catch (e) {
        debugPrint('Error setting up connectivity monitoring: $e');
      }
    });
  }

  /// Handle connectivity restoration
  Future<void> _onConnectivityRestored() async {
    debugPrint('Connectivity restored - syncing pending actions');
    await syncPendingActions();
    await refreshCriticalData();
  }

  /// Handle connectivity loss
  void _onConnectivityLost() {
    debugPrint('Connectivity lost - switching to offline mode');
  }

  /// Handle app resume - check connectivity and sync if needed
  Future<void> handleAppResume() async {
    try {
      debugPrint('OfflineService: Handling app resume');

      // Force check connectivity status
      final connectivityResults = await _connectivity.checkConnectivity();
      final wasOnline = _isOnline;
      _isOnline = !connectivityResults.contains(ConnectivityResult.none);

      debugPrint('OfflineService: Connectivity status - Online: $_isOnline');

      // If connectivity status changed, notify listeners
      if (wasOnline != _isOnline) {
        _connectivityController.add(_isOnline);

        if (_isOnline) {
          // If we're back online, sync pending actions
          await _onConnectivityRestored();
        } else {
          _onConnectivityLost();
        }
      } else if (_isOnline) {
        // Even if status didn't change, refresh critical data if online
        await refreshCriticalData();
      }
    } catch (e) {
      debugPrint('Error handling app resume in OfflineService: $e');
    }
  }

  /// Cache services data
  Future<void> cacheServices(List<ServiceModel> services) async {
    try {
      final servicesJson =
          services.map((s) => _convertToJsonSafe(s.toFirestore())).toList();
      await _prefs.setString(
        _servicesKey,
        jsonEncode({
          'data': servicesJson,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        }),
      );
      debugPrint('Cached ${services.length} services');
    } catch (e) {
      debugPrint('Error caching services: $e');
    }
  }

  /// Get cached services
  Future<List<ServiceModel>?> getCachedServices() async {
    try {
      // Ensure service is initialized before accessing _prefs
      if (!_isInitialized) {
        debugPrint('OfflineService not initialized, cannot retrieve cached services');
        return null;
      }
      
      final cachedData = _prefs.getString(_servicesKey);
      if (cachedData == null) return null;

      final decoded = jsonDecode(cachedData);
      final timestamp = DateTime.fromMillisecondsSinceEpoch(
        decoded['timestamp'],
      );

      // Check if cache is still valid
      if (DateTime.now().difference(timestamp) > _servicesExpiry) {
        debugPrint('Services cache expired');
        return null;
      }

      final servicesData = decoded['data'] as List;
      final services =
          servicesData
              .map(
                (data) => ServiceModel.fromFirestore(
                  // Create a mock DocumentSnapshot
                  MockDocumentSnapshot(data),
                ),
              )
              .toList();

      debugPrint('Retrieved ${services.length} cached services');
      return services;
    } catch (e) {
      debugPrint('Error retrieving cached services: $e');
      return null;
    }
  }

  /// Cache user requests
  Future<void> cacheUserRequests(List<RequestModel> requests) async {
    try {
      if (!_isInitialized) {
        debugPrint('OfflineService not initialized, cannot cache user requests');
        return;
      }
      final requestsJson =
          requests.map((r) => _convertToJsonSafe(r.toFirestore())).toList();
      await _prefs.setString(
        _userRequestsKey,
        jsonEncode({
          'data': requestsJson,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        }),
      );
      debugPrint('Cached ${requests.length} user requests');
    } catch (e) {
      debugPrint('Error caching user requests: $e');
    }
  }

  /// Get cached user requests
  Future<List<RequestModel>?> getCachedUserRequests() async {
    try {
      if (!_isInitialized) {
        debugPrint('OfflineService not initialized, cannot retrieve cached user requests');
        return null;
      }
      final cachedData = _prefs.getString(_userRequestsKey);
      if (cachedData == null) return null;

      final decoded = jsonDecode(cachedData);
      final timestamp = DateTime.fromMillisecondsSinceEpoch(
        decoded['timestamp'],
      );

      if (DateTime.now().difference(timestamp) > _requestsExpiry) {
        debugPrint('User requests cache expired');
        return null;
      }

      final requestsData = decoded['data'] as List;
      final requests =
          requestsData
              .map(
                (data) =>
                    RequestModel.fromFirestore(MockDocumentSnapshot(data)),
              )
              .toList();

      debugPrint('Retrieved ${requests.length} cached user requests');
      return requests;
    } catch (e) {
      debugPrint('Error retrieving cached user requests: $e');
      return null;
    }
  }

  /// Cache user profile
  Future<void> cacheUserProfile(UserModel user) async {
    try {
      await _prefs.setString(
        _userProfileKey,
        jsonEncode({
          'data': _convertToJsonSafe(user.toFirestore()),
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        }),
      );
      debugPrint('Cached user profile');
    } catch (e) {
      debugPrint('Error caching user profile: $e');
    }
  }

  /// Get cached user profile
  Future<UserModel?> getCachedUserProfile() async {
    try {
      final cachedData = _prefs.getString(_userProfileKey);
      if (cachedData == null) return null;

      final decoded = jsonDecode(cachedData);
      final timestamp = DateTime.fromMillisecondsSinceEpoch(
        decoded['timestamp'],
      );

      if (DateTime.now().difference(timestamp) > _profileExpiry) {
        debugPrint('User profile cache expired');
        return null;
      }

      final userData = decoded['data'];
      final user = UserModel.fromFirestore(MockDocumentSnapshot(userData));

      debugPrint('Retrieved cached user profile');
      return user;
    } catch (e) {
      debugPrint('Error retrieving cached user profile: $e');
      return null;
    }
  }

  /// Cache chat messages for a request
  Future<void> cacheChatMessages(
    String requestId,
    List<ChatMessageModel> messages,
  ) async {
    try {
      final messagesJson =
          messages.map((m) => _convertToJsonSafe(m.toFirestore())).toList();
      final key = '${_chatMessagesKey}_$requestId';

      await _prefs.setString(
        key,
        jsonEncode({
          'data': messagesJson,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        }),
      );
      debugPrint(
        'Cached ${messages.length} chat messages for request $requestId',
      );
    } catch (e) {
      debugPrint('Error caching chat messages: $e');
    }
  }

  /// Get cached chat messages for a request
  Future<List<ChatMessageModel>?> getCachedChatMessages(
    String requestId,
  ) async {
    try {
      final key = '${_chatMessagesKey}_$requestId';
      final cachedData = _prefs.getString(key);
      if (cachedData == null) return null;

      final decoded = jsonDecode(cachedData);
      final timestamp = DateTime.fromMillisecondsSinceEpoch(
        decoded['timestamp'],
      );

      if (DateTime.now().difference(timestamp) > _messagesExpiry) {
        debugPrint('Chat messages cache expired for request $requestId');
        return null;
      }

      final messagesData = decoded['data'] as List;
      final messages =
          messagesData
              .map(
                (data) =>
                    ChatMessageModel.fromFirestore(MockDocumentSnapshot(data)),
              )
              .toList();

      debugPrint(
        'Retrieved ${messages.length} cached chat messages for request $requestId',
      );
      return messages;
    } catch (e) {
      debugPrint('Error retrieving cached chat messages: $e');
      return null;
    }
  }

  /// Add pending action to be synced when online
  Future<void> addPendingAction(Map<String, dynamic> action) async {
    try {
      if (!_isInitialized) {
        debugPrint('OfflineService not initialized, cannot add pending action');
        return;
      }
      final pendingActions = await getPendingActions();
      pendingActions.add({
        ...action,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
      });

      await _prefs.setString(_pendingActionsKey, jsonEncode(pendingActions));
      debugPrint('Added pending action: ${action['type']}');
    } catch (e) {
      debugPrint('Error adding pending action: $e');
    }
  }

  /// Get all pending actions
  Future<List<Map<String, dynamic>>> getPendingActions() async {
    try {
      final pendingData = _prefs.getString(_pendingActionsKey);
      if (pendingData == null) return [];

      final decoded = jsonDecode(pendingData) as List;
      return decoded.cast<Map<String, dynamic>>();
    } catch (e) {
      debugPrint('Error getting pending actions: $e');
      return [];
    }
  }

  /// Sync all pending actions when connectivity is restored
  Future<void> syncPendingActions() async {
    if (!_isOnline) return;

    try {
      final pendingActions = await getPendingActions();
      if (pendingActions.isEmpty) return;

      debugPrint('Syncing ${pendingActions.length} pending actions');
      final databaseService = DatabaseService();

      for (final action in pendingActions) {
        try {
          await _processPendingAction(action, databaseService);
        } catch (e) {
          debugPrint('Error processing pending action ${action['id']}: $e');
          // Continue with other actions
        }
      }

      // Clear pending actions after successful sync
      await _prefs.remove(_pendingActionsKey);
      debugPrint('Pending actions synced successfully');
    } catch (e) {
      debugPrint('Error syncing pending actions: $e');
    }
  }

  /// Process a single pending action
  Future<void> _processPendingAction(
    Map<String, dynamic> action,
    DatabaseService databaseService,
  ) async {
    final type = action['type'] as String;

    switch (type) {
      case 'create_request':
        // Handle offline request creation
        await databaseService.createRequest(
          RequestModel.fromFirestore(MockDocumentSnapshot(action['data'])),
        );
        break;

      case 'update_request':
        // Handle offline request updates
        await databaseService.updateRequestStatus(
          action['requestId'],
          RequestStatus.values.firstWhere(
            (status) => status.toString().split('.').last == action['status'],
            orElse: () => RequestStatus.pending,
          ),
        );
        break;

      case 'send_message':
        // Handle offline chat messages
        // This would need to be implemented in ChatService
        break;

      case 'update_profile':
        // Handle offline profile updates
        await databaseService.createOrUpdateUser(
          UserModel.fromFirestore(MockDocumentSnapshot(action['userData'])),
        );
        break;

      default:
        debugPrint('Unknown pending action type: $type');
    }
  }

  /// Refresh critical data when connectivity is restored
  Future<void> refreshCriticalData() async {
    if (!_isOnline) return;

    try {
      final databaseService = DatabaseService();

      // Refresh services
      final services = await databaseService.getServices();
      await cacheServices(services);

      // Refresh user profile
      final user = await databaseService.getCurrentUser();
      if (user != null) {
        await cacheUserProfile(user);
      }

      // Update last sync timestamp
      await _prefs.setInt(_lastSyncKey, DateTime.now().millisecondsSinceEpoch);

      debugPrint('Critical data refreshed');
    } catch (e) {
      debugPrint('Error refreshing critical data: $e');
    }
  }

  /// Get last sync timestamp
  Future<DateTime?> getLastSyncTime() async {
    final timestamp = _prefs.getInt(_lastSyncKey);
    return timestamp != null
        ? DateTime.fromMillisecondsSinceEpoch(timestamp)
        : null;
  }

  /// Clear all cached data
  Future<void> clearCache() async {
    try {
      await _prefs.remove(_servicesKey);
      await _prefs.remove(_userRequestsKey);
      await _prefs.remove(_userProfileKey);
      await _prefs.remove(_pendingActionsKey);

      // Clear chat messages cache
      final keys = _prefs.getKeys().where(
        (key) => key.startsWith(_chatMessagesKey),
      );
      for (final key in keys) {
        await _prefs.remove(key);
      }

      debugPrint('All cache cleared');
    } catch (e) {
      debugPrint('Error clearing cache: $e');
    }
  }

  /// Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _connectivityController.close();
  }
}

/// Mock DocumentSnapshot for offline data reconstruction
class MockDocumentSnapshot implements DocumentSnapshot<Map<String, dynamic>> {
  final Map<String, dynamic> _data;
  final String _id;

  MockDocumentSnapshot(Map<String, dynamic> originalData) 
    : _data = _convertTimestampsBack(originalData),
      _id = originalData['id'] ?? '';

  /// Convert integer timestamps back to Timestamp objects
  static Map<String, dynamic> _convertTimestampsBack(Map<String, dynamic> data) {
    final result = <String, dynamic>{};
    
    for (final entry in data.entries) {
      final key = entry.key;
      final value = entry.value;
      
      // Convert timestamps for known timestamp fields
      if ((key.contains('_at') || key.contains('At') || key == 'timestamp') && value is int) {
        result[key] = Timestamp.fromMillisecondsSinceEpoch(value);
      } else if (value is Map<String, dynamic>) {
        result[key] = _convertTimestampsBack(value);
      } else if (value is List) {
        result[key] = value.map((item) {
          if (item is Map<String, dynamic>) {
            return _convertTimestampsBack(item);
          }
          return item;
        }).toList();
      } else {
        result[key] = value;
      }
    }
    
    return result;
  }

  @override
  Map<String, dynamic>? data() => _data;

  @override
  String get id => _id;

  @override
  bool get exists => true;

  // Implement other required methods with minimal functionality
  @override
  DocumentReference<Map<String, dynamic>> get reference =>
      throw UnimplementedError();

  @override
  SnapshotMetadata get metadata => throw UnimplementedError();

  @override
  dynamic get(Object field) => _data[field];

  @override
  dynamic operator [](Object field) => _data[field];
}
