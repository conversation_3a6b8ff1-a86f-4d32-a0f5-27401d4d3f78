import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>gle, Bug, Info, RefreshCw, Wifi, WifiOff } from 'lucide-react';
import { Button } from '../ui/button';
import { Alert, AlertDescription, AlertTitle } from '../ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';

export type ErrorType =
  | 'network'
  | 'validation'
  | 'permission'
  | 'not-found'
  | 'server'
  | 'unknown';
export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

export interface ErrorDisplayProps {
  error: Error | string;
  type?: ErrorType;
  severity?: ErrorSeverity;
  title?: string;
  description?: string;
  showRetry?: boolean;
  showDetails?: boolean;
  onRetry?: () => void;
  onDismiss?: () => void;
  className?: string;
  compact?: boolean;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  type = 'unknown',
  severity = 'medium',
  title,
  description,
  showRetry = false,
  showDetails = false,
  onRetry,
  onDismiss,
  className = '',
  compact = false,
}) => {
  const errorMessage = typeof error === 'string' ? error : error.message;
  const errorStack = typeof error === 'object' && error.stack ? error.stack : null;

  const getErrorConfig = () => {
    const configs = {
      network: {
        icon: WifiOff,
        title: 'Connection Problem',
        description: 'Please check your internet connection and try again.',
        color: 'text-orange-600',
        bgColor: 'bg-orange-50',
        borderColor: 'border-orange-200',
      },
      validation: {
        icon: AlertCircle,
        title: 'Invalid Input',
        description: 'Please check your input and try again.',
        color: 'text-yellow-600',
        bgColor: 'bg-yellow-50',
        borderColor: 'border-yellow-200',
      },
      permission: {
        icon: AlertTriangle,
        title: 'Access Denied',
        description: "You don't have permission to perform this action.",
        color: 'text-red-600',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200',
      },
      'not-found': {
        icon: Info,
        title: 'Not Found',
        description: 'The requested resource could not be found.',
        color: 'text-blue-600',
        bgColor: 'bg-blue-50',
        borderColor: 'border-blue-200',
      },
      server: {
        icon: AlertTriangle,
        title: 'Server Error',
        description: 'A server error occurred. Please try again later.',
        color: 'text-red-600',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-200',
      },
      unknown: {
        icon: Bug,
        title: 'Unexpected Error',
        description: 'An unexpected error occurred. Please try again.',
        color: 'text-gray-600',
        bgColor: 'bg-gray-50',
        borderColor: 'border-gray-200',
      },
    };

    return configs[type];
  };

  const getSeverityConfig = () => {
    const configs = {
      low: {
        variant: 'default' as const,
        urgency: '',
      },
      medium: {
        variant: 'default' as const,
        urgency: '',
      },
      high: {
        variant: 'destructive' as const,
        urgency: 'Attention Required: ',
      },
      critical: {
        variant: 'destructive' as const,
        urgency: 'Critical: ',
      },
    };

    return configs[severity];
  };

  const errorConfig = getErrorConfig();
  const severityConfig = getSeverityConfig();
  const Icon = errorConfig.icon;

  if (compact) {
    return (
      <Alert variant={severityConfig.variant} className={className}>
        <Icon className='h-4 w-4' />
        <AlertTitle>
          {severityConfig.urgency}
          {title || errorConfig.title}
        </AlertTitle>
        <AlertDescription className='flex items-center justify-between'>
          <span>{description || errorMessage}</span>
          {showRetry && onRetry && (
            <Button variant='outline' size='sm' onClick={onRetry} className='ml-2 h-6 px-2 text-xs'>
              <RefreshCw className='h-3 w-3 mr-1' />
              Retry
            </Button>
          )}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Card className={`${errorConfig.borderColor} ${className}`}>
      <CardHeader className={`${errorConfig.bgColor} border-b ${errorConfig.borderColor}`}>
        <div className='flex items-center gap-3'>
          <div
            className={`p-2 rounded-full ${errorConfig.bgColor} border ${errorConfig.borderColor}`}
          >
            <Icon className={`h-5 w-5 ${errorConfig.color}`} />
          </div>
          <div className='flex-1'>
            <CardTitle className={`text-lg ${errorConfig.color}`}>
              {severityConfig.urgency}
              {title || errorConfig.title}
            </CardTitle>
            <CardDescription className='mt-1'>
              {description || errorConfig.description}
            </CardDescription>
          </div>
        </div>
      </CardHeader>

      <CardContent className='pt-4'>
        <div className='space-y-4'>
          <div className='text-sm text-gray-700'>
            <strong>Error:</strong> {errorMessage}
          </div>

          {showDetails && errorStack && process.env.NODE_ENV === 'development' && (
            <details className='text-xs'>
              <summary className='cursor-pointer font-medium text-gray-600 hover:text-gray-800'>
                Technical Details (Development Only)
              </summary>
              <pre className='mt-2 p-3 bg-gray-100 border rounded overflow-auto max-h-40 text-xs'>
                {errorStack}
              </pre>
            </details>
          )}

          <div className='flex gap-2 pt-2'>
            {showRetry && onRetry && (
              <Button
                onClick={onRetry}
                variant='default'
                size='sm'
                className='flex items-center gap-2'
              >
                <RefreshCw className='h-4 w-4' />
                Try Again
              </Button>
            )}

            {onDismiss && (
              <Button onClick={onDismiss} variant='outline' size='sm'>
                Dismiss
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Specialized error components
export const NetworkError: React.FC<Omit<ErrorDisplayProps, 'type'>> = (props) => (
  <ErrorDisplay {...props} type='network' />
);

export const ValidationError: React.FC<Omit<ErrorDisplayProps, 'type'>> = (props) => (
  <ErrorDisplay {...props} type='validation' />
);

export const PermissionError: React.FC<Omit<ErrorDisplayProps, 'type'>> = (props) => (
  <ErrorDisplay {...props} type='permission' />
);

export const NotFoundError: React.FC<Omit<ErrorDisplayProps, 'type'>> = (props) => (
  <ErrorDisplay {...props} type='not-found' />
);

export const ServerError: React.FC<Omit<ErrorDisplayProps, 'type'>> = (props) => (
  <ErrorDisplay {...props} type='server' />
);

// Inline error message component
export interface InlineErrorProps {
  message: string;
  className?: string;
}

export const InlineError: React.FC<InlineErrorProps> = ({ message, className = '' }) => (
  <div className={`flex items-center gap-1 text-sm text-destructive ${className}`}>
    <AlertCircle className='h-4 w-4' />
    <span>{message}</span>
  </div>
);

// Error toast component (for use with toast notifications)
export interface ErrorToastProps {
  title: string;
  message: string;
  onRetry?: () => void;
}

export const ErrorToast: React.FC<ErrorToastProps> = ({ title, message, onRetry }) => (
  <div className='space-y-2'>
    <div className='font-medium'>{title}</div>
    <div className='text-sm text-muted-foreground'>{message}</div>
    {onRetry && (
      <Button variant='outline' size='sm' onClick={onRetry} className='h-6 px-2 text-xs'>
        <RefreshCw className='h-3 w-3 mr-1' />
        Retry
      </Button>
    )}
  </div>
);

export default ErrorDisplay;
