const admin = require('firebase-admin');
const functions = require('firebase-functions');

// Initialize Firebase Admin SDK with service account
try {
  // Check if already initialized
  if (!admin.apps.length) {
    // Try to use service account key from environment variables first
    const serviceAccount = {
      type: "service_account",
      project_id: "stopnow-be6b7",
      private_key_id: functions.config().firebase?.private_key_id || "456a5fffe5c0cddbdf64738a942bb20014f7eeee",
**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
      client_email: functions.config().firebase?.client_email || "<EMAIL>",
      client_id: functions.config().firebase?.client_id || "117773039498232961516",
      auth_uri: "https://accounts.google.com/o/oauth2/auth",
      token_uri: "https://oauth2.googleapis.com/token",
      auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
      client_x509_cert_url: functions.config().firebase?.client_x509_cert_url || "https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-zqeye%40stopnow-be6b7.iam.gserviceaccount.com",
      universe_domain: "googleapis.com"
    };

    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      databaseURL: 'https://stopnow-be6b7-default-rtdb.europe-west1.firebasedatabase.app'
    });
    console.log('Firebase Admin SDK initialized successfully with service account');
  }
} catch (error) {
  console.error('Error initializing Firebase Admin SDK:', error);
  // Fallback to application default credentials
  try {
    if (!admin.apps.length) {
      admin.initializeApp({
        credential: admin.credential.applicationDefault()
      });
      console.log('Firebase Admin SDK initialized with application default credentials');
    }
  } catch (fallbackError) {
    console.error('Error with fallback initialization:', fallbackError);
  }
}

// Import modules
const notificationsModule = require('./src/notifications');
const createTechnicianModule = require('./src/createTechnician');
const paymentCallbacksModule = require('./src/paymentCallbacks');

// Export all functions
exports.sendFcmNotification = notificationsModule.sendFcmNotification;
exports.createTechnicianAccount = createTechnicianModule.createTechnicianAccount;
exports.paymobTransactionProcessed = paymentCallbacksModule.paymobTransactionProcessed;
exports.paymobTransactionResponse = paymentCallbacksModule.paymobTransactionResponse;