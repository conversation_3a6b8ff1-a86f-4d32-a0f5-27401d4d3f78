import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/translation_service.dart';
import '../services/auth_service.dart';
import 'onboarding_screen.dart';
import 'main_navigation.dart';
import 'email_verification_screen.dart';
import '../widgets/text_styles.dart';
import 'package:firebase_auth/firebase_auth.dart';

class SignupScreen extends StatefulWidget {
  final VoidCallback? onSignupSuccess;

  const SignupScreen({super.key, this.onSignupSuccess});

  @override
  State<SignupScreen> createState() => _SignupScreenState();
}

class _SignupScreenState extends State<SignupScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _isLoading = false;
  bool _hidePassword = true;
  bool _hideConfirmPassword = true;
  String? _errorMessage;
  final AuthService _authService = AuthService();

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  // Sanitize email input
  String _sanitizeEmail(String email) {
    return email.trim().toLowerCase();
  }

  // Sanitize name input
  String _sanitizeName(String name) {
    // Trim whitespace and capitalize each word
    return name
        .trim()
        .split(' ')
        .map((word) {
          if (word.isNotEmpty) {
            return word[0].toUpperCase() + word.substring(1).toLowerCase();
          }
          return '';
        })
        .join(' ');
  }

  // Pre-validate password strength before submission
  String? _validatePassword(String password) {
    return _authService.validatePassword(password);
  }

  Future<void> _signUp() async {
    // Clear previous errors
    setState(() {
      _errorMessage = null;
    });

    // Validate the form
    if (!_formKey.currentState!.validate()) return;

    // Extra validation for password
    final passwordError = _validatePassword(_passwordController.text);
    if (passwordError != null) {
      setState(() {
        _errorMessage = passwordError;
      });
      return;
    }

    // Sanitize inputs
    final sanitizedEmail = _sanitizeEmail(_emailController.text);
    final sanitizedName = _sanitizeName(_nameController.text);

    setState(() => _isLoading = true);

    try {
      // Use AuthService to create a new user with email and password
      await _authService.createUserWithEmailAndPassword(
        sanitizedEmail,
        _passwordController.text,
        sanitizedName,
      );

      // Call the success callback
      widget.onSignupSuccess?.call();

      // Navigate to email verification screen
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const EmailVerificationScreen(),
          ),
        );
      }
    } catch (e) {
      // Use sanitized error messages
      setState(() {
        if (e is Exception) {
          _errorMessage = e.toString().replaceFirst('Exception: ', '');
        } else if (e is FirebaseAuthException) {
          // Use the error code to determine the message
          switch (e.code) {
            case 'email-already-in-use':
              _errorMessage = translate('This email is already in use.');
              break;
            case 'invalid-email':
              _errorMessage = translate('Please enter a valid email address.');
              break;
            case 'weak-password':
              _errorMessage = translate('Password is too weak. Use at least 8 characters with uppercase, lowercase, numbers, and special characters.');
              break;
            case 'operation-not-allowed':
              _errorMessage = translate('Email/password accounts are not enabled.');
              break;
            case 'network-request-failed':
              _errorMessage = translate('Network error. Check your connection.');
              break;
            default:
              _errorMessage = translate('Registration failed. Please try again.');
          }
        } else {
          _errorMessage = translate('Registration failed. Please try again.');
        }
      });
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _signUpWithGoogle() async {
    // Clear previous errors
    setState(() {
      _errorMessage = null;
    });

    setState(() => _isLoading = true);

    try {
      // Use AuthService to sign in with Google
      await _authService.signInWithGoogle();

      // Call the success callback
      widget.onSignupSuccess?.call();

      // Check if user needs onboarding
      final user = await _authService.getCurrentUserDetails();
      if (user != null && !user.isOnboarded) {
        // Navigate to onboarding screen for new users
        if (mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(builder: (context) => const OnboardingScreen()),
          );
        }
      } else {
        // User already completed onboarding, navigate directly to MainNavigation
        if (mounted) {
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(builder: (context) => const MainNavigation()),
            (route) => false, // This removes all previous routes
          );
        }
      }
    } catch (e) {
      // Use sanitized error messages
      setState(() {
        if (e is Exception) {
          _errorMessage = e.toString().replaceFirst('Exception: ', '');
        } else {
          _errorMessage = translate('Google sign-in failed. Please try again.');
        }
      });
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final translationService = Provider.of<TranslationService>(context);
    final translate = translationService.translate;
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: BackButton(color: colorScheme.onSurface),
        title: Text(
          translate('Create Account'),
          style: AppTextStyles.headingMedium(
            context,
            color: colorScheme.onSurface,
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Simplified logo without shadow
                  Center(
                    child: Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.grey.shade300,
                          width: 2,
                        ),
                        color: colorScheme.surface,
                      ),
                      child: ClipOval(
                        child: Image.asset(
                          'assets/images/logo.png',
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Icon(
                              Icons.engineering,
                              size: 40,
                              color: colorScheme.primary,
                            );
                          },
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 32),

                  // Welcome text
                  Text(
                    translate('Join Mr.Tech'),
                    style: AppTextStyles.headingLarge(
                      context,
                      color: colorScheme.onSurface,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 8),

                  Text(
                    translate('Create an account to get started'),
                    style: AppTextStyles.bodyMedium(
                      context,
                      color: colorScheme.onSurface.withOpacity(0.7),
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 32),

                  // Error message if any
                  if (_errorMessage != null) ...[
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: colorScheme.error.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colorScheme.error.withOpacity(0.3),
                        ),
                      ),
                      child: Text(
                        _errorMessage!,
                        style: AppTextStyles.bodyMedium(
                          context,
                          color: colorScheme.error,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Name field
                  _buildTextField(
                    controller: _nameController,
                    label: translate('Full Name'),
                    keyboardType: TextInputType.name,
                    textInputAction: TextInputAction.next,
                    prefixIcon: Icons.person_outline,
                    textCapitalization: TextCapitalization.words,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return translate('Please enter your name');
                      }
                      // Check for valid name format
                      if (value.trim().length < 2) {
                        return translate('Name is too short');
                      }
                      // Check for numbers or special characters
                      if (RegExp(
                        r'[0-9!@#$%^&*(),.?":{}|<>]',
                      ).hasMatch(value)) {
                        return translate(
                          'Name should not contain numbers or special characters',
                        );
                      }
                      return null;
                    },
                  ),

                  const SizedBox(height: 16),

                  // Email field
                  _buildTextField(
                    controller: _emailController,
                    label: translate('Email'),
                    keyboardType: TextInputType.emailAddress,
                    textInputAction: TextInputAction.next,
                    prefixIcon: Icons.email_outlined,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return translate('Please enter your email');
                      }
                      // Enhanced email validation
                      final emailPattern = RegExp(
                        r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
                      );
                      if (!emailPattern.hasMatch(value.trim())) {
                        return translate('Please enter a valid email address');
                      }
                      return null;
                    },
                  ),

                  const SizedBox(height: 16),

                  // Password field
                  _buildTextField(
                    controller: _passwordController,
                    label: translate('Password'),
                    obscureText: _hidePassword,
                    textInputAction: TextInputAction.next,
                    prefixIcon: Icons.lock_outline,
                    suffixIcon:
                        _hidePassword
                            ? Icons.visibility_outlined
                            : Icons.visibility_off_outlined,
                    onSuffixIconTap: () {
                      setState(() {
                        _hidePassword = !_hidePassword;
                      });
                    },
                    helperText: translate(
                      'Use at least 8 characters with uppercase, lowercase, numbers, and special characters',
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return translate('Please enter a password');
                      }
                      return null; // Detailed validation happens in _validatePassword
                    },
                  ),

                  const SizedBox(height: 16),

                  // Confirm Password field
                  _buildTextField(
                    controller: _confirmPasswordController,
                    label: translate('Confirm Password'),
                    obscureText: _hideConfirmPassword,
                    textInputAction: TextInputAction.done,
                    prefixIcon: Icons.lock_outline,
                    suffixIcon:
                        _hideConfirmPassword
                            ? Icons.visibility_outlined
                            : Icons.visibility_off_outlined,
                    onSuffixIconTap: () {
                      setState(() {
                        _hideConfirmPassword = !_hideConfirmPassword;
                      });
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return translate('Please confirm your password');
                      }
                      if (value != _passwordController.text) {
                        return translate('Passwords do not match');
                      }
                      return null;
                    },
                  ),

                  const SizedBox(height: 16),

                  // Terms and conditions
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Text(
                      translate(
                        'By creating an account, you agree to our Terms of Service and Privacy Policy',
                      ),
                      style: AppTextStyles.bodySmall(
                        context,
                        color: colorScheme.onSurface.withOpacity(0.6),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Sign up button
                  SizedBox(
                    height: 52,
                    child: FilledButton(
                      onPressed: _isLoading ? null : _signUp,
                      style: FilledButton.styleFrom(
                        backgroundColor: colorScheme.primary,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 0,
                      ),
                      child:
                          _isLoading
                              ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white,
                                  ),
                                ),
                              )
                              : Text(
                                translate('Create Account'),
                                style: AppTextStyles.buttonMedium(
                                  context,
                                  color: Colors.white,
                                ),
                              ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // OR divider
                  Row(
                    children: [
                      Expanded(
                        child: Divider(
                          color: Colors.grey.shade300,
                          thickness: 1,
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Text(
                          translate('OR'),
                          style: AppTextStyles.bodyMedium(
                            context,
                            color: colorScheme.onSurface.withOpacity(0.6),
                          ),
                        ),
                      ),
                      Expanded(
                        child: Divider(
                          color: Colors.grey.shade300,
                          thickness: 1,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 24),

                  // Google sign up button
                  SizedBox(
                    height: 52,
                    child: OutlinedButton(
                      onPressed: _isLoading ? null : _signUpWithGoogle,
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: Colors.grey.shade300),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        backgroundColor: Colors.white,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(
                            'assets/images/google_logo.png',
                            height: 24,
                            width: 24,
                            errorBuilder: (context, error, stackTrace) {
                              return Icon(
                                Icons.login,
                                size: 24,
                                color: colorScheme.primary,
                              );
                            },
                          ),
                          const SizedBox(width: 12),
                          Text(
                            translate('Sign up with Google'),
                            style: AppTextStyles.buttonMedium(
                              context,
                              color: Colors.black87,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Already have an account
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        translate('Already have an account?'),
                        style: AppTextStyles.bodyMedium(
                          context,
                          color: colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                      TextButton(
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        child: Text(
                          translate('Sign In'),
                          style: AppTextStyles.buttonMedium(
                            context,
                            color: colorScheme.primary,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Helper method to build text fields with consistent styling
  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    TextInputType? keyboardType,
    TextInputAction? textInputAction,
    IconData? prefixIcon,
    IconData? suffixIcon,
    VoidCallback? onSuffixIconTap,
    bool obscureText = false,
    TextCapitalization textCapitalization = TextCapitalization.none,
    String? helperText,
    String? Function(String?)? validator,
  }) {
    final colorScheme = Theme.of(context).colorScheme;

    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      textInputAction: textInputAction,
      obscureText: obscureText,
      textCapitalization: textCapitalization,
      validator: validator,
      style: AppTextStyles.bodyMedium(context),
      decoration: InputDecoration(
        labelText: label,
        labelStyle: AppTextStyles.bodyMedium(
          context,
          color: colorScheme.onSurface.withOpacity(0.7),
        ),
        helperText: helperText,
        helperStyle: AppTextStyles.bodySmall(
          context,
          color: colorScheme.onSurface.withOpacity(0.6),
        ),
        helperMaxLines: 2,
        prefixIcon:
            prefixIcon != null
                ? Icon(
                  prefixIcon,
                  color: colorScheme.onSurface.withOpacity(0.7),
                )
                : null,
        suffixIcon:
            suffixIcon != null
                ? IconButton(
                  icon: Icon(
                    suffixIcon,
                    color: colorScheme.onSurface.withOpacity(0.7),
                  ),
                  onPressed: onSuffixIconTap,
                )
                : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.error),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: colorScheme.error, width: 2),
        ),
        filled: true,
        fillColor: colorScheme.surface,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
      ),
    );
  }
}
