import {
  type DocumentData,
  type DocumentReference,
  type DocumentSnapshot,
  type QueryConstraint,
  type QuerySnapshot,
  Timestamp,
  addDoc,
  collection,
  deleteDoc,
  doc,
  getDoc,
  getDocs,
  limit,
  orderBy,
  query,
  serverTimestamp,
  setDoc,
  startAfter,
  updateDoc,
  where,
} from 'firebase/firestore';
import { db } from '../config/firebase';
import { logger } from '../utils/logger';
import type {
  ApiResponse,
  AppError,
  PaginatedResponse,
  PaginationParams,
  ServiceResponse
} from '../types/common';
import { ErrorCode } from '../types/enums';
import { isObject, isString } from '../utils/typeGuards';

export interface FirebaseQueryOptions extends PaginationParams {
  startAfter?: DocumentSnapshot;
  orderBy?: { field: string; direction: 'asc' | 'desc' };
  where?: Array<{ field: string; operator: string; value: unknown }>;
}

export interface FirebaseServiceConfig {
  enableLogging?: boolean;
  enableRetry?: boolean;
  maxRetries?: number;
  retryDelay?: number;
}

export class FirebaseService<T extends DocumentData> {
  protected collectionName: string;
  protected config: FirebaseServiceConfig;

  constructor(collectionName: string, config: FirebaseServiceConfig = {}) {
    this.collectionName = collectionName;
    this.config = {
      enableLogging: true,
      enableRetry: true,
      maxRetries: 3,
      retryDelay: 1000,
      ...config,
    };
  }

  /**
   * Create a standardized error response
   */
  protected createError(
    code: ErrorCode,
    message: string,
    details?: Record<string, unknown>
  ): AppError {
    return {
      code,
      message,
      details,
      timestamp: new Date(),
      context: this.collectionName,
    };
  }

  /**
   * Create a standardized service response
   */
  protected createResponse<TData>(
    success: boolean,
    data?: TData,
    error?: AppError
  ): ServiceResponse<TData> {
    return {
      success,
      data,
      error,
      metadata: {
        collection: this.collectionName,
        timestamp: new Date().toISOString(),
      },
    };
  }

  /**
   * Log service operations
   */
  protected log(level: 'info' | 'warn' | 'error', message: string, data?: Record<string, unknown>): void {
    if (this.config.enableLogging) {
      logger[level](`[${this.collectionName}] ${message}`, data);
    }
  }

  /**
   * Execute operation with retry logic
   */
  protected async executeWithRetry<TResult>(
    operation: () => Promise<TResult>,
    operationName: string
  ): Promise<TResult> {
    let lastError: Error;

    for (let attempt = 1; attempt <= (this.config.maxRetries || 3); attempt++) {
      try {
        const result = await operation();
        if (attempt > 1) {
          this.log('info', `${operationName} succeeded on attempt ${attempt}`);
        }
        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        if (attempt === (this.config.maxRetries || 3)) {
          this.log('error', `${operationName} failed after ${attempt} attempts`, {
            error: lastError.message,
          });
          break;
        }

        this.log('warn', `${operationName} failed on attempt ${attempt}, retrying...`, {
          error: lastError.message,
          nextAttemptIn: this.config.retryDelay,
        });

        await new Promise(resolve => setTimeout(resolve, this.config.retryDelay || 1000));
      }
    }

    throw lastError!;
  }

  /**
   * Get a single document by ID with proper error handling
   */
  async getById(id: string): Promise<ServiceResponse<T>> {
    if (!isString(id) || !id.trim()) {
      const error = this.createError(
        ErrorCode.VALIDATION_REQUIRED_FIELD,
        'Document ID is required and must be a non-empty string'
      );
      return this.createResponse(false, undefined, error);
    }

    try {
      const result = await this.executeWithRetry(async () => {
        const docRef = doc(db, this.collectionName, id);
        const docSnap = await getDoc(docRef);

        if (docSnap.exists()) {
          return { id: docSnap.id, ...docSnap.data() } as unknown as T;
        } else {
          return null;
        }
      }, `getById(${id})`);

      if (result === null) {
        const error = this.createError(
          ErrorCode.BUSINESS_RESOURCE_NOT_FOUND,
          `Document with ID '${id}' not found in ${this.collectionName}`
        );
        return this.createResponse(false, undefined, error);
      }

      this.log('info', 'Document retrieved successfully', { id });
      return this.createResponse(true, result);
    } catch (error) {
      const appError = this.createError(
        ErrorCode.NETWORK_SERVER_ERROR,
        `Failed to retrieve document: ${error instanceof Error ? error.message : String(error)}`,
        { id, originalError: error }
      );
      this.log('error', 'Failed to get document by ID', { id, error: appError });
      return this.createResponse(false, undefined, appError);
    }
  }

  /**
   * Get all documents with optional query and pagination
   */
  async getAll(options?: FirebaseQueryOptions): Promise<ServiceResponse<T[]>> {
    try {
      const result = await this.executeWithRetry(async () => {
        const constraints: QueryConstraint[] = [];

        // Add where constraints
        if (options?.where) {
          options.where.forEach((w) => {
            constraints.push(where(w.field, w.operator as any, w.value));
          });
        }

        // Add orderBy constraint
        if (options?.orderBy) {
          constraints.push(orderBy(options.orderBy.field, options.orderBy.direction));
        }

        // Add pagination constraints
        if (options?.limit) {
          constraints.push(limit(options.limit));
        }

        if (options?.startAfter) {
          constraints.push(startAfter(options.startAfter));
        }

        const q = query(collection(db, this.collectionName), ...constraints);
        const querySnapshot = await getDocs(q);

        return querySnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as unknown as T[];
      }, 'getAll');

      this.log('info', `Retrieved ${result.length} documents`, {
        count: result.length,
        hasFilters: !!options?.where?.length,
        hasOrdering: !!options?.orderBy,
        hasLimit: !!options?.limit,
      });

      return this.createResponse(true, result);
    } catch (error) {
      const appError = this.createError(
        ErrorCode.NETWORK_SERVER_ERROR,
        `Failed to retrieve documents: ${error instanceof Error ? error.message : String(error)}`,
        { options, originalError: error }
      );
      this.log('error', 'Failed to get all documents', { error: appError, options });
      return this.createResponse(false, undefined, appError);
    }
  }

  /**
   * Create a new document with proper validation and error handling
   */
  async create(data: Omit<T, 'id'>): Promise<ServiceResponse<T>> {
    if (!isObject(data)) {
      const error = this.createError(
        ErrorCode.VALIDATION_REQUIRED_FIELD,
        'Document data is required and must be an object'
      );
      return this.createResponse(false, undefined, error);
    }

    try {
      const result = await this.executeWithRetry(async () => {
        const docData = {
          ...data,
          created_at: serverTimestamp(),
          updated_at: serverTimestamp(),
        };

        const docRef = await addDoc(collection(db, this.collectionName), docData);

        // Get the created document directly to avoid circular dependency
        const newDoc = await getDoc(docRef);
        if (!newDoc.exists()) {
          throw new Error('Failed to retrieve created document');
        }

        return { id: newDoc.id, ...newDoc.data() } as unknown as T;
      }, 'create');

      this.log('info', 'Document created successfully', { id: result.id });
      return this.createResponse(true, result);
    } catch (error) {
      const appError = this.createError(
        ErrorCode.NETWORK_SERVER_ERROR,
        `Failed to create document: ${error instanceof Error ? error.message : String(error)}`,
        { data, originalError: error }
      );
      this.log('error', 'Failed to create document', { error: appError });
      return this.createResponse(false, undefined, appError);
    }
  }

  /**
   * Update a document with proper validation and error handling
   */
  async update(id: string, data: Partial<T>): Promise<ServiceResponse<T>> {
    if (!isString(id) || !id.trim()) {
      const error = this.createError(
        ErrorCode.VALIDATION_REQUIRED_FIELD,
        'Document ID is required and must be a non-empty string'
      );
      return this.createResponse(false, undefined, error);
    }

    if (!isObject(data) || Object.keys(data).length === 0) {
      const error = this.createError(
        ErrorCode.VALIDATION_REQUIRED_FIELD,
        'Update data is required and must be a non-empty object'
      );
      return this.createResponse(false, undefined, error);
    }

    try {
      const result = await this.executeWithRetry(async () => {
        const docRef = doc(db, this.collectionName, id);

        // Remove id from data if present to avoid conflicts
        const { id: _, ...updateData } = data as Record<string, unknown>;

        await updateDoc(docRef, {
          ...updateData,
          updated_at: serverTimestamp(),
        });

        // Get the updated document directly
        const updatedDoc = await getDoc(docRef);
        if (!updatedDoc.exists()) {
          throw new Error('Document not found after update');
        }

        return { id: updatedDoc.id, ...updatedDoc.data() } as unknown as T;
      }, `update(${id})`);

      this.log('info', 'Document updated successfully', { id, fieldsUpdated: Object.keys(data) });
      return this.createResponse(true, result);
    } catch (error) {
      const appError = this.createError(
        ErrorCode.NETWORK_SERVER_ERROR,
        `Failed to update document: ${error instanceof Error ? error.message : String(error)}`,
        { id, data, originalError: error }
      );
      this.log('error', 'Failed to update document', { id, error: appError });
      return this.createResponse(false, undefined, appError);
    }
  }

  /**
   * Delete a document with proper validation and error handling
   */
  async delete(id: string): Promise<ServiceResponse<boolean>> {
    if (!isString(id) || !id.trim()) {
      const error = this.createError(
        ErrorCode.VALIDATION_REQUIRED_FIELD,
        'Document ID is required and must be a non-empty string'
      );
      return this.createResponse(false, undefined, error);
    }

    try {
      await this.executeWithRetry(async () => {
        const docRef = doc(db, this.collectionName, id);
        await deleteDoc(docRef);
      }, `delete(${id})`);

      this.log('info', 'Document deleted successfully', { id });
      return this.createResponse(true, true);
    } catch (error) {
      const appError = this.createError(
        ErrorCode.NETWORK_SERVER_ERROR,
        `Failed to delete document: ${error instanceof Error ? error.message : String(error)}`,
        { id, originalError: error }
      );
      this.log('error', 'Failed to delete document', { id, error: appError });
      return this.createResponse(false, undefined, appError);
    }
  }

  /**
   * Soft delete (mark as not visible) with proper error handling
   */
  async softDelete(id: string): Promise<ServiceResponse<T>> {
    return this.update(id, { is_visible: false } as unknown as Partial<T>);
  }

  /**
   * Query documents with custom constraints and proper error handling
   */
  async query(constraints: QueryConstraint[]): Promise<ServiceResponse<T[]>> {
    try {
      const result = await this.executeWithRetry(async () => {
        const q = query(collection(db, this.collectionName), ...constraints);
        const querySnapshot = await getDocs(q);

        return querySnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as unknown as T[];
      }, 'query');

      this.log('info', 'Query executed successfully', {
        resultCount: result.length,
        constraintCount: constraints.length,
      });

      return this.createResponse(true, result);
    } catch (error) {
      const appError = this.createError(
        ErrorCode.NETWORK_SERVER_ERROR,
        `Failed to execute query: ${error instanceof Error ? error.message : String(error)}`,
        { constraintCount: constraints.length, originalError: error }
      );
      this.log('error', 'Failed to execute query', { error: appError });
      return this.createResponse(false, undefined, appError);
    }
  }

  /**
   * Get paginated results with proper error handling
   */
  async getPaginated(options: FirebaseQueryOptions = {}): Promise<ServiceResponse<PaginatedResponse<T>>> {
    try {
      const { page = 1, limit: pageSize = 20 } = options;

      if (page < 1 || pageSize < 1 || pageSize > 100) {
        const error = this.createError(
          ErrorCode.VALIDATION_OUT_OF_RANGE,
          'Page must be >= 1 and limit must be between 1 and 100'
        );
        return this.createResponse(false, undefined, error);
      }

      const result = await this.executeWithRetry(async () => {
        const constraints: QueryConstraint[] = [];

        // Add where constraints
        if (options.where) {
          options.where.forEach((w) => {
            constraints.push(where(w.field, w.operator as any, w.value));
          });
        }

        // Add orderBy constraint
        if (options.orderBy) {
          constraints.push(orderBy(options.orderBy.field, options.orderBy.direction));
        }

        // Add pagination constraints
        constraints.push(limit(pageSize + 1)); // Get one extra to check if there's a next page

        if (options.startAfter) {
          constraints.push(startAfter(options.startAfter));
        }

        const q = query(collection(db, this.collectionName), ...constraints);
        const querySnapshot = await getDocs(q);

        const docs = querySnapshot.docs.map((doc) => ({
          id: doc.id,
          ...doc.data(),
        })) as unknown as T[];

        const hasNext = docs.length > pageSize;
        const data = hasNext ? docs.slice(0, pageSize) : docs;

        // For total count, we'd need a separate query in production
        // This is a simplified implementation
        const total = data.length; // In real implementation, use count aggregation

        return {
          data,
          pagination: {
            page,
            limit: pageSize,
            total,
            totalPages: Math.ceil(total / pageSize),
            hasNext,
            hasPrev: page > 1,
          },
        };
      }, 'getPaginated');

      this.log('info', 'Paginated query executed successfully', {
        page,
        pageSize,
        resultCount: result.data.length,
        hasNext: result.pagination.hasNext,
      });

      return this.createResponse(true, result);
    } catch (error) {
      const appError = this.createError(
        ErrorCode.NETWORK_SERVER_ERROR,
        `Failed to execute paginated query: ${error instanceof Error ? error.message : String(error)}`,
        { options, originalError: error }
      );
      this.log('error', 'Failed to execute paginated query', { error: appError, options });
      return this.createResponse(false, undefined, appError);
    }
  }

  // Convert Date to Timestamp for Firestore
  protected toTimestamp(date: Date | Timestamp | undefined): Timestamp | undefined {
    if (!date) return undefined;
    if (date instanceof Timestamp) return date;
    return Timestamp.fromDate(date);
  }

  // Convert Timestamp to Date for application use
  protected toDate(timestamp: Timestamp | Date | undefined): Date | undefined {
    if (!timestamp) return undefined;
    if (timestamp instanceof Date) return timestamp;
    return timestamp.toDate();
  }

  // Create a new document with a specific ID
  async createWithId(id: string, data: Omit<T, 'id'>): Promise<T> {
    try {
      const docData = {
        ...data,
        created_at: serverTimestamp(),
        updated_at: serverTimestamp(),
      };
      const docRef = doc(db, this.collectionName, id);
      await setDoc(docRef, docData);
      const createdDoc = await this.getById(id);
      if (!createdDoc) {
        throw new Error('Failed to retrieve created document');
      }
      return createdDoc;
    } catch (error) {
      console.error(`Error creating document with ID in ${this.collectionName}:`, error);
      throw error;
    }
  }

  // Create a document in a different collection with the same ID
  async createInCollection(collectionName: string, id: string, data: any): Promise<void> {
    try {
      const docData = {
        ...data,
        created_at: serverTimestamp(),
        updated_at: serverTimestamp(),
      };
      const docRef = doc(db, collectionName, id);
      await setDoc(docRef, docData);
    } catch (error) {
      console.error(`Error creating document in ${collectionName}:`, error);
      throw error;
    }
  }
}
