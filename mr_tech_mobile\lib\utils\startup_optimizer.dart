import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:async';

/// Startup optimization utilities to improve app launch time
/// Manages critical vs non-critical initialization tasks
class StartupOptimizer {
  // Private constructor to prevent instantiation
  StartupOptimizer._();

  static bool _isInitialized = false;
  static final List<Future<void> Function()> _deferredTasks = [];
  static final List<Future<void> Function()> _backgroundTasks = [];
  static final Stopwatch _startupStopwatch = Stopwatch();

  /// Initialize startup optimization
  static void initialize() {
    if (_isInitialized) return;

    _startupStopwatch.start();
    _isInitialized = true;
    debugPrint('StartupOptimizer initialized');
  }

  /// Add a task to be executed after critical startup is complete
  static void addDeferredTask(Future<void> Function() task) {
    _deferredTasks.add(task);
  }

  /// Add a task to be executed in the background
  static void addBackgroundTask(Future<void> Function() task) {
    _backgroundTasks.add(task);
  }

  /// Execute critical startup tasks only
  static Future<void> executeCriticalStartup() async {
    try {
      debugPrint('Executing critical startup tasks...');
      
      // Critical tasks that must complete before showing UI
      await _executeCriticalTasks();
      
      final criticalTime = _startupStopwatch.elapsedMilliseconds;
      debugPrint('Critical startup completed in ${criticalTime}ms');
      
      // Schedule deferred tasks to run after UI is shown
      _scheduleDeferredTasks();
      
      // Schedule background tasks
      _scheduleBackgroundTasks();
      
    } catch (e) {
      debugPrint('Error in critical startup: $e');
      rethrow;
    }
  }

  /// Execute only the most critical tasks needed for UI
  static Future<void> _executeCriticalTasks() async {
    final tasks = <Future<void>>[];

    // Only the most essential tasks
    tasks.add(_preloadCriticalAssets());
    tasks.add(_initializeEssentialServices());

    // Execute critical tasks in parallel where possible
    await Future.wait(tasks);
  }

  /// Preload only critical assets needed for initial UI
  static Future<void> _preloadCriticalAssets() async {
    try {
      // Preload only essential images/assets for splash and login screens
      await _preloadEssentialImages();
      
      // Preload critical fonts if needed
      await _preloadCriticalFonts();
      
    } catch (e) {
      debugPrint('Error preloading critical assets: $e');
      // Don't rethrow - asset loading failures shouldn't block startup
    }
  }

  /// Preload essential images for initial screens
  static Future<void> _preloadEssentialImages() async {
    try {
      // Only preload images that are immediately visible
      const essentialImages = [
        'assets/images/logo.png',
        'assets/images/welcome_bg.png',
        // Add other critical images here
      ];

      final preloadTasks = essentialImages.map((imagePath) async {
        try {
          await rootBundle.load(imagePath);
        } catch (e) {
          debugPrint('Failed to preload image $imagePath: $e');
          // Continue with other images
        }
      });

      await Future.wait(preloadTasks);
      debugPrint('Essential images preloaded');
    } catch (e) {
      debugPrint('Error preloading essential images: $e');
    }
  }

  /// Preload critical fonts
  static Future<void> _preloadCriticalFonts() async {
    try {
      // Preload fonts that are used in initial screens
      const criticalFonts = [
        'Montserrat',
        'Playfair Display',
        // Add other critical fonts here
      ];

      for (final fontFamily in criticalFonts) {
        try {
          await _preloadFont(fontFamily);
        } catch (e) {
          debugPrint('Failed to preload font $fontFamily: $e');
          // Continue with other fonts
        }
      }

      debugPrint('Critical fonts preloaded');
    } catch (e) {
      debugPrint('Error preloading critical fonts: $e');
    }
  }

  /// Preload a specific font family
  static Future<void> _preloadFont(String fontFamily) async {
    try {
      // Create a temporary text widget to trigger font loading
      final textPainter = TextPainter(
        text: TextSpan(
          text: 'Preload',
          style: TextStyle(fontFamily: fontFamily),
        ),
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.dispose();
    } catch (e) {
      debugPrint('Error preloading font $fontFamily: $e');
    }
  }

  /// Initialize only essential services needed for basic functionality
  static Future<void> _initializeEssentialServices() async {
    try {
      // Initialize only services that are absolutely required for UI
      await _initializeThemeService();
      await _initializeBasicPreferences();
      
    } catch (e) {
      debugPrint('Error initializing essential services: $e');
      rethrow; // These are critical, so rethrow
    }
  }

  /// Initialize theme service for proper UI rendering
  static Future<void> _initializeThemeService() async {
    try {
      // Initialize theme-related preferences
      // This should be very fast and only load essential theme data
      debugPrint('Theme service initialized for startup');
    } catch (e) {
      debugPrint('Error initializing theme service: $e');
      rethrow;
    }
  }

  /// Initialize basic preferences needed for startup
  static Future<void> _initializeBasicPreferences() async {
    try {
      // Load only essential preferences needed for initial UI
      // Defer loading of other preferences
      debugPrint('Basic preferences initialized for startup');
    } catch (e) {
      debugPrint('Error initializing basic preferences: $e');
      rethrow;
    }
  }

  /// Schedule deferred tasks to run after UI is shown
  static void _scheduleDeferredTasks() {
    if (_deferredTasks.isEmpty) return;

    // Run deferred tasks after a short delay to allow UI to render
    Timer(const Duration(milliseconds: 100), () async {
      try {
        debugPrint('Executing ${_deferredTasks.length} deferred tasks...');
        
        // Execute deferred tasks in batches to avoid blocking UI
        await _executeDeferredTasksInBatches();
        
        final deferredTime = _startupStopwatch.elapsedMilliseconds;
        debugPrint('Deferred tasks completed in ${deferredTime}ms total');
        
      } catch (e) {
        debugPrint('Error executing deferred tasks: $e');
      }
    });
  }

  /// Execute deferred tasks in small batches
  static Future<void> _executeDeferredTasksInBatches() async {
    const batchSize = 3; // Process 3 tasks at a time
    
    for (int i = 0; i < _deferredTasks.length; i += batchSize) {
      final batch = _deferredTasks.skip(i).take(batchSize);
      
      try {
        // Execute batch in parallel
        await Future.wait(batch.map((task) => task()));
        
        // Small delay between batches to keep UI responsive
        if (i + batchSize < _deferredTasks.length) {
          await Future.delayed(const Duration(milliseconds: 50));
        }
        
      } catch (e) {
        debugPrint('Error in deferred task batch: $e');
        // Continue with next batch
      }
    }
  }

  /// Schedule background tasks to run with low priority
  static void _scheduleBackgroundTasks() {
    if (_backgroundTasks.isEmpty) return;

    // Run background tasks after a longer delay
    Timer(const Duration(milliseconds: 500), () async {
      try {
        debugPrint('Executing ${_backgroundTasks.length} background tasks...');
        
        // Execute background tasks one by one with delays
        for (final task in _backgroundTasks) {
          try {
            await task();
            // Delay between background tasks to avoid impacting performance
            await Future.delayed(const Duration(milliseconds: 100));
          } catch (e) {
            debugPrint('Error in background task: $e');
            // Continue with next task
          }
        }
        
        final totalTime = _startupStopwatch.elapsedMilliseconds;
        debugPrint('All startup tasks completed in ${totalTime}ms total');
        
      } catch (e) {
        debugPrint('Error executing background tasks: $e');
      }
    });
  }

  /// Optimize widget tree for faster rendering
  static Widget optimizeWidgetTree(Widget child) {
    return RepaintBoundary(
      child: child,
    );
  }

  /// Create an optimized splash screen that doesn't block startup
  static Widget createOptimizedSplashScreen({
    required Widget child,
    Duration minDisplayTime = const Duration(milliseconds: 1000),
  }) {
    return _OptimizedSplashScreen(
      minDisplayTime: minDisplayTime,
      child: child,
    );
  }

  /// Get startup performance metrics
  static Map<String, dynamic> getStartupMetrics() {
    return {
      'totalStartupTime': _startupStopwatch.elapsedMilliseconds,
      'isInitialized': _isInitialized,
      'deferredTasksCount': _deferredTasks.length,
      'backgroundTasksCount': _backgroundTasks.length,
    };
  }

  /// Reset startup optimizer for testing
  static void reset() {
    _isInitialized = false;
    _deferredTasks.clear();
    _backgroundTasks.clear();
    _startupStopwatch.reset();
  }
}

/// Optimized splash screen widget
class _OptimizedSplashScreen extends StatefulWidget {
  final Widget child;
  final Duration minDisplayTime;

  const _OptimizedSplashScreen({
    required this.child,
    required this.minDisplayTime,
  });

  @override
  State<_OptimizedSplashScreen> createState() => _OptimizedSplashScreenState();
}

class _OptimizedSplashScreenState extends State<_OptimizedSplashScreen> {
  bool _showChild = false;
  late final Timer _minDisplayTimer;

  @override
  void initState() {
    super.initState();
    
    // Ensure splash is shown for minimum time
    _minDisplayTimer = Timer(widget.minDisplayTime, () {
      if (mounted) {
        setState(() {
          _showChild = true;
        });
      }
    });
  }

  @override
  void dispose() {
    _minDisplayTimer.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_showChild) {
      return widget.child;
    }

    // Show optimized splash screen
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).colorScheme.primary,
              Theme.of(context).colorScheme.primaryContainer,
            ],
          ),
        ),
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Optimized logo/branding
              FlutterLogo(size: 80),
              SizedBox(height: 24),
              Text(
                'Mr.Tech',
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              SizedBox(height: 48),
              // Minimal loading indicator
              SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
