const { onCall, HttpsError } = require('firebase-functions/v2/https');
const { getAuth } = require('firebase-admin/auth');
const { getFirestore } = require('firebase-admin/firestore');
const admin = require('firebase-admin');

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

const auth = getAuth();
const db = getFirestore();

/**
 * Cloud Function to create a technician user account without auto-login
 * This prevents the admin from being logged out when creating technician accounts
 */
exports.createTechnicianAccount = onCall(async (request) => {
  try {
    // Verify the caller is authenticated and is an admin
    if (!request.auth) {
      throw new HttpsError('unauthenticated', 'User must be authenticated');
    }

    // Check if the caller is an admin
    const callerDoc = await db.collection('admins').doc(request.auth.uid).get();
    const callerUserDoc = await db.collection('users').doc(request.auth.uid).get();
    
    const isAdmin = callerDoc.exists || 
      (callerUserDoc.exists && callerUserDoc.data().role === 'admin');
    
    if (!isAdmin) {
      throw new HttpsError('permission-denied', 'Only admins can create technician accounts');
    }

    const { email, password, name, technicianData } = request.data;

    if (!email || !password || !name || !technicianData) {
      throw new HttpsError('invalid-argument', 'Missing required fields');
    }

    // Create the user account using Admin SDK (doesn't auto-login)
    const userRecord = await auth.createUser({
      email: email,
      password: password,
      displayName: name,
      emailVerified: false
    });

    console.log('Created user account:', userRecord.uid);

    // Create technician document ONLY in technicians collection
    await db.collection('technicians').doc(userRecord.uid).set({
      ...technicianData,
      email: email,
      name: name,
      role: 'technician',
      created_at: admin.firestore.FieldValue.serverTimestamp(),
      updated_at: admin.firestore.FieldValue.serverTimestamp(),
    });

    console.log('Created technician document for:', userRecord.uid);

    return {
      success: true,
      uid: userRecord.uid,
      message: 'Technician account created successfully'
    };

  } catch (error) {
    console.error('Error creating technician account:', error);
    
    if (error instanceof HttpsError) {
      throw error;
    }
    
    throw new HttpsError('internal', 'Failed to create technician account: ' + error.message);
  }
});
