import React, { useCallback, useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import {
  BarChart3,
  Calendar,
  CheckCircle,
  Clock,
  TrendingDown,
  TrendingUp,
  Users,
  XCircle,
} from 'lucide-react';
import {
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  Line,
  LineChart,
  Pie,
  PieChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';
import performanceService, {
  type PerformanceChartData,
  type PerformanceStats,
  type TechnicianPerformanceMetrics,
} from '../services/performanceService';
import technicianService from '../services/technicianService';
import { type TechnicianModel } from '../types/technician';
import { format, subDays } from 'date-fns';

interface PerformanceTrackingProps {
  technicianId?: string;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

const PerformanceTracking: React.FC<PerformanceTrackingProps> = ({ technicianId }) => {
  const [performanceStats, setPerformanceStats] = useState<PerformanceStats | null>(null);
  const [chartData, setChartData] = useState<PerformanceChartData[]>([]);
  const [topPerformers, setTopPerformers] = useState<TechnicianPerformanceMetrics[]>([]);
  const [selectedTechnician, setSelectedTechnician] = useState<string>(technicianId || 'all');
  const [technicians, setTechnicians] = useState<TechnicianModel[]>([]);
  const [dateRange, setDateRange] = useState<string>('30');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadTechnicians();
  }, []);

  useEffect(() => {
    loadPerformanceData();
  }, [loadPerformanceData]);

  const loadTechnicians = async () => {
    try {
      const techList = await technicianService.getAll();
      setTechnicians(techList);
    } catch (error) {
      console.error('Error loading technicians:', error);
    }
  };

  const loadPerformanceData = useCallback(async () => {
    setLoading(true);
    try {
      const days = parseInt(dateRange);
      const startDate = subDays(new Date(), days);
      const endDate = new Date();

      // Load overall stats
      const stats = await performanceService.getOverallPerformanceStats(startDate, endDate);
      setPerformanceStats(stats);

      // Load chart data
      const chartDataResult = await performanceService.getPerformanceChartData(
        Math.min(days, 14), // Limit chart to 14 days max for readability
        selectedTechnician === 'all' ? undefined : selectedTechnician,
      );
      setChartData(chartDataResult);

      // Load top performers (only for overall view)
      if (selectedTechnician === 'all') {
        const performers = await performanceService.getTopPerformers(5, startDate, endDate);
        setTopPerformers(performers);
      } else {
        setTopPerformers([]);
      }
    } catch (error) {
      console.error('Error loading performance data:', error);
    } finally {
      setLoading(false);
    }
  }, [selectedTechnician, dateRange]);

  const getPerformanceColor = (value: number, type: 'rate' | 'time' | 'count') => {
    if (type === 'rate') {
      return value < 5 ? 'text-green-600' : value < 15 ? 'text-yellow-600' : 'text-red-600';
    }
    if (type === 'count') {
      return value > 10 ? 'text-green-600' : value > 5 ? 'text-yellow-600' : 'text-red-600';
    }
    return 'text-blue-600';
  };

  const getTrendIcon = (value: number, isGoodWhenHigh: boolean = true) => {
    const isPositive = isGoodWhenHigh ? value > 0 : value < 0;
    return isPositive ? (
      <TrendingUp className='h-4 w-4 text-green-600' />
    ) : (
      <TrendingDown className='h-4 w-4 text-red-600' />
    );
  };

  if (loading) {
    return (
      <div className='space-y-6'>
        <div className='flex items-center justify-between'>
          <h2 className='text-2xl font-bold'>Performance Tracking</h2>
        </div>
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
          {[...Array(4)].map((_, i) => (
            <Card key={i} className='animate-pulse'>
              <CardHeader className='pb-2'>
                <div className='h-4 bg-gray-200 rounded w-3/4'></div>
              </CardHeader>
              <CardContent>
                <div className='h-8 bg-gray-200 rounded w-1/2 mb-2'></div>
                <div className='h-3 bg-gray-200 rounded w-full'></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <h2 className='text-2xl font-bold'>Performance Tracking</h2>
        <div className='flex gap-4'>
          <Select value={selectedTechnician} onValueChange={setSelectedTechnician}>
            <SelectTrigger className='w-48'>
              <SelectValue placeholder='Select technician' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>All Technicians</SelectItem>
              {technicians.map((tech) => (
                <SelectItem key={tech.id} value={tech.id}>
                  {tech.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className='w-32'>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='7'>Last 7 days</SelectItem>
              <SelectItem value='30'>Last 30 days</SelectItem>
              <SelectItem value='90'>Last 90 days</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* KPI Cards */}
      {performanceStats && (
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Total Requests</CardTitle>
              <BarChart3 className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{performanceStats.totalRequests}</div>
              <p className='text-xs text-muted-foreground'>
                {performanceStats.completedRequests} completed
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Avg Response Time</CardTitle>
              <Clock className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{performanceStats.avgAcceptTime}</div>
              <p className='text-xs text-muted-foreground'>Time to accept requests</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Completion Rate</CardTitle>
              <CheckCircle className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>
                {performanceStats.totalRequests > 0
                  ? Math.round(
                      (performanceStats.completedRequests / performanceStats.totalRequests) * 100,
                    )
                  : 0}
                %
              </div>
              <p className='text-xs text-muted-foreground'>Successfully completed</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>Cancellation Rate</CardTitle>
              <XCircle className='h-4 w-4 text-muted-foreground' />
            </CardHeader>
            <CardContent>
              <div
                className={`text-2xl font-bold ${getPerformanceColor(performanceStats.cancellationRate, 'rate')}`}
              >
                {performanceStats.cancellationRate.toFixed(1)}%
              </div>
              <p className='text-xs text-muted-foreground'>Cancelled requests</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Charts */}
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6'>
        {/* Performance Trend Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Performance Trend</CardTitle>
            <CardDescription>Daily completed requests over time</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width='100%' height={300}>
              <LineChart data={chartData}>
                <CartesianGrid strokeDasharray='3 3' />
                <XAxis
                  dataKey='date'
                  tickFormatter={(value) => format(new Date(value), 'MMM dd')}
                />
                <YAxis />
                <Tooltip labelFormatter={(value) => format(new Date(value), 'MMM dd, yyyy')} />
                <Line
                  type='monotone'
                  dataKey='completed'
                  stroke='#8884d8'
                  strokeWidth={2}
                  name='Completed Requests'
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Response Time Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Average Response Time</CardTitle>
            <CardDescription>Daily average response time in minutes</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width='100%' height={300}>
              <BarChart data={chartData}>
                <CartesianGrid strokeDasharray='3 3' />
                <XAxis
                  dataKey='date'
                  tickFormatter={(value) => format(new Date(value), 'MMM dd')}
                />
                <YAxis />
                <Tooltip
                  labelFormatter={(value) => format(new Date(value), 'MMM dd, yyyy')}
                  formatter={(value: number) => [`${value.toFixed(1)} min`, 'Avg Response Time']}
                />
                <Bar dataKey='avgResponseTime' fill='#82ca9d' name='Avg Response Time' />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Top Performers */}
      {selectedTechnician === 'all' && topPerformers.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Top Performers</CardTitle>
            <CardDescription>Best performing technicians in the selected period</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              {topPerformers.map((performer, index) => (
                <div
                  key={performer.technicianId}
                  className='flex items-center justify-between p-4 border rounded-lg'
                >
                  <div className='flex items-center space-x-4'>
                    <div className='flex items-center justify-center w-8 h-8 rounded-full bg-primary text-primary-foreground text-sm font-bold'>
                      {index + 1}
                    </div>
                    <div>
                      <h4 className='font-semibold'>{performer.technicianName}</h4>
                      <p className='text-sm text-muted-foreground'>
                        {performer.totalCompleted} completed requests
                      </p>
                    </div>
                  </div>
                  <div className='flex space-x-4 text-sm'>
                    <div className='text-center'>
                      <div className='font-semibold'>
                        {(performer.avgAcceptTimeMs / (1000 * 60)).toFixed(0)}m
                      </div>
                      <div className='text-muted-foreground'>Avg Accept</div>
                    </div>
                    <div className='text-center'>
                      <div className='font-semibold'>
                        {(performer.avgCompletionTimeMs / (1000 * 60)).toFixed(0)}m
                      </div>
                      <div className='text-muted-foreground'>Avg Complete</div>
                    </div>
                    <div className='text-center'>
                      <div
                        className={`font-semibold ${getPerformanceColor(performer.cancellationRate, 'rate')}`}
                      >
                        {performer.cancellationRate.toFixed(1)}%
                      </div>
                      <div className='text-muted-foreground'>Cancel Rate</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Individual Technician Performance */}
      {selectedTechnician !== 'all' && (
        <Card>
          <CardHeader>
            <CardTitle>Individual Performance Details</CardTitle>
            <CardDescription>Detailed metrics for selected technician</CardDescription>
          </CardHeader>
          <CardContent>
            <div className='text-center py-8 text-muted-foreground'>
              <Users className='h-12 w-12 mx-auto mb-4 opacity-50' />
              <p>Individual technician performance details will be displayed here.</p>
              <p className='text-sm'>This feature can be expanded to show detailed breakdowns.</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default PerformanceTracking;
