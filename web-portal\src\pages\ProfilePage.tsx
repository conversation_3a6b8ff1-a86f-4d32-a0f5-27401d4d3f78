import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  AlertCircle,
  Award,
  Camera,
  CheckCircle,
  Loader2,
  Mail,
  Phone,
  Save,
  Settings,
  Shield,
  Upload,
  User,
  X,
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '../components/ui/avatar';
import { Badge } from '../components/ui/badge';
import { Textarea } from '../components/ui/textarea';
import { Switch } from '../components/ui/switch';
import { Separator } from '../components/ui/separator';
import { Alert, AlertDescription } from '../components/ui/alert';
import ProfilePictureUpload from '../components/profile/ProfilePictureUpload';
import SpecialtiesManager from '../components/profile/SpecialtiesManager';
import ProfileIntegrationTest from '../components/profile/ProfileIntegrationTest';
import { profileService } from '../services/profileService';
import { technicianService } from '../services/technicianService';
import type { TechnicianModel } from '../types/technician';

// Profile form validation schema
const profileSchema = z.object({
  name: z
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must be less than 100 characters'),
  email: z.string().email('Invalid email address'),
  phone_number: z.string().optional(),
  bio: z.string().max(500, 'Bio must be less than 500 characters').optional(),
  specialties: z.array(z.string()).optional(),
  is_available: z.boolean().optional(),
  status: z.enum(['active', 'offline', 'busy', 'onLeave']).optional(),
});

type ProfileFormData = z.infer<typeof profileSchema>;

interface ProfilePageState {
  isLoading: boolean;
  isSaving: boolean;
  saveSuccess: boolean;
  error: string | null;
  technicianData: TechnicianModel | null;
}

const ProfilePage: React.FC = () => {
  const { user, updateProfile } = useAuth();
  const [state, setState] = useState<ProfilePageState>({
    isLoading: true,
    isSaving: false,
    saveSuccess: false,
    error: null,
    technicianData: null,
  });

  const [profilePictureUrl, setProfilePictureUrl] = useState<string | null>(
    user?.photo_url || null,
  );
  const [activeTab, setActiveTab] = useState<'personal' | 'professional' | 'settings' | 'test'>(
    'personal',
  );

  const form = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      name: user?.name || '',
      email: user?.email || '',
      phone_number: user?.phone || '',
      bio: '',
      specialties: [],
      is_available: true,
      status: 'active',
    },
  });

  // Load profile data on component mount
  useEffect(() => {
    loadProfileData();
  }, [user]);

  const loadProfileData = async () => {
    if (!user) return;

    try {
      setState((prev) => ({ ...prev, isLoading: true, error: null }));

      // Load technician data if user is a technician
      if (user.role === 'technician') {
        const technicianData = await technicianService.getById(user.uid);
        if (technicianData) {
          setState((prev) => ({ ...prev, technicianData }));

          // Update form with technician data
          form.reset({
            name: technicianData.name,
            email: technicianData.email,
            phone_number: technicianData.phone_number || '',
            bio: technicianData.bio || '',
            specialties: technicianData.specialties || [],
            is_available: technicianData.is_available,
            status: technicianData.status,
          });

          setProfilePictureUrl(technicianData.photo_url || null);
        }
      } else {
        // For non-technician users, use user data
        form.reset({
          name: user.name,
          email: user.email,
          phone_number: user.phone || '',
          bio: '',
          specialties: [],
          is_available: true,
          status: 'active',
        });
      }
    } catch (error) {
      console.error('Error loading profile data:', error);
      setState((prev) => ({
        ...prev,
        error: 'Failed to load profile data. Please try again.',
      }));
    } finally {
      setState((prev) => ({ ...prev, isLoading: false }));
    }
  };

  const onSubmit = async (data: ProfileFormData) => {
    if (!user) return;

    try {
      setState((prev) => ({ ...prev, isSaving: true, error: null, saveSuccess: false }));

      // Prepare update data with snake_case fields (excluding photo_url which is handled separately)
      const updateData = {
        name: data.name,
        email: data.email,
        phone: data.phone_number,
        // photo_url is handled by ProfilePictureUpload component
      };

      // Update profile based on user role
      if (user.role === 'admin') {
        // For admins, only update the admin collection
        console.warn('Updating admin profile');
        await profileService.updateUserProfile(user.uid, updateData);
      } else if (user.role === 'technician') {
        // For technicians, update both collections
        console.warn('Updating technician profile');
        await profileService.updateUserProfile(user.uid, updateData);
        await profileService.updateTechnicianProfile(user.uid, {
          name: data.name,
          email: data.email,
          phone: data.phone_number,
          bio: data.bio,
          specialties: data.specialties || [],
          is_available: data.is_available,
          status: data.status,
        });
      } else {
        // For regular users
        console.warn('Updating user profile');
        await profileService.updateUserProfile(user.uid, updateData);
      }

      // Update the auth context with new profile data
      await updateProfile({
        name: data.name,
        email: data.email,
        phone: data.phone_number,
        // photo_url is already updated by ProfilePictureUpload component
      });

      setState((prev) => ({ ...prev, saveSuccess: true }));

      // Clear success message after 3 seconds
      setTimeout(() => {
        setState((prev) => ({ ...prev, saveSuccess: false }));
      }, 3000);
    } catch (error) {
      console.error('Error updating profile:', error);
      setState((prev) => ({
        ...prev,
        error: 'Failed to update profile. Please try again.',
      }));
    } finally {
      setState((prev) => ({ ...prev, isSaving: false }));
    }
  };

  const handleProfilePictureUpdate = (newUrl: string | null) => {
    setProfilePictureUrl(newUrl);
  };

  if (state.isLoading) {
    return (
      <div className='flex items-center justify-center min-h-[400px]'>
        <Loader2 className='h-8 w-8 animate-spin' />
      </div>
    );
  }

  return (
    <div className='space-y-6 max-w-5xl mx-auto'>
      {/* Header */}
      <div className='flex flex-col md:flex-row md:items-center md:justify-between gap-4'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>Profile</h1>
          <p className='text-muted-foreground mt-2'>
            Manage your personal information and preferences
          </p>
        </div>
      </div>

      {/* Success/Error Messages */}
      {state.saveSuccess && (
        <Alert className='border-green-200 bg-green-50'>
          <CheckCircle className='h-4 w-4 text-green-600' />
          <AlertDescription className='text-green-800'>
            Profile updated successfully!
          </AlertDescription>
        </Alert>
      )}

      {state.error && (
        <Alert variant='destructive'>
          <AlertCircle className='h-4 w-4' />
          <AlertDescription>{state.error}</AlertDescription>
        </Alert>
      )}

      {/* Profile Content */}
      <div className='grid grid-cols-1 md:grid-cols-4 gap-6'>
        {/* Left Column - Profile Picture and Summary */}
        <div className='md:col-span-1 space-y-6'>
          {/* Profile Picture Card */}
          <Card>
            <CardHeader className='pb-2'>
              <CardTitle className='text-lg'>Profile Picture</CardTitle>
            </CardHeader>
            <CardContent className='flex flex-col items-center'>
              <ProfilePictureUpload
                currentImageUrl={profilePictureUrl}
                onImageUpdate={handleProfilePictureUpdate}
                userId={user?.uid || ''}
                userName={form.watch('name')}
              />
            </CardContent>
          </Card>

          {/* Profile Summary Card */}
          <Card>
            <CardHeader className='pb-2'>
              <CardTitle className='text-lg'>Profile Summary</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='flex items-center gap-2'>
                <User className='h-4 w-4 text-muted-foreground' />
                <span className='text-sm'>{form.watch('name')}</span>
              </div>
              <div className='flex items-center gap-2'>
                <Mail className='h-4 w-4 text-muted-foreground' />
                <span className='text-sm'>{form.watch('email')}</span>
              </div>
              {form.watch('phone_number') && (
                <div className='flex items-center gap-2'>
                  <Phone className='h-4 w-4 text-muted-foreground' />
                  <span className='text-sm'>{form.watch('phone_number')}</span>
                </div>
              )}
              {user?.role === 'technician' && (
                <div className='flex items-center gap-2'>
                  <Badge variant={form.watch('is_available') ? 'default' : 'outline'}>
                    {form.watch('is_available') ? 'Available' : 'Unavailable'}
                  </Badge>
                  <Badge variant='outline' className='capitalize'>
                    {form.watch('status')}
                  </Badge>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Right Column - Tabs */}
        <div className='md:col-span-3'>
          <Card>
            <CardHeader className='pb-0'>
              <Tabs
                defaultValue='personal'
                value={activeTab}
                onValueChange={(value) => setActiveTab(value as any)}
                className='w-full'
              >
                <TabsList className='grid grid-cols-3 mb-2'>
                  <TabsTrigger value='personal'>Personal</TabsTrigger>
                  {user?.role === 'technician' && (
                    <TabsTrigger value='professional'>Professional</TabsTrigger>
                  )}
                  <TabsTrigger value='settings'>Settings</TabsTrigger>
                </TabsList>

                <TabsContent value='personal'>
                  <form onSubmit={form.handleSubmit(onSubmit)}>
                    <div className='space-y-6'>
                      <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                        <div className='space-y-2'>
                          <Label htmlFor='name'>
                            Full Name <span className='text-destructive'>*</span>
                          </Label>
                          <Input
                            id='name'
                            placeholder='Your full name'
                            {...form.register('name')}
                            disabled={state.isSaving}
                            className={
                              form.formState.errors.name
                                ? 'border-destructive focus-visible:ring-destructive'
                                : ''
                            }
                            aria-invalid={form.formState.errors.name ? 'true' : 'false'}
                            aria-describedby={form.formState.errors.name ? 'name-error' : undefined}
                          />
                          {form.formState.errors.name && (
                            <p
                              id='name-error'
                              className='text-sm text-destructive flex items-center gap-1'
                            >
                              <AlertCircle className='w-4 h-4' />
                              {form.formState.errors.name.message}
                            </p>
                          )}
                        </div>
                        <div className='space-y-2'>
                          <Label htmlFor='email'>
                            Email Address <span className='text-destructive'>*</span>
                          </Label>
                          <Input
                            id='email'
                            type='email'
                            placeholder='Your email address'
                            {...form.register('email')}
                            disabled={state.isSaving}
                            className={
                              form.formState.errors.email
                                ? 'border-destructive focus-visible:ring-destructive'
                                : ''
                            }
                            aria-invalid={form.formState.errors.email ? 'true' : 'false'}
                            aria-describedby={
                              form.formState.errors.email ? 'email-error' : undefined
                            }
                          />
                          {form.formState.errors.email && (
                            <p
                              id='email-error'
                              className='text-sm text-destructive flex items-center gap-1'
                            >
                              <AlertCircle className='w-4 h-4' />
                              {form.formState.errors.email.message}
                            </p>
                          )}
                        </div>
                      </div>

                      <div className='space-y-2'>
                        <Label htmlFor='phone_number'>Phone Number</Label>
                        <Input
                          id='phone_number'
                          placeholder='Your phone number'
                          {...form.register('phone_number')}
                        />
                      </div>

                      <div className='flex justify-end'>
                        <Button type='submit' disabled={state.isSaving}>
                          {state.isSaving ? (
                            <>
                              <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                              Saving...
                            </>
                          ) : (
                            <>
                              <Save className='mr-2 h-4 w-4' />
                              Save Changes
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  </form>
                </TabsContent>

                {user?.role === 'technician' && (
                  <TabsContent value='professional'>
                    <form onSubmit={form.handleSubmit(onSubmit)}>
                      <div className='space-y-6'>
                        <div className='space-y-2'>
                          <Label htmlFor='bio'>Bio</Label>
                          <Textarea
                            id='bio'
                            placeholder='Tell us about yourself and your expertise'
                            className='min-h-32'
                            {...form.register('bio')}
                          />
                          {form.formState.errors.bio && (
                            <p className='text-sm text-destructive'>
                              {form.formState.errors.bio.message}
                            </p>
                          )}
                        </div>

                        <div className='space-y-2'>
                          <Label>Specialties</Label>
                          <SpecialtiesManager
                            specialties={form.watch('specialties') || []}
                            onSpecialtiesChange={(specialties) =>
                              form.setValue('specialties', specialties)
                            }
                          />
                        </div>

                        <div className='space-y-4'>
                          <div className='flex items-center justify-between'>
                            <div className='space-y-0.5'>
                              <Label htmlFor='is_available'>Availability</Label>
                              <p className='text-sm text-muted-foreground'>
                                Toggle your availability to take new requests
                              </p>
                            </div>
                            <Switch
                              id='is_available'
                              checked={form.watch('is_available')}
                              onCheckedChange={(checked) => form.setValue('is_available', checked)}
                            />
                          </div>

                          <div className='space-y-2'>
                            <Label htmlFor='status'>Status</Label>
                            <select
                              id='status'
                              className='w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background'
                              value={form.watch('status')}
                              onChange={(e) => form.setValue('status', e.target.value as any)}
                            >
                              <option value='active'>Active</option>
                              <option value='offline'>Offline</option>
                              <option value='busy'>Busy</option>
                              <option value='onLeave'>On Leave</option>
                            </select>
                          </div>
                        </div>

                        <div className='flex justify-end'>
                          <Button type='submit' disabled={state.isSaving}>
                            {state.isSaving ? (
                              <>
                                <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                                Saving...
                              </>
                            ) : (
                              <>
                                <Save className='mr-2 h-4 w-4' />
                                Save Changes
                              </>
                            )}
                          </Button>
                        </div>
                      </div>
                    </form>
                  </TabsContent>
                )}

                <TabsContent value='settings'>
                  <form onSubmit={form.handleSubmit(onSubmit)}>
                    <div className='space-y-6'>
                      <div className='space-y-2'>
                        <h3 className='text-lg font-medium'>Account Settings</h3>
                        <p className='text-sm text-muted-foreground'>
                          Manage your account settings and preferences
                        </p>
                      </div>

                      <Separator />

                      <div className='space-y-4'>
                        <div className='flex items-center justify-between'>
                          <div className='space-y-0.5'>
                            <Label>Email Notifications</Label>
                            <p className='text-sm text-muted-foreground'>
                              Receive email notifications for important updates
                            </p>
                          </div>
                          <Switch checked={true} />
                        </div>

                        <div className='flex items-center justify-between'>
                          <div className='space-y-0.5'>
                            <Label>Push Notifications</Label>
                            <p className='text-sm text-muted-foreground'>
                              Receive push notifications on your device
                            </p>
                          </div>
                          <Switch checked={true} />
                        </div>

                        <div className='flex items-center justify-between'>
                          <div className='space-y-0.5'>
                            <Label>Dark Mode</Label>
                            <p className='text-sm text-muted-foreground'>
                              Toggle between light and dark mode
                            </p>
                          </div>
                          <Switch checked={false} />
                        </div>
                      </div>

                      <Separator />

                      <div className='space-y-2'>
                        <h3 className='text-lg font-medium'>Privacy</h3>
                        <p className='text-sm text-muted-foreground'>
                          Manage your privacy settings
                        </p>
                      </div>

                      <div className='space-y-4'>
                        <div className='flex items-center justify-between'>
                          <div className='space-y-0.5'>
                            <Label>Profile Visibility</Label>
                            <p className='text-sm text-muted-foreground'>
                              Control who can see your profile
                            </p>
                          </div>
                          <select
                            className='rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background'
                            defaultValue='public'
                          >
                            <option value='public'>Public</option>
                            <option value='private'>Private</option>
                            <option value='contacts'>Contacts Only</option>
                          </select>
                        </div>
                      </div>

                      <div className='flex justify-end'>
                        <Button type='submit' disabled={state.isSaving}>
                          {state.isSaving ? (
                            <>
                              <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                              Saving...
                            </>
                          ) : (
                            <>
                              <Save className='mr-2 h-4 w-4' />
                              Save Changes
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  </form>
                </TabsContent>
              </Tabs>
            </CardHeader>
            <CardContent className='pt-6'>
              {/* Save button can be placed here if needed */}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
