import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  addDays,
  eachDayOfInterval,
  endOfWeek,
  format,
  isSameDay,
  parseISO,
  startOfWeek,
} from 'date-fns';
import { useAuth } from '../contexts/AuthContext';
import { usePermissions } from '../hooks/usePermissions';
import PermissionGate from '../components/auth/PermissionGate';
import { Permission } from '../types/permissions';
import {
  Activity,
  AlertCircle,
  BarChart3,
  Calendar,
  CalendarDays,
  CheckCircle,
  Clock,
  Edit,
  Mail,
  MapPin,
  MoreVertical,
  Phone,
  Plus,
  Search,
  Settings,
  Star,
  Trash2,
  UserCheck,
  UserX,
  Users,
  X,
  XCircle,
} from 'lucide-react';
import PerformanceTracking from '../components/PerformanceTracking';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Avatar } from '../components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../components/ui/dropdown-menu';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '../components/ui/sheet';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../components/ui/form';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../components/ui/tabs';

// Import services and types
import technicianService from '../services/technicianService';
import {
  type CreateTechnicianInput,
  type TechnicianModel,
  TechnicianStatus,
  type UpdateTechnicianInput,
} from '../types/technician';
import authService from '../services/authService';
import { createUserWithEmailAndPassword, updateProfile } from 'firebase/auth';
import { auth } from '../config/firebase';
import cloudFunctionService from '../services/cloudFunctionService';

// Availability management types
interface WorkingHours {
  start: string; // HH:mm format
  end: string; // HH:mm format
  isWorking: boolean;
}

interface WeeklySchedule {
  monday: WorkingHours;
  tuesday: WorkingHours;
  wednesday: WorkingHours;
  thursday: WorkingHours;
  friday: WorkingHours;
  saturday: WorkingHours;
  sunday: WorkingHours;
}

interface AvailabilityData {
  technicianId: string;
  weeklySchedule: WeeklySchedule;
  leaveSchedule: string[]; // Array of date strings (YYYY-MM-DD)
  lastUpdated: string;
}

const defaultWorkingHours: WorkingHours = {
  start: '09:00',
  end: '17:00',
  isWorking: true,
};

const defaultWeeklySchedule: WeeklySchedule = {
  monday: { ...defaultWorkingHours },
  tuesday: { ...defaultWorkingHours },
  wednesday: { ...defaultWorkingHours },
  thursday: { ...defaultWorkingHours },
  friday: { ...defaultWorkingHours },
  saturday: { start: '09:00', end: '13:00', isWorking: false },
  sunday: { start: '09:00', end: '17:00', isWorking: false },
};

// Form validation schema
const technicianFormSchema = z.object({
  name: z
    .string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must be less than 100 characters'),
  email: z.string().email('Please enter a valid email address'),
  phone_number: z
    .string()
    .regex(/^\+?[\d\s\-()]+$/, 'Please enter a valid phone number')
    .optional()
    .or(z.literal('')),
  photo_url: z.string().url('Please enter a valid URL').optional().or(z.literal('')),
  specialties: z.array(z.string()).min(1, 'At least one specialty is required'),
  status: z.nativeEnum(TechnicianStatus).optional(),
  is_available: z.boolean().optional(),
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/[0-9]/, 'Password must contain at least one number')
    .optional()
    .or(z.literal('')),
  createUserAccount: z.boolean().optional(),
});

type TechnicianFormData = z.infer<typeof technicianFormSchema>;

// Technician Form Component
interface TechnicianFormProps {
  initialData?: TechnicianModel;
  onSubmit: (data: CreateTechnicianInput | UpdateTechnicianInput) => Promise<void>;
  onCancel: () => void;
  submitLabel: string;
}

const TechnicianForm: React.FC<TechnicianFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  submitLabel,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [specialtyInput, setSpecialtyInput] = useState('');

  const form = useForm<TechnicianFormData>({
    resolver: zodResolver(technicianFormSchema),
    defaultValues: {
      name: initialData?.name || '',
      email: initialData?.email || '',
      phone_number: initialData?.phone_number || '',
      photo_url: initialData?.photo_url || '',
      specialties: initialData?.specialties || [],
      status: initialData?.status || TechnicianStatus.ACTIVE,
      is_available: initialData?.is_available ?? true,
      password: '',
      createUserAccount: !initialData, // Only for new technicians
    },
  });

  const specialties = form.watch('specialties');

  const handleSubmit = async (data: TechnicianFormData) => {
    try {
      setIsLoading(true);
      await onSubmit(data);
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const addSpecialty = () => {
    if (specialtyInput.trim() && !specialties.includes(specialtyInput.trim())) {
      const currentSpecialties = form.getValues('specialties');
      form.setValue('specialties', [...currentSpecialties, specialtyInput.trim()]);
      setSpecialtyInput('');
    }
  };

  const removeSpecialty = (index: number) => {
    const currentSpecialties = form.getValues('specialties');
    form.setValue(
      'specialties',
      currentSpecialties.filter((_, i) => i !== index),
    );
  };

  const handleSpecialtyKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addSpecialty();
    }
  };

  return (
    <div className='mt-6'>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className='space-y-6'>
          {/* Name Field */}
          <FormField
            control={form.control}
            name='name'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Full Name <span className='text-destructive'>*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter technician's full name"
                    disabled={isLoading}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Email Field */}
          <FormField
            control={form.control}
            name='email'
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  Email Address <span className='text-destructive'>*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    type='email'
                    placeholder='<EMAIL>'
                    disabled={isLoading}
                    autoComplete='email'
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Phone Number Field */}
          <FormField
            control={form.control}
            name='phone_number'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone Number (Optional)</FormLabel>
                <FormControl>
                  <Input
                    placeholder='+1234567890'
                    disabled={isLoading}
                    autoComplete='tel'
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Photo URL Field */}
          <FormField
            control={form.control}
            name='photo_url'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Photo URL (Optional)</FormLabel>
                <FormControl>
                  <Input type='url' placeholder='https://example.com/photo.jpg' {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Specialties Field */}
          <FormField
            control={form.control}
            name='specialties'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Specialties</FormLabel>
                <FormControl>
                  <div className='space-y-2'>
                    <div className='flex gap-2'>
                      <Input
                        placeholder='Add a specialty (e.g., PC Repair, Mobile Repair)'
                        value={specialtyInput}
                        onChange={(e) => setSpecialtyInput(e.target.value)}
                        onKeyPress={handleSpecialtyKeyPress}
                      />
                      <Button
                        type='button'
                        variant='outline'
                        onClick={addSpecialty}
                        disabled={!specialtyInput.trim()}
                      >
                        Add
                      </Button>
                    </div>

                    {specialties.length > 0 && (
                      <div className='flex flex-wrap gap-2'>
                        {specialties.map((specialty, index) => (
                          <Badge
                            key={index}
                            variant='secondary'
                            className='flex items-center gap-1'
                          >
                            {specialty}
                            <button
                              type='button'
                              onClick={() => removeSpecialty(index)}
                              className='ml-1 hover:text-destructive'
                            >
                              <X className='h-3 w-3' />
                            </button>
                          </Badge>
                        ))}
                      </div>
                    )}
                  </div>
                </FormControl>
                <FormDescription>Add the technician's areas of expertise</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* User Account Creation (for new technicians only) */}
          {!initialData && (
            <>
              <FormField
                control={form.control}
                name='createUserAccount'
                render={({ field }) => (
                  <FormItem className='flex flex-row items-center justify-between rounded-lg border p-4 bg-secondary'>
                    <div className='space-y-0.5'>
                      <FormLabel className='text-base'>Create User Account</FormLabel>
                      <FormDescription>
                        Create a login account for this technician to access the portal
                      </FormDescription>
                    </div>
                    <FormControl>
                      <input
                        type='checkbox'
                        checked={field.value}
                        onChange={field.onChange}
                        className='h-4 w-4'
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {form.watch('createUserAccount') && (
                <FormField
                  control={form.control}
                  name='password'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <Input
                          type='password'
                          placeholder='Enter a secure password (min 6 characters)'
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        This password will be used for the technician's login account
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </>
          )}

          {/* Status Field (for edit mode) */}
          {initialData && (
            <FormField
              control={form.control}
              name='status'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status</FormLabel>
                  <FormControl>
                    <select
                      {...field}
                      className='w-full px-3 py-2 border border-input bg-background rounded-md text-sm'
                    >
                      <option value={TechnicianStatus.ACTIVE}>Active</option>
                      <option value={TechnicianStatus.OFFLINE}>Offline</option>
                      <option value={TechnicianStatus.ON_LEAVE}>On Leave</option>
                    </select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}

          {/* Availability Field (for edit mode) */}
          {initialData && (
            <FormField
              control={form.control}
              name='is_available'
              render={({ field }) => (
                <FormItem className='flex flex-row items-center justify-between rounded-lg border p-4'>
                  <div className='space-y-0.5'>
                    <FormLabel className='text-base'>Available for Assignments</FormLabel>
                    <FormDescription>
                      Whether this technician is available to take new requests
                    </FormDescription>
                  </div>
                  <FormControl>
                    <input
                      type='checkbox'
                      checked={field.value}
                      onChange={field.onChange}
                      className='h-4 w-4'
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          )}

          {/* Form Actions */}
          <div className='flex gap-3 pt-4'>
            <Button type='submit' disabled={isLoading} className='flex-1'>
              {isLoading ? (
                <>
                  <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-primary-foreground mr-2'></div>
                  Saving...
                </>
              ) : (
                submitLabel
              )}
            </Button>
            <Button type='button' variant='outline' onClick={onCancel} disabled={isLoading}>
              Cancel
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

// Availability Management Component
interface AvailabilityManagementProps {
  technician: TechnicianModel;
  onClose: () => void;
  onSuccess: () => void;
}

const AvailabilityManagement: React.FC<AvailabilityManagementProps> = ({
  technician,
  onClose,
  onSuccess,
}) => {
  const [schedule, setSchedule] = useState<WeeklySchedule>(defaultWeeklySchedule);
  const [leaveSchedule, setLeaveSchedule] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [newLeaveDate, setNewLeaveDate] = useState('');

  // Get current week dates for display
  const today = new Date();
  const weekStart = startOfWeek(today, { weekStartsOn: 1 }); // Monday
  const weekEnd = endOfWeek(today, { weekStartsOn: 1 });
  const weekDays = eachDayOfInterval({ start: weekStart, end: weekEnd });

  const dayNames = [
    'monday',
    'tuesday',
    'wednesday',
    'thursday',
    'friday',
    'saturday',
    'sunday',
  ] as const;

  // Load existing availability data (placeholder - would come from service)
  useEffect(() => {
    // In a real implementation, this would fetch from the backend
    // For now, we'll use default schedule
    setSchedule(defaultWeeklySchedule);
    setLeaveSchedule([]);
  }, [technician.id]);

  const updateDaySchedule = (
    day: keyof WeeklySchedule,
    field: keyof WorkingHours,
    value: string | boolean,
  ) => {
    setSchedule((prev) => ({
      ...prev,
      [day]: {
        ...prev[day],
        [field]: value,
      },
    }));
  };

  const addLeaveDate = () => {
    if (newLeaveDate && !leaveSchedule.includes(newLeaveDate)) {
      setLeaveSchedule((prev) => [...prev, newLeaveDate].sort());
      setNewLeaveDate('');
    }
  };

  const removeLeaveDate = (dateToRemove: string) => {
    setLeaveSchedule((prev) => prev.filter((date) => date !== dateToRemove));
  };

  const saveAvailability = async () => {
    try {
      setIsLoading(true);

      // In a real implementation, this would save to backend
      const availabilityData: AvailabilityData = {
        technicianId: technician.id,
        weeklySchedule: schedule,
        leaveSchedule,
        lastUpdated: new Date().toISOString(),
      };

      console.warn('Saving availability:', availabilityData);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      onSuccess();
    } catch (error) {
      console.error('Error saving availability:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className='space-y-6'>
      {/* Weekly Schedule */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <CalendarDays className='h-5 w-5' />
            Weekly Schedule
          </CardTitle>
          <CardDescription>Set working hours for each day of the week</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            {dayNames.map((day, index) => {
              const daySchedule = schedule[day];
              const weekDay = weekDays[index];
              const isToday = isSameDay(weekDay, today);

              return (
                <div key={day} className={`p-4 border rounded-lg ${isToday ? 'bg-secondary' : ''}`}>
                  <div className='flex items-center justify-between mb-3'>
                    <div className='flex items-center gap-2'>
                      <h4 className='font-medium capitalize'>
                        {day} {isToday && <Badge variant='secondary'>Today</Badge>}
                      </h4>
                      <span className='text-sm text-muted-foreground'>
                        {format(weekDay, 'MMM dd')}
                      </span>
                    </div>
                    <div className='flex items-center gap-2'>
                      <Label htmlFor={`${day}-working`} className='text-sm'>
                        Working:
                      </Label>
                      <input
                        id={`${day}-working`}
                        type='checkbox'
                        checked={daySchedule.isWorking}
                        onChange={(e) => updateDaySchedule(day, 'isWorking', e.target.checked)}
                        className='h-4 w-4'
                      />
                    </div>
                  </div>

                  {daySchedule.isWorking && (
                    <div className='flex items-center gap-4'>
                      <div className='flex items-center gap-2'>
                        <Label htmlFor={`${day}-start`} className='text-sm'>
                          Start:
                        </Label>
                        <Input
                          id={`${day}-start`}
                          type='time'
                          value={daySchedule.start}
                          onChange={(e) => updateDaySchedule(day, 'start', e.target.value)}
                          className='w-24'
                        />
                      </div>
                      <div className='flex items-center gap-2'>
                        <Label htmlFor={`${day}-end`} className='text-sm'>
                          End:
                        </Label>
                        <Input
                          id={`${day}-end`}
                          type='time'
                          value={daySchedule.end}
                          onChange={(e) => updateDaySchedule(day, 'end', e.target.value)}
                          className='w-24'
                        />
                      </div>
                    </div>
                  )}

                  {!daySchedule.isWorking && (
                    <p className='text-sm text-muted-foreground'>Not working this day</p>
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Leave Schedule */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <Calendar className='h-5 w-5' />
            Leave Schedule
          </CardTitle>
          <CardDescription>Manage planned leave dates and holidays</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            {/* Add new leave date */}
            <div className='flex gap-2'>
              <Input
                type='date'
                value={newLeaveDate}
                onChange={(e) => setNewLeaveDate(e.target.value)}
                min={format(today, 'yyyy-MM-dd')}
                className='flex-1'
              />
              <Button onClick={addLeaveDate} disabled={!newLeaveDate} variant='outline'>
                Add Leave
              </Button>
            </div>

            {/* Leave dates list */}
            {leaveSchedule.length > 0 ? (
              <div className='space-y-2'>
                <Label className='text-sm font-medium'>Scheduled Leave:</Label>
                <div className='space-y-2'>
                  {leaveSchedule.map((date) => (
                    <div
                      key={date}
                      className='flex items-center justify-between p-2 border rounded'
                    >
                      <span className='text-sm'>
                        {format(parseISO(date), 'EEEE, MMMM dd, yyyy')}
                      </span>
                      <Button
                        size='sm'
                        variant='ghost'
                        onClick={() => removeLeaveDate(date)}
                        className='h-6 w-6 p-0'
                      >
                        <X className='h-3 w-3' />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <p className='text-sm text-muted-foreground'>No leave scheduled</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Actions */}
      <div className='flex gap-3'>
        <Button onClick={saveAvailability} disabled={isLoading} className='flex-1'>
          {isLoading ? (
            <>
              <div className='animate-spin rounded-full h-4 w-4 border-b-2 border-primary-foreground mr-2'></div>
              Saving...
            </>
          ) : (
            'Save Availability'
          )}
        </Button>
        <Button variant='outline' onClick={onClose} disabled={isLoading}>
          Cancel
        </Button>
      </div>
    </div>
  );
};

const TechniciansPage: React.FC = () => {
  const { user } = useAuth();
  const { hasPermission } = usePermissions();

  // State management
  const [technicians, setTechnicians] = useState<TechnicianModel[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<TechnicianStatus | 'all'>('all');
  const [isCreateSheetOpen, setIsCreateSheetOpen] = useState(false);
  const [editingTechnician, setEditingTechnician] = useState<TechnicianModel | null>(null);
  const [managingAvailability, setManagingAvailability] = useState<TechnicianModel | null>(null);
  const [activeTab, setActiveTab] = useState('management');
  const [workloadStats, setWorkloadStats] = useState({
    available: 0,
    busy: 0,
    offline: 0,
    onLeave: 0,
    totalActive: 0,
  });

  // Load technicians and stats
  useEffect(() => {
    loadTechnicians();
    loadWorkloadStats();
  }, []);

  const loadTechnicians = async () => {
    try {
      setLoading(true);
      console.warn('Loading technicians...');
      const data = await technicianService.getAll({
        orderBy: { field: 'name', direction: 'asc' },
      });
      console.warn('Loaded technicians:', data);
      setTechnicians(data);
    } catch (error) {
      console.error('Error loading technicians:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadWorkloadStats = async () => {
    try {
      const stats = await technicianService.getWorkloadStats();
      setWorkloadStats(stats);
    } catch (error) {
      console.error('Error loading workload stats:', error);
    }
  };

  // Filter technicians based on search and status
  const filteredTechnicians = technicians.filter((tech) => {
    const matchesSearch =
      tech.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tech.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = selectedStatus === 'all' || tech.status === selectedStatus;
    return matchesSearch && matchesStatus;
  });

  // Status badge styling
  const getStatusBadge = (status: TechnicianStatus, isAvailable: boolean) => {
    if (status === TechnicianStatus.ACTIVE && isAvailable) {
      return (
        <Badge variant='default' className='bg-green-500 hover:bg-green-600'>
          Available
        </Badge>
      );
    } else if (status === TechnicianStatus.BUSY) {
      return <Badge variant='destructive'>Busy</Badge>;
    } else if (status === TechnicianStatus.OFFLINE) {
      return <Badge variant='secondary'>Offline</Badge>;
    } else if (status === TechnicianStatus.ON_LEAVE) {
      return <Badge variant='outline'>On Leave</Badge>;
    } else {
      return <Badge variant='secondary'>Inactive</Badge>;
    }
  };

  // Toggle technician availability
  const handleToggleAvailability = async (technicianId: string) => {
    try {
      await technicianService.toggleAvailability(technicianId);
      await loadTechnicians();
      await loadWorkloadStats();
    } catch (error) {
      console.error('Error toggling availability:', error);
    }
  };

  // Update technician status
  const handleStatusUpdate = async (technicianId: string, status: TechnicianStatus) => {
    try {
      await technicianService.updateStatus(technicianId, status);
      await loadTechnicians();
      await loadWorkloadStats();
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  // Delete technician
  const handleDeleteTechnician = async (technicianId: string) => {
    if (confirm('Are you sure you want to delete this technician? This action cannot be undone.')) {
      try {
        await technicianService.delete(technicianId);
        await loadTechnicians();
        await loadWorkloadStats();
      } catch (error) {
        console.error('Error deleting technician:', error);
      }
    }
  };

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>Technicians</h1>
          <p className='text-muted-foreground mt-2'>
            Manage technician profiles and track performance
          </p>
        </div>

        <PermissionGate permissions={[Permission.VIEW_TECHNICIANS]}>
          <Sheet open={isCreateSheetOpen} onOpenChange={setIsCreateSheetOpen}>
            <SheetTrigger asChild>
              <Button>
                <Plus className='h-4 w-4 mr-2' />
                Add Technician
              </Button>
            </SheetTrigger>
            <CreateTechnicianSheet
              onClose={() => setIsCreateSheetOpen(false)}
              onSuccess={() => {
                loadTechnicians();
                loadWorkloadStats();
                setIsCreateSheetOpen(false);
              }}
            />
          </Sheet>
        </PermissionGate>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className='w-full'>
        <TabsList className='grid w-full grid-cols-2'>
          <TabsTrigger value='management' className='flex items-center gap-2'>
            <Users className='h-4 w-4' />
            Management
          </TabsTrigger>
          <TabsTrigger value='performance' className='flex items-center gap-2'>
            <BarChart3 className='h-4 w-4' />
            Performance
          </TabsTrigger>
        </TabsList>

        <TabsContent value='management' className='space-y-6'>
          {/* Stats Cards */}
          <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-5'>
            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Available</CardTitle>
                <UserCheck className='h-4 w-4 text-green-600' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold text-green-600'>{workloadStats.available}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Busy</CardTitle>
                <Activity className='h-4 w-4 text-red-600' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold text-red-600'>{workloadStats.busy}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Offline</CardTitle>
                <UserX className='h-4 w-4 text-gray-600' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold text-gray-600'>{workloadStats.offline}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>On Leave</CardTitle>
                <Clock className='h-4 w-4 text-yellow-600' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold text-yellow-600'>{workloadStats.onLeave}</div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                <CardTitle className='text-sm font-medium'>Total</CardTitle>
                <Users className='h-4 w-4 text-blue-600' />
              </CardHeader>
              <CardContent>
                <div className='text-2xl font-bold text-blue-600'>{technicians.length}</div>
              </CardContent>
            </Card>
          </div>

          {/* Search and Filters */}
          <div className='flex gap-4'>
            <div className='relative flex-1'>
              <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground' />
              <Input
                placeholder='Search technicians by name or email...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className='pl-10'
              />
            </div>

            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value as TechnicianStatus | 'all')}
              className='px-3 py-2 border border-input bg-background rounded-md text-sm'
            >
              <option value='all'>All Status</option>
              <option value={TechnicianStatus.ACTIVE}>Available</option>
              <option value={TechnicianStatus.BUSY}>Busy</option>
              <option value={TechnicianStatus.OFFLINE}>Offline</option>
              <option value={TechnicianStatus.ON_LEAVE}>On Leave</option>
            </select>
          </div>

          {/* Technicians List */}
          <Card>
            <CardHeader>
              <CardTitle>Technicians ({filteredTechnicians.length})</CardTitle>
              <CardDescription>
                Manage technician profiles and track their performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className='text-center py-8'>Loading technicians...</div>
              ) : technicians.length === 0 ? (
                <div className='text-center py-8 text-muted-foreground'>
                  <div className='space-y-2'>
                    <p>No technicians found.</p>
                    <p className='text-sm'>
                      If you have technicians in Firebase but they're not showing up, they might be
                      missing required fields like 'name', 'email', or 'specialties'.
                    </p>
                    <p className='text-sm'>Check the browser console for more details.</p>
                  </div>
                </div>
              ) : filteredTechnicians.length === 0 ? (
                <div className='text-center py-8 text-muted-foreground'>
                  No technicians match your current filters.
                </div>
              ) : (
                <div className='space-y-4'>
                  {filteredTechnicians.map((technician) => (
                    <TechnicianCard
                      key={technician.id}
                      technician={technician}
                      onToggleAvailability={() => handleToggleAvailability(technician.id)}
                      onStatusUpdate={(status) => handleStatusUpdate(technician.id, status)}
                      onEdit={() => setEditingTechnician(technician)}
                      onDelete={() => handleDeleteTechnician(technician.id)}
                      onManageAvailability={() => setManagingAvailability(technician)}
                    />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Edit Technician Sheet */}
          {editingTechnician && (
            <EditTechnicianSheet
              technician={editingTechnician}
              onClose={() => setEditingTechnician(null)}
              onSuccess={() => {
                loadTechnicians();
                loadWorkloadStats();
                setEditingTechnician(null);
              }}
            />
          )}

          {/* Availability Management Sheet */}
          {managingAvailability && (
            <Sheet open={true} onOpenChange={() => setManagingAvailability(null)}>
              <SheetContent className='w-[600px] sm:w-[700px] max-w-[90vw] overflow-y-auto'>
                <SheetHeader>
                  <SheetTitle>Manage Availability - {managingAvailability.name}</SheetTitle>
                  <SheetDescription>
                    Set working hours, schedule, and manage leave dates
                  </SheetDescription>
                </SheetHeader>
                <AvailabilityManagement
                  technician={managingAvailability}
                  onClose={() => setManagingAvailability(null)}
                  onSuccess={() => {
                    loadTechnicians();
                    loadWorkloadStats();
                    setManagingAvailability(null);
                  }}
                />
              </SheetContent>
            </Sheet>
          )}
        </TabsContent>

        <TabsContent value='performance' className='space-y-6'>
          <PerformanceTracking />
        </TabsContent>
      </Tabs>
    </div>
  );
};

// Technician Card Component
interface TechnicianCardProps {
  technician: TechnicianModel;
  onToggleAvailability: () => void;
  onStatusUpdate: (status: TechnicianStatus) => void;
  onEdit: () => void;
  onDelete: () => void;
  onManageAvailability: () => void;
}

const TechnicianCard: React.FC<TechnicianCardProps> = ({
  technician,
  onToggleAvailability,
  onStatusUpdate,
  onEdit,
  onDelete,
  onManageAvailability,
}) => {
  const { hasPermission } = usePermissions();

  const getStatusBadge = (status: TechnicianStatus, isAvailable: boolean) => {
    if (status === TechnicianStatus.ACTIVE && isAvailable) {
      return (
        <Badge variant='default' className='bg-green-500 hover:bg-green-600'>
          Available
        </Badge>
      );
    } else if (status === TechnicianStatus.BUSY) {
      return <Badge variant='destructive'>Busy</Badge>;
    } else if (status === TechnicianStatus.OFFLINE) {
      return <Badge variant='secondary'>Offline</Badge>;
    } else if (status === TechnicianStatus.ON_LEAVE) {
      return <Badge variant='outline'>On Leave</Badge>;
    } else {
      return <Badge variant='secondary'>Inactive</Badge>;
    }
  };

  return (
    <div className='flex items-center justify-between p-4 border rounded-lg hover:bg-secondary transition-colors'>
      <div className='flex items-center space-x-4'>
        <Avatar className='h-12 w-12'>
          {technician.photo_url ? (
            <img src={technician.photo_url} alt={technician.name} className='object-cover' />
          ) : (
            <div className='flex items-center justify-center bg-primary text-primary-foreground'>
              {technician.name.charAt(0).toUpperCase()}
            </div>
          )}
        </Avatar>

        <div className='space-y-1'>
          <div className='flex items-center gap-2'>
            <h3 className='font-semibold'>{technician.name}</h3>
            {getStatusBadge(technician.status, technician.is_available)}
          </div>

          <div className='flex items-center gap-4 text-sm text-muted-foreground'>
            <div className='flex items-center gap-1'>
              <Mail className='h-3 w-3' />
              {technician.email}
            </div>
            {technician.phone_number && (
              <div className='flex items-center gap-1'>
                <Phone className='h-3 w-3' />
                {technician.phone_number}
              </div>
            )}
          </div>

          <div className='flex items-center gap-4 text-sm'>
            <div className='flex items-center gap-1'>
              <Star className='h-3 w-3 text-yellow-500' />
              <span>{technician.rating.toFixed(1)}</span>
            </div>
            <span className='text-muted-foreground'>{technician.completed_requests} completed</span>
            {technician.active_requests > 0 && (
              <span className='text-muted-foreground'>{technician.active_requests} active</span>
            )}
          </div>

          {technician.specialties.length > 0 && (
            <div className='flex gap-1 mt-2'>
              {technician.specialties.slice(0, 3).map((specialty, index) => (
                <Badge key={index} variant='outline' className='text-xs'>
                  {specialty}
                </Badge>
              ))}
              {technician.specialties.length > 3 && (
                <Badge variant='outline' className='text-xs'>
                  +{technician.specialties.length - 3} more
                </Badge>
              )}
            </div>
          )}
        </div>
      </div>

      <PermissionGate permissions={[Permission.VIEW_TECHNICIANS]}>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant='ghost' size='sm'>
              <MoreVertical className='h-4 w-4' />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align='end'>
            <DropdownMenuItem onClick={onEdit}>
              <Edit className='h-4 w-4 mr-2' />
              Edit Profile
            </DropdownMenuItem>

            <DropdownMenuItem onClick={onManageAvailability}>
              <Settings className='h-4 w-4 mr-2' />
              Manage Schedule
            </DropdownMenuItem>

            <DropdownMenuItem onClick={onToggleAvailability}>
              {technician.is_available ? (
                <>
                  <UserX className='h-4 w-4 mr-2' />
                  Set Unavailable
                </>
              ) : (
                <>
                  <UserCheck className='h-4 w-4 mr-2' />
                  Set Available
                </>
              )}
            </DropdownMenuItem>

            <DropdownMenuSeparator />

            <DropdownMenuItem
              onClick={() => onStatusUpdate(TechnicianStatus.ACTIVE)}
              disabled={technician.status === TechnicianStatus.ACTIVE}
            >
              Set Active
            </DropdownMenuItem>

            <DropdownMenuItem
              onClick={() => onStatusUpdate(TechnicianStatus.OFFLINE)}
              disabled={technician.status === TechnicianStatus.OFFLINE}
            >
              Set Offline
            </DropdownMenuItem>

            <DropdownMenuItem
              onClick={() => onStatusUpdate(TechnicianStatus.ON_LEAVE)}
              disabled={technician.status === TechnicianStatus.ON_LEAVE}
            >
              Set On Leave
            </DropdownMenuItem>

            <DropdownMenuSeparator />

            <DropdownMenuItem
              onClick={onDelete}
              className='text-destructive focus:text-destructive'
            >
              <Trash2 className='h-4 w-4 mr-2' />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </PermissionGate>
    </div>
  );
};

// Create Technician Sheet Component
const CreateTechnicianSheet: React.FC<{
  onClose: () => void;
  onSuccess: () => void;
}> = ({ onClose, onSuccess }) => {
  return (
    <SheetContent className='w-[500px] sm:w-[600px] overflow-y-auto'>
      <SheetHeader className='border-b pb-4 mb-6'>
        <SheetTitle className='text-xl'>Add New Technician</SheetTitle>
        <SheetDescription className='text-base'>
          Create a new technician profile and optionally set up their login account
        </SheetDescription>
      </SheetHeader>

      <div className='px-1'>
        <TechnicianForm
          onSubmit={async (data: any) => {
            try {
              if (data.createUserAccount) {
                const { password, createUserAccount, ...rest } = data;
                // Create auth user via Cloud Function (doesn't auto-login)
                console.warn('Creating technician with auth account using Cloud Function...');

                // Add a loading state indicator
                const loadingElement = document.createElement('div');
                loadingElement.id = 'technician-creation-loading';
                loadingElement.innerHTML = 'Creating technician account... Please wait.';
                loadingElement.style.position = 'fixed';
                loadingElement.style.top = '50%';
                loadingElement.style.left = '50%';
                loadingElement.style.transform = 'translate(-50%, -50%)';
                loadingElement.style.padding = '20px';
                loadingElement.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
                loadingElement.style.color = 'white';
                loadingElement.style.borderRadius = '5px';
                loadingElement.style.zIndex = '9999';
                document.body.appendChild(loadingElement);

                try {
                  // Prepare technician data
                  const technicianData = {
                    phone_number: rest.phone_number,
                    photo_url: rest.photo_url,
                    specialties: rest.specialties,
                    status: rest.status || 'active',
                    is_available: rest.is_available ?? true,
                    rating: 0,
                    completed_requests: 0,
                    active_requests: 0,
                  };

                  // Use Cloud Function to create technician account (doesn't auto-login)
                  const result = await cloudFunctionService.createTechnicianAccount({
                    email: rest.email,
                    password,
                    name: rest.name,
                    technicianData,
                  });

                  console.warn('Technician created successfully:', result);

                  // Success message
                  document.getElementById('technician-creation-loading')!.innerHTML =
                    'Technician account created successfully! Admin remains logged in.';

                  // Remove loading indicator after a delay
                  setTimeout(() => {
                    document.getElementById('technician-creation-loading')?.remove();
                    onSuccess();
                  }, 2000);
                } catch (error) {
                  console.error('Error creating technician:', error);

                  // Error message
                  document.getElementById('technician-creation-loading')!.innerHTML =
                    `Error: ${error instanceof Error ? error.message : 'Unknown error'}`;

                  // Remove loading indicator after a delay
                  setTimeout(() => {
                    document.getElementById('technician-creation-loading')?.remove();
                  }, 3000);

                  throw error;
                }
              } else {
                const { password, createUserAccount, ...rest } = data;
                await technicianService.createTechnician(rest);
                onSuccess();
              }
            } catch (e) {
              console.error('Error creating technician account:', e);
              alert((e as Error).message);
            }
          }}
          onCancel={onClose}
          submitLabel='Create Technician'
        />
      </div>
    </SheetContent>
  );
};

// Edit Technician Sheet Component
const EditTechnicianSheet: React.FC<{
  technician: TechnicianModel;
  onClose: () => void;
  onSuccess: () => void;
}> = ({ technician, onClose, onSuccess }) => {
  return (
    <Sheet open={true} onOpenChange={onClose}>
      <SheetContent className='w-[500px] sm:w-[600px] overflow-y-auto'>
        <SheetHeader className='border-b pb-4 mb-6'>
          <SheetTitle className='text-xl'>Edit Technician</SheetTitle>
          <SheetDescription className='text-base'>
            Update {technician.name}'s profile and settings
          </SheetDescription>
        </SheetHeader>

        <div className='px-1'>
          <TechnicianForm
            initialData={technician}
            onSubmit={async (data) => {
              await technicianService.updateTechnician(technician.id, data);
              onSuccess();
            }}
            onCancel={onClose}
            submitLabel='Update Technician'
          />
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default TechniciansPage;
