import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../services/secure_storage_service.dart';
import '../services/remote_config_service.dart';

/// A utility class for handling environment variables in the app
/// Uses flutter_dotenv to load variables from .env file
/// Sensitive data is stored in secure storage
class EnvConfig {
  static EnvConfig? _instance;
  Map<String, String> _envVars = {};
  bool _isInitialized = false;
  final _secureStorage = SecureStorageService();
  final _remoteConfig = RemoteConfigService();

  // List of sensitive keys that should be stored in secure storage
  static const List<String> _sensitiveKeys = [
    'PAYMOB_API_KEY',
    'PAYMOB_SECRET_KEY',
    'PAYMOB_INTEGRATION_ID',
    'PAYMOB_PUBLIC_KEY',
    'FIREBASE_API_KEY',
    'GOOGLE_API_KEY',
  ];

  // Keys that can be fetched from Remote Config as fallback
  static const Map<String, String> _remoteConfigMapping = {
    'PAYMOB_SECRET_KEY': 'paymob_secret_key',
    'PAYMOB_PUBLIC_KEY': 'paymob_public_key',
    'PAYMOB_INTEGRATION_ID': 'paymob_integration_id',
    'PAYMOB_API_KEY': 'paymob_api_key',
    'PAYMENT_MODE': 'payment_mode',
  };

  // Environment keys that should be stored securely
  static const List<String> _secureKeys = [
    'PAYMOB_API_KEY',
    'PAYMOB_SECRET_KEY',
    'PAYMOB_INTEGRATION_ID',
  ];

  /// Private constructor for singleton
  EnvConfig._();

  /// Get singleton instance
  static EnvConfig get instance {
    _instance ??= EnvConfig._();
    return _instance!;
  }

  /// Check if config is initialized
  bool get isInitialized => _isInitialized;

  /// Initialize environment variables from .env file
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Load .env file using flutter_dotenv
      await dotenv.load(fileName: '.env');

      // Copy non-sensitive values to our internal map
      _envVars = Map.from(dotenv.env);

      // Move sensitive values to secure storage
      await _storeSensitiveValues();

      // Add compile-time environment variables if any
      await _addDartDefines();

      // Initialize Remote Config for additional security
      await _initializeRemoteConfig();

      _isInitialized = true;

      // Debug log the loaded env vars for Paymob (without showing actual values)
      if (kDebugMode) {
        await _debugPrintEnvironmentStatus();
      }

      debugPrint('Environment configuration loaded');
    } catch (e) {
      debugPrint('Error initializing environment configuration: $e');
      // Initialize with empty map to avoid null checks
      _envVars = {};
      _isInitialized = true;
    }
  }

  /// Store sensitive values in secure storage
  Future<void> _storeSensitiveValues() async {
    for (final key in _sensitiveKeys) {
      final value = dotenv.env[key];
      if (value != null && value.isNotEmpty) {
        // Store in secure storage
        await _secureStorage.storeData(key, value);

        // Remove from in-memory map for security
        _envVars.remove(key);
      }
    }
  }

  /// Initialize Remote Config service
  Future<void> _initializeRemoteConfig() async {
    try {
      debugPrint('🔄 Initializing Remote Config for secure configuration...');
      await _remoteConfig.initialize();
      debugPrint('✅ Remote Config initialized successfully');
    } catch (e) {
      debugPrint('❌ Remote Config initialization failed: $e');
      // Continue without Remote Config - fallback to local .env
    }
  }

  /// Debug print environment status
  Future<void> _debugPrintEnvironmentStatus() async {
    debugPrint('\n======= ENV CONFIG STATUS =======');
    debugPrint(
      'Total environment variables loaded: ${_envVars.length + _sensitiveKeys.length}',
    );

    // Debug: Check if important keys are loaded (without revealing values)
    final hasApiKey = await _checkSecureKeyExists('PAYMOB_API_KEY');
    final hasIntegrationId = await _checkSecureKeyExists(
      'PAYMOB_INTEGRATION_ID',
    );

    debugPrint(
      'PAYMOB_API_KEY: ${hasApiKey ? "✓ LOADED (SECURE)" : "✗ MISSING"}',
    );
    debugPrint(
      'PAYMOB_INTEGRATION_ID: ${hasIntegrationId ? "✓ LOADED (SECURE)" : "✗ MISSING"}',
    );

    // Print .env file loading path
    debugPrint(
      'ENV_FILE_LOCATION: ${dotenv.env['DOTENV_PATH'] ?? File('.env').absolute.path}',
    );
    debugPrint('================================\n');
  }

  /// Check if a secure key exists
  Future<bool> _checkSecureKeyExists(String key) async {
    final value = await _secureStorage.getData(key);
    return value != null && value.isNotEmpty;
  }

  /// Add environment variables from dart defines (--dart-define)
  Future<void> _addDartDefines() async {
    // These are passed at build time with --dart-define
    // Store common environment variables
    final paymobApiKey = const String.fromEnvironment('PAYMOB_API_KEY');
    final paymobIntegrationId = const String.fromEnvironment(
      'PAYMOB_INTEGRATION_ID',
    );

    if (paymobApiKey.isNotEmpty) {
      await _secureStorage.storeData('PAYMOB_API_KEY', paymobApiKey);
      debugPrint('Loaded PAYMOB_API_KEY from dart define (SECURE)');
    }
    if (paymobIntegrationId.isNotEmpty) {
      await _secureStorage.storeData(
        'PAYMOB_INTEGRATION_ID',
        paymobIntegrationId,
      );
      debugPrint('Loaded PAYMOB_INTEGRATION_ID from dart define (SECURE)');
    }
  }

  /// Get environment variable by key
  Future<String?> get(String key) async {
    if (!_isInitialized) {
      debugPrint('Warning: EnvConfig not initialized before use');
      return null;
    }

    String? value;

    // Check if this is a sensitive key that should be in secure storage
    if (_sensitiveKeys.contains(key)) {
      // PAYMOB CREDENTIALS: Remote Config ONLY (no fallback)
      if (_remoteConfigMapping.containsKey(key)) {
        try {
          final remoteKey = _remoteConfigMapping[key]!;
          final remoteValue = await _remoteConfig.getString(remoteKey);
          
          if (remoteValue.isNotEmpty) {
            debugPrint('🔄 Using Remote Config value for $key');
            return remoteValue;
          } else {
            debugPrint('❌ $key not found in Remote Config');
            return null;
          }
        } catch (e) {
          debugPrint('❌ Error fetching $key from Remote Config: $e');
          return null;
        }
      }

      // For other sensitive keys (non-Paymob), check secure storage
      if (!key.contains('PAYMOB')) {
        value = await _secureStorage.getData(key);
        return value;
      }

      return null;
    }

    // For non-sensitive keys, try Remote Config first
    if (_remoteConfigMapping.containsKey(key)) {
      try {
        final remoteKey = _remoteConfigMapping[key]!;
        final remoteValue = await _remoteConfig.getString(remoteKey);
        
        if (remoteValue.isNotEmpty) {
          debugPrint('🔄 Using Remote Config value for $key');
          return remoteValue;
        }
      } catch (e) {
        debugPrint('❌ Error fetching $key from Remote Config: $e');
      }
    }

    // Fallback to local environment variables for non-payment keys
    value = _envVars[key];
    value ??= dotenv.env[key];

    if (value == null && key.contains('PAYMOB')) {
      debugPrint('❌ Missing Paymob credential: $key');
      debugPrint('💡 Set $key in Firebase Remote Config - user must be online for payments');
    }

    return value;
  }

  /// Get environment variable by key with default value
  Future<String> getOrDefault(String key, String defaultValue) async {
    return (await get(key)) ?? defaultValue;
  }

  /// Return all non-sensitive environment variables (for debugging)
  Map<String, String> getNonSensitiveVariables() {
    return Map.from(_envVars);
  }

  /// Force refresh Remote Config values and update secure storage
  Future<bool> refreshRemoteConfig() async {
    try {
      debugPrint('🔄 Force refreshing Remote Config values...');
      final success = await _remoteConfig.forceFetch();
      
      if (success) {
        // Update secure storage with new Remote Config values
        for (final entry in _remoteConfigMapping.entries) {
          final localKey = entry.key;
          final remoteKey = entry.value;
          
          try {
            final remoteValue = await _remoteConfig.getString(remoteKey);
            if (remoteValue.isNotEmpty) {
              await _secureStorage.storeData(localKey, remoteValue);
              debugPrint('✅ Updated $localKey from Remote Config');
            }
          } catch (e) {
            debugPrint('❌ Error updating $localKey: $e');
          }
        }
        
        debugPrint('✅ Remote Config refresh completed');
        return true;
      }
      
      return false;
    } catch (e) {
      debugPrint('❌ Error refreshing Remote Config: $e');
      return false;
    }
  }

  /// Get Remote Config service instance for advanced operations
  RemoteConfigService get remoteConfig => _remoteConfig;
}
