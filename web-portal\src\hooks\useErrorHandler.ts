import { useCallback, useRef, useState } from 'react';
import { logger } from '../utils/logger';
import type { AppError } from '../types/common';

export interface ErrorState {
  error: Error | null;
  isError: boolean;
  errorId: string | null;
  retryCount: number;
  isRetrying: boolean;
}

export interface RetryOptions {
  maxRetries?: number;
  retryDelay?: number;
  exponentialBackoff?: boolean;
  retryCondition?: (error: Error) => boolean;
}

export interface UseErrorHandlerOptions {
  onError?: (error: Error, context?: Record<string, unknown>) => void;
  logErrors?: boolean;
  defaultRetryOptions?: RetryOptions;
}

export function useErrorHandler(options: UseErrorHandlerOptions = {}) {
  const {
    onError,
    logErrors = true,
    defaultRetryOptions = {
      maxRetries: 3,
      retryDelay: 1000,
      exponentialBackoff: true,
    },
  } = options;

  const [errorState, setErrorState] = useState<ErrorState>({
    error: null,
    isError: false,
    errorId: null,
    retryCount: 0,
    isRetrying: false,
  });

  const retryTimeoutRef = useRef<NodeJS.Timeout>();

  const generateErrorId = () => {
    return `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  };

  const logError = useCallback(
    (error: Error, context?: Record<string, unknown>) => {
      if (logErrors) {
        logger.error('Error handled by useErrorHandler', {
          error: error.message,
          stack: error.stack,
          errorId: errorState.errorId,
          context,
          retryCount: errorState.retryCount,
        });
      }
    },
    [logErrors, errorState.errorId, errorState.retryCount],
  );

  const setError = useCallback(
    (error: Error | null, context?: Record<string, unknown>) => {
      const errorId = error ? generateErrorId() : null;

      setErrorState({
        error,
        isError: !!error,
        errorId,
        retryCount: 0,
        isRetrying: false,
      });

      if (error) {
        logError(error, context);
        onError?.(error, context);
      }
    },
    [logError, onError],
  );

  const clearError = useCallback(() => {
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
    }
    setErrorState({
      error: null,
      isError: false,
      errorId: null,
      retryCount: 0,
      isRetrying: false,
    });
  }, []);

  const executeWithErrorHandling = useCallback(
    async <T>(
      operation: () => Promise<T>,
      context?: Record<string, unknown>,
      retryOptions?: RetryOptions,
    ): Promise<T | null> => {
      const options = { ...defaultRetryOptions, ...retryOptions };
      const {
        maxRetries = 3,
        retryDelay = 1000,
        exponentialBackoff = true,
        retryCondition,
      } = options;

      let lastError: Error | null = null;
      let currentRetryCount = 0;

      const attemptOperation = async (): Promise<T | null> => {
        try {
          clearError();
          const result = await operation();
          return result;
        } catch (error) {
          lastError = error instanceof Error ? error : new Error(String(error));
          currentRetryCount++;

          // Check if we should retry
          const shouldRetry =
            currentRetryCount <= maxRetries &&
            (retryCondition ? retryCondition(lastError) : isRetryableError(lastError));

          if (shouldRetry) {
            setErrorState((prev) => ({
              ...prev,
              retryCount: currentRetryCount,
              isRetrying: true,
            }));

            // Calculate delay
            const delay = exponentialBackoff
              ? retryDelay * Math.pow(2, currentRetryCount - 1)
              : retryDelay;

            logger.info('Retrying operation', {
              attempt: currentRetryCount,
              maxRetries,
              delay,
              error: lastError.message,
            });

            // Wait before retry
            await new Promise((resolve) => {
              retryTimeoutRef.current = setTimeout(resolve, delay);
            });

            return attemptOperation();
          } else {
            // Max retries reached or non-retryable error
            setError(lastError, context);
            return null;
          }
        }
      };

      return attemptOperation();
    },
    [defaultRetryOptions, clearError, setError],
  );

  const retry = useCallback(
    async <T>(operation: () => Promise<T>, context?: Record<string, unknown>): Promise<T | null> => {
      if (!errorState.isError) {
        return null;
      }

      setErrorState((prev) => ({
        ...prev,
        isRetrying: true,
      }));

      try {
        const result = await operation();
        clearError();
        return result;
      } catch (error) {
        const newError = error instanceof Error ? error : new Error(String(error));
        setError(newError, context);
        return null;
      }
    },
    [errorState.isError, clearError, setError],
  );

  return {
    ...errorState,
    setError,
    clearError,
    executeWithErrorHandling,
    retry,
  };
}

// Helper function to determine if an error is retryable
function isRetryableError(error: Error): boolean {
  const retryablePatterns = [
    /network/i,
    /timeout/i,
    /connection/i,
    /unavailable/i,
    /rate.?limit/i,
    /too.?many.?requests/i,
    /internal.?server.?error/i,
    /bad.?gateway/i,
    /service.?unavailable/i,
    /gateway.?timeout/i,
  ];

  const errorMessage = error.message.toLowerCase();
  return retryablePatterns.some((pattern) => pattern.test(errorMessage));
}

// Hook for handling form errors specifically
export function useFormErrorHandler() {
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const [generalError, setGeneralError] = useState<string | null>(null);

  const setFieldError = useCallback((field: string, error: string) => {
    setFieldErrors((prev) => ({
      ...prev,
      [field]: error,
    }));
  }, []);

  const clearFieldError = useCallback((field: string) => {
    setFieldErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  }, []);

  const clearAllErrors = useCallback(() => {
    setFieldErrors({});
    setGeneralError(null);
  }, []);

  const hasErrors = Object.keys(fieldErrors).length > 0 || !!generalError;

  return {
    fieldErrors,
    generalError,
    hasErrors,
    setFieldError,
    setGeneralError,
    clearFieldError,
    clearAllErrors,
  };
}

// Hook for handling async operations with loading states
export function useAsyncOperation<T = any>() {
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState<T | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const [isError, setIsError] = useState(false);

  const execute = useCallback(
    async (
      operation: () => Promise<T>,
      options?: {
        onSuccess?: (data: T) => void;
        onError?: (error: Error) => void;
        retryOptions?: RetryOptions;
      },
    ) => {
      setIsLoading(true);
      setError(null);
      setIsError(false);

      try {
        const result = await operation();
        setData(result);
        options?.onSuccess?.(result);
        return result;
      } catch (err) {
        const error = err instanceof Error ? err : new Error(String(err));
        setError(error);
        setIsError(true);
        options?.onError?.(error);
        return null;
      } finally {
        setIsLoading(false);
      }
    },
    [],
  );

  const reset = useCallback(() => {
    setIsLoading(false);
    setData(null);
    setError(null);
    setIsError(false);
  }, []);

  const retry = useCallback(
    async (operation?: () => Promise<T>) => {
      if (!operation) return null;
      return execute(operation);
    },
    [execute],
  );

  return {
    isLoading,
    data,
    error,
    isError,
    execute,
    retry,
    reset,
  };
}
