import React, { useEffect, useState } from 'react';
import { format } from 'date-fns';
import {
  AlertCircle,
  CheckCircle,
  Clock,
  CreditCard,
  Download,
  Eye,
  Filter,
  RefreshCw,
  Search,
  TrendingDown,
  TrendingUp,
  XCircle,
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Badge } from '../components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../components/ui/dialog';
import { useToast } from '../components/ui/use-toast';
import paymentService from '../services/paymentService';
import {
  addTestPaymentData,
  migratePaymentResponses,
  testFirebaseConnection,
} from '../utils/testPaymentData';
import type {
  PaymentFilters,
  PaymentMethod,
  PaymentStatus,
  PaymentSummary,
  PaymentTransaction,
} from '../types/payment';
import PermissionGate from '../components/auth/PermissionGate';
import { Permission } from '../types/permissions';
import { useAuth } from '../contexts/AuthContext';
import { Pagination, usePagination } from '../components/ui/pagination';

const PaymentsPage: React.FC = () => {
  const [transactions, setTransactions] = useState<PaymentTransaction[]>([]);
  const [totalTransactions, setTotalTransactions] = useState(0);
  const [summary, setSummary] = useState<PaymentSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<PaymentFilters>({});
  const [selectedTransaction, setSelectedTransaction] = useState<PaymentTransaction | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const { toast } = useToast();
  const { user, loading: authLoading } = useAuth();

  // Pagination
  const {
    currentPage,
    pageSize,
    handlePageChange,
    handlePageSizeChange,
    resetPagination,
    createPaginationInfo,
  } = usePagination(20);

  useEffect(() => {
    if (!authLoading) {
      console.warn('🔐 Auth state:', { user: user?.email, authLoading });
      loadData();
    }
  }, [filters, authLoading, currentPage, pageSize]);

  // Reset to first page when filters change
  useEffect(() => {
    resetPagination();
  }, [filters, resetPagination]);

  const loadData = async () => {
    try {
      setLoading(true);

      console.warn(
        '🔄 Loading payment data with filters:',
        filters,
        'page:',
        currentPage,
        'pageSize:',
        pageSize,
      );

      // Load transactions and summary in parallel
      const [transactionsResult, summaryResult] = await Promise.all([
        paymentService.getTransactions(filters, currentPage, pageSize),
        paymentService.getPaymentSummary(filters),
      ]);

      console.warn('📊 Loaded transactions:', transactionsResult.transactions.length);
      console.warn('📈 Summary data:', summaryResult);

      setTransactions(transactionsResult.transactions);
      setTotalTransactions(transactionsResult.total);
      setSummary(summaryResult);
    } catch (error) {
      console.error('❌ Error loading payment data:', error);
      toast({
        title: 'Error',
        description: `Failed to load payment data: ${error.message}`,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      loadData();
      return;
    }

    try {
      setLoading(true);
      const results = await paymentService.searchTransactions(searchQuery, filters);
      setTransactions(results);
    } catch (error) {
      console.error('Error searching transactions:', error);
      toast({
        title: 'Error',
        description: 'Failed to search transactions. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleTestConnection = async () => {
    try {
      await testFirebaseConnection();
      toast({
        title: 'Success',
        description: 'Firebase connection test completed. Check console for details.',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Firebase connection test failed. Check console for details.',
        variant: 'destructive',
      });
    }
  };

  const handleAddTestData = async () => {
    try {
      setLoading(true);
      await addTestPaymentData();
      await loadData(); // Reload data after adding test data
      toast({
        title: 'Success',
        description: 'Test payment data added successfully!',
      });
    } catch (error) {
      console.error('Error adding test data:', error);
      toast({
        title: 'Error',
        description: 'Failed to add test data. Check console for details.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleMigrateResponses = async () => {
    try {
      setLoading(true);
      await migratePaymentResponses();
      await loadData(); // Reload data after migration
      toast({
        title: 'Success',
        description: 'Payment responses migrated successfully!',
      });
    } catch (error) {
      console.error('Error migrating responses:', error);
      toast({
        title: 'Error',
        description: 'Failed to migrate responses. Check console for details.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: PaymentStatus) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className='h-4 w-4 text-green-500' />;
      case 'failed':
        return <XCircle className='h-4 w-4 text-red-500' />;
      case 'pending':
        return <Clock className='h-4 w-4 text-yellow-500' />;
      case 'refunded':
        return <RefreshCw className='h-4 w-4 text-blue-500' />;
      default:
        return <AlertCircle className='h-4 w-4 text-gray-500' />;
    }
  };

  const getStatusBadge = (status: PaymentStatus) => {
    const variants = {
      completed: 'default',
      failed: 'destructive',
      pending: 'secondary',
      refunded: 'outline',
      cancelled: 'secondary',
    } as const;

    return (
      <Badge variant={variants[status] || 'secondary'} className='flex items-center gap-1'>
        {getStatusIcon(status)}
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const formatCurrency = (amount: number, currency: string = 'EGP') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const SummaryCards = () => {
    if (!summary) return null;

    const cards = [
      {
        title: 'Total Transactions',
        value: summary.totalTransactions.toLocaleString(),
        amount: formatCurrency(summary.totalAmount),
        icon: CreditCard,
        color: 'text-blue-600',
      },
      {
        title: 'Completed',
        value: summary.completedTransactions.toLocaleString(),
        amount: formatCurrency(summary.completedAmount),
        icon: CheckCircle,
        color: 'text-green-600',
      },
      {
        title: 'Failed',
        value: summary.failedTransactions.toLocaleString(),
        amount: formatCurrency(summary.failedAmount),
        icon: XCircle,
        color: 'text-red-600',
      },
      {
        title: 'Pending',
        value: summary.pendingTransactions.toLocaleString(),
        amount: formatCurrency(summary.pendingAmount),
        icon: Clock,
        color: 'text-yellow-600',
      },
    ];

    return (
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        {cards.map((card, index) => (
          <Card key={index}>
            <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
              <CardTitle className='text-sm font-medium'>{card.title}</CardTitle>
              <card.icon className={`h-4 w-4 ${card.color}`} />
            </CardHeader>
            <CardContent>
              <div className='text-2xl font-bold'>{card.value}</div>
              <p className='text-xs text-muted-foreground'>{card.amount}</p>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  const TransactionDetailsDialog = ({ transaction }: { transaction: PaymentTransaction }) => (
    <DialogContent className='max-w-2xl'>
      <DialogHeader>
        <DialogTitle>Transaction Details</DialogTitle>
        <DialogDescription>Transaction ID: {transaction.paymobTransactionId}</DialogDescription>
      </DialogHeader>

      <div className='grid gap-4'>
        <div className='grid grid-cols-2 gap-4'>
          <div>
            <label className='text-sm font-medium'>Status</label>
            <div className='mt-1'>{getStatusBadge(transaction.status)}</div>
          </div>
          <div>
            <label className='text-sm font-medium'>Amount</label>
            <div className='mt-1 text-lg font-semibold'>
              {formatCurrency(transaction.amount, transaction.currency)}
            </div>
          </div>
        </div>

        <div className='grid grid-cols-2 gap-4'>
          <div>
            <label className='text-sm font-medium'>Payment Method</label>
            <div className='mt-1 capitalize'>{transaction.paymentMethod}</div>
          </div>
          <div>
            <label className='text-sm font-medium'>Environment</label>
            <div className='mt-1'>
              <Badge variant={transaction.isLive ? 'default' : 'secondary'}>
                {transaction.isLive ? 'Live' : 'Test'}
              </Badge>
            </div>
          </div>
        </div>

        {transaction.requestId && (
          <div>
            <label className='text-sm font-medium'>Request ID</label>
            <div className='mt-1 font-mono text-sm'>{transaction.requestId}</div>
          </div>
        )}

        <div className='grid grid-cols-2 gap-4'>
          <div>
            <label className='text-sm font-medium'>Created At</label>
            <div className='mt-1 text-sm'>{format(transaction.createdAt, 'PPpp')}</div>
          </div>
          <div>
            <label className='text-sm font-medium'>Processed At</label>
            <div className='mt-1 text-sm'>{format(transaction.processedAt, 'PPpp')}</div>
          </div>
        </div>

        {transaction.errorOccurred && (
          <div>
            <label className='text-sm font-medium text-red-600'>Error Details</label>
            <div className='mt-1 text-sm text-red-600'>
              {transaction.dataMessage || 'Unknown error occurred'}
            </div>
          </div>
        )}

        {transaction.txnResponseCode && (
          <div>
            <label className='text-sm font-medium'>Response Code</label>
            <div className='mt-1 font-mono text-sm'>{transaction.txnResponseCode}</div>
          </div>
        )}
      </div>
    </DialogContent>
  );

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>Payments</h1>
          <p className='text-muted-foreground'>Manage and monitor payment transactions</p>
          {user && <p className='text-xs text-green-600 mt-1'>✅ Authenticated as: {user.email}</p>}
          {!user && !authLoading && (
            <p className='text-xs text-red-600 mt-1'>❌ Not authenticated - Please log in</p>
          )}
        </div>

        <div className='flex items-center gap-2'>
          <PermissionGate permissions={[Permission.EXPORT_TRANSACTIONS]}>
            <Button variant='outline' size='sm'>
              <Download className='h-4 w-4 mr-2' />
              Export
            </Button>
          </PermissionGate>

          <Button onClick={loadData} variant='outline' size='sm'>
            <RefreshCw className='h-4 w-4 mr-2' />
            Refresh
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <SummaryCards />

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <CardTitle>Transactions ({totalTransactions})</CardTitle>
            <Button variant='outline' size='sm' onClick={() => setShowFilters(!showFilters)}>
              <Filter className='h-4 w-4 mr-2' />
              Filters
            </Button>
          </div>
        </CardHeader>

        <CardContent>
          <div className='flex items-center gap-2 mb-4'>
            <div className='flex-1'>
              <Input
                placeholder='Search by transaction ID, request ID...'
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
            <Button onClick={handleSearch}>
              <Search className='h-4 w-4 mr-2' />
              Search
            </Button>
          </div>

          {showFilters && (
            <div className='grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 p-4 border rounded-lg'>
              <Select
                value={filters.status?.[0] || ''}
                onValueChange={(value) =>
                  setFilters((prev) => ({
                    ...prev,
                    status: value ? [value as PaymentStatus] : undefined,
                  }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder='Status' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value=''>All Statuses</SelectItem>
                  <SelectItem value='completed'>Completed</SelectItem>
                  <SelectItem value='failed'>Failed</SelectItem>
                  <SelectItem value='pending'>Pending</SelectItem>
                  <SelectItem value='refunded'>Refunded</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={filters.paymentMethod?.[0] || ''}
                onValueChange={(value) =>
                  setFilters((prev) => ({
                    ...prev,
                    paymentMethod: value ? [value as PaymentMethod] : undefined,
                  }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder='Payment Method' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value=''>All Methods</SelectItem>
                  <SelectItem value='paymob'>Paymob</SelectItem>
                  <SelectItem value='test'>Test</SelectItem>
                </SelectContent>
              </Select>

              <Select
                value={filters.isLive?.toString() || ''}
                onValueChange={(value) =>
                  setFilters((prev) => ({
                    ...prev,
                    isLive: value === '' ? undefined : value === 'true',
                  }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder='Environment' />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value=''>All Environments</SelectItem>
                  <SelectItem value='true'>Live</SelectItem>
                  <SelectItem value='false'>Test</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Transactions Table */}
          <div className='rounded-md border'>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Transaction ID</TableHead>
                  <TableHead>Request ID</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Method</TableHead>
                  <TableHead>Environment</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={8} className='text-center py-8'>
                      Loading transactions...
                    </TableCell>
                  </TableRow>
                ) : transactions.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className='text-center py-8'>
                      No transactions found
                    </TableCell>
                  </TableRow>
                ) : (
                  transactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell className='font-mono text-sm'>
                        {transaction.paymobTransactionId}
                      </TableCell>
                      <TableCell className='font-mono text-sm'>
                        {transaction.requestId || '-'}
                      </TableCell>
                      <TableCell className='font-semibold'>
                        {formatCurrency(transaction.amount, transaction.currency)}
                      </TableCell>
                      <TableCell>{getStatusBadge(transaction.status)}</TableCell>
                      <TableCell className='capitalize'>{transaction.paymentMethod}</TableCell>
                      <TableCell>
                        <Badge variant={transaction.isLive ? 'default' : 'secondary'}>
                          {transaction.isLive ? 'Live' : 'Test'}
                        </Badge>
                      </TableCell>
                      <TableCell>{format(transaction.createdAt, 'MMM dd, yyyy HH:mm')}</TableCell>
                      <TableCell>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant='ghost' size='sm'>
                              <Eye className='h-4 w-4' />
                            </Button>
                          </DialogTrigger>
                          <TransactionDetailsDialog transaction={transaction} />
                        </Dialog>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {totalTransactions > 0 && (
            <Pagination
              pagination={createPaginationInfo(totalTransactions)}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
              className='border-t pt-4 mt-6'
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default PaymentsPage;
