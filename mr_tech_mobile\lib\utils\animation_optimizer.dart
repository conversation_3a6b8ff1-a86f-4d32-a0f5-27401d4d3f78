import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'dart:math' as math;

/// Animation performance optimization utilities
/// Provides optimized animations and performance monitoring
class AnimationOptimizer {
  // Private constructor to prevent instantiation
  AnimationOptimizer._();

  static bool _isInitialized = false;
  static bool _animationsEnabled = true;
  static double _animationScale = 1.0;

  /// Initialize animation optimizer
  static void initialize() {
    if (_isInitialized) return;

    // Check device capabilities and adjust animation settings
    _detectDeviceCapabilities();
    
    // Setup frame rate monitoring in debug mode
    if (kDebugMode) {
      _setupFrameRateMonitoring();
    }

    _isInitialized = true;
    debugPrint('AnimationOptimizer initialized - animations: $_animationsEnabled, scale: $_animationScale');
  }

  /// Detect device capabilities and adjust animation settings
  static void _detectDeviceCapabilities() {
    try {
      // Check if animations are disabled system-wide
      final timeDilation = SchedulerBinding.instance.timeDilation;
      if (timeDilation != 1.0) {
        _animationScale = 1.0 / timeDilation;
        debugPrint('Animation time dilation detected: $timeDilation');
      }

      // In a real app, you might check device specs here
      // For now, we'll use a simple heuristic
      _animationsEnabled = true;
      _animationScale = 1.0;

    } catch (e) {
      debugPrint('Error detecting device capabilities: $e');
      // Default to enabled animations
      _animationsEnabled = true;
      _animationScale = 1.0;
    }
  }

  /// Setup frame rate monitoring for performance analysis
  static void _setupFrameRateMonitoring() {
    if (!kDebugMode) return;

    SchedulerBinding.instance.addTimingsCallback((timings) {
      for (final timing in timings) {
        final frameTime = timing.totalSpan.inMicroseconds / 1000.0;
        if (frameTime > 16.67) { // More than 60 FPS threshold
          debugPrint('Slow frame detected: ${frameTime.toStringAsFixed(2)}ms');
        }
      }
    });
  }

  /// Create an optimized fade transition
  static Widget optimizedFadeTransition({
    required Animation<double> animation,
    required Widget child,
    bool alwaysIncludeSemantics = false,
  }) {
    if (!_animationsEnabled) {
      return child;
    }

    return FadeTransition(
      opacity: animation,
      alwaysIncludeSemantics: alwaysIncludeSemantics,
      child: RepaintBoundary(child: child),
    );
  }

  /// Create an optimized slide transition
  static Widget optimizedSlideTransition({
    required Animation<Offset> position,
    required Widget child,
    TextDirection? textDirection,
    bool transformHitTests = true,
  }) {
    if (!_animationsEnabled) {
      return child;
    }

    return SlideTransition(
      position: position,
      textDirection: textDirection,
      transformHitTests: transformHitTests,
      child: RepaintBoundary(child: child),
    );
  }

  /// Create an optimized scale transition
  static Widget optimizedScaleTransition({
    required Animation<double> scale,
    required Widget child,
    Alignment alignment = Alignment.center,
  }) {
    if (!_animationsEnabled) {
      return child;
    }

    return ScaleTransition(
      scale: scale,
      alignment: alignment,
      child: RepaintBoundary(child: child),
    );
  }

  /// Create an optimized rotation transition
  static Widget optimizedRotationTransition({
    required Animation<double> turns,
    required Widget child,
    Alignment alignment = Alignment.center,
  }) {
    if (!_animationsEnabled) {
      return child;
    }

    return RotationTransition(
      turns: turns,
      alignment: alignment,
      child: RepaintBoundary(child: child),
    );
  }

  /// Create an optimized animated container
  static Widget optimizedAnimatedContainer({
    Key? key,
    required Duration duration,
    Curve curve = Curves.linear,
    double? width,
    double? height,
    Color? color,
    Decoration? decoration,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    AlignmentGeometry? alignment,
    Widget? child,
    VoidCallback? onEnd,
  }) {
    if (!_animationsEnabled) {
      return Container(
        key: key,
        width: width,
        height: height,
        color: color,
        decoration: decoration,
        padding: padding,
        margin: margin,
        alignment: alignment,
        child: child,
      );
    }

    return RepaintBoundary(
      child: AnimatedContainer(
        key: key,
        duration: _scaleDuration(duration),
        curve: curve,
        width: width,
        height: height,
        color: color,
        decoration: decoration,
        padding: padding,
        margin: margin,
        alignment: alignment,
        onEnd: onEnd,
        child: child,
      ),
    );
  }

  /// Create an optimized animated opacity
  static Widget optimizedAnimatedOpacity({
    Key? key,
    required Widget child,
    required double opacity,
    required Duration duration,
    Curve curve = Curves.linear,
    VoidCallback? onEnd,
    bool alwaysIncludeSemantics = false,
  }) {
    if (!_animationsEnabled) {
      return Opacity(
        opacity: opacity,
        child: child,
      );
    }

    return RepaintBoundary(
      child: AnimatedOpacity(
        key: key,
        opacity: opacity,
        duration: _scaleDuration(duration),
        curve: curve,
        onEnd: onEnd,
        alwaysIncludeSemantics: alwaysIncludeSemantics,
        child: child,
      ),
    );
  }

  /// Create an optimized animated positioned
  static Widget optimizedAnimatedPositioned({
    Key? key,
    required Widget child,
    required Duration duration,
    Curve curve = Curves.linear,
    double? left,
    double? top,
    double? right,
    double? bottom,
    double? width,
    double? height,
    VoidCallback? onEnd,
  }) {
    if (!_animationsEnabled) {
      return Positioned(
        key: key,
        left: left,
        top: top,
        right: right,
        bottom: bottom,
        width: width,
        height: height,
        child: child,
      );
    }

    return RepaintBoundary(
      child: AnimatedPositioned(
        key: key,
        duration: _scaleDuration(duration),
        curve: curve,
        left: left,
        top: top,
        right: right,
        bottom: bottom,
        width: width,
        height: height,
        onEnd: onEnd,
        child: child,
      ),
    );
  }

  /// Create an optimized page route transition
  static PageRouteBuilder<T> optimizedPageRoute<T>({
    required Widget page,
    RouteSettings? settings,
    Duration transitionDuration = const Duration(milliseconds: 300),
    Duration reverseTransitionDuration = const Duration(milliseconds: 300),
    PageTransitionType transitionType = PageTransitionType.slide,
  }) {
    return PageRouteBuilder<T>(
      settings: settings,
      transitionDuration: _scaleDuration(transitionDuration),
      reverseTransitionDuration: _scaleDuration(reverseTransitionDuration),
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        if (!_animationsEnabled) {
          return child;
        }

        return RepaintBoundary(
          child: _buildPageTransition(
            animation: animation,
            secondaryAnimation: secondaryAnimation,
            child: child,
            transitionType: transitionType,
          ),
        );
      },
    );
  }

  /// Build page transition based on type
  static Widget _buildPageTransition({
    required Animation<double> animation,
    required Animation<double> secondaryAnimation,
    required Widget child,
    required PageTransitionType transitionType,
  }) {
    switch (transitionType) {
      case PageTransitionType.fade:
        return FadeTransition(
          opacity: animation,
          child: child,
        );
      
      case PageTransitionType.slide:
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(1.0, 0.0),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: animation,
            curve: Curves.easeInOut,
          )),
          child: child,
        );
      
      case PageTransitionType.scale:
        return ScaleTransition(
          scale: Tween<double>(
            begin: 0.0,
            end: 1.0,
          ).animate(CurvedAnimation(
            parent: animation,
            curve: Curves.easeInOut,
          )),
          child: child,
        );
      
      case PageTransitionType.rotation:
        return RotationTransition(
          turns: Tween<double>(
            begin: 0.25,
            end: 0.0,
          ).animate(CurvedAnimation(
            parent: animation,
            curve: Curves.easeInOut,
          )),
          child: child,
        );
    }
  }

  /// Create an optimized staggered animation
  static List<Animation<double>> createStaggeredAnimations({
    required AnimationController controller,
    required int count,
    Duration interval = const Duration(milliseconds: 100),
    Curve curve = Curves.easeOut,
  }) {
    if (!_animationsEnabled || count == 0) {
      return List.generate(count, (_) => AlwaysStoppedAnimation(1.0));
    }

    final animations = <Animation<double>>[];
    final intervalValue = interval.inMilliseconds / controller.duration!.inMilliseconds;
    
    for (int i = 0; i < count; i++) {
      final start = math.min(i * intervalValue, 1.0);
      final end = math.min(start + (1.0 - start), 1.0);
      
      animations.add(
        Tween<double>(begin: 0.0, end: 1.0).animate(
          CurvedAnimation(
            parent: controller,
            curve: Interval(start, end, curve: curve),
          ),
        ),
      );
    }
    
    return animations;
  }

  /// Scale duration based on animation settings
  static Duration _scaleDuration(Duration duration) {
    if (!_animationsEnabled) {
      return Duration.zero;
    }
    
    final scaledMilliseconds = (duration.inMilliseconds / _animationScale).round();
    return Duration(milliseconds: scaledMilliseconds);
  }

  /// Enable or disable animations
  static void setAnimationsEnabled(bool enabled) {
    _animationsEnabled = enabled;
    debugPrint('Animations ${enabled ? 'enabled' : 'disabled'}');
  }

  /// Set animation scale factor
  static void setAnimationScale(double scale) {
    _animationScale = math.max(0.1, math.min(2.0, scale));
    debugPrint('Animation scale set to $_animationScale');
  }

  /// Check if animations are enabled
  static bool get animationsEnabled => _animationsEnabled;

  /// Get current animation scale
  static double get animationScale => _animationScale;

  /// Get performance metrics
  static Map<String, dynamic> getPerformanceMetrics() {
    return {
      'animationsEnabled': _animationsEnabled,
      'animationScale': _animationScale,
      'isInitialized': _isInitialized,
    };
  }
}

/// Page transition types
enum PageTransitionType {
  fade,
  slide,
  scale,
  rotation,
}
